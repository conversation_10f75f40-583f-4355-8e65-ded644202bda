package com.cosfo.manage.system.service.Impl;

import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.system.mapper.SystemParametersMapper;
import com.cosfo.manage.system.model.po.SystemParameters;
import com.cosfo.manage.system.service.SystemParametersService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/24 12:47
 */
@Service
public class SystemParametersServiceImpl implements SystemParametersService {

    @Resource
    private SystemParametersMapper systemParametersMapper;

    @Override
    public ResultDTO listInitIcon() {
        SystemParameters parameters = systemParametersMapper.selectByKey(Constants.ICON_KEY);
        return ResultDTO.success(parameters.getParamValue());
    }

    @Override
    public SystemParameters selectByKey(String key) {
        return systemParametersMapper.selectBy<PERSON>ey(key);
    }
}
