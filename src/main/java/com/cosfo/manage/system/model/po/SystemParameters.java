package com.cosfo.manage.system.model.po;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Data;

/**
 * system_parameters
 * <AUTHOR>
@Data
public class SystemParameters implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 参数key
     */
    private String paramKey;

    /**
     * 参数value
     */
    private String paramValue;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 账号id
     */
    private Long accountId;

    private static final long serialVersionUID = 1L;
}