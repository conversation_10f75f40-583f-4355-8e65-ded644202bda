package com.cosfo.manage.wechat.bean.paymch;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import javax.xml.bind.annotation.adapters.XmlAdapter;
import java.util.List;

@Data
public class Detail {

    private Integer cost_price; // 可选 32
    // 订单原价，商户侧一张小票订单可能被分多次支付，订单原价用于记录整张小票的支付金额。当订单原价与支付金额不相等则被判定为拆单，无法享受优惠。

    private String receipt_id; // 可选 32 商家小票ID

    private List<GoodsDetail> goods_detail;// 服务商必填

    static class JsonXmlAdapter extends XmlAdapter<String, Detail> {

        @Override
        public String marshal(Detail v) throws Exception {
            return "<![CDATA[" + JSON.toJSONString(v) + "]]>";
        }

        @Override
        public Detail unmarshal(String v) throws Exception {
            return JSON.parseObject(v, Detail.class);
        }

    }
}
