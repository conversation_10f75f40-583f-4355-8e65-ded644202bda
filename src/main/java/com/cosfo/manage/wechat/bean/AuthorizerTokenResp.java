package com.cosfo.manage.wechat.bean;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

/**
 * @Package: com.cosfo.bean
 * @Description:
 * @author: <EMAIL>
 * @Date: 2022/4/27
 */
@Getter
@Setter
public class AuthorizerTokenResp extends BaseResult{
    @JSONField(alternateNames = {"authorizer_access_token", "component_access_token"})
    private String  accessToken;
    @JSONField(alternateNames = {"expires_in"})
    private Long    expiresIn;  //秒
    @JSONField(alternateNames = {"authorizer_refresh_token"})
    private String  refreshToken;
}
