package com.cosfo.manage.wechat.bean.paymch;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.common.util.JsonUtil;
import lombok.Data;

import javax.xml.bind.annotation.adapters.XmlAdapter;

@Data
public class SceneInfo {

    private H5Info h5_info;

    private StoreInfo store_info;

    @Data
    public static class H5Info {

        private String type;

        private String app_name;

        private String package_name;

        private String wap_url;

        private String wap_name;
    }

    @Data
    public static class StoreInfo {

        private String id;

        private String name;

        private String area_code;

        private String address;

    }

    static class JsonXmlAdapter extends XmlAdapter<String, SceneInfo> {

        @Override
        public String marshal(SceneInfo arg0) throws Exception {
            return "<![CDATA[" + JSON.toJSONString(arg0) + "]]>";
        }

        @Override
        public SceneInfo unmarshal(String arg0) throws Exception {
            return JSON.parseObject(arg0, SceneInfo.class);
        }
    }
}
