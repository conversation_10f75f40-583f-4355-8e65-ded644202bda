package com.cosfo.manage.wechat.bean.wxa.urllink;

import lombok.Data;

/**
 * @author: monna.chen
 * @Date: 2024/2/19 17:32
 * @Description:
 */
@Data
public class QueryUrlLinkInfoResult {
    /**
     * 小程序 appid
     */
    private String appid;

    /**
     * 小程序页面路径
     */
    private String path;

    /**
     * 小程序页面query
     */
    private String query;

    /**
     * 创建时间，为 Unix 时间戳
     */
    private Long create_time;

    /**
     * 到期失效时间，为 Unix 时间戳，0 表示永久生效
     */
    private Long expire_time;

    /**
     * 要打开的小程序版本。正式版为"release"，体验版为"trial"，开发版为"develop"
     */
    private String env_version;
}
