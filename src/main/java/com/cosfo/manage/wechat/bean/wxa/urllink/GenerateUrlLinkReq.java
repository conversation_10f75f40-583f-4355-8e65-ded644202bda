package com.cosfo.manage.wechat.bean.wxa.urllink;

import lombok.Data;

/**
 * @author: monna.chen
 * @Date: 2024/2/19 16:18
 * @Description:
 */
@Data
public class GenerateUrlLinkReq {
    /**
     * 通过 URL Link 进入的小程序页面路径，必须是已经发布的小程序存在的页面，不可携带 query 。path 为空时会跳转小程序主页
     */
    private String path;

    /**
     * 默认值0.小程序 URL Link 失效类型，失效时间：0，失效间隔天数：1
     */
    private Integer expire_type;

    /**
     * 到期失效的 URL Link 的失效时间，为 Unix 时间戳。生成的到期失效 URL Link 在该时间前有效。最长有效期为30天。expire_type 为 0 必填
     */
    private Long expire_time;

    /**
     * 到期失效的URL Link的失效间隔天数。生成的到期失效URL Link在该间隔时间到达前有效。最长间隔天数为30天。expire_type 为 1 必填
     */
    private Integer expire_interval;

}
