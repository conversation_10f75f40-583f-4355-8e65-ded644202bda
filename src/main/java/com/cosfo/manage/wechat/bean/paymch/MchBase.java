package com.cosfo.manage.wechat.bean.paymch;

import lombok.Data;

import javax.xml.bind.annotation.XmlTransient;

@Data
public abstract class MchBase {

    protected String return_code;

    protected String return_msg;

    protected String appid;

    protected String mch_id;

    protected String nonce_str;

    protected String sign;

    protected String sign_type;

    protected String result_code;

    protected String err_code;

    protected String err_code_des;

    /**
     * @since 2.8.5
     */
    protected String sub_appid;

    /**
     * @since 2.8.5
     */
    protected String sub_mch_id;

    @XmlTransient
    protected Boolean sign_status;

}
