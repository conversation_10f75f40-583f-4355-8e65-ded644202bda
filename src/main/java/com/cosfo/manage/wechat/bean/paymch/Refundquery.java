package com.cosfo.manage.wechat.bean.paymch;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 退款查询
 * 
 * <AUTHOR>
 * 
 */

@XmlRootElement(name = "xml")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class Refundquery {

	@XmlElement
	private String appid;
	
	@XmlElement
	private String mch_id;
	
	@XmlElement
	private String device_info;
	
	@XmlElement
	private String nonce_str;
	
	@XmlElement
	private String sign;
	
	@XmlElement
	private String sign_type;
	
	@XmlElement
	private String transaction_id;
	
	@XmlElement
	private String out_trade_no;
	
	@XmlElement
	private String out_refund_no;
	
	@XmlElement
	private String refund_id;
	
	/**
	 * @since 2.8.5
	 */
	@XmlElement
	private String sub_appid;

	/**
	 * @since 2.8.5
	 */
	@XmlElement
	private String sub_mch_id;
	
	/**
	 * @since 2.8.31
	 */
	@XmlElement
	private Integer offset;

	
}
