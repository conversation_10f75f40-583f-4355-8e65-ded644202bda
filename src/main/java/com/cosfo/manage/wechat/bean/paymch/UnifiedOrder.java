package com.cosfo.manage.wechat.bean.paymch;

import com.cosfo.manage.wechat.bean.AdaptorCDATA;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.math.BigDecimal;

@XmlRootElement(name = "xml")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class UnifiedOrder extends MchVersion{

    private String appid;

    private String mch_id;

    private String device_info;

    private String nonce_str;

    /**
     * @since 2.8.5
     */
    @XmlJavaTypeAdapter(value = Detail.JsonXmlAdapter.class)
    private Detail detail;

    @XmlJavaTypeAdapter(value = AdaptorCDATA.class)
    private String sign;

    /**
     * 签名类型
     * @since 2.8.5
     * @param sign_type HMAC-SHA256和MD5
     */
    private String sign_type;

    @XmlJavaTypeAdapter(value = AdaptorCDATA.class)
    private String body;

    @XmlJavaTypeAdapter(value = AdaptorCDATA.class)
    private String attach;

    private String out_trade_no;

    private String fee_type;

    private String total_fee;

    private BigDecimal order_amount;

    private String spbill_create_ip;

    private String time_start;

    private String time_expire;

    private String goods_tag;

    private String notify_url;

    /**
     * 支付类型
     *
     * @param trade_type
     *            <br>
     *
     *            JSAPI--公众号支付<br>
     *            NATIVE--原生扫码支付 <br>
     *            APP--APP支付 <br>
     *            MWEB--H5
     */
    private String trade_type;

    private String product_id;

    private String limit_pay;

    private String openid;

    /**
     * @since 2.8.5
     */
    private String sub_appid;

    /**
     * @since 2.8.5
     */
    private String sub_mch_id;

    /**
     * @since 2.8.5
     */
    private String sub_openid;

    /**
     * @since 2.8.21
     */
    @XmlJavaTypeAdapter(value= SceneInfo.JsonXmlAdapter.class)
    private SceneInfo scene_info;

    /**
     * @since 2.8.27
     */
    private String receipt;

    /**
     * @since 2.8.27
     */
    private String profit_sharing;
}
