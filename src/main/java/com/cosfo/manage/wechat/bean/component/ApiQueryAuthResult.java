package com.cosfo.manage.wechat.bean.component;

import com.cosfo.manage.wechat.bean.BaseResult;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class ApiQueryAuthResult extends BaseResult {
	private AuthorizationInfo authorization_info;

	@Data
	public static class AuthorizationInfo implements Serializable {
		private String authorizer_appid;
		private String authorizer_access_token;
		private Long   expires_in;  //秒
		private String authorizer_refresh_token;
		private List<FuncInfo> func_info;
	}

	@Data
	public static class FuncInfo implements Serializable {
		private FuncscopeCategory funcscope_category;
	}

	@Data
	public static class FuncscopeCategory implements Serializable {
		private Integer id;
	}

}
