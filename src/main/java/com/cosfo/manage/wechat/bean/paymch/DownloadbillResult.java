package com.cosfo.manage.wechat.bean.paymch;

import com.cosfo.manage.wechat.bean.BillResult;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "xml")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class DownloadbillResult extends MchBase implements BillResult {

	private String data;

	/**
	 * CSV 格式数据，包含UTF-8 头部信息。
	 * @since 2.8.31
	 * @return
	 */
	public String csvData() {
		if (data != null) {
			// UTF-8编码
			byte[] headCode = { (byte) 0xEF, (byte) 0xBB, (byte) 0xBF };
			return new String(headCode) + data;
		}
		return null;
	}
}
