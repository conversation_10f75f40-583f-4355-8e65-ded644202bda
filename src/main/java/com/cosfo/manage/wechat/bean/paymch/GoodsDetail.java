package com.cosfo.manage.wechat.bean.paymch;

import lombok.Data;

@Data
public class GoodsDetail {

    private String goods_id; // 必填 32 商品的编号

    private String wxpay_goods_id; // 可选 32 微信支付定义的统一商品编号

    private String goods_name; // 可选 256 商品名称

    private Integer quantity; // 必填 32 商品数量

    private Integer price; // 必填 32 商品单价，如果商户有优惠，需传输商户优惠后的单价

    private String goods_category;	//类目

    private String body;			//名称
}
