package com.cosfo.manage.wechat.model.po;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * wechat_lite_config
 * <AUTHOR>
@Data
public class WechatLiteConfig implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 授权人APPID
     */
    private String appid;

    /**
     * 小程序名称
     */
    private String name;

    /**
     * 微信原始id
     */
    private String originalId;

    /**
     * 授权的权限集
     */
    private String authIds;

    /**
     * 头像
     */
    private String headImg;

    /**
     * 主体名称
     */
    private String principalName;

    /**
     * -1 未认证 0 微信认证
     */
    private Integer verify;

    /**
     * 线上版本关联id
     */
    private Long onlineId;

    /**
     * 状态默认1
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}