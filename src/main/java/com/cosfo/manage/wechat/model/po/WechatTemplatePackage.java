package com.cosfo.manage.wechat.model.po;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * wechat_template_package
 * <AUTHOR>
@Data
public class WechatTemplatePackage implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 授权人APPID
     */
    private String appid;

    /**
     * 审核编号
     */
    private String auditId;

    /**
     * 版本状态
     */
    private Integer pkgStatus;

    /**
     * 版本号
     */
    private String version;

    /**
     * 驳回原因
     */
    private String remark;

    /**
     * 版本描述
     */
    private String pkgDesc;

    /**
     * 对应模板id
     */
    private Integer templateId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}