package com.cosfo.manage.wechat.model.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Setter
public class TenantTemplateDto implements Serializable {

    /**
     * 授权人APPID
     */
    private String appId;
    /**
     * 租户ID
     */
    private Long TenantId;
    /**
     * 租户名称
     */
    private String tenantName;
    /**
     * 授权人接口调用凭据
     */
    private String accessToken;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 线上版本对象
     */
    private OnlineVersionDto onlineVersionDto;

    /**
     * 审核版本对象
     */
    private AuditVersionDto auditVersionDto;

    /**
     * 体验版本对象
     */
    private ExpVersionDto expVersionDto;


}
