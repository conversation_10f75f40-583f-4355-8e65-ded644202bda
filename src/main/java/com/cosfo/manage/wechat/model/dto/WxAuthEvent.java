package com.cosfo.manage.wechat.model.dto;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * Created by maobg on 2017/7/12.
 */
@XStreamAlias("xml")
public class WxAuthEvent {
    @XStreamAlias("AppId")
    private String appId;

    @XStreamAlias("Encrypt")
    private String encrypt;

    @XStreamAlias("InfoType")
    private String infoType;

    @XStreamAlias("ComponentVerifyTicket")
    private String componentVerifyTicket;

    @XStreamAlias("CreateTime")
    private Long createTime;

    @XStreamAlias("PreAuthCode")
    private String preAuthCode;

    @XStreamAlias("AuthorizerAppid")
    private String authorizerAppid;

    @XStreamAlias("AuthorizationCode")
    private String authorizationCode;

    @XStreamAlias("AuthorizationCodeExpiredTime")
    private Long authorizationCodeExpiredTime;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getEncrypt() {
        return encrypt;
    }

    public void setEncrypt(String encrypt) {
        this.encrypt = encrypt;
    }

    public String getInfoType() {
        return infoType;
    }

    public void setInfoType(String infoType) {
        this.infoType = infoType;
    }

    public String getComponentVerifyTicket() {
        return componentVerifyTicket;
    }

    public void setComponentVerifyTicket(String componentVerifyTicket) {
        this.componentVerifyTicket = componentVerifyTicket;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getAuthorizerAppid() {
        return authorizerAppid;
    }

    public void setAuthorizerAppid(String authorizerAppid) {
        this.authorizerAppid = authorizerAppid;
    }

    public String getAuthorizationCode() {
        return authorizationCode;
    }

    public void setAuthorizationCode(String authorizationCode) {
        this.authorizationCode = authorizationCode;
    }

    public Long getAuthorizationCodeExpiredTime() {
        return authorizationCodeExpiredTime;
    }

    public void setAuthorizationCodeExpiredTime(Long authorizationCodeExpiredTime) {
        this.authorizationCodeExpiredTime = authorizationCodeExpiredTime;
    }

    public String getPreAuthCode() {
        return preAuthCode;
    }

    public void setPreAuthCode(String preAuthCode) {
        this.preAuthCode = preAuthCode;
    }
}
