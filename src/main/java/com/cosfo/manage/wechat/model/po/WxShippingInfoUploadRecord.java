package com.cosfo.manage.wechat.model.po;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * wx_shipping_info_upload_record
 * <AUTHOR>
@Data
public class WxShippingInfoUploadRecord implements Serializable {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 支付id
     */
    private Long paymentId;

    /**
     * 交易流水号（请求流水号）
     */
    private String transactionId;

    /**
     * 状态：1-已上传发货信息
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}