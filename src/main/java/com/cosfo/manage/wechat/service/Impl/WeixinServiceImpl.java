package com.cosfo.manage.wechat.service.Impl;

import com.cosfo.manage.common.context.EventType;
import com.cosfo.manage.common.context.WeiXinMsgType;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.wechat.api.AuthAPI;
import com.cosfo.manage.wechat.bean.WxConverter;
import com.cosfo.manage.wechat.bean.WxXmlMessage;
import com.cosfo.manage.wechat.bean.sns.Jscode2sessionResult;
import com.cosfo.manage.wechat.model.dto.WechatAuthorizerDto;
import com.cosfo.manage.wechat.model.po.WechatAuthorizer;
import com.cosfo.manage.wechat.service.AuthorizerService;
import com.cosfo.manage.wechat.service.WechatPackageService;
import com.cosfo.manage.wechat.service.WeixinService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;

@Service
@Slf4j
public class WeixinServiceImpl implements WeixinService {

    @Resource
    WechatPackageService wechatPackageService;
    @Resource
    AuthorizerService authorizerService;
    @Resource
    WeixinTpServiceImpl weixinTpServiceImpl;

    @Override
    public void processMessage(WechatAuthorizer wechatAuthorizer, WxXmlMessage message, String sesssion) {

        switch (message.getMsgType()){
            case WeiXinMsgType.EVENT:
            switch (message.getEvent()) {
                case EventType.WEAPP_AUDIT_SUCCESS:
                case EventType.WEAPP_AUDIT_FAIL:
                case EventType.WEAPP_AUDIT_DELAY:
                    wechatPackageService.changePackageStateByAuditMsg(message);

                default:
                    break;
            }

            default:
                break;
        }
    }

    @Override
    public ResultDTO getCode2Session(String appId, String code) throws Exception {
        Assert.notNull(appId,"appId不能为空");
        Assert.notNull(code,"登录code不能为空");
        WechatAuthorizerDto wechatAuthorizerDto = authorizerService.getAuthorizer();
        Jscode2sessionResult sessionResult = AuthAPI.componentJscode2session(appId, code, wechatAuthorizerDto.getAppId(), wechatAuthorizerDto.getAccessToken());
        weixinTpServiceImpl.assertSuccess(appId,sessionResult);
        return ResultDTO.success(WxConverter.toSessionKeyVo(sessionResult));
    }
}
