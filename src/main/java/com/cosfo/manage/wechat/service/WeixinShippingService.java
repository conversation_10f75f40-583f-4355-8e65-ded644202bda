package com.cosfo.manage.wechat.service;

import com.cosfo.manage.bill.model.po.Payment;

import java.util.List;

public interface WeixinShippingService {

    /**
     * 指定的小程序appId，根据支付单信息上传发货信息到微信
     * @param payment
     * @param orderId
     * @param appId
     */
    void uploadShippingInfo(Payment payment, Long orderId, String appId);

    /**
     * 根据订单和租户，查找支付单信息上传发货信息到微信
     * @param orderId
     * @param tenantId
     */
    void uploadShippingInfoByOrderId(Long orderId, Long tenantId);

    /**
     * 获取微信小程序开通发货信息管理的租户id
     * @return
     */
    List<Long> queryOpenShippingTenantIds();

    /**
     * 更新小程序开通发货信息管理的租户id
     * @param tenantIds
     */
    void changeOpenShippingTenantIds(List<Long> tenantIds);
}
