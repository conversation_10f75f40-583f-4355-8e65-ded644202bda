package com.cosfo.manage.wechat.service.Impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.context.*;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.common.util.DateUtil;
import com.cosfo.manage.common.util.NumUtils;
import com.cosfo.manage.common.util.RedisUtils;
import com.cosfo.manage.common.context.WeiXinMsgType;
import com.cosfo.manage.common.util.XstreamUtil;
import com.cosfo.manage.common.util.aes.WXBizMsgCrypt;
import com.cosfo.manage.system.service.Impl.DictSysParameter;
import com.cosfo.manage.tenant.mapper.TenantAuthConnectionMapper;
import com.cosfo.manage.tenant.model.po.TenantAuthConnection;
import com.cosfo.manage.wechat.api.ComponentApi;
import com.cosfo.manage.wechat.bean.AuthorizerTokenResp;
import com.cosfo.manage.wechat.bean.BaseResult;
import com.cosfo.manage.wechat.bean.WxConverter;
import com.cosfo.manage.wechat.bean.WxXmlMessage;
import com.cosfo.manage.wechat.bean.component.PreAuthCode;
import com.cosfo.manage.wechat.mapper.WechatAuthorizerMapper;
import com.cosfo.manage.wechat.model.dto.PreAuthCodeDto;
import com.cosfo.manage.wechat.model.dto.WechatAuthorizerDto;
import com.cosfo.manage.wechat.model.dto.WxAuthEvent;
import com.cosfo.manage.wechat.model.po.WechatAuthorizer;
import com.cosfo.manage.wechat.service.AuthorizerService;
import com.cosfo.manage.wechat.service.WeixinService;
import com.cosfo.manage.wechat.service.WeixinTpService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.net.URISyntaxException;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class WeixinTpServiceImpl implements WeixinTpService {

    private static final Integer ERR_CODE_USER_LIMITED = 50002;
    private static final List<Integer> ERR_CODE_TOKEN_EXPIRE = Arrays.asList(40001,42001);

    @Resource
    AuthorizerService authorizerService;
    @Resource
    DictSysParameter dictSysParameter;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    WechatAuthorizerMapper wechatAuthorizerMapper;
    @Resource
    TenantAuthConnectionMapper tenantAuthConnectionMapper;
    @Resource
    WeixinService weixinService;



    @Override
    public String processAuthEvent(WechatAuthorizerDto wechatAuthorizerDto, Map<String, String> params, String postBody) {
        //log.info("开始处理授权变更通知推送 wechatAuthorizerDto={}",wechatAuthorizerDto.toString());
        log.info(Base64.encodeBase64String(UUID.randomUUID().toString().replaceAll("-","").getBytes()));
        log.info("开始处理授权变更通知推送 params={}",params.toString());
        log.info("开始处理授权变更通知推送 postBody={}", postBody);
        try {
            String msgSignature = params.get(WechatCheckParEnum.IN_AUDIT.getStr());
            String timestamp  = params.get(WechatCheckParEnum.AUDIT_SUCCESS.getStr());
            String nonce = params.get(WechatCheckParEnum.AUDIT_FAIL.getStr());
            //XStream xstream = XStreamFactory.getStream(WxAuthEvent.class);
            WxAuthEvent cryptAuthEvent = XstreamUtil.xmlToObject(postBody,WxAuthEvent.class);
            if (ObjectUtils.isEmpty(wechatAuthorizerDto)){
                wechatAuthorizerDto = authorizerService.getAuthorizer();
            }
            WXBizMsgCrypt wxBizMsgCrypt = new WXBizMsgCrypt(wechatAuthorizerDto.getToken(), wechatAuthorizerDto.getEncodingAesKey(),wechatAuthorizerDto.getAppId());
            String decryptBody = wxBizMsgCrypt.decryptMsg(msgSignature, timestamp, nonce, cryptAuthEvent.getEncrypt());
            if(null != decryptBody){
                log.info("authevent postbody decrypt={}",decryptBody.replaceAll("\\s", ""));
            }
            WxAuthEvent authEvent = XstreamUtil.xmlToObject(decryptBody,WxAuthEvent.class);
            log.info("推送接收内容 decrypt={}",JSON.toJSONString(authEvent));
            log.info("***********事件类型 infoType={}*************",authEvent.getInfoType());
            if(StringUtils.equals(authEvent.getInfoType(), InfoType.COMPONENT_VERIFY_TICKET.getStr())){
                authorizerService.updateTpTicket(wechatAuthorizerDto.getAppId(),authEvent.getComponentVerifyTicket());
                authorizerService.updateTpToken(Boolean.FALSE,wechatAuthorizerDto);
            }else {
                if(StringUtils.equals(authEvent.getInfoType(), InfoType.AUTHORIZED.getStr())){
                    WechatAuthorizer authorizer = authorizerService.authorized(wechatAuthorizerDto,authEvent.getAuthorizerAppid(),authEvent.getAuthorizationCode(),authEvent.getPreAuthCode());
                }else if(StringUtils.equals(authEvent.getInfoType(), InfoType.UPDATE_AUTHORIZED.getStr())){
                    authorizerService.authorized(wechatAuthorizerDto,authEvent.getAuthorizerAppid(),authEvent.getAuthorizationCode(),authEvent.getPreAuthCode());
                }else if(StringUtils.equals(authEvent.getInfoType(), InfoType.UN_AUTHORIZED.getStr())){
                    authorizerService.unAuthorized(authEvent.getAuthorizerAppid());
                }
            }
        }catch (Exception e){
            log.error(e.getMessage(),e);
        }
        return "success";
    }

    @Override
    public String tenantAuthEvent(WechatAuthorizerDto wechatAuthorizerDto, Map<String, String> params, String postBody) {
        try {
            String msgSignature = params.get(WechatCheckParEnum.IN_AUDIT.getStr());
            String timestamp  = params.get(WechatCheckParEnum.AUDIT_SUCCESS.getStr());
            String nonce = params.get(WechatCheckParEnum.AUDIT_FAIL.getStr());
            WxAuthEvent cryptAuthEvent = XstreamUtil.xmlToObject(postBody,WxAuthEvent.class);
            wechatAuthorizerDto = authorizerService.getAuthorizer();
            WXBizMsgCrypt wxBizMsgCrypt = new WXBizMsgCrypt(dictSysParameter.getParameter(SysParamKey.SYS_PARAM_KEY_TP_TOKEN.getKey(),""),dictSysParameter.getParameter(SysParamKey.SYS_PARAM_KEY_TP_ENCODING_AES_KEY.getKey(),""), wechatAuthorizerDto.getAppId());
            String decryptBody = wxBizMsgCrypt.decryptMsg(msgSignature, timestamp, nonce, cryptAuthEvent.getEncrypt());
            if(null != decryptBody){
                log.info("authevent postbody decrypt={}",decryptBody.replaceAll("\\s", ""));
            }
            WxAuthEvent authEvent = XstreamUtil.xmlToObject(decryptBody,WxAuthEvent.class);
            if(StringUtils.equals(authEvent.getInfoType(), InfoType.AUTHORIZED.getStr())){
                WechatAuthorizer authorizer = authorizerService.authorized(wechatAuthorizerDto,authEvent.getAuthorizerAppid(),authEvent.getAuthorizationCode(),authEvent.getPreAuthCode());
            }else if(StringUtils.equals(authEvent.getInfoType(), InfoType.UPDATE_AUTHORIZED.getStr())){
                //authorizerService.authorized();
            }else if(StringUtils.equals(authEvent.getInfoType(), InfoType.UN_AUTHORIZED.getStr())){
                //authorizerService.unAuthorized();
            }
        }catch (Exception e){
            log.error(e.getMessage(),e);
        }
        return "success";
    }

    @Override
    public ResultDTO createPreAuthUrl(Long tenantId, HttpServletRequest request) throws URISyntaxException {
        WechatAuthorizerDto wechatAuthorizerDto = authorizerService.getAuthorizer();
        PreAuthCodeDto preAuthCodeDto = new PreAuthCodeDto();
        preAuthCodeDto.setTenantId(tenantId);
        URIBuilder preAuthUrlBuilder = null;
        URIBuilder backBuilder = new URIBuilder(this.getBaseUrl(request) + "/weixin/tenant/authcallback");
        if(tenantId != null) {
            backBuilder.addParameter(CommonContext.PARM_NAME_TENANT.getStr(), String.valueOf(tenantId));
        }
        PreAuthCode preAuthCode = ComponentApi.apiCreatePreAuthCode(wechatAuthorizerDto.getAccessToken(),wechatAuthorizerDto.getAppId());
        preAuthCodeDto.setPreAuthCode(preAuthCode.getPreAuthCode());
        preAuthUrlBuilder = new URIBuilder("https://mp.weixin.qq.com/cgi-bin/componentloginpage");
        preAuthUrlBuilder.setParameter("component_appid",wechatAuthorizerDto.getAppId());
        preAuthUrlBuilder.setParameter("pre_auth_code", preAuthCodeDto.getPreAuthCode());
        preAuthUrlBuilder.setParameter("redirect_uri", backBuilder.build().toString());
        preAuthUrlBuilder.setParameter("auth_type", "3");
        //这里将preAuthCodeDto缓存
        redisUtils.set(preAuthCodeDto.getPreAuthCode(),preAuthCodeDto, TimeUnit.DAYS.toMillis(1));
        log.info("preAuthCodeDto缓存,key=cosfo_{}, expires={}", preAuthCodeDto.getPreAuthCode(),preAuthCode.getExpiresIn());
        return ResultDTO.success(preAuthUrlBuilder.build().toString());
    }

    @Override
    public void updateAuthorizerTenant(String authCode, Long tenantId) {
        log.info("授权回跳参数 authCode={}",authCode);
        log.info("授权回跳参数 tenantId={}",tenantId);
        try {
            if(tenantId == null){
                return;
            }

            //授权码查询appId
            WechatAuthorizer wechatAuthorizer = wechatAuthorizerMapper.selectByAuthCode(authCode);
            if(ObjectUtils.isEmpty(wechatAuthorizer)) {
                //发现有后台通知和回调的authCode不一致问题,查询最后2分钟授权过来没有关联的商户进行关联
                Date time = new Date(System.currentTimeMillis() - 2 * 60 * 1000L);
                List<WechatAuthorizer> Authorizers = wechatAuthorizerMapper.selectByTime(LiteConfigPublicConsts.NORMAL_CONFIG_STATE.getCode(),time);
                if (!CollectionUtils.isEmpty(Authorizers)){
                    wechatAuthorizer = Authorizers.get(NumberConstants.ZERO);
                }
            }
            if(!ObjectUtils.isEmpty(wechatAuthorizer)) {
                String appId = wechatAuthorizer.getAppId();
                //根据appId查询
                TenantAuthConnection tenantAuthConnection =  tenantAuthConnectionMapper.selectByAppId(appId);
                TenantAuthConnection authConnection = new TenantAuthConnection();
                authConnection.setId(tenantAuthConnection.getId());
                authConnection.setTenantId(tenantId);
                tenantAuthConnectionMapper.updateByPrimaryKeySelective(authConnection);
            }

        }catch (Exception e){
            log.error("授权回跳异常",e);
        }
    }

    @Override
    public void processMessage(String appId, Map<String, String> params, String body) {
        log.info("开始处理处理第三方开发者，消息事件 params={}",params.toString());
        log.info("开始处理处理第三方开发者，消息事件 postBody={}", body);
        //授权方信息
        WechatAuthorizer wechatAuthorizer = wechatAuthorizerMapper.selectByAppId(appId);
        //第三方信息
        WechatAuthorizerDto wechatAuthorizerDto = authorizerService.getAuthorizer();
        if (wechatAuthorizerDto.getAppId().equals(appId) && ObjectUtils.isEmpty(wechatAuthorizer)){
            wechatAuthorizer = WxConverter.toAuthorizer(wechatAuthorizerDto);
        }
        String msgSignature = params.get(WechatCheckParEnum.IN_AUDIT.getStr());
        String timestamp  = params.get(WechatCheckParEnum.AUDIT_SUCCESS.getStr());
        String nonce = params.get(WechatCheckParEnum.AUDIT_FAIL.getStr());
        try {
            String xmlMessage = decodeMessage(wechatAuthorizerDto,wechatAuthorizer,msgSignature,timestamp,nonce, body);
            if (StringUtils.isBlank(xmlMessage)) {
                log.error("消息解密失败 appId={}",appId);
            } else {
                log.info("消息解密成功 xmlMessage={}", xmlMessage);
                WxXmlMessage message = XstreamUtil.xmlToObject(xmlMessage,WxXmlMessage.class);
                log.info("***********消息类型 msgType={}*************",message.getMsgType());
                if(NumUtils.isEqual(wechatAuthorizer.getAppType(), AppType.WEIXIN_LITE.getCode())){
                        if(StringUtils.equals(message.getMsgType(), WeiXinMsgType.EVENT)){
                            log.info("***********事件类型 eventType={}*************",message.getEvent());
                            //事件类，全部程序处理
                            weixinService.processMessage(wechatAuthorizer, message, null);
                        }
                }
            }
        } catch (Exception e){
            log.info("处理第三方开发者事件失败", e);
        }
    }

    @Override
        public String decodeMessage(WechatAuthorizerDto authorizerDto,WechatAuthorizer wechatAuthorizer, String msgSignature, String timestamp, String nonce, String body) {
        try {
            WxXmlMessage cryptMessage = XstreamUtil.xmlToObject(body,WxXmlMessage.class);
            if(StringUtils.isBlank(cryptMessage.getEncrypt())){
                return body.replace("MsgID","MsgId");
            }
            WXBizMsgCrypt wxBizMsgCrypt = new WXBizMsgCrypt(authorizerDto.getToken(), authorizerDto.getEncodingAesKey(), authorizerDto.getAppId());
            String decryptBody = wxBizMsgCrypt.decryptMsg(msgSignature, timestamp, nonce, cryptMessage.getEncrypt());
            //微信消息中消息ID，有两种节点定义,MsgId-消息流水号、MsgID--模板消息发送结果
            return decryptBody.replace("MsgID","MsgId");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }


    /**
     *  获取应用授权token
     *  manage
     */
    public WechatAuthorizerDto getAuthToken(WechatAuthorizerDto authorizer) throws Exception {
        AuthorizerTokenResp authorizerTokenResp = null;
        if(AppType.WEIXIN_TP.getCode().equals(authorizer.getAppType())){
            authorizerTokenResp = ComponentApi.apiComponentToken(authorizer.getAppId(), authorizer.getAccessSecret(), authorizer.getTicket());
        }else {
            WechatAuthorizerDto wechatAuthorizerDto = authorizerService.getAuthorizer();
            WechatAuthorizer wechatAuthorizer = null;
            if (StringUtils.isBlank(authorizer.getRefreshToken())){
                wechatAuthorizer = authorizerService.getAuthInfo(authorizer.getAppId(),authorizer.getAuthorizationCode());
                authorizerTokenResp = ComponentApi.api_authorizer_token(wechatAuthorizerDto.getAccessToken(), wechatAuthorizerDto.getAppId(),wechatAuthorizer.getAppId(),wechatAuthorizer.getRefreshToken());
            }else {
                authorizerTokenResp = ComponentApi.api_authorizer_token(wechatAuthorizerDto.getAccessToken(), wechatAuthorizerDto.getAppId(),authorizer.getAppId(),authorizer.getRefreshToken());
            }
        }
        log.info("***********authorizerTokenResp={}*************", JSON.toJSONString(authorizerTokenResp));
        if(NumUtils.isEqual(authorizerTokenResp.getErrcode(),ERR_CODE_USER_LIMITED)){
            //该帐号已冻结,tp无法更新token了
            authorizer.setStatus(LiteConfigPublicConsts.ONLINE_RETAILERS_OFF.getCode());
            return authorizer;
        }
        assertSuccess(authorizer.getAppId(),authorizerTokenResp);
        authorizer.setAccessToken(authorizerTokenResp.getAccessToken());
        Date time = new Date(System.currentTimeMillis() + authorizerTokenResp.getExpiresIn() * 1000L);
        authorizer.setAccessTokenExpiretime(DateUtil.toLocalDate(time));
        authorizer.setRefreshToken(authorizerTokenResp.getRefreshToken());

        return  authorizer;
    }

    /**
     * manage
     * @param appId
     * @param response
     * @throws Exception
     */
    public void assertSuccess(String appId, BaseResult response) throws Exception {
        if(null == response) {
            throw new Exception("wechat.api.call.fail,调用微信服务失败");
        }else if(!response.successed()){
            if(ERR_CODE_TOKEN_EXPIRE.contains(response.getErrcode())){
                //此处刷新token
                authorizerService.refreshAuthorizerToken(Boolean.TRUE,appId);
            }
            throw new Exception(response.getErrcode() +Optional.ofNullable(response.getErrmsg()).orElse(String.valueOf(response.getErrcode())));
        }
    }

    public String getBaseUrl(HttpServletRequest request) {
        String scheme = dictSysParameter.getParameter(SysParamKey.SYS_PARAM_KEY_SCHEME.getKey(),"");
        /*if (org.apache.commons.lang3.StringUtils.isNotBlank(request.getHeader("x-forwarded-proto"))) {
            scheme = request.getHeader("x-forwarded-proto");
        } else if (org.apache.commons.lang3.StringUtils.isNotBlank(request.getHeader("scheme"))) {
            scheme = request.getHeader("scheme");
        }*/

        String host = dictSysParameter.getParameter().getBaseUrl();
        /*if (org.apache.commons.lang3.StringUtils.isNotBlank(request.getHeader("x-forwarded-host"))) {
            host = request.getHeader("x-forwarded-host");
        } else if (StringUtils.isNotBlank(request.getHeader("host"))) {
            host = request.getHeader("host");
        }*/
        return scheme + "://" + host;
    }
}
