package com.cosfo.manage.wechat.service.Impl;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.context.LiteConfigPublicConsts;
import com.cosfo.manage.common.context.SysParamKey;
import com.cosfo.manage.common.context.VersionStatus;
import com.cosfo.manage.common.context.WxDomainEnum;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.common.result.ResultDTOEnum;
import com.cosfo.manage.system.service.Impl.DictSysParameter;
import com.cosfo.manage.tenant.mapper.TenantAuthConnectionMapper;
import com.cosfo.manage.wechat.api.ComponentApi;
import com.cosfo.manage.wechat.api.WxaAPI;
import com.cosfo.manage.wechat.bean.BaseResult;
import com.cosfo.manage.wechat.bean.WxConverter;
import com.cosfo.manage.wechat.bean.WxSetPrivacyReq;
import com.cosfo.manage.wechat.bean.privateSettingResp;
import com.cosfo.manage.wechat.bean.wxa.*;
import com.cosfo.manage.wechat.mapper.WechatAuthorizerMapper;
import com.cosfo.manage.wechat.mapper.WechatLiteConfigMapper;
import com.cosfo.manage.wechat.mapper.WechatTemplatePackageMapper;
import com.cosfo.manage.wechat.model.dto.*;
import com.cosfo.manage.wechat.model.po.WechatAuthorizer;
import com.cosfo.manage.wechat.model.po.WechatLiteConfig;
import com.cosfo.manage.wechat.model.po.WechatTemplatePackage;
import com.cosfo.manage.wechat.model.vo.BindTesterVo;
import com.cosfo.manage.wechat.model.vo.CommitAuditVo;
import com.cosfo.manage.wechat.model.vo.CommitCodePreVo;
import com.cosfo.manage.wechat.model.vo.PrivateSettingVo;
import com.cosfo.manage.wechat.service.AuthorizerService;
import com.cosfo.manage.wechat.service.WeixinTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class WeixinTemplateServiceImpl implements WeixinTemplateService {

    @Resource
    WechatAuthorizerMapper wechatAuthorizerMapper;
    @Resource
    TenantAuthConnectionMapper tenantAuthConnectionMapper;
    @Resource
    AuthorizerService authorizerService;
    @Resource
    WechatTemplatePackageMapper templatePackageMapper;
    @Resource
    WechatLiteConfigMapper wechatLiteConfigMapper;
    @Resource
    DictSysParameter dictSysParameter;


//
//    @Override
//    public List<TenantTemplateDto> getAuthTenants() {
//        List<TenantTemplateDto> templateInfos = tenantAuthConnectionMapper.selectTenantInfos();
//        List<Integer> onlineStatus = new ArrayList<>();
//        onlineStatus.add(VersionStatus.ONLINE_VERSION.getCode());
//        List<Integer> auditStatus = new ArrayList<>();
//        auditStatus.add(VersionStatus.AUDIT_ING.getCode());
//        auditStatus.add(VersionStatus.AUDIT_UNPASS.getCode());
//        auditStatus.add(VersionStatus.AUDIT_PASS.getCode());
//        auditStatus.add(VersionStatus.AUDIT_DELAY.getCode());
//        List<Integer> expStatus = new ArrayList<>();
//        expStatus.add(VersionStatus.DEV_VERSION.getCode());
//
//        templateInfos.stream().forEach(authorizer->{
//            List<WechatTemplatePackage> OnlinePackages = templatePackageMapper.selectPackageListByStatus(authorizer.getAppId(),onlineStatus);
//            if (!CollectionUtils.isEmpty(OnlinePackages)){
//                authorizer.setOnlineVersionDto(WxConverter.toOnlineDto(OnlinePackages.get(NumberConstants.ZERO)));
//            }
//            List<WechatTemplatePackage> auditPackages = templatePackageMapper.selectPackageListByStatus(authorizer.getAppId(),auditStatus);
//            if (!CollectionUtils.isEmpty(auditPackages)){
//                authorizer.setAuditVersionDto(WxConverter.toAuditDto(auditPackages.get(NumberConstants.ZERO)));
//            }
//            List<WechatTemplatePackage> expPackages = templatePackageMapper.selectPackageListByStatus(authorizer.getAppId(),expStatus);
//            if (!CollectionUtils.isEmpty(expPackages)){
//                authorizer.setExpVersionDto(WxConverter.toExpDto(expPackages.get(NumberConstants.ZERO)));
//            }
//        });
//
//        return templateInfos;
//    }
//
//    @Override
//    public List<WxTemplateDTO> getTemplateList() {
//        WechatAuthorizerDto wechatAuthorizerDto =  authorizerService.getAuthorizer();
//        Assert.notNull(wechatAuthorizerDto,"app对象为空");
//        List<TemplateItem> templateList  = WxaAPI.gettemplatelist(wechatAuthorizerDto.getAccessToken()).getTemplate_list();
//        if (!CollectionUtils.isEmpty(templateList)){
//            return  templateList.stream().sorted(Comparator.comparing(TemplateItem::getTemplate_id).reversed()).map(WxConverter::toDto).collect(Collectors.toList());
//        }else{
//            return null;
//        }
//    }
//
//    @Override
//    public List<DraftDto> getDraftList() {
//        WechatAuthorizerDto wechatAuthorizerDto =  authorizerService.getAuthorizer();
//        List<TemplateItem> response = WxaAPI.gettemplatedraftlist(wechatAuthorizerDto.getAccessToken()).getDraft_list();
//        if (!CollectionUtils.isEmpty(response)){
//            return  response.stream().sorted(Comparator.comparing(TemplateItem::getDraft_id).reversed()).map(WxConverter::toDraftDto).collect(Collectors.toList());
//        }else {
//            return null;
//        }
//    }
//
//    @Override
//    public byte[] getTpQrCode(String appId, String path) throws IOException {
//        Assert.notNull(appId,"appId is null");
//        WechatAuthorizer wechatAuthorizer =  authorizerService.getAccessTokenByAppId(appId);
//        Assert.notNull(wechatAuthorizer,"app对象为空");
//        return WxaAPI.get_qrcode(wechatAuthorizer.getAccessToken(),path);
//    }

    @Override
    public void bindTester(BindTesterVo bindTesterVo) {
        Assert.notNull(bindTesterVo.getAppId(),"appId is null");
        WechatAuthorizer wechatAuthorizer =  authorizerService.getAccessTokenByAppId(bindTesterVo.getAppId());
        WxaAPI.bind_tester(wechatAuthorizer.getAccessToken(),bindTesterVo.getWeChatId());
    }

//    @Override
//    public void addTpDraftToTemplate(String draftId) {
//        WechatAuthorizerDto wechatAuthorizerDto =  authorizerService.getAuthorizer();
//        WxaAPI.addtotemplate(wechatAuthorizerDto.getAccessToken(),draftId);
//    }
//
//    @Override
//    public void delTpTemplate(String templateId) {
//        WechatAuthorizerDto wechatAuthorizerDto =  authorizerService.getAuthorizer();
//        WxaAPI.deletetemplate(wechatAuthorizerDto.getAccessToken(),templateId);
//    }
//
//    @Override
//    public void commitCodeExperience(CommitCodePreVo commitCodePreVo) throws Exception {
//        Assert.notNull(commitCodePreVo.getTemplateId(),"templateId is null");
//        Assert.notNull(commitCodePreVo.getUserVersion(),"userVersion is null");
//        Assert.notNull(commitCodePreVo.getUserDesc(),"userDesc is null");
//        List<WechatAuthorizer> authorizerList = new ArrayList<>();
//        if (!StringUtils.isEmpty(commitCodePreVo.getAppId())){
//            WechatAuthorizer wechatAuthorizer =  authorizerService.getAccessTokenByAppId(commitCodePreVo.getAppId());
//            if (ObjectUtils.isEmpty(wechatAuthorizer)) {
//                throw new Exception("appId不存在对象");
//            }
//            authorizerList.add(wechatAuthorizer);
//        }else {
//            authorizerList = wechatAuthorizerMapper.getAuthTenants();
//        }
//        authorizerList.stream().forEach(info->{
//            log.info("生成体验版的appId appId={}",info.getAppId());
//            Commit commit = new Commit();
//            commit.setExt_json(getExtJson(info.getAppId()));
//            commit.setTemplate_id(commitCodePreVo.getTemplateId().toString());
//            commit.setUser_desc(commitCodePreVo.getUserDesc());
//            commit.setUser_version(commitCodePreVo.getUserVersion());
//            WxaAPI.commit(info.getAccessToken(),commit);
//            WechatTemplatePackage wechatTemplatePackage = templatePackageMapper.selectPackageByStatus(info.getAppId(),VersionStatus.DEV_VERSION.getCode());
//            if (ObjectUtils.isEmpty(wechatTemplatePackage)){
//                WechatTemplatePackage Package = new WechatTemplatePackage();
//                Package.setAppid(info.getAppId());
//                Package.setPkgStatus(VersionStatus.DEV_VERSION.getCode());
//                Package.setCreateTime(LocalDateTime.now());
//                Package.setVersion(commitCodePreVo.getUserVersion());
//                Package.setTemplateId(commitCodePreVo.getTemplateId());
//                Package.setPkgDesc(commitCodePreVo.getUserDesc());
//                templatePackageMapper.insertSelective(Package);
//            }else {
//                wechatTemplatePackage.setPkgDesc(commitCodePreVo.getUserDesc());
//                wechatTemplatePackage.setTemplateId(commitCodePreVo.getTemplateId());
//                wechatTemplatePackage.setVersion(commitCodePreVo.getUserVersion());
//                wechatTemplatePackage.setUpdateTime(LocalDateTime.now());
//                templatePackageMapper.updateByPrimaryKeySelective(wechatTemplatePackage);
//            }
//        });
//        log.info("***********************生成体验版结束***********************");
//    }

//    private String getExtJson(String appId){
//        ExtJsonDto extJsonDto = new ExtJsonDto();
//        extJsonDto.setExtEnable(Boolean.TRUE);
//        extJsonDto.setExtAppid(appId);
//        return JSONObject.toJSONString(extJsonDto);
//    }
//
//    @Override
//    public void setPrivacySetting(PrivateSettingVo privateSettingVo,int ver) throws Exception {
//        Assert.notNull(privateSettingVo,"privateSettingVo is null");
//        Assert.notNull(privateSettingVo.getAppId(),"appId is null");
//        WechatAuthorizer wechatAuthorizer =  authorizerService.getAccessTokenByAppId(privateSettingVo.getAppId());
//        if (ObjectUtils.isEmpty(wechatAuthorizer)) {
//            throw new Exception("appId不存在对象");
//        }
//        WxSetPrivacyReq wxSetPrivacyReq = new WxSetPrivacyReq();
//        wxSetPrivacyReq.setPrivacy_ver(ver);
//        wxSetPrivacyReq.setOwner_setting(privateSettingVo.getOwner_setting());
//        wxSetPrivacyReq.setSetting_list(privateSettingVo.getSetting_list());
//        ComponentApi.setPrivacySetting(wechatAuthorizer.getAccessToken(),wxSetPrivacyReq);
//    }
//
//    public void assertSuccess(String appId, BaseResult response) throws Exception {
//        if(null == response) {
//            throw new Exception("wechat.api.call.fail,调用微信服务失败");
//        }else if(!response.successed()){
//            throw new Exception(response.getErrcode() + Optional.ofNullable(response.getErrmsg()).orElse(String.valueOf(response.getErrcode())));
//        }
//    }
//
//    @Override
//    public ResultDTO submitAuditPackage(CommitAuditVo commitAuditVo) throws Exception {
//        List<Integer> devList = new ArrayList<>();
//        devList.add(VersionStatus.DEV_VERSION.getCode());
//        devList.add(VersionStatus.AUDIT_UNPASS.getCode());
//        List<WechatAuthorizer> authorizers = new ArrayList<>();
//        if (!StringUtils.isEmpty(commitAuditVo.getAppId())){
//            List<WechatTemplatePackage> templatePackages = templatePackageMapper.selectPackageListByStatus(commitAuditVo.getAppId(),devList);
//            if (CollectionUtils.isEmpty(templatePackages)) {
//                return ResultDTO.fail("非开发版本或者失败版本不可提交审核");
//            }
//
//            WechatAuthorizer wechatAuthorizer =  authorizerService.getAccessTokenByAppId(commitAuditVo.getAppId());
//            authorizers.add(wechatAuthorizer);
//        }else {
//            List<WechatTemplatePackage> templatePackages = templatePackageMapper.selectPagListByStatus(devList);
//            if (!CollectionUtils.isEmpty(templatePackages)){
//                List<String> appIds = new ArrayList<>();
//                templatePackages.stream().forEach(templatePackage->{
//                    appIds.add(templatePackage.getAppid());
//                });
//                authorizers = authorizerService.getAuthorizers(appIds);
//            }else {
//                return ResultDTO.fail("全量提交审核：暂无可提交审核的小程序");
//            }
//        }
//        List<Integer> auditList = new ArrayList<>();
//        auditList.add(VersionStatus.AUDIT_ING.getCode());
//        auditList.add(VersionStatus.AUDIT_PASS.getCode());
//        auditList.add(VersionStatus.AUDIT_UNPASS.getCode());
//        auditList.add(VersionStatus.AUDIT_DELAY.getCode());
//
//        List<Integer> continueList = new ArrayList<>();
//        continueList.add(VersionStatus.ONLINE_VERSION.getCode());
//        continueList.add(VersionStatus.AUDIT_ING.getCode());
//        continueList.add(VersionStatus.AUDIT_PASS.getCode());
//
//        SubmitAudit submitAudit = new SubmitAudit();
//        submitAudit.setItem_list(commitAuditVo.getItemList());
//        for (WechatAuthorizer authorizer:authorizers){
//                log.info("提交审核的appId appId={}",authorizer.getAppId());
//                //判断当前体验版的模板Id和线上的模板Id或者审核中、审核成功的模板Id一样就不需要提交审核
//                Boolean outFlag = false;
//                WechatTemplatePackage templatePackages = templatePackageMapper.selectPackageByStatus(authorizer.getAppId(),VersionStatus.DEV_VERSION.getCode());
//                if (!ObjectUtils.isEmpty(templatePackages)){
//                    List<WechatTemplatePackage> continuePackages = templatePackageMapper.selectPackageListByStatus(authorizer.getAppId(),continueList);
//                    for (WechatTemplatePackage e : continuePackages) {
//                        if (templatePackages.getTemplateId().intValue() == e.getTemplateId()) {
//                            outFlag = true;
//                        }
//                    }
//                    if (outFlag) {
//                        continue;
//                    }
//                }
//                SubmitAuditResult submitAuditResult = WxaAPI.submit_audit(authorizer.getAccessToken(),submitAudit);
//                if(!submitAuditResult.successed()){
//                    log.info("微信返回有误,errMsg="+submitAuditResult.getErrmsg());
//                    continue;
//                }
//                //此处删除所有老的审核版本，包括审核中，审核失败，审核延迟，审核通过
//                List<WechatTemplatePackage> auditPackages = templatePackageMapper.selectPackageListByStatus(authorizer.getAppId(),auditList);
//                auditPackages.stream().forEach(pack->{
//                templatePackageMapper.deleteByPrimaryKey(pack.getId());
//                });
//                //将最近的体验版本设置为审核版本
//                if (ObjectUtils.isEmpty(templatePackages)){
//                    log.info("未找到开发版本,appId="+authorizer.getAppId());
//                   continue;
//                }
//                templatePackages.setPkgStatus(VersionStatus.AUDIT_ING.getCode());
//                templatePackages.setAuditId(submitAuditResult.getAuditid());
//                templatePackages.setUpdateTime(LocalDateTime.now());
//                templatePackageMapper.updateByPrimaryKeySelective(templatePackages);
//        }
//        log.info("***********************提交审核结束***********************");
//        return ResultDTO.success();
//    }
//
//    @Override
//    public ResultDTO releasePackage(CommitAuditVo commitAuditVo) throws Exception {
//        List<WechatAuthorizer> wechatAuthorizers = new ArrayList<>();
//        List<Integer> passList = new ArrayList<>();
//        passList.add(VersionStatus.AUDIT_PASS.getCode());
//        if (!ObjectUtils.isEmpty(commitAuditVo) && !StringUtils.isEmpty(commitAuditVo.getAppId())){
//            WechatTemplatePackage templatePackages = templatePackageMapper.selectPackageByStatus(commitAuditVo.getAppId(),VersionStatus.AUDIT_PASS.getCode());
//            if (ObjectUtils.isEmpty(templatePackages)) {
//                return ResultDTO.fail(commitAuditVo.getAppId() + "没有审核通过的记录");
//            }
//            WechatAuthorizer wechatAuthorizer =  authorizerService.getAccessTokenByAppId(commitAuditVo.getAppId());
//            wechatAuthorizers.add(wechatAuthorizer);
//        }else {
//            wechatAuthorizers =  wechatAuthorizerMapper.getAuthTenants();
//            List<WechatTemplatePackage> templatePackages = templatePackageMapper.selectPagListByStatus(passList);
//            if (wechatAuthorizers.size()!=templatePackages.size()) {
//                return ResultDTO.fail("全量发布：有小程序没有通过审核");
//            }
//        }
//        wechatAuthorizers.stream().forEach(authorizer -> {
//            log.info("发布的appId appId={}",authorizer.getAppId());
//            WxaAPI.release(authorizer.getAccessToken());
//            //发布前设置小程序域名
//            try {
//                setModifyDomain(authorizer.getAppId());
//                //setModifyWebviewDomain(authorizer.getAppId());
//            } catch (Exception e) {
//                log.error("异常信息：{}", e.getMessage(), e);
//            }
//            //查询当前审核通过的记录
//            WechatTemplatePackage passPackages = templatePackageMapper.selectPackageByStatus(authorizer.getAppId(),VersionStatus.AUDIT_PASS.getCode());
//            //查询是否存在上一个生产环境版本
//            WechatTemplatePackage curProdPackages = templatePackageMapper.selectPackageByStatus(authorizer.getAppId(),VersionStatus.ONLINE_VERSION.getCode());
//            //更新当前的审核通过的版本为生产环境版本
//            passPackages.setPkgStatus(VersionStatus.ONLINE_VERSION.getCode());
//            templatePackageMapper.updateByPrimaryKeySelective(passPackages);
//            //更新配置表关联的线上版本id
//            WechatLiteConfig wechatLiteConfig = wechatLiteConfigMapper.selectByAppId(authorizer.getAppId());
//            wechatLiteConfig.setOnlineId(passPackages.getId());
//            wechatLiteConfigMapper.updateByPrimaryKeySelective(wechatLiteConfig);
//            //如果存在上个生产环境版本则把生产环境版本设置为当前的上个版本
//            if(null != curProdPackages) {
//                curProdPackages.setPkgStatus(VersionStatus.PRE_VERSION.getCode());
//                templatePackageMapper.updateByPrimaryKeySelective(curProdPackages);
//            }
//        });
//        log.info("***********************发布结束***********************");
//       return ResultDTO.success();
//    }
//
//    @Override
//    public ResultDTO rollbackPackage(CommitAuditVo commitAuditVo) throws Exception {
//        List<WechatAuthorizer> authorizers = new ArrayList<>();
//        List<Integer> onlineList = new ArrayList<>();
//        onlineList.add(VersionStatus.ONLINE_VERSION.getCode());
//
//        if (!ObjectUtils.isEmpty(commitAuditVo) && !StringUtils.isEmpty(commitAuditVo.getAppId())){
//            WechatTemplatePackage templatePackages = templatePackageMapper.selectPackageByStatus(commitAuditVo.getAppId(),VersionStatus.ONLINE_VERSION.getCode());
//            if (ObjectUtils.isEmpty(templatePackages)) {
//                return ResultDTO.fail(commitAuditVo.getAppId() + "没有可回退的版本");
//            }
//            WechatAuthorizer wechatAuthorizer =  authorizerService.getAccessTokenByAppId(commitAuditVo.getAppId());
//            authorizers.add(wechatAuthorizer);
//        }else {
//            List<WechatTemplatePackage> templatePackages = templatePackageMapper.selectPagListByStatus(onlineList);
//            if (!CollectionUtils.isEmpty(templatePackages)){
//                List<String> appIds = new ArrayList<>();
//                templatePackages.stream().forEach(templatePackage->{
//                    appIds.add(templatePackage.getAppid());
//                });
//                authorizers = authorizerService.getAuthorizers(appIds);
//            }else {
//                return ResultDTO.fail(commitAuditVo.getAppId()+"没有可回退的版本");
//            }
//        }
//        authorizers.stream().forEach(authorizer->{
//            log.info("版本退回的appId appId={}",authorizer.getAppId());
//            WxaAPI.revertcoderelease(authorizer.getAccessToken());
//            //删除当前版本信息
//            WechatTemplatePackage passPackages = templatePackageMapper.selectPackageByStatus(authorizer.getAppId(),VersionStatus.ONLINE_VERSION.getCode());
//            passPackages.setPkgStatus(VersionStatus.OTHER.getCode());
//            templatePackageMapper.updateByPrimaryKeySelective(passPackages);
//            //如果有上个版本信息则把上个版本信息回滚到当前版本去
//            WechatTemplatePackage curProdPackages = templatePackageMapper.selectPackageByStatus(authorizer.getAppId(),VersionStatus.PRE_VERSION.getCode());
//            if (!ObjectUtils.isEmpty(curProdPackages)){
//                curProdPackages.setPkgStatus(VersionStatus.ONLINE_VERSION.getCode());
//                templatePackageMapper.updateByPrimaryKeySelective(curProdPackages);
//                //更新配置表关联关系
//                WechatLiteConfig wechatLiteConfig = wechatLiteConfigMapper.selectByAppId(authorizer.getAppId());
//                wechatLiteConfig.setOnlineId(curProdPackages.getId());
//                wechatLiteConfigMapper.updateByPrimaryKeySelective(wechatLiteConfig);
//            }
//        });
//        log.info("***********************版本退回结束***********************");
//        return ResultDTO.success();
//    }

//    @Override
//    public ResultDTO withdrawPackage(CommitAuditVo commitAuditVo) throws Exception {
//        List<WechatAuthorizer> authorizers = new ArrayList<>();
//        List<Integer> auditList = new ArrayList<>();
//        auditList.add(VersionStatus.AUDIT_ING.getCode());
//        auditList.add(VersionStatus.AUDIT_UNPASS.getCode());
//        auditList.add(VersionStatus.AUDIT_PASS.getCode());
//        auditList.add(VersionStatus.AUDIT_DELAY.getCode());
//        if (!ObjectUtils.isEmpty(commitAuditVo) && !StringUtils.isEmpty(commitAuditVo.getAppId())){
//            List<WechatTemplatePackage> templatePackages = templatePackageMapper.selectPackageListByStatus(commitAuditVo.getAppId(),auditList);
//            if (CollectionUtils.isEmpty(templatePackages)) {
//                return ResultDTO.fail(commitAuditVo.getAppId() + "没有审核相关的记录");
//            }
//            WechatAuthorizer wechatAuthorizer =  authorizerService.getAccessTokenByAppId(commitAuditVo.getAppId());
//            authorizers.add(wechatAuthorizer);
//        }else {
//            List<WechatTemplatePackage> templatePackages = templatePackageMapper.selectPagListByStatus(auditList);
//            if (!CollectionUtils.isEmpty(templatePackages)) {
//                List<String> appIds = new ArrayList<>();
//                templatePackages.stream().forEach(templatePackage -> {
//                    appIds.add(templatePackage.getAppid());
//                });
//                authorizers = authorizerService.getAuthorizers(appIds);
//            }else {
//                return ResultDTO.fail("全量审核撤回:没有可撤回的版本");
//            }
//        }
//        authorizers.stream().forEach(info->{
//            log.info("审核撤回的appId appId={}",info.getAppId());
//            WxaAPI.undocodeaudit(info.getAccessToken());
//            //设置已经过审核和审核中或者被拒绝的版本为已撤回
//            List<WechatTemplatePackage> templatePackages = templatePackageMapper.selectPackageListByStatus(info.getAppId(),auditList);
//            WechatTemplatePackage templatePackage = templatePackages.get(LiteConfigPublicConsts.ONLINE_RETAILERS_OFF.getCode());
//            templatePackage.setPkgStatus(VersionStatus.DEV_VERSION.getCode());
//            templatePackageMapper.updateByPrimaryKeySelective(templatePackage);
//        });
//        log.info("***********************审核撤回结束***********************");
//        return ResultDTO.success();
//    }

    @Override
    public void setModifyDomain(String appId) throws Exception {
        Assert.hasText(appId, "缺少参数appId");
        WechatAuthorizer wechatAuthorizer =  wechatAuthorizerMapper.selectOneByAppId(appId);
        authorizerService.setModifyDomain(wechatAuthorizer.getAccessToken(), WxDomainEnum.SET.getKey(),
                dictSysParameter.getParameter().getLiteRequestDownloadDomain(),
                dictSysParameter.getParameter().getLiteRequestRequestDomain(),
                dictSysParameter.getParameter().getLiteRequestSocketDomain(),
                dictSysParameter.getParameter().getLiteRequestUploadDomain());
    }

//    @Override
//    public void setModifyWebviewDomain(String appId) throws Exception {
//        Assert.hasText(appId, "缺少参数appId");
//        WechatAuthorizer wechatAuthorizer =  wechatAuthorizerMapper.selectOneByAppId(appId);
//        authorizerService.setModifyWebviewDomain(wechatAuthorizer.getAccessToken(), WxDomainEnum.SET.getKey(),dictSysParameter.getParameter().getLiteWebviewWebViewDomain());
//    }

    @Override
    public void initDomain(String appId) throws Exception {
        setModifyDomain(appId);
        //setModifyWebviewDomain(appId);
    }

    @Override
    public PrivateSettingDto getPrivacySetting(String appId,Integer privacyVer) throws Exception {
        Assert.notNull(appId,"appId is null");
        WechatAuthorizer wechatAuthorizer =  authorizerService.getAccessTokenByAppId(appId);
        if (ObjectUtils.isEmpty(wechatAuthorizer)) {
            throw new Exception("appId不存在对象");
        }
        PrivateSettingDto privateSettingDto = new PrivateSettingDto();
        privateSettingResp privacySetting = ComponentApi.getPrivacySetting(wechatAuthorizer.getAccessToken(),privacyVer);
        BeanUtils.copyProperties(privacySetting, privateSettingDto);
        return privateSettingDto;
    }
}
