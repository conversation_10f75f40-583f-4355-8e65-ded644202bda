package com.cosfo.manage.wechat.service.Impl;

import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson.JSON;
import com.cosfo.manage.bill.mapper.PaymentItemMapper;
import com.cosfo.manage.bill.mapper.PaymentMapper;
import com.cosfo.manage.bill.model.po.Payment;
import com.cosfo.manage.bill.model.po.PaymentItem;
import com.cosfo.manage.common.context.PaymentEnum;
import com.cosfo.manage.common.context.PaymentTradeTypeEnum;
import com.cosfo.manage.facade.ordercenter.OrderItemQueryFacade;
import com.cosfo.manage.order.model.vo.OrderItemVO;
import com.cosfo.manage.system.mapper.SystemParametersMapper;
import com.cosfo.manage.system.model.po.SystemParameters;
import com.cosfo.manage.tenant.mapper.TenantAuthConnectionMapper;
import com.cosfo.manage.tenant.model.po.TenantAuthConnection;
import com.cosfo.manage.wechat.api.WxaAPI;
import com.cosfo.manage.wechat.bean.wxa.UploadShippingInfoReq;
import com.cosfo.manage.wechat.bean.wxa.UploadShippingInfoReq.OrderKey;
import com.cosfo.manage.wechat.bean.wxa.UploadShippingInfoReq.Payer;
import com.cosfo.manage.wechat.bean.wxa.UploadShippingInfoReq.Shipping;
import com.cosfo.manage.wechat.bean.wxa.UploadShippingInfoResult;
import com.cosfo.manage.wechat.mapper.WechatAuthorizerMapper;
import com.cosfo.manage.wechat.mapper.WxShippingInfoUploadRecordMapper;
import com.cosfo.manage.wechat.model.po.WechatAuthorizer;
import com.cosfo.manage.wechat.model.po.WxShippingInfoUploadRecord;
import com.cosfo.manage.wechat.service.WeixinShippingService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * @author: xiaowk
 * @time: 2023/7/11 下午5:48
 */
@Service
@Slf4j
public class WeixinShippingServiceImpl implements WeixinShippingService {

    @Resource
    private WxShippingInfoUploadRecordMapper wxShippingInfoUploadRecordMapper;
    @Resource
    private WechatAuthorizerMapper wechatAuthorizerMapper;
    @Resource
    private SystemParametersMapper systemParametersMapper;
    @Resource
    private PaymentItemMapper paymentItemMapper;
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private TenantAuthConnectionMapper tenantAuthConnectionMapper;
    @Resource
    private OrderItemQueryFacade orderItemQueryFacade;

    private static final String OPEN_SHIPPING_TENANTIDS = "open_shipping_tenantids";


    @Override
    public void uploadShippingInfo(Payment payment, Long orderId, String appId) {
        uploadShippingInfoCommon(payment, orderId, appId);
    }

    private void uploadShippingInfoCommon(Payment payment, Long orderId, String appId) {
        if (payment == null || orderId == null || StringUtils.isBlank(appId)) {
            log.error("参数为空异常");
            return;
        }
        WxShippingInfoUploadRecord record = wxShippingInfoUploadRecordMapper.selectByTransactionId(payment.getTransactionId());
        if (record != null) {
            log.info("支付单已上传发货信息，transactionId={}", payment.getTransactionId());
            return;
        }
        try {
            WechatAuthorizer wechatAuthorizer = wechatAuthorizerMapper.selectOneByAppId(appId);
            String accessToken = wechatAuthorizer.getAccessToken();

            List<OrderItemVO> orderItemVOS = orderItemQueryFacade.queryOrderItemByOrderId(orderId);
            orderItemVOS = orderItemVOS.subList(0, Math.min(5, orderItemVOS.size()));
            List<String> titleList = orderItemVOS.stream().map(e -> e.getTitle()).collect(Collectors.toList());
            String titles = StringUtils.join(titleList.toArray(), ";");
            // 限120个字以内
            titles = titles.substring(0, Math.min(120, titles.length()));

            UploadShippingInfoReq req = new UploadShippingInfoReq();
            OrderKey orderKey = new OrderKey();
            orderKey.setOrder_number_type(2);
            orderKey.setTransaction_id(payment.getTransactionId());
            req.setOrder_key(orderKey);
            req.setLogistics_type(2);
            req.setDelivery_mode(1);
            Shipping shipping = new Shipping();
            shipping.setItem_desc(titles);
            req.setShipping_list(Lists.newArrayList(shipping));
            req.setUpload_time(ZonedDateTime.now().format(DatePattern.createFormatter(DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN)));
            req.setPayer(new Payer(payment.getSpOpenid()));
            UploadShippingInfoResult result = WxaAPI.upload_shipping_info(accessToken, req);
            if (result == null || !result.successed()) {
                log.error("微信上传发货信息错误， req={}, result={}", req, result);
            } else {
                // 上传发货信息接口成功，插入日志记录，用来避免重复上传
                WxShippingInfoUploadRecord saveRecord = new WxShippingInfoUploadRecord();
                saveRecord.setTenantId(payment.getTenantId());
                saveRecord.setOrderId(orderId);
                saveRecord.setPaymentId(payment.getId());
                saveRecord.setTransactionId(payment.getTransactionId());
                saveRecord.setStatus(1);
                saveRecord.setCreateTime(LocalDateTime.now());
                saveRecord.setUpdateTime(LocalDateTime.now());
                wxShippingInfoUploadRecordMapper.insertSelective(saveRecord);
            }
        } catch (Exception e) {
            log.error("微信上传发货信息错误, payment={}", payment, e);
        }
    }

    @Override
    public void uploadShippingInfoByOrderId(Long orderId, Long tenantId) {
        List<Long> tenantIds = queryOpenShippingTenantIds();
        if (!tenantIds.contains(tenantId)) {
            return;
        }

        PaymentItem paymentItem = paymentItemMapper.selectPaySuccessByOrderId(tenantId, orderId);
        if (paymentItem == null) {
            return;
        }

        Long paymentId = paymentItem.getPaymentId();
        Payment payment = paymentMapper.selectByPrimaryKey(paymentId);
        boolean checkFlag = checkPaymentForShipping(payment);
        if (!checkFlag) {
            return;
        }
        TenantAuthConnection tenantAuthConnection = tenantAuthConnectionMapper.selectByTenantId(tenantId);
        if (tenantAuthConnection == null) {
            return;
        }

        String appId = tenantAuthConnection.getAppId();
        uploadShippingInfoCommon(payment, orderId, appId);
    }

    private boolean checkPaymentForShipping(Payment payment) {
        if (payment == null) {
            return false;
        }

        if(!Arrays.asList(PaymentTradeTypeEnum.JSAPI.getDesc(), PaymentTradeTypeEnum.T_MINIAPP.getDesc()).contains(payment.getTradeType())){
            return false;
        }

        // 非支付成功
        if (!PaymentEnum.Status.SUCCESS.getCode().equals(payment.getStatus())) {
            return false;
        }

        return true;
    }

    @Override
    public List<Long> queryOpenShippingTenantIds() {
        SystemParameters systemParameters = systemParametersMapper.selectByKey(OPEN_SHIPPING_TENANTIDS);
        if (systemParameters == null || StringUtils.isBlank(systemParameters.getParamValue())) {
            return Collections.emptyList();
        }
        try {
            List<Long> tenantIds = JSON.parseArray(systemParameters.getParamValue(), Long.class);
            return tenantIds;
        } catch (Exception e) {
            log.error("获取开通发货信息的租户id错误", e);
        }

        return Collections.emptyList();
    }

    @Override
    public void changeOpenShippingTenantIds(List<Long> tenantIds) {
        SystemParameters systemParameters = systemParametersMapper.selectByKey(OPEN_SHIPPING_TENANTIDS);
        if (systemParameters == null){
            systemParameters = new SystemParameters();
            systemParameters.setParamKey(OPEN_SHIPPING_TENANTIDS);
            systemParameters.setParamValue(JSON.toJSONString(tenantIds));
            systemParameters.setDescription("小程序开通发货信息管理的租户id");
            systemParameters.setCreateTime(new Date());
            systemParameters.setUpdateTime(new Date());
            systemParametersMapper.insertSelective(systemParameters);
        }else{
            List<Long> existTenantIds = JSON.parseArray(systemParameters.getParamValue(), Long.class);
            Set<Long> tenantIdSet = Sets.newHashSet();
            tenantIdSet.addAll(existTenantIds);
            tenantIdSet.addAll(tenantIds);
            SystemParameters updateObj = new SystemParameters();
            updateObj.setId(systemParameters.getId());
            updateObj.setParamValue(JSON.toJSONString(tenantIdSet));
            updateObj.setUpdateTime(new Date());
            systemParametersMapper.updateByPrimaryKeySelective(updateObj);
        }
    }
}
