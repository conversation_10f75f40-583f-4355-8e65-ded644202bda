package com.cosfo.manage.wechat.mapper;

import com.cosfo.manage.wechat.model.po.WechatAuthorizer;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface WechatAuthorizerMapper {

    /**
     * 删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(WechatAuthorizer record);

    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(WechatAuthorizer record);

    /**
     * 查询
     * @param id
     * @return
     */
    WechatAuthorizer selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(WechatAuthorizer record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(WechatAuthorizer record);

    /**
     * 根据appId更新授权信息
     * @param record
     * @return
     */
    int updateByAppId(WechatAuthorizer record);

    /**
     * 根据授权码获取授权方信息
     * @param authCode
     * @return
     */
    WechatAuthorizer selectByAuthCode(@Param("authCode") String authCode);

    /**
     * 定时任务批量获取授权方
     * @param appId
     * @param status
     * @return
     */
    List<WechatAuthorizer> selectByRefreshToken(@Param("appId") String appId,@Param("status") Integer status);

    /**
     * 根据appId获取授权方
     * @param appId
     * @return
     */
    WechatAuthorizer selectByAppId(@Param("appId") String appId);

    /**
     * 根据appId获取授权方
     * @param appId
     * @return
     */
    WechatAuthorizer selectOneByAppId(@Param("appId") String appId);

    /**
     * 查询最后2分钟授权过来没有关联的商户进行关联
     * @param status
     * @param createTime
     * @return
     */
    List<WechatAuthorizer> selectByTime(@Param("status") Integer status,@Param("createTime") Date createTime);

    /**
     * 获取授权小程序appId列表
     * @return
     */
    List<WechatAuthorizer> getAuthTenants();

    List<WechatAuthorizer> getAuthTenantsByPage(@Param("maxId") Long maxId, @Param("size") Integer size);

    /**
     * 根据appId获取授权方列表
     * @param appIds
     * @return
     */
    List<WechatAuthorizer> selectListAppIds(@Param("appIds") List<String> appIds);



}
