package com.cosfo.manage.wechat.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.cosfo.manage.wechat.model.po.WechatLiteConfig;
import com.cosfo.manage.wechat.model.vo.AuthorizedVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface WechatLiteConfigMapper {

    /**
     * 删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(WechatLiteConfig record);

    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(WechatLiteConfig record);

    /**
     * 查询
     * @param id
     * @return
     */
    WechatLiteConfig selectByPrimaryKey(Long id);

    /***
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(WechatLiteConfig record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(WechatLiteConfig record);

    /**
     * 根据appId查询
     * @param appId
     * @return
     */
    WechatLiteConfig selectByAppId(@Param("appId") String appId);

    /**
     * 查询
     * @param OriginalId
     * @return
     */
    WechatLiteConfig selectByOriginalId(@Param("OriginalId") String OriginalId);

    /**
     * 查询
     * @param tenantId
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<AuthorizedVo> selectAuthorizedList(@Param("tenantId") Long tenantId);


}
