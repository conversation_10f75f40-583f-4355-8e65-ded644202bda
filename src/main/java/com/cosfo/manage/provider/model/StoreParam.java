package com.cosfo.manage.provider.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreCommandReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantAddressResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;

/**
 * @Author: fansongsong
 * @Date: 2023-12-28
 * @Description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class StoreParam {

    /**
     * 门店req信息
     */
    private MerchantStoreCommandReq merchantStoreCommandReq;

    /**
     * 门店旧地址
     */
    private MerchantAddressResultResp oldAddress;

    /**
     * 门店新地址
     */
    private MerchantAddressCommandReq newAddress;

    /**
     * 之前的门店信息 from db
     */
    private MerchantStoreResultResp merchantStoreResultResp;

    /**
     * 租户id
     */
    private Long tenantId;

}
