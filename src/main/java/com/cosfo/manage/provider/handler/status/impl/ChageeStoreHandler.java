package com.cosfo.manage.provider.handler.status.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.config.ChageeNacosConfig;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.context.MerchantStoreEnum;
import com.cosfo.manage.provider.handler.status.StoreStatusHandler;
import com.cosfo.manage.provider.model.StoreParam;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.CommonResult;
import net.xianmu.robot.feishu.FeishuBotUtil;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreCommandReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantAddressResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 霸王茶姬租户 的门店处理
 * @date 2025-04-16
 */
@Slf4j
@Component
public class ChageeStoreHandler implements StoreStatusHandler {

    @Resource
    private ChageeNacosConfig chageeNacosConfig;

    @Override
    public boolean support(Long tenantId) {
        return chageeNacosConfig.getChageeTenantIdList().contains(tenantId);
    }

    @Override
    public void builderOrderDeliveringDTO(StoreParam storeParam) {
        MerchantStoreCommandReq merchantStoreCommandReq = storeParam.getMerchantStoreCommandReq();
        MerchantAddressResultResp oldAddress = storeParam.getOldAddress();
        MerchantAddressCommandReq newAddress = storeParam.getNewAddress();
        if (null == merchantStoreCommandReq) {
            return;
        }
        boolean isCreateNew = null == oldAddress;
        if (isCreateNew){
            merchantStoreCommandReq.setStatus(MerchantStoreEnum.Status.IN_AUDIT.getCode());
            return;
        }
        String oldAddressDetail = oldAddress.getProvince() + oldAddress.getCity() + oldAddress.getArea() + oldAddress.getAddress();
        String newAddressDetail = newAddress.getProvince() + newAddress.getCity() + newAddress.getArea() + newAddress.getAddress();
        if (!oldAddressDetail.equals(newAddressDetail)) {
            merchantStoreCommandReq.setStatus(MerchantStoreEnum.Status.IN_AUDIT.getCode());
        }

    }

    @Override
    public void asyncExecuteNotify(StoreParam storeParam) {
        log.info("asyncExecuteNotify log >>> {}", JSON.toJSONString(storeParam));
        MerchantStoreCommandReq merchantStoreCommandReq = storeParam.getMerchantStoreCommandReq();
        MerchantStoreResultResp merchantStoreResultResp = storeParam.getMerchantStoreResultResp();
        MerchantAddressResultResp oldAddress = storeParam.getOldAddress();
        MerchantAddressCommandReq newAddress = storeParam.getNewAddress();
        if (merchantStoreCommandReq == null || newAddress == null) {
            return;
        }

        if (!MerchantStoreEnum.Status.IN_AUDIT.getCode().equals(merchantStoreCommandReq.getStatus())){
            log.info("门店状态不是待审核状态，无需发送飞书群告警消息,status:{}", merchantStoreCommandReq.getStatus());
            return;
        }
        boolean isCreateNew = null == oldAddress;

        String auditMsg = "";
        // 待审核发送飞书群告警消息
        if (isCreateNew) {
            auditMsg = "【新增门店】";
        } else if (MerchantStoreEnum.Status.AUDIT_SUCCESS.getCode().equals(merchantStoreResultResp.getStatus())) {
            // 在已审核状态下，校验省市区是否变更；若变更，则门店 进入地址变更待审核，飞书通知到群。
            auditMsg = "【地址变更】";
        } else  {
            return;
        }

        StringBuilder text = new StringBuilder();
        text.append(Constants.LINE).append(auditMsg).append("门店审核提醒").append(Constants.LINE).append("租户ID：").append(merchantStoreCommandReq.getTenantId()).append(Constants.LINE).append("门店ID：").append(merchantStoreCommandReq.getId())
                .append(Constants.COMMA).append("门店编号：").append(merchantStoreCommandReq.getStoreNo()).append(Constants.COMMA).append("门店名称：").append(merchantStoreCommandReq.getStoreName())
                .append(Constants.LINE).append("门店地址：").append(newAddress.getProvince()).append(newAddress.getCity()).append(newAddress.getArea()).append(newAddress.getAddress())
                .append("，请尽快审核");

        log.info("发送飞书群告警消息,text:{}", text.toString());
        CommonResult<Boolean> result = FeishuBotUtil.sendTextMsgAndAtAll(chageeNacosConfig.getOpenApiChageeStoreWarnUrl(), text.toString());
        log.info("发送飞书群告警消息结果,text:{}", JSON.toJSONString(result));
    }




}
