package com.cosfo.manage.provider.impl;

import com.cosfo.manage.client.tenant.SpecialTenantCommandProvider;
import com.cosfo.manage.merchant.service.impl.MerchantDeliveryInfoService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * 特殊定制化租户 查询服务
 *
 * <AUTHOR>
 * @Date 2025/4/15 14:51
 * @Version 1.0
 */
@Slf4j
@DubboService
public class SpecialTenantCommandProviderImpl implements SpecialTenantCommandProvider {

    @Resource
    private MerchantDeliveryInfoService merchantDeliveryInfoService;

    @Override
    public DubboResponse<Void> sendChageeMerchantDeliveryInfo(Long storeId) {
        merchantDeliveryInfoService.sendMerchantDeliveryInfo(storeId);
        return DubboResponse.getOK();
    }
}
