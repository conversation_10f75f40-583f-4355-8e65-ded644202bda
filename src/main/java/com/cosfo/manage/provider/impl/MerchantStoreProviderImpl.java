package com.cosfo.manage.provider.impl;

import com.cosfo.manage.client.merchant.MerchantStoreProvider;
import com.cosfo.manage.client.merchant.req.MerchantStoreQueryReq;
import com.cosfo.manage.client.merchant.resp.MerchantStoreAddressResp;
import com.cosfo.manage.client.merchant.resp.MerchantStoreResp;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreQueryDTO;
import com.cosfo.manage.merchant.model.po.MerchantStore;
import com.cosfo.manage.merchant.service.MerchantStoreService;
import com.cosfo.manage.provider.convert.MerchantStoreConvert;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import com.cosfo.manage.merchant.model.dto.MerchantStoreAddressDTO;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/3/27
 */
@DubboService
@Component
@Slf4j
public class MerchantStoreProviderImpl implements MerchantStoreProvider {
    @Resource
    private MerchantStoreService merchantStoreService;

    @Override
    public DubboResponse<List<MerchantStoreResp>> batchQueryStoreInfo(MerchantStoreQueryReq merchantStoreQueryReq) {
        MerchantStoreQueryDTO merchantStoreQueryDTO = MerchantStoreConvert.convertToMerchantStoreQueryDTO(merchantStoreQueryReq);
        merchantStoreQueryDTO.setPageSize(Optional.ofNullable(merchantStoreQueryReq).map(MerchantStoreQueryReq::getStoreIds).map(List::size).orElse(NumberConstant.ONE));
        List<MerchantStoreDTO> merchantStoreDTOS = merchantStoreService.listByCondition(merchantStoreQueryDTO);
        List<MerchantStoreResp> merchantStoreResps = MerchantStoreConvert.convertToMerchantStoreRespList(merchantStoreDTOS);
        return DubboResponse.getOK(merchantStoreResps);
    }

    @Override
    public DubboResponse<List<MerchantStoreAddressResp>> batchQueryStoreAddress(Long tenantId) {
        List<MerchantStoreAddressDTO> dtos = merchantStoreService.listStoreIdAndAddress (tenantId, Collections.emptyList ());
        List<MerchantStoreAddressResp> respList = MerchantStoreConvert.convertToMerchantStoreAddressRespList(dtos);
        return DubboResponse.getOK(respList);
    }

    @Override
    public DubboResponse<List<MerchantStoreAddressResp>> batchQueryStoreAddressByIds(Long tenantId, List<Long> storeIds) {
        List<MerchantStoreAddressDTO> dtos = merchantStoreService.listStoreIdAndAddress (tenantId, storeIds);
        List<MerchantStoreAddressResp> respList = MerchantStoreConvert.convertToMerchantStoreAddressRespList(dtos);
        return DubboResponse.getOK(respList);
    }

    @Override
    public DubboResponse<MerchantStoreAddressResp> queryStoreAddress(Long tenantId,Long storeId) {
        MerchantStoreAddressDTO dto = merchantStoreService.getStoreIdAndAddress (tenantId,storeId);
        MerchantStoreAddressResp resp = MerchantStoreConvert.convertToMerchantStoreAddressResp(dto);
        return DubboResponse.getOK(resp);
    }
}
