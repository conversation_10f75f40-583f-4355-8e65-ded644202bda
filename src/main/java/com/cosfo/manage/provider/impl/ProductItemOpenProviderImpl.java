package com.cosfo.manage.provider.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.cosfo.manage.client.productitem.openapi.ProductItemOpenProvider;
import com.cosfo.manage.client.productitem.req.ProductItemReq;
import com.cosfo.manage.client.productitem.resp.ProductItemResp;
import com.cosfo.manage.common.config.OpenApiConfig;
import com.cosfo.manage.common.util.RedisUtils;
import com.cosfo.manage.facade.ProductCommandFacade;
import com.cosfo.manage.good.model.dto.ProductSpuAddInput;
import com.cosfo.manage.market.service.MarketItemService;
import com.cosfo.manage.good.service.ProductAgentApplicationRecordService;
import com.cosfo.manage.good.service.ProductService;
import com.cosfo.manage.market.model.dto.*;
import com.cosfo.manage.market.service.MarketService;
import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import com.cosfo.manage.product.model.po.ProductSkuUnitMultiple;
import com.cosfo.manage.product.repository.ProductSkuUnitMultipleRepository;
import com.cosfo.manage.provider.convert.ProductItemConvert;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.account.IsvInfo;
import net.xianmu.common.account.IsvInfoHolder;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

@Component
@DubboService
@Slf4j
public class ProductItemOpenProviderImpl implements ProductItemOpenProvider {


    @Resource
    private ProductService productService;

    @Resource
    private MarketService marketService;

    @Resource
    private MarketItemService marketItemService;

    @Resource
    private ProductSkuUnitMultipleRepository productSkuUnitMultipleRepository;

    @Resource
    private ProductAgentApplicationRecordService productAgentApplicationRecordService;

    @Resource
    private ProductCommandFacade productCommandFacade;


    @NacosValue(value = "${openapi.default.categoryId:1036}", autoRefreshed = true)
    public Long categoryId;

    @NacosValue(value = "${openapi.default.classificationId:{2L:112L}}", autoRefreshed = true)
    public String classificationId;

    @Resource
    private RedisUtils redisUtils;
    @Resource
    private OpenApiConfig openApiConfig;
    @Override
    public DubboResponse<ProductItemResp> saveOrUpdate(@Valid ProductItemReq req) {

        Map<Long,Long> classificationMap = JSON.parseObject (classificationId,Map.class);
        Map<Long,Long> storeGruopMap = openApiConfig.getOpenApiDefaultStoreGroupMap();

        ProductItemResp productItemResp = new ProductItemResp ();
        productItemResp.setCustomerSkuCode (req.getCustomerSkuCode ());
        Long tenantId = Optional.ofNullable(IsvInfoHolder.getAccount()).map(IsvInfo::getAccountId).orElse(null);
        if (Objects.isNull(tenantId)) {
            log.info("下发商品错误,租戶信息不存在 IsvInfo:{},req:{}", JSON.toJSONString(IsvInfoHolder.getAccount()), JSON.toJSONString(req));
            throw new BizException ("租戶信息不存在");
        }
        Long storeGroupId  =storeGruopMap.get (tenantId);
        String customerSkuCode = req.getCustomerSkuCode ();
        List<ProductSkuDTO> skuDTOS = productService.queryByCustomSkuCodeList (Collections.singletonList (customerSkuCode), tenantId);
        String skuCode = req.getSkuCode ();
        if(CollectionUtil.isNotEmpty (skuDTOS)){
            ProductSkuDTO skuDTO = skuDTOS.get (0);
            Long spuId = skuDTO.getSpuId ();
            Long skuId = skuDTO.getId ();

            //  修改货品
            ProductSpuAddInput productSpuAddInput = ProductItemConvert.INSTANCE.convert2ProductSpuAddInput (req);
            productSpuAddInput.setId (spuId);
            productSpuAddInput.getProductSkuAddInputList ().get (0).setId (skuId);
            productService.updateProduct (productSpuAddInput, tenantId);

            saveOrUpdateProductSkuUnitMultipleRepository (req,skuId,skuCode,tenantId);

            //修改商品
            List<MarketItemDTO> marketItemDTOS = marketItemService.queryBySkuIds (Collections.singletonList (skuId), tenantId);
            if(CollectionUtil.isNotEmpty (marketItemDTOS)){
                MarketItemDTO marketItemDTO = marketItemDTOS.get (0);
                Long itemId = marketItemDTO.getId ();
                Long marketId = marketItemDTO.getMarketId ();
                MarketSpuInput marketSpuInput = ProductItemConvert.INSTANCE.convert2MarketSpuInput (req,storeGroupId);
                marketSpuInput.setId (marketId);
                marketService.update (marketSpuInput,tenantId);

                MarketItemInput marketItemInput = marketSpuInput.getMarketItemInput ();
                marketItemInput.setId (itemId);
                marketItemInput.setMarketId (marketId);
                marketItemInput.setSkuId (skuId);
                marketItemService.update(marketSpuInput.getMarketItemInput (),tenantId);
            }else{
                //创建商品
                MarketSpuInput marketSpuInput = ProductItemConvert.INSTANCE.convert2MarketSpuInput (req,storeGroupId);
                marketSpuInput.setClassificationId (classificationMap.get (tenantId));
                marketSpuInput.getMarketItemInput ().setSkuId (skuId);
                marketService.add (marketSpuInput,tenantId);
            }

            if(ObjectUtil.isNotNull (skuDTO.getSkuMapping ())){
                productItemResp.setSkuCode (skuDTO.getSkuMapping ().getSku ());
            }
        }else {
            //创建货品
            ProductSpuAddInput productSpuAddInput = ProductItemConvert.INSTANCE.convert2ProductSpuAddInput (req);
            productSpuAddInput.setCategoryId (categoryId);
            skuCode = productCommandFacade.takeGoodsCode (categoryId);
            productItemResp.setSkuCode (skuCode);
            productSpuAddInput.getProductSkuAddInputList ().get (0).setSkuCode (skuCode);
            Long spuId = productService.createProduct (productSpuAddInput, tenantId);
            Long skuId = productService.getDetail (spuId, tenantId).getProductSkuVoList ().get (0).getId ();

            saveOrUpdateProductSkuUnitMultipleRepository (req,skuId,skuCode,tenantId);

            //创建redis商品
            MarketSpuInput marketSpuInput = ProductItemConvert.INSTANCE.convert2MarketSpuInput (req,storeGroupId);
            marketSpuInput.setClassificationId (classificationMap.get (tenantId));
            marketSpuInput.getMarketItemInput ().setSkuId (skuId);
            String marketSpuInputString = JSON.toJSONString (marketSpuInput);
            log.info ("classificationMap={},classificationId={}",JSON.toJSONString (classificationMap),classificationMap.get (tenantId));
            log.info ("marketSpuInput={}",marketSpuInputString);
            redisUtils.set (String.valueOf (skuId),marketSpuInputString);
        }
        return DubboResponse.getOK(productItemResp);
    }

    private void saveOrUpdateProductSkuUnitMultipleRepository(ProductItemReq req,Long skuId,String skuCode,Long tenantId) {
        ProductSkuUnitMultiple productSkuUnitMultiple = new ProductSkuUnitMultiple ();
        productSkuUnitMultiple.setSkuId(skuId);
        productSkuUnitMultiple.setSkuCode(skuCode);
        productSkuUnitMultiple.setTenantId(tenantId);
        productSkuUnitMultiple.setSpecificationUnit(req.getSpecificationUnit ());
        productSkuUnitMultiple.setTargetSpecificationUnit(req.getStockUnit ());
        productSkuUnitMultiple.setTargetMultipl(req.getUnitRate ());
        if(ObjectUtil.isNull (req.getStockUnit ())){
            productSkuUnitMultiple.setTargetSpecificationUnit (req.getSpecificationUnit ());
        }
        if(ObjectUtil.isNull (req.getUnitRate ())){
            productSkuUnitMultiple.setTargetMultipl (1);
        }
        List<ProductSkuUnitMultiple> productSkuUnitMultiples = productSkuUnitMultipleRepository.queryBySkuId (skuId, tenantId);
        if(CollectionUtil.isNotEmpty (productSkuUnitMultiples)){
            ProductSkuUnitMultiple productSkuUnitMultipleDb = productSkuUnitMultiples.get (0);
            productSkuUnitMultiple.setId (productSkuUnitMultipleDb.getId ());
        }
        productSkuUnitMultipleRepository.saveOrUpdate (productSkuUnitMultiple);
    }
}
