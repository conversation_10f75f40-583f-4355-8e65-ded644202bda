package com.cosfo.manage.provider.impl;

import com.cosfo.manage.client.tenant.SpecialTenantQueryProvider;
import com.cosfo.manage.common.config.ChageeNacosConfig;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 * 特殊定制化租户 查询服务
 *
 * <AUTHOR>
 * @Date 2025/4/15 14:51
 * @Version 1.0
 */
@Slf4j
@DubboService
public class SpecialTenantQueryProviderImpl implements SpecialTenantQueryProvider {

    @Resource
    private ChageeNacosConfig chageeNacosConfig;

    @Override
    public DubboResponse<Boolean> judgeIsTenantOfChagee(Long tenantId) {
        return DubboResponse.getOK(chageeNacosConfig.judgeIsTenantOfChagee(tenantId));
    }

    @Override
    public DubboResponse<List<Long>> queryChageeTenantIdList() {
        return DubboResponse.getOK(chageeNacosConfig.getChageeTenantIdList());
    }
}
