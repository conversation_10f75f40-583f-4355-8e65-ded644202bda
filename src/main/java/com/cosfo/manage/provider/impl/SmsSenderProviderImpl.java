package com.cosfo.manage.provider.impl;

import com.cosfo.manage.client.sms.SmsSenderProvider;
import com.cosfo.manage.client.sms.req.SmsReq;
import com.cosfo.manage.common.sms.model.Sms;
import com.cosfo.manage.common.sms.model.SmsSenderFactory;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * @author: xiaowk
 * @time: 2024/4/1 下午2:52
 */
@DubboService
@Slf4j
public class SmsSenderProviderImpl implements SmsSenderProvider {

    @Resource
    private SmsSenderFactory smsSenderFactory;

    @Override
    public DubboResponse<Boolean> sendSms(SmsReq smsReq) {
        Sms sendSms = new Sms();
        sendSms.setPhone(smsReq.getPhone());
        sendSms.setSceneId(smsReq.getSceneId());
        sendSms.setArgs(smsReq.getArgs());
        boolean success = smsSenderFactory.getSmsSender().sendSms(sendSms);
        return DubboResponse.getOK(success);
    }
}
