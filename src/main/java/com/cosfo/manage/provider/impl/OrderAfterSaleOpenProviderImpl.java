package com.cosfo.manage.provider.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.manage.client.order.openapi.OrderAfterSaleOpenProvider;
import com.cosfo.manage.client.order.req.OrderAfterSaleBatchReq;
import com.cosfo.manage.client.order.req.OrderAfterSaleReq;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.exception.OpenApiProviderException;
import com.cosfo.manage.common.exception.code.OpenApiErrorCode;
import com.cosfo.manage.common.util.AssertCheckParams;
import com.cosfo.manage.facade.ordercenter.*;
import com.cosfo.manage.facade.usercenter.UserCenterTenantFacade;
import com.cosfo.ordercenter.client.common.OrderAfterSaleServiceTypeEnum;
import com.cosfo.ordercenter.client.common.OrderAfterSaleTypeEnum;
import com.cosfo.ordercenter.client.common.OrderStatusEnum;
import com.cosfo.ordercenter.client.req.OrderItemExtraQueryReq;
import com.cosfo.ordercenter.client.req.OrderQueryReq;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleDTO;
import com.cosfo.ordercenter.client.resp.order.OrderItemExtraResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.google.common.base.Functions;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Table;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.account.IsvInfo;
import net.xianmu.common.account.IsvInfoHolder;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: fansongsong
 * @Date: 2023-09-26
 * @Description:
 */
@Slf4j
@Component
@DubboService
public class OrderAfterSaleOpenProviderImpl implements OrderAfterSaleOpenProvider {

    @Resource
    private UserCenterTenantFacade userCenterTenantFacade;
    @Resource
    private OrderItemExtraQueryFacade orderItemExtraQueryFacade;
    @Resource
    private OrderAfterSaleFacade orderAfterSaleFacade;
    @Resource
    private OrderQueryFacade orderQueryFacade;
    @Resource
    private OrderItemSnapshotQueryFacade orderItemSnapshotQueryFacade;
    @Resource
    private OrderItemQueryFacade orderItemQueryFacade;

    /**
     * openApi支持的售后类型
     */
    private static final List<Integer> OPEN_API_SERVICE_TYPE_LIST = Lists.newArrayList(OrderAfterSaleServiceTypeEnum.REFUND_ENTER_BILL.getValue());

    @Override
    public DubboResponse<Boolean> batchCreateAfterSale(OrderAfterSaleBatchReq orderAfterSaleBatchReq) {
        Long tenantId = Optional.ofNullable(IsvInfoHolder.getAccount()).map(IsvInfo::getAccountId).orElse(null);
        // 参数格式校验
        validOrderAfterSaleBatchReq(orderAfterSaleBatchReq, tenantId);
        // 校验订单信息
        List<OrderResp> orderDTOList = checkAndQueryOrder(orderAfterSaleBatchReq, tenantId);
        Map<String, OrderResp> customOrderMap = orderDTOList.stream().collect(Collectors.toMap(OrderResp::getCustomerOrderId, Function.identity(), (v1, v2) -> v1));

        // 从扩展表里查询出数据组装，数据
        List<String> customerOrderItemIdList = orderAfterSaleBatchReq.getApplyAfterSaleList().stream().map(OrderAfterSaleReq::getCustomerOrderItemId).collect(Collectors.toList());
        List<OrderItemExtraResp> orderItemExtraList = orderItemExtraQueryFacade.queryOrderItemExtraList(OrderItemExtraQueryReq.builder().tenantId(tenantId).customerOrderItemIdList(customerOrderItemIdList).build());
        List<Long> orderItemIdList = orderItemExtraList.stream().map(OrderItemExtraResp::getOrderItemId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderItemIdList)) {
            throw new OpenApiProviderException("外部系统子订单号找不到对应订单信息", OpenApiErrorCode.CREATE_AFTER_VALID_CODE);
        }

        // 构建默认申请金额(默认数量*商品价格=售后金额)
        List<OrderAfterSaleDTO> orderAfterSaleDTOList = builderDefaultReqData(orderAfterSaleBatchReq, orderItemExtraList, customOrderMap);

        List<Long> idList = orderAfterSaleFacade.batchCreateAfterSaleForOpen(orderAfterSaleDTOList);
        return DubboResponse.getOK(CollectionUtils.isNotEmpty(idList));
    }

    /**
     * 校验查询订单信息
     * @param orderAfterSaleBatchReq
     * @param tenantId
     * @return
     */
    private List<OrderResp> checkAndQueryOrder(OrderAfterSaleBatchReq orderAfterSaleBatchReq, Long tenantId) {
        List<String> customerOrderIdList = orderAfterSaleBatchReq.getApplyAfterSaleList().stream().map(dto -> dto.getCustomerOrderId()).distinct().collect(Collectors.toList());
        OrderQueryReq orderQueryReq = new OrderQueryReq();
        orderQueryReq.setTenantId(tenantId);
        orderQueryReq.setCustomerOrderIds(customerOrderIdList);
        List<OrderResp> orderResps = Optional.ofNullable(orderQueryFacade.queryOrderList(orderQueryReq)).orElse(Collections.EMPTY_LIST);
        orderResps = orderResps.stream().filter(orderDTO -> !OrderStatusEnum.CANCELED.getCode().equals(orderDTO.getStatus())).collect(Collectors.toList());
        if (customerOrderIdList.size() != orderResps.size()) {
            List<String> dbCustomerOrderId = orderResps.stream().map(OrderResp::getCustomerOrderId).collect(Collectors.toList());
            customerOrderIdList.removeAll(dbCustomerOrderId);
            log.info("售后单对应订单不存在, customerOrderIdList={},orderAfterSaleBatchReq={}", JSON.toJSON(customerOrderIdList), JSON.toJSONString(orderAfterSaleBatchReq));
            throw new BizException("售后单对应订单不存在,订单号：" + StringUtils.join(customerOrderIdList, ','), OpenApiErrorCode.CREATE_AFTER_VALID_CODE);
        }

        // 校验当前订单状态是否支持配送后售后
        Optional<OrderResp> orderOptional = orderResps.stream().filter(order -> !OrderStatusEnum.ableApplyDeliveredAfterSale(order.getStatus())).findFirst();
        if (orderOptional.isPresent()) {
            log.info("订单当前状态不支持发起配送后售后,订单号={}", orderOptional.get().getCustomerOrderId());
            throw new BizException("订单当前状态不支持发起配送后售后,订单号：" + orderOptional.get().getCustomerOrderId(), OpenApiErrorCode.ORDER_STATUS_UN_SUPPORT_CODE);
        }

        return orderResps;
    }

    public OrderAfterSaleDTO orderAfterSaleReqToDto(OrderAfterSaleReq orderAfterSaleReq, Table<Long, String, Long> customerOrderItemIdTable, OrderResp order) {
        if (orderAfterSaleReq == null) {
            return null;
        }

        if (order == null) {
            throw new OpenApiProviderException("订单异常");
        }
        Long orderItemId = Optional.ofNullable(order).map(OrderResp::getId)
                .map(orderId -> customerOrderItemIdTable.get(orderId, orderAfterSaleReq.getCustomerOrderItemId())).orElse(null);
        AssertCheckParams.notNullWithOpenApiProviderException(orderItemId, "外部子订单不存在,外部子单号" + orderAfterSaleReq.getCustomerOrderItemId(), OpenApiErrorCode.CREATE_AFTER_VALID_CODE);

        OrderAfterSaleDTO orderAfterSaleDTO = new OrderAfterSaleDTO();
        orderAfterSaleDTO.setOrderId(order.getId());
        orderAfterSaleDTO.setTenantId(order.getTenantId());
        orderAfterSaleDTO.setStoreId(order.getStoreId());
        orderAfterSaleDTO.setAccountId(order.getAccountId());
        orderAfterSaleDTO.setWarehouseType(order.getWarehouseType());
        orderAfterSaleDTO.setOrderItemId(orderItemId);
        orderAfterSaleDTO.setAmount(orderAfterSaleReq.getAmount());
        orderAfterSaleDTO.setAfterSaleType(orderAfterSaleReq.getAfterSaleType());
        orderAfterSaleDTO.setServiceType(orderAfterSaleReq.getServiceType());
        orderAfterSaleDTO.setApplyPrice(orderAfterSaleReq.getApplyPrice());
        orderAfterSaleDTO.setTotalPrice(orderAfterSaleReq.getApplyPrice());
        orderAfterSaleDTO.setReason(orderAfterSaleReq.getReason());
        orderAfterSaleDTO.setUserRemark(orderAfterSaleReq.getUserRemark());
        orderAfterSaleDTO.setProofPicture(orderAfterSaleReq.getProofPicture());
        orderAfterSaleDTO.setCustomerAfterSaleOrderNo(orderAfterSaleReq.getCustomerAfterSaleOrderNo());
        orderAfterSaleDTO.setReqSource(Constants.OPEN_API_REQ_SOURCE);
        return orderAfterSaleDTO;
    }

    /**
     * 批量售后请求，基础校验
     *
     * @param orderAfterSaleBatchReq
     * @param tenantId
     */
    private void validOrderAfterSaleBatchReq(OrderAfterSaleBatchReq orderAfterSaleBatchReq, Long tenantId) {
        if (ObjectUtil.isNull(tenantId)) {
            log.info("批量创建售后单,租戶编号为空 IsvInfo:{},orderAfterSaleBatchReq:{}", JSON.toJSONString(IsvInfoHolder.getAccount()), JSON.toJSONString(orderAfterSaleBatchReq));
            throw new BizException("租戶编号为空", OpenApiErrorCode.CREATE_AFTER_VALID_CODE);
        }

        TenantResultResp tenant = userCenterTenantFacade.getTenantById(tenantId);
        if (ObjectUtil.isNull(tenant)) {
            log.info("批量创建售后单,租戶信息不存在 IsvInfo:{},orderAfterSaleBatchReq:{}", JSON.toJSONString(IsvInfoHolder.getAccount()), JSON.toJSONString(orderAfterSaleBatchReq));
            throw new BizException("租戶信息不存在", OpenApiErrorCode.CREATE_AFTER_VALID_CODE);
        }

        long orderItemIdNum = orderAfterSaleBatchReq.getApplyAfterSaleList().stream().map(OrderAfterSaleReq::getCustomerOrderItemId).distinct().count();
        if (orderAfterSaleBatchReq.getApplyAfterSaleList().size() > orderItemIdNum) {
            log.info("批量创建售后单，单笔订单商品子项不可重复 ,orderAfterSaleBatchReq:{}", JSON.toJSONString(orderAfterSaleBatchReq));
            throw new BizException("批量创建售后单，单笔订单商品子项不可重复", OpenApiErrorCode.CREATE_AFTER_VALID_CODE);
        }

        // 配送后售后参数校验
        boolean anyMatch = orderAfterSaleBatchReq.getApplyAfterSaleList().stream().anyMatch(req -> !OrderAfterSaleTypeEnum.DELIVERED.getType().equals(req.getAfterSaleType()));
        if (anyMatch) {
            log.info("批量创建售后单，当前不支持配送前售后 ,orderAfterSaleBatchReq:{}", JSON.toJSONString(orderAfterSaleBatchReq));
            throw new BizException("批量创建售后单，当前不支持配送前售后", OpenApiErrorCode.CREATE_AFTER_VALID_CODE);
        }

        // 是否存在不支持的售后单类型
        Optional<OrderAfterSaleReq> errorAfterSaleReq = orderAfterSaleBatchReq.getApplyAfterSaleList().stream().filter(req -> !OPEN_API_SERVICE_TYPE_LIST.contains(req.getServiceType())).findFirst();
        if (errorAfterSaleReq.isPresent()) {
            log.info("批量创建售后单，存在不支持的售后单类型 ,orderAfterSaleBatchReq:{}", JSON.toJSONString(orderAfterSaleBatchReq));
            throw new BizException("存在不支持的售后单serviceType，当前只支持退款录入账单，不支持售后类型的售后单号:" + errorAfterSaleReq.get().getCustomerAfterSaleOrderNo(), OpenApiErrorCode.CREATE_AFTER_VALID_CODE);
        }

        // 验证批量售后单订单售后类型
        Map<String, List<OrderAfterSaleReq>> orderAfterSaleMap = orderAfterSaleBatchReq.getApplyAfterSaleList().stream().collect(Collectors.groupingBy(OrderAfterSaleReq::getCustomerOrderId));
        for (Map.Entry<String, List<OrderAfterSaleReq>> entry : orderAfterSaleMap.entrySet()) {
            List<OrderAfterSaleReq> orderAfterSaleReqList = entry.getValue();
            // 单个订单的申请售后类型
            long singleOrderServiceType = orderAfterSaleReqList.stream().map(OrderAfterSaleReq::getServiceType).distinct().count();
            if (singleOrderServiceType > NumberConstant.ONE) {
                log.info("批量创建售后单，同一订单，批量售后不支持多种售后类型,订单号:{},orderAfterSaleBatchReq:{}", entry.getKey(), JSON.toJSONString(orderAfterSaleBatchReq));
                throw new BizException("同一订单，批量售后不支持多种售后类型,订单号:" + entry.getKey(), OpenApiErrorCode.CREATE_AFTER_SERVICE_TYPE_VALID_CODE);
            }
        }
    }

    /**
     * 组装默认数据
     * @param orderAfterSaleBatchReq
     * @param orderItemExtraList
     */
    private List<OrderAfterSaleDTO> builderDefaultReqData(OrderAfterSaleBatchReq orderAfterSaleBatchReq, List<OrderItemExtraResp> orderItemExtraList, Map<String, OrderResp> customOrderMap) {
        // 填充售后图片属性
        orderAfterSaleBatchReq.getApplyAfterSaleList().forEach(orderAfterSaleReq -> {
            if (StringUtils.isEmpty(orderAfterSaleReq.getProofPicture())) {
                orderAfterSaleReq.setProofPicture(StringUtils.EMPTY);
            }
        });

        Table<Long, String, Long> customerOrderItemIdTable = HashBasedTable.create();
        for (OrderItemExtraResp orderItemExtraDTO : orderItemExtraList) {
            customerOrderItemIdTable.put(orderItemExtraDTO.getOrderId(), orderItemExtraDTO.getCustomerOrderItemId(), orderItemExtraDTO.getOrderItemId());
        }
        List<Long> orderItemIdList = orderItemExtraList.stream().map(OrderItemExtraResp::getOrderItemId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderItemIdList)) {
            throw new OpenApiProviderException("订单子项参数异常", OpenApiErrorCode.CREATE_AFTER_VALID_CODE);
        }

        // 批量查询订单子项数据、快照数据
        List<OrderItemResp> orderItemResps = orderItemQueryFacade.queryByIds(orderItemIdList);
        Map<Long, OrderItemResp> orderItemMap = orderItemResps.stream().collect(Collectors.toMap(OrderItemResp::getId, Function.identity(), (v1, v2) -> v1));

        List<OrderItemSnapshotResp> orderItemSnapshotResps = orderItemSnapshotQueryFacade.queryByOrderItemIds(orderItemIdList);
        Map<Long, OrderItemSnapshotResp> orderItemSnapshotMap = orderItemSnapshotResps.stream().collect(Collectors.toMap(OrderItemSnapshotResp::getOrderItemId, Functions.identity(), (v1, v2) -> v1));

        for (OrderAfterSaleReq orderAfterSaleReq : orderAfterSaleBatchReq.getApplyAfterSaleList()) {
            Long orderItemId = Optional.ofNullable(customOrderMap.get(orderAfterSaleReq.getCustomerOrderId())).map(OrderResp::getId)
                    .map(orderId -> customerOrderItemIdTable.get(orderId, orderAfterSaleReq.getCustomerOrderItemId())).orElse(null);
            AssertCheckParams.notNullWithOpenApiProviderException(orderItemId, "外部子订单不存在,外部子单号" + orderAfterSaleReq.getCustomerOrderItemId(), OpenApiErrorCode.CREATE_AFTER_VALID_CODE);
            OrderItemResp orderItem = orderItemMap.get(orderItemId);
            AssertCheckParams.notNullWithOpenApiProviderException(orderItem, "外部子订单不存在,外部子单号" + orderAfterSaleReq.getCustomerOrderItemId(), OpenApiErrorCode.CREATE_AFTER_VALID_CODE);
            // 退货退款录入账单类型
            if (OrderAfterSaleServiceTypeEnum.RETURN_REFUND_ENTER_BILL.getValue().equals(orderAfterSaleReq.getServiceType())) {
                BigDecimal applyPrice = NumberUtil.mul(orderItem.getPayablePrice(), orderAfterSaleReq.getAmount());
                // 设置并校验申请金额
                validOrderAfterSaleApplyPrice(orderAfterSaleReq, applyPrice);
            }
            // 退款录入账单类型
            if (OrderAfterSaleServiceTypeEnum.REFUND_ENTER_BILL.getValue().equals(orderAfterSaleReq.getServiceType())) {
                OrderItemSnapshotResp orderItemSnapshot = orderItemSnapshotMap.get(orderItemId);
                AssertCheckParams.notNullWithOpenApiProviderException(orderItemSnapshot, "外部子订单不存在,外部子单号" + orderAfterSaleReq.getCustomerOrderItemId(), OpenApiErrorCode.CREATE_AFTER_VALID_CODE);
                // 仅退款场景，openApi仅支持基于购买件数单位退款，因此乘以最大售后数量
                orderAfterSaleReq.setAmount(orderAfterSaleReq.getAmount() * orderItemSnapshot.getMaxAfterSaleAmount());

                BigDecimal applyTempPrice = NumberUtil.mul(orderAfterSaleReq.getAmount(), orderItem.getTotalPrice());
                Integer maxApplyAmount = orderItem.getAmount() * orderItemSnapshot.getMaxAfterSaleAmount();
                BigDecimal applyPrice = NumberUtil.div(applyTempPrice, maxApplyAmount, 2);
                // 设置并校验申请金额
                validOrderAfterSaleApplyPrice(orderAfterSaleReq, applyPrice);

            }
        }

        // 转换为dto
        return orderAfterSaleBatchReq.getApplyAfterSaleList().stream().map(req -> orderAfterSaleReqToDto(req, customerOrderItemIdTable, customOrderMap.get(req.getCustomerOrderId()))
        ).collect(Collectors.toList());
    }

    /**
     * 设置并校验申请金额
     *
     * @param orderAfterSaleReq
     * @param applyPrice
     */
    private void validOrderAfterSaleApplyPrice(OrderAfterSaleReq orderAfterSaleReq, BigDecimal applyPrice) {
        // 没传申请金额，则使用计算的售后金额
        if (ObjectUtil.isNull(orderAfterSaleReq.getApplyPrice())) {
            orderAfterSaleReq.setApplyPrice(applyPrice);
        }
        // 判断金额是否满足条件
        if (orderAfterSaleReq.getApplyPrice().compareTo(applyPrice) > 0) {
            throw new OpenApiProviderException("申请退款金额超额，外部售后单:" + orderAfterSaleReq.getCustomerAfterSaleOrderNo() + "申请退款数" + orderAfterSaleReq.getAmount() + "，最大售后金额为" + applyPrice + "元", OpenApiErrorCode.CREATE_AFTER_VALID_CODE);
        }
    }
}
