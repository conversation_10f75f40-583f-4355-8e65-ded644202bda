package com.cosfo.manage.provider.impl;

import com.cosfo.manage.bill.model.dto.TenantBillQueryDTO;
import com.cosfo.manage.bill.service.FinancialTenantBillService;
import com.cosfo.manage.client.file.FileExportProvider;
import com.cosfo.manage.client.file.req.TenantBillQueryReq;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * @author: xiaowk
 * @time: 2024/1/10 下午3:31
 */
@DubboService
@Slf4j
public class FileExportProviderImpl implements FileExportProvider {

    @Resource
    private FinancialTenantBillService financialTenantBillService;

    @Override
    public DubboResponse<Boolean> tenantBillExport(TenantBillQueryReq tenantBillQueryReq) {

        TenantBillQueryDTO tenantBillQueryDTO = new TenantBillQueryDTO();
        tenantBillQueryDTO.setStartTime(tenantBillQueryReq.getStartTime());
        tenantBillQueryDTO.setEndTime(tenantBillQueryReq.getEndTime());
        tenantBillQueryDTO.setType(tenantBillQueryReq.getType());
        tenantBillQueryDTO.setSupplierId(tenantBillQueryReq.getSupplierId());
        tenantBillQueryDTO.setTenantId(tenantBillQueryReq.getTenantId());
        tenantBillQueryDTO.setExportContentType(tenantBillQueryReq.getExportContentType());
        tenantBillQueryDTO.setReqSource("cosfo-oms");

        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(tenantBillQueryReq.getTenantId());

        financialTenantBillService.export(tenantBillQueryDTO, loginContextInfoDTO);

        return DubboResponse.getOK(true);
    }
}
