package com.cosfo.manage.provider.impl;

import com.cosfo.manage.agentorder.dao.PlanOrderDao;
import com.cosfo.manage.agentorder.service.PlanOrderService;
import com.cosfo.manage.client.planorder.PlanOrderQueryProvider;
import com.cosfo.manage.client.planorder.req.CountPlanOrderStatusNumReq;
import com.cosfo.manage.client.planorder.req.PlanOrderQueryReq;
import com.cosfo.manage.client.planorder.resp.PlanOrderDetailResp;
import com.cosfo.manage.client.planorder.resp.PlanOrderResp;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * @author: xiaowk
 * @time: 2024/2/23 下午2:11
 */
@DubboService
@Slf4j
public class PlanOrderQueryProviderImpl implements PlanOrderQueryProvider {

    @Resource
    private PlanOrderDao planOrderDao;
    @Resource
    private PlanOrderService planOrderService;

    @Override
    public DubboResponse<PlanOrderDetailResp> queryByPlanOrderNo(String planOrderNo) {
        return DubboResponse.getOK(planOrderService.planOrderDetail(planOrderNo));
    }

    @Override
    public DubboResponse<PageInfo<PlanOrderResp>> queryPlanOrderPage(PlanOrderQueryReq planOrderQueryReq) {
        PageInfo<PlanOrderResp> planOrderRespPageInfo = planOrderService.queryPlanOrderPage(planOrderQueryReq);
        return DubboResponse.getOK(planOrderRespPageInfo);
    }

    @Override
    public DubboResponse<Integer> countPlanOrderStatusNum(CountPlanOrderStatusNumReq req) {
        long count = planOrderDao.countByParam(req.getTenantId(), req.getStoreIds(), req.getPlanOrderStatusList());
        return DubboResponse.getOK((int)count);
    }
}
