package com.cosfo.manage.provider.impl;

import com.cosfo.manage.agentorder.model.dto.PlanOrderCancelDto;
import com.cosfo.manage.agentorder.service.PlanOrderService;
import com.cosfo.manage.client.planorder.PlanOrderCommandProvider;
import com.cosfo.manage.client.planorder.req.CancelPlanOrderReq;
import com.cosfo.manage.client.planorder.req.CreateOrderFailReq;
import com.cosfo.manage.client.planorder.req.CreateOrderSuccessReq;
import com.cosfo.manage.common.context.AgentOrderEnum;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * @author: xiaowk
 * @time: 2024/2/23 下午2:00
 */
@DubboService
@Slf4j
public class PlanOrderCommandProviderImpl implements PlanOrderCommandProvider {

    @Resource
    private PlanOrderService planOrderService;

    @Override
    public DubboResponse<Boolean> cancelPlanOrder(CancelPlanOrderReq cancelPlanOrderReq) {
        PlanOrderCancelDto cancelDto = new PlanOrderCancelDto();
        cancelDto.setPlanOrderId(cancelPlanOrderReq.getPlanOrderId());
        cancelDto.setPlanOrderNo(cancelPlanOrderReq.getPlanOrderNo());
        cancelDto.setTenantId(cancelPlanOrderReq.getTenantId());
        cancelDto.setOperatorId(cancelPlanOrderReq.getOperatorId());
        cancelDto.setCancelRemark(cancelPlanOrderReq.getCancelRemark());
        cancelDto.setOperatorSource(AgentOrderEnum.CancelOrderRoleEnum.getByName(cancelPlanOrderReq.getOperatorSource()));
        planOrderService.cancelPlanOrder(cancelDto);
        return DubboResponse.getOK(true);
    }

    @Override
    public DubboResponse<Boolean> updateCreateOrderFail(CreateOrderFailReq createOrderFailReq) {
        return DubboResponse.getOK(planOrderService.updateCreateOrderFail(createOrderFailReq));
    }

    @Override
    public DubboResponse<Boolean> updateCreateOrderSuccess(CreateOrderSuccessReq createOrderSuccessReq) {
        return DubboResponse.getOK(planOrderService.updateCreateOrderSuccess(createOrderSuccessReq));
    }


}
