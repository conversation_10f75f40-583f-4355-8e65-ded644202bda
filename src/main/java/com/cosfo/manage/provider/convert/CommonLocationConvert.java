//package com.cosfo.manage.provider.convert;
//
//import com.cosfo.manage.client.common.location.resp.CommonLocationCityResp;
//import com.cosfo.manage.system.model.dto.CommonLocationCityDTO;
//import org.mapstruct.Mapper;
//import org.mapstruct.factory.Mappers;
//
//import java.util.List;
//
///**
// * @Author: fansongsong
// * @Date: 2024-03-20
// * @Description:
// */
//
//@Mapper
//public interface CommonLocationConvert {
//
//    CommonLocationConvert INSTANCE = Mappers.getMapper(CommonLocationConvert.class);
//
//    List<CommonLocationCityResp> cityList2RespList(List<CommonLocationCityDTO> commonLocationCityDTOS);
//}
