package com.cosfo.manage.provider.convert;

import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.client.productitem.req.ProductItemReq;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.common.context.OnSaleTypeEnum;
import com.cosfo.manage.good.model.dto.ProductSkuAddInput;
import com.cosfo.manage.good.model.dto.ProductSpuAddInput;
import com.cosfo.manage.market.model.dto.MarketAreaItemMappingInput;
import com.cosfo.manage.market.model.dto.MarketItemInput;
import com.cosfo.manage.market.model.dto.MarketItemUnfairPriceStrategyDTO;
import com.cosfo.manage.market.model.dto.MarketSpuInput;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Mapper
public interface ProductItemConvert {

    Integer onSale= OnSaleTypeEnum.ON_SALE.getCode ();
    Integer itemSaleMode = 0;

    ProductItemConvert INSTANCE = Mappers.getMapper(ProductItemConvert.class);

    @Mapping(source = "skuTitle", target = "title")
    @Mapping(target = "storageTemperature", expression = "java(com.cosfo.manage.good.model.dto.ProductSpuAddInput.getDefaultStorageTemperature(req.getStorageLocation(),req.getStorageTemperature()))")
    @Mapping(target = "guaranteePeriod",defaultValue = "1200")
    @Mapping(target = "guaranteeUnit",defaultValue = "1")
    @Mapping(target = "origin",expression = "java(com.cosfo.manage.provider.convert.ProductItemConvert.removeSpaces(req.getOrigin()))")
    @Mapping(target = "brandName",expression = "java(com.cosfo.manage.provider.convert.ProductItemConvert.removeSpaces(req.getBrandName()))")
    @Mapping(source = "req", target = "productSkuAddInputList", qualifiedByName = "convert2ProductSkuAddInputList")
    ProductSpuAddInput convert2ProductSpuAddInput(ProductItemReq req);

    @Named("convert2ProductSkuAddInputList")
    static List<ProductSkuAddInput> convert2ProductSkuAddInputList(ProductItemReq req) {
        ProductSkuAddInput productSkuAddInput = new ProductSkuAddInput ();
        productSkuAddInput.setSpecification(removeSpaces(req.getSpecification ()));
        productSkuAddInput.setSpecificationUnit(req.getSpecificationUnit ());
        productSkuAddInput.setPlaceType(req.getPlaceType ());
        productSkuAddInput.setVolume(removeSpaces(req.getVolume ()));
        productSkuAddInput.setVolumeUnit(req.getVolumeUnit ());
        productSkuAddInput.setWeight(req.getWeight ());
        productSkuAddInput.setTaxRateValue(req.getTaxRateValue ());
        productSkuAddInput.setCustomSkuCode(req.getCustomerSkuCode ());
        return Collections.singletonList (productSkuAddInput);
    }
    @Mapping(target = "marketItemInput", expression = "java(com.cosfo.manage.provider.convert.ProductItemConvert.convert2MarketItemInput(req,storeGroupId))")
    MarketSpuInput convert2MarketSpuInput(ProductItemReq req,Long storeGroupId);

    static MarketItemInput convert2MarketItemInput(ProductItemReq req, Long storeGroupId) {
        MarketItemInput marketItemInput =new MarketItemInput();
        marketItemInput.setGoodsType(2);
        marketItemInput.setMiniOrderQuantity(1);

        marketItemInput.setItemCode (req.getCustomerSkuCode ());
        marketItemInput.setDefaultPrice(buildMarketAreaItemMappingInput(storeGroupId));
        marketItemInput.setMarketItemUnfairPriceStrategyDTO(buildMarketItemUnfairPriceStrategyDTO());
        marketItemInput.setSpecification(removeSpaces(req.getSpecification ()));
        marketItemInput.setMaxAfterSaleAmount(Objects.isNull(req.getMaxAfterSaleAmount()) ? NumberConstants.ONE :req.getMaxAfterSaleAmount());
        marketItemInput.setAfterSaleUnit(StringUtils.isBlank(req.getAfterSaleUnit()) ? req.getSpecificationUnit () : req.getAfterSaleUnit());

        marketItemInput.setOnSale (onSale);
        marketItemInput.setItemSaleMode (itemSaleMode);
        return marketItemInput;
    }

    static MarketItemUnfairPriceStrategyDTO buildMarketItemUnfairPriceStrategyDTO() {
        MarketItemUnfairPriceStrategyDTO dto = new MarketItemUnfairPriceStrategyDTO ();
        dto.setDefaultFlag(1);
        dto.setStrategyType(1);
        return dto;
    }

    static MarketAreaItemMappingInput buildMarketAreaItemMappingInput(Long storeGroupId) {
        MarketAreaItemMappingInput mappingInput = new MarketAreaItemMappingInput ();
        mappingInput.setPriceType(2);
        mappingInput.setType(2);
        mappingInput.setMappingNumber(BigDecimal.ZERO);
        mappingInput.setStoreGroupIds (Collections.singletonList (storeGroupId));
        return mappingInput;
    }
    static String removeSpaces(String input) {
        if (input == null) {
            return null;
        }
        return input.replace(" ", "");
    }
}
