package com.cosfo.manage.provider.convert;

import com.cosfo.manage.client.tenant.req.TenantQueryReq;
import com.cosfo.manage.client.tenant.resp.TenantBaseResp;
import com.cosfo.manage.client.tenant.resp.TenantInfoResp;
import com.cosfo.manage.client.tenant.resp.TenantResp;
import com.cosfo.manage.tenant.model.dto.TenantDTO;
import com.cosfo.manage.tenant.model.dto.TenantInfoDTO;
import com.cosfo.manage.tenant.model.dto.TenantQueryDTO;

import java.util.Objects;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/26
 */
public class TenantConvert {

    /**
     * 转化为TenantInfoResp
     *
     * @param tenantInfoDTO
     * @return
     */
    public static TenantInfoResp convertToTenantInfoResp(TenantInfoDTO tenantInfoDTO){
        if(Objects.isNull(tenantInfoDTO)){
            return null;
        }

        TenantInfoResp tenantInfoResp = new TenantInfoResp();
        tenantInfoResp.setTenantId(tenantInfoDTO.getTenantId());
        tenantInfoResp.setName(tenantInfoDTO.getName());
        tenantInfoResp.setAdminId(tenantInfoDTO.getAdminId());
        tenantInfoResp.setPhone(tenantInfoDTO.getPhone());
        tenantInfoResp.setTenantAccountId(tenantInfoDTO.getTenantAccountId());
        tenantInfoResp.setCompanyName(tenantInfoDTO.getCompanyName());
        tenantInfoResp.setTenantAccountName(tenantInfoDTO.getNickName());
        return tenantInfoResp;
    }

    /**
     * 转换为TenantQueryDTO
     *
     * @param tenantQueryReq
     * @return
     */
    public static TenantQueryDTO convertToTenantQueryDTO(TenantQueryReq tenantQueryReq){
        TenantQueryDTO tenantQueryDTO = new TenantQueryDTO();
        if(Objects.isNull(tenantQueryReq)){
            return tenantQueryDTO;
        }

        tenantQueryDTO.setTenantId(tenantQueryReq.getTenantId());
        tenantQueryDTO.setAdminId(tenantQueryReq.getAdminId());
        tenantQueryDTO.setTenantIds(tenantQueryReq.getTenantIds());
        return tenantQueryDTO;
    }

    /**
     * 转换为TenantResp
     *
     * @param tenantDTO
     * @return
     */
    public static TenantResp convertToTenantResp(TenantDTO tenantDTO){
        if(Objects.isNull(tenantDTO)){
            return null;
        }

        TenantResp tenantResp = new TenantResp();
        tenantResp.setId(tenantDTO.getId());
        tenantResp.setPhone(tenantDTO.getPhone());
        tenantResp.setTenantName(tenantDTO.getTenantName());
        tenantResp.setAdminId(tenantDTO.getAdminId());
        tenantResp.setCompanyName(tenantDTO.getCompanyName());
        return tenantResp;
    }

    public static TenantBaseResp convertToTenantBaseResp(TenantDTO tenantDTO) {
        if(Objects.isNull(tenantDTO)){
            return null;
        }

        TenantBaseResp tenantResp = new TenantBaseResp();
        tenantResp.setId(tenantDTO.getId());
        tenantResp.setTenantName(tenantDTO.getTenantName());
        tenantResp.setTenantBrandName(tenantDTO.getTenantBrandName());
        return tenantResp;
    }
}
