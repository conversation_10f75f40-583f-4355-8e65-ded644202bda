package com.cosfo.manage.provider.convert;

import cn.hutool.core.util.ObjectUtil;
import com.cosfo.manage.client.pos.req.ItemList;
import com.cosfo.manage.client.pos.req.QimaiPosOrderParams;
import com.cosfo.manage.client.pos.req.RefundItemList;
import com.cosfo.manage.client.pos.resp.FailPosResp;
import com.cosfo.manage.pos.model.dto.PosOrderDTO;
import com.cosfo.manage.pos.model.dto.PosOrderItemDTO;
import com.cosfo.manage.pos.model.dto.PostOrderItemRefundDTO;
import com.cosfo.manage.pos.model.vo.FailPosOrderVO;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@Mapper
public interface QimaiPosOrderConvert {
    QimaiPosOrderConvert INSTANCE = Mappers.getMapper(QimaiPosOrderConvert.class);
//    merchantStoreCode    detailInfo channelType
    @Mapping(source = "req.shopCode",target = "outStoreCode")
    @Mapping(source = "req.shopName",target = "outStoreName")
    @Mapping(source = "req.createdAt",target = "availableDate")
    @Mapping(source = "req.buyerRemarks",target = "remarks")
    @Mapping(target = "totalPrice", expression = "java(com.cosfo.manage.provider.convert.QimaiPosOrderConvert.fen2Yuan(req.getTotalAmount()))")
    @Mapping(target = "discountPrice", expression = "java(com.cosfo.manage.provider.convert.QimaiPosOrderConvert.fen2Yuan(req.getDiscountAmount()))")
    @Mapping(source = "req.itemList", target = "orderItemDTOList", qualifiedByName = "convert2PosOrderItemList")
    PosOrderDTO req2PosOrderDTO(QimaiPosOrderParams req,Long tenantId);


    @Named("convert2PosOrderItemList")
    static List<PosOrderItemDTO> convert2PosOrderItemList(List<ItemList> itemList) {
        return itemList.stream ().map (e->{
            PosOrderItemDTO posOrderItemDTO = new PosOrderItemDTO ();
            posOrderItemDTO.setOutMenuCode(e.getItemSign ());
            posOrderItemDTO.setOutMenuName(e.getItemName ());
            posOrderItemDTO.setQuantity(e.getNum ());
            posOrderItemDTO.setTotalPrice(fen2Yuan(e.getItemPrice ()).multiply (new BigDecimal (e.getNum ())));
            posOrderItemDTO.setThirdId (e.getId ());
//            posOrderItemDTO.setOutMenuSpecification();
//            posOrderItemDTO.setDiscountPrice(fen2Yuan(e.));
//            posOrderItemDTO.setDiscountRate();
//            posOrderItemDTO.setTaxRate();
//            posOrderItemDTO.setTaxPrice();
//            posOrderItemDTO.setExcludeTaxPrice();
            return posOrderItemDTO;
        }).collect(Collectors.toList());
    }
    static BigDecimal fen2Yuan(BigDecimal fen) {
        if(ObjectUtil.isNull (fen)){
            return BigDecimal.ZERO;
        }
        return fen.divide (new BigDecimal (100));
    }


    List<FailPosResp> failVO2Resp(List<FailPosOrderVO> failPosOrderVOS);


    List<PostOrderItemRefundDTO> req2ItemRefundDTOs(List<RefundItemList> refundItemList);
    @Mapping(source = "id",target = "thirdId")
    @Mapping(target = "refundAmount", expression = "java(com.cosfo.manage.provider.convert.QimaiPosOrderConvert.fen2Yuan(req.getRefundAmount()))")
    @Mapping(target = "refundNum", expression = "java(com.cosfo.manage.provider.convert.QimaiPosOrderConvert.getRefundNum(req.getNum(),req.getRefundNum()))")
    PostOrderItemRefundDTO req2ItemRefundDTO(RefundItemList req);
    static int getRefundNum(String num,Integer refundNum) {
        if(ObjectUtil.isNotNull (refundNum)){
            return refundNum;
        }
        if(StringUtils.isNotEmpty (num)){
            return Integer.valueOf (num);
        }
        return 0;
    }
}
