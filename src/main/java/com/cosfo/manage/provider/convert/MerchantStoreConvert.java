package com.cosfo.manage.provider.convert;

import com.cosfo.manage.client.merchant.req.MerchantStoreQueryReq;
import com.cosfo.manage.client.merchant.resp.MerchantStoreAddressResp;
import com.cosfo.manage.client.merchant.resp.MerchantStoreResp;
import com.cosfo.manage.merchant.model.dto.MerchantStoreAddressDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreQueryDTO;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/3/27
 */
public class MerchantStoreConvert {

    /**
     * 转化为MerchantStoreQueryDTO
     *
     * @param merchantStoreQueryReq
     * @return
     */
    public static MerchantStoreQueryDTO convertToMerchantStoreQueryDTO(MerchantStoreQueryReq merchantStoreQueryReq){
        if (merchantStoreQueryReq == null) {
            return null;
        }

        MerchantStoreQueryDTO merchantStoreQueryDTO = new MerchantStoreQueryDTO();
        merchantStoreQueryDTO.setStoreIds(merchantStoreQueryReq.getStoreIds());
        return merchantStoreQueryDTO;
    }

    public static List<MerchantStoreResp> convertToMerchantStoreRespList(List<MerchantStoreDTO> merchantStoreDTOS){

        if (merchantStoreDTOS == null) {
            return Collections.emptyList();
        }

        List<MerchantStoreResp> merchantStoreRespList = new ArrayList<>();
        for (MerchantStoreDTO merchantStoreDTO : merchantStoreDTOS) {
            merchantStoreRespList.add(toMerchantStoreResp(merchantStoreDTO));
        }

        return merchantStoreRespList;
    }

    public static MerchantStoreResp toMerchantStoreResp(MerchantStoreDTO merchantStoreDTO) {
        if (merchantStoreDTO == null) {
            return null;
        }

        MerchantStoreResp merchantStoreResp = new MerchantStoreResp();
        merchantStoreResp.setId(merchantStoreDTO.getId());
        merchantStoreResp.setTenantId(merchantStoreDTO.getTenantId());
        merchantStoreResp.setStoreName(merchantStoreDTO.getStoreName());
        merchantStoreResp.setType(merchantStoreDTO.getType());
        merchantStoreResp.setRegisterTime(merchantStoreDTO.getRegisterTime());
        merchantStoreResp.setStatus(merchantStoreDTO.getStatus());
        merchantStoreResp.setAuditRemark(merchantStoreDTO.getAuditRemark());
        merchantStoreResp.setAuditTime(merchantStoreDTO.getAuditTime());
        merchantStoreResp.setBillSwitch(merchantStoreDTO.getBillSwitch());
        merchantStoreResp.setOnlinePayment(merchantStoreDTO.getOnlinePayment());
        merchantStoreResp.setStoreNo(merchantStoreDTO.getStoreNo());
        return merchantStoreResp;
    }

    public static List<MerchantStoreAddressResp> convertToMerchantStoreAddressRespList(List<MerchantStoreAddressDTO> dtos) {
        if (dtos == null) {
            return Collections.emptyList();
        }
        return dtos.stream ().map (e-> convertToMerchantStoreAddressResp(e)).collect(Collectors.toList());
    }

    public static MerchantStoreAddressResp convertToMerchantStoreAddressResp(MerchantStoreAddressDTO e) {
        MerchantStoreAddressResp resp = new MerchantStoreAddressResp ();
        resp.setStoreId(e.getStoreId ());
        resp.setProvince(e.getProvince ());
        resp.setCity(e.getCity ());
        resp.setArea(e.getArea ());
        resp.setCityId(e.getCityId ());
        return resp;
    }
}
