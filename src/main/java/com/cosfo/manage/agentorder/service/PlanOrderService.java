package com.cosfo.manage.agentorder.service;

import com.cosfo.manage.agentorder.model.dto.PlanOrderCancelDto;
import com.cosfo.manage.agentorder.model.input.query.PlanOrderListQueryInput;
import com.cosfo.manage.agentorder.model.input.query.PlanOrderQueryInput;
import com.cosfo.manage.agentorder.model.vo.AgentOrderAgainDetailVO;
import com.cosfo.manage.agentorder.model.vo.PlanOrderDetailTotalVO;
import com.cosfo.manage.agentorder.model.vo.PlanOrderQueryListVO;
import com.cosfo.manage.client.planorder.req.CreateOrderFailReq;
import com.cosfo.manage.client.planorder.req.CreateOrderSuccessReq;
import com.cosfo.manage.client.planorder.req.PlanOrderQueryReq;
import com.cosfo.manage.client.planorder.resp.PlanOrderDetailResp;
import com.cosfo.manage.client.planorder.resp.PlanOrderResp;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2024/2/19 11:15
 * @Description:
 */
public interface PlanOrderService {

    /**
     * 再来一单 根据计划单ID查询详情
     *
     * @param planOrderNo
     * @return
     */
    AgentOrderAgainDetailVO planOrderAgain(String planOrderNo);

    /**
     * 一键提醒
     */
    void notifyAllStore();

    /**
     * 提醒门店
     *
     * @param planOrderId
     */
    void notifyStore(Long planOrderId, LoginContextInfoDTO loginInfo);

    /**
     * 取消订单
     *
     * @param planOrderCancelDto
     * @return
     */
    void cancelPlanOrder(PlanOrderCancelDto planOrderCancelDto);

    /**
     * 根据代下单编号，批量取消计划单
     *
     * @param agentOrderNo
     */
    void autoCancelPanOrder(String agentOrderNo);

    /**
     * 商城端分页查询计划单
     *
     * @param planOrderQueryReq
     * @return
     */
    PageInfo<PlanOrderResp> queryPlanOrderPage(PlanOrderQueryReq planOrderQueryReq);

    /**
     * manage后台分布查询计划单
     *
     * @param queryInput
     * @return
     */
    PageInfo<PlanOrderQueryListVO> queryManageOrderPage(PlanOrderListQueryInput queryInput);

    /**
     * 查询计划单详情
     *
     * @param queryInput
     * @return
     */
    PlanOrderDetailTotalVO planOrderDetail(PlanOrderQueryInput queryInput);

    /**
     * 小程序端查看计划单详情
     *
     * @param planOrderNo
     * @return
     */
    PlanOrderDetailResp planOrderDetail(String planOrderNo);

    /**
     * 计划单创建订单失败，更新
     *
     * @param createOrderFailReq
     * @return
     */
    boolean updateCreateOrderFail(CreateOrderFailReq createOrderFailReq);

    /**
     * 计划单创建订单成功，更新
     *
     * @param createOrderSuccessReq
     * @return
     */
    boolean updateCreateOrderSuccess(CreateOrderSuccessReq createOrderSuccessReq);

    /**
     * 为所有符合条件的计划单生成订单
     * 入参为空时，查询所有
     */
    void planOrderCreateOrder(List<String> planOrderNos);
}
