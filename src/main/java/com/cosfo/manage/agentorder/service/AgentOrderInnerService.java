package com.cosfo.manage.agentorder.service;

import com.cosfo.manage.agentorder.model.dto.AddPlanOrderReturnDto;
import com.cosfo.manage.agentorder.model.input.command.AgentOrderAddInput;

/**
 * @author: monna.chen
 * @Date: 2024/2/6 15:14
 * @Description:
 */
public interface AgentOrderInnerService {

    /**
     * 保存新增的代下单与计划单
     *
     * @param agentOrderAddInput
     * @return
     */
    AddPlanOrderReturnDto saveAgentPlanOrder(AgentOrderAddInput agentOrderAddInput);
}
