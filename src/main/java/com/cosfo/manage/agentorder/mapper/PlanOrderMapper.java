package com.cosfo.manage.agentorder.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.manage.agentorder.model.param.PlanOrderQueryParam;
import com.cosfo.manage.agentorder.model.po.PlanOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 计划订单表(PlanOrder)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-02-06 10:50:41
 */
@Mapper
public interface PlanOrderMapper extends BaseMapper<PlanOrder> {

    /**
     * 查询租户下，所有可以提醒的计划单
     * * 1.计划单状态=待确认
     * * 2.下单方式=生成计划单
     * * 3.上一次通知时间is null  or 前一天（0点刷新）
     *
     * @param tenantId
     * @return
     */
    List<PlanOrder> listNotifyPlanOrders(@Param("tenantId") Long tenantId, @Param("planOrderStatus") Integer planOrderStatus, @Param("planTypeList") List<String> planTypeList, @Param("today")LocalDateTime today);


    Page<PlanOrder> queryPage(Page<PlanOrder> page, @Param("queryParam") PlanOrderQueryParam queryParam);

    /**
     * 查询所有待生成订单的计划单
     *
     * @return
     */
    List<PlanOrder> listWaitCreateOrders(@Param("planOrderStatus") Integer planOrderStatus, @Param("planType") String planType, @Param("planOrderNos") List<String> planOrderNos);

}

