package com.cosfo.manage.agentorder.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.agentorder.mapper.AgentOrderItemMapper;
import com.cosfo.manage.agentorder.model.po.AgentOrderItem;
import net.xianmu.common.exception.BizException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 代门店下单表(AgentOrderItem)表Dao查询服务
 *
 * <AUTHOR>
 * @since 2024-02-06 11:49:40
 */
@Service
public class AgentOrderItemDao extends ServiceImpl<AgentOrderItemMapper, AgentOrderItem> {

    /**
     * 根据代下单编号，查询代下单商品信息
     *
     * @param agentOrderNo
     * @return
     */
    public List<AgentOrderItem> listByAgentOrderNo(String agentOrderNo) {
        if (Objects.isNull(agentOrderNo)) {
            throw new BizException("查询代下单商品详情，代下单编号不可为空");
        }
        LambdaQueryWrapper<AgentOrderItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentOrderItem::getAgentOrderNo, agentOrderNo);
        return list(queryWrapper);
    }

    /**
     * 根据代下单编号，查询代下单商品信息
     * key-agentOrderNo value:代下单商品信息
     *
     * @param agentOrderNos
     * @return
     */
    public Map<String, List<AgentOrderItem>> listByAgentOrderNos(Collection<String> agentOrderNos) {
        if (CollectionUtils.isEmpty(agentOrderNos)) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<AgentOrderItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AgentOrderItem::getAgentOrderNo, agentOrderNos);
        List<AgentOrderItem> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.groupingBy(AgentOrderItem::getAgentOrderNo));
    }

}

