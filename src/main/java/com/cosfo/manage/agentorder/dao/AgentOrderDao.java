package com.cosfo.manage.agentorder.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.agentorder.mapper.AgentOrderMapper;
import com.cosfo.manage.agentorder.model.po.AgentOrder;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 代门店下单表(AgentOrder)表Dao查询服务
 *
 * <AUTHOR>
 * @since 2024-02-06 11:49:40
 */
@Service
public class AgentOrderDao extends ServiceImpl<AgentOrderMapper, AgentOrder> {

    /**
     * 根据代下单编号查询信息
     *
     * @param agentOrderNo
     * @return
     */
    public AgentOrder getByAgentOrderNo(String agentOrderNo) {
        if (Objects.isNull(agentOrderNo)) {
            throw new BizException("查询代下单详情，代下单编号不可为空");
        }
        LambdaQueryWrapper<AgentOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentOrder::getAgentOrderNo, agentOrderNo);
        return getOne(queryWrapper);
    }

}

