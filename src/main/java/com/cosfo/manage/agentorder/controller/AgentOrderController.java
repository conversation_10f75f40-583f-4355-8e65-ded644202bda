package com.cosfo.manage.agentorder.controller;

import com.cosfo.manage.agentorder.model.dto.PlanOrderCancelDto;
import com.cosfo.manage.agentorder.model.input.command.AgentOrderAddInput;
import com.cosfo.manage.agentorder.model.input.command.NotifyStoreInput;
import com.cosfo.manage.agentorder.model.input.command.PlanOrderCancelInput;
import com.cosfo.manage.agentorder.model.input.query.AgentOrderQueryInput;
import com.cosfo.manage.agentorder.model.input.query.PlanOrderListQueryInput;
import com.cosfo.manage.agentorder.model.input.query.PlanOrderQueryInput;
import com.cosfo.manage.agentorder.model.vo.AgentOrderAgainDetailVO;
import com.cosfo.manage.agentorder.model.vo.AgentOrderCheckVO;
import com.cosfo.manage.agentorder.model.vo.PlanOrderDetailTotalVO;
import com.cosfo.manage.agentorder.model.vo.PlanOrderQueryListVO;
import com.cosfo.manage.agentorder.service.AgentOrderService;
import com.cosfo.manage.agentorder.service.PlanOrderService;
import com.cosfo.manage.common.context.AgentOrderEnum;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * 代门店下单管理
 *
 * @author: monna.chen
 * @Date: 2024/2/4 17:41
 * @Description: 代门店下单管理
 */
@RestController
@RequestMapping("/agent-order")
public class AgentOrderController extends BaseController {

    @Resource
    private AgentOrderService agentOrderService;
    @Resource
    private PlanOrderService planOrderService;

    /**
     * 代下单列表
     *
     * @param
     * @return
     */
    @PostMapping("/list")
    @RequiresPermissions(value = {"cosfo_manage:agent-order-manage:query"}, logical = Logical.OR)
    public CommonResult<PageInfo<PlanOrderQueryListVO>> listPlanOrder(@Valid @RequestBody PlanOrderListQueryInput planOrderListQueryInput) {
        if (Objects.isNull(planOrderListQueryInput.getPageIndex()) || Objects.isNull(planOrderListQueryInput.getPageSize())) {
            throw new ParamsException("分页参数不可为空！");
        }
        return CommonResult.ok(planOrderService.queryManageOrderPage(planOrderListQueryInput));
    }

    /**
     * 一键提醒
     *
     * @param
     * @return
     */
    @RequiresPermissions(value = {"cosfo_manage:agent-order-manage-notify:update"}, logical = Logical.OR)
    @PostMapping("/notify-all")
    public CommonResult<Void> notifyAllStore() {
        planOrderService.notifyAllStore();
        return CommonResult.ok();
    }

    /**
     * 提醒门店
     *
     * @param
     * @return
     */
    @RequiresPermissions(value = {"cosfo_manage:agent-order-manage-notify:update"}, logical = Logical.OR)
    @PostMapping("/notify")
    public CommonResult<Void> notifyStore(@Valid @RequestBody NotifyStoreInput notifyStoreInput) {
        planOrderService.notifyStore(notifyStoreInput.getPlanOrderId(), UserLoginContextUtils.getMerchantInfoDTO());
        return CommonResult.ok();
    }

    /**
     * 代门店下单
     * 新增代下单+计划单
     *
     * @param
     * @return 代下单编号
     */
    @RequiresPermissions(value = {"cosfo_manage:agent-order-manage:add"}, logical = Logical.OR)
    @PostMapping("/upsert/add")
    public CommonResult<String> addPlanOrder(@Valid @RequestBody AgentOrderAddInput agentOrderAddInput) {
        return CommonResult.ok(agentOrderService.addPlanOrder(agentOrderAddInput, getMerchantInfoDTO().getTenantId()));
    }


    /**
     * 代下单提交前校验商品
     *
     * @param
     * @return
     */
    @PostMapping("/plan-order-check")
    public CommonResult<List<AgentOrderCheckVO>> checkPlanOrder(@Valid @RequestBody AgentOrderAddInput agentOrderAddInput) {
        return CommonResult.ok(agentOrderService.checkPlanOrder(agentOrderAddInput, getMerchantInfoDTO().getTenantId()));
    }


    /**
     * 快速下单 - 校验单号是否正常
     *
     * @param
     * @return
     */
    @PostMapping("/agent-order-again-check")
    public CommonResult<Boolean> checkAgentOrderAgain(@Valid @RequestBody AgentOrderQueryInput agentOrderQueryInput) {
        return CommonResult.ok(agentOrderService.checkAgentOrderAgain(agentOrderQueryInput.getAgentOrderNo()));
    }

    /**
     * 快速下单
     * 根据代下单编号查询详情
     *
     * @param
     * @return
     */
    @PostMapping("/agent-order-again")
    public CommonResult<AgentOrderAgainDetailVO> agentOrderAgain(@Valid @RequestBody AgentOrderQueryInput agentOrderQueryInput) {
        return CommonResult.ok(agentOrderService.agentOrderAgain(agentOrderQueryInput.getAgentOrderNo()));
    }

    /**
     * 再来一单
     * 根据计划单ID查询详情
     *
     * @param
     * @return
     */
    @PostMapping("/plan-order-again")
    public CommonResult<AgentOrderAgainDetailVO> planOrderAgain(@Valid @RequestBody PlanOrderQueryInput planOrderQueryInput) {
        return CommonResult.ok(planOrderService.planOrderAgain(planOrderQueryInput.getPlanOrderNo()));
    }

    /**
     * 取消订单
     *
     * @param
     * @return
     */
    @RequiresPermissions(value = {"cosfo_manage:agent-order-manage-cancel:update"}, logical = Logical.OR)
    @PostMapping("/upsert/cancel")
    public CommonResult<Void> cancelPlanOrder(@Valid @RequestBody PlanOrderCancelInput planOrderCancelInput) {
        PlanOrderCancelDto cancelDto = new PlanOrderCancelDto();
        cancelDto.setPlanOrderId(planOrderCancelInput.getPlanOrderId());
        cancelDto.setTenantId(UserLoginContextUtils.getTenantId());
        cancelDto.setOperatorId(UserLoginContextUtils.getAuthUserId());
        cancelDto.setCancelRemark(planOrderCancelInput.getCancelRemark());
        cancelDto.setOperatorSource(AgentOrderEnum.CancelOrderRoleEnum.TENANT);
        planOrderService.cancelPlanOrder(cancelDto);
        return CommonResult.ok();
    }

    /**
     * 计划单详情
     *
     * @param
     * @return
     */
    @PostMapping("/plan-order-detail")
    public CommonResult<PlanOrderDetailTotalVO> planOrderDetail(@Valid @RequestBody PlanOrderQueryInput planOrderQueryInput) {
        return CommonResult.ok(planOrderService.planOrderDetail(planOrderQueryInput));
    }

}
