package com.cosfo.manage.agentorder.convert;

import com.cofso.item.client.resp.MarketItemClassificationResp;
import com.cosfo.common.util.DateUtil;
import com.cosfo.manage.agentorder.model.dto.AgentOrderStoreInfoDto;
import com.cosfo.manage.agentorder.model.dto.PlanOrderCancelDto;
import com.cosfo.manage.agentorder.model.dto.PlanOrderCancelInfoDto;
import com.cosfo.manage.agentorder.model.input.query.PlanOrderListQueryInput;
import com.cosfo.manage.agentorder.model.param.PlanOrderQueryParam;
import com.cosfo.manage.agentorder.model.po.AgentOrderItem;
import com.cosfo.manage.agentorder.model.po.PlanOrder;
import com.cosfo.manage.agentorder.model.vo.*;
import com.cosfo.manage.common.constant.AgentOrderConstant;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.market.model.dto.MarketItemInfoDTO;
import com.cosfo.manage.marketing.model.dto.ItemSaleLimitConfigDTO;
import com.cosfo.manage.order.model.vo.OrderItemVO;
import com.cosfo.manage.product.model.dto.ProductCategoryDTO;
import net.xianmu.common.input.PageSortInput;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: monna.chen
 * @Date: 2024/2/7 10:40
 * @Description:
 */
public class AgentOrderConvert {

    public static List<AgentOrderAgainItemVO> convert2AgentItemVos(List<AgentOrderItem> agentOrderItems, Map<Long, MarketItemInfoDTO> itemMap,
                                                                   Map<Long, MarketItemClassificationResp> classificationMap, Map<Long, ProductCategoryDTO> categoryDTOMap,
                                                                   Map<Long, ItemSaleLimitConfigDTO>  itemSaleLimitConfigMap) {
        if (CollectionUtils.isEmpty(agentOrderItems)) {
            return Collections.emptyList();
        }
        List<AgentOrderAgainItemVO> againItemVOS = new ArrayList<>();
        for (AgentOrderItem agentOrderItem : agentOrderItems) {
            MarketItemInfoDTO itemInfoDTO = itemMap.get(agentOrderItem.getItemId());
            if (Objects.isNull(itemInfoDTO)) {
                continue;
            }
            MarketItemClassificationResp classificationResp = classificationMap.getOrDefault(itemInfoDTO.getMarketId(), new MarketItemClassificationResp());
            ProductCategoryDTO categoryDTO = categoryDTOMap.getOrDefault(itemInfoDTO.getCategoryId(), new ProductCategoryDTO());
            ItemSaleLimitConfigDTO itemSaleLimitConfigDTO = itemSaleLimitConfigMap.getOrDefault(agentOrderItem.getItemId(), ItemSaleLimitConfigDTO.DEFAULT());
            AgentOrderAgainItemVO againItemVO = convert2AgentItemVo(itemInfoDTO, classificationResp, categoryDTO, itemSaleLimitConfigDTO);
            againItemVO.setItemAmount(agentOrderItem.getItemQuantity());
            againItemVOS.add(againItemVO);
        }
        return againItemVOS;
    }

    public static AgentOrderAgainItemVO convert2AgentItemVo(MarketItemInfoDTO itemInfoDTO, MarketItemClassificationResp classificationResp, ProductCategoryDTO categoryDTO, ItemSaleLimitConfigDTO itemSaleLimitConfigDTO) {

        if (itemInfoDTO == null) {
            return null;
        }
        AgentOrderAgainItemVO agentOrderAgainItemVO = new AgentOrderAgainItemVO();
        agentOrderAgainItemVO.setItemId(itemInfoDTO.getItemId());
        agentOrderAgainItemVO.setSpuTitle(itemInfoDTO.getItemTitle());
        agentOrderAgainItemVO.setSpecification(itemInfoDTO.getSpecification());
        agentOrderAgainItemVO.setSpecificationUnit(itemInfoDTO.getSpecificationUnit());
        agentOrderAgainItemVO.setMainPicture(itemInfoDTO.getMainPicture());
        agentOrderAgainItemVO.setItemCode(itemInfoDTO.getItemCode());
        agentOrderAgainItemVO.setPriceRange(itemInfoDTO.getPriceStr());
        agentOrderAgainItemVO.setMinPrice(itemInfoDTO.getMinPrice());
        agentOrderAgainItemVO.setMaxPrice(itemInfoDTO.getMaxPrice());
        agentOrderAgainItemVO.setGoodsType(itemInfoDTO.getGoodsType());
        agentOrderAgainItemVO.setBrandName(itemInfoDTO.getBrandName());
        agentOrderAgainItemVO.setFirstClassificationId(classificationResp.getFirstClassificationId());
        agentOrderAgainItemVO.setFirstClassificationName(classificationResp.getFirstClassificationName());
        agentOrderAgainItemVO.setSecondClassificationId(classificationResp.getSecondClassificationId());
        agentOrderAgainItemVO.setSecondClassificationName(classificationResp.getSecondClassificationName());
        StringBuffer classificationName = new StringBuffer();
        if (StringUtils.isNotBlank(classificationResp.getFirstClassificationName())) {
            classificationName.append(classificationResp.getFirstClassificationName());
        }
        if (StringUtils.isNotBlank(classificationResp.getSecondClassificationName())) {
            classificationName.append(StringConstants.LEFT_SLASH).append(classificationResp.getSecondClassificationName());
        }
        agentOrderAgainItemVO.setMarketItemClassificationStr(classificationName.toString());
        agentOrderAgainItemVO.setFirstCategoryId(categoryDTO.getFirstCategoryId());
        agentOrderAgainItemVO.setFirstCategory(categoryDTO.getFirstCategoryName());
        agentOrderAgainItemVO.setSecondCategoryId(categoryDTO.getSecondCategoryId());
        agentOrderAgainItemVO.setSecondCategory(categoryDTO.getSecondCategoryName());
        agentOrderAgainItemVO.setThirdCategoryId(categoryDTO.getThirdCategoryId());
        agentOrderAgainItemVO.setThirdCategory(categoryDTO.getThirdCategoryName());
        agentOrderAgainItemVO.setCategoryStr(categoryDTO.getCategoryStr());

        agentOrderAgainItemVO.setMiniOrderQuantity(itemInfoDTO.getMiniOrderQuantity());
        agentOrderAgainItemVO.setBuyMultipleSwitch(itemInfoDTO.getBuyMultipleSwitch());
        agentOrderAgainItemVO.setBuyMultiple(itemInfoDTO.getBuyMultiple());
        agentOrderAgainItemVO.setItemSaleMode(itemInfoDTO.getItemSaleMode());
        agentOrderAgainItemVO.setSaleLimitRule(itemSaleLimitConfigDTO.getSaleLimitRule());
        agentOrderAgainItemVO.setSaleLimitQuantity(itemSaleLimitConfigDTO.getSaleLimitQuantity());
        return agentOrderAgainItemVO;
    }

    public static List<AgentOrderAgainStoreVO> convert2AgentStoreVos(List<AgentOrderStoreInfoDto> agentOrderStoreList, List<MerchantStoreResultResp> storeRespList) {
        if (CollectionUtils.isEmpty(agentOrderStoreList)) {
            return Collections.emptyList();
        }
        Map<Long, MerchantStoreResultResp> storeRespMap = storeRespList.stream().collect(Collectors.toMap(MerchantStoreResultResp::getId, Function.identity(), (k1, k2) -> k2));
        List<AgentOrderAgainStoreVO> againStoreVOList = new ArrayList<>();
        for (AgentOrderStoreInfoDto storeInfoDto : agentOrderStoreList) {
            MerchantStoreResultResp storeResp = storeRespMap.get(storeInfoDto.getStoreId());
            if (Objects.isNull(storeResp)) {
                continue;
            }
            AgentOrderAgainStoreVO againStoreVO = convert2AgentStoreVos(storeResp);
            againStoreVO.setPlanType(storeInfoDto.getPlanType());
            againStoreVOList.add(againStoreVO);
        }
        return againStoreVOList;
    }

    public static AgentOrderAgainStoreVO convert2AgentStoreVos(MerchantStoreResultResp storeResp) {
        if (storeResp == null) {
            return null;
        }
        AgentOrderAgainStoreVO agentOrderAgainStoreVO = new AgentOrderAgainStoreVO();
        agentOrderAgainStoreVO.setStoreId(storeResp.getId());
        agentOrderAgainStoreVO.setStoreNo(storeResp.getStoreNo());
        agentOrderAgainStoreVO.setStoreName(storeResp.getStoreName());
        agentOrderAgainStoreVO.setStoreType(storeResp.getType());
        agentOrderAgainStoreVO.setBillSwitch(storeResp.getBillSwitch());
        return agentOrderAgainStoreVO;
    }

    public static PlanOrderCancelInfoDto convert2CancelInfo(PlanOrderCancelDto cancelDto) {
        if (cancelDto == null) {
            return null;
        }
        PlanOrderCancelInfoDto planOrderCancelInfoDto = new PlanOrderCancelInfoDto();
        planOrderCancelInfoDto.setPlanOrderId(cancelDto.getPlanOrderId());
        planOrderCancelInfoDto.setTenantId(cancelDto.getTenantId());
        planOrderCancelInfoDto.setOperatorId(cancelDto.getOperatorId());
        planOrderCancelInfoDto.setOperatorSource(cancelDto.getOperatorSource());
        planOrderCancelInfoDto.setCancelRemark(cancelDto.getCancelRemark());
        return planOrderCancelInfoDto;
    }

    public static PlanOrderDetailItemVO convert2ItemSnapshot(MarketItemInfoDTO itemInfoDTO) {
        if (itemInfoDTO == null) {
            return null;
        }
        PlanOrderDetailItemVO planOrderDetailItemVO = new PlanOrderDetailItemVO();
        planOrderDetailItemVO.setItemId(itemInfoDTO.getItemId());
        planOrderDetailItemVO.setSpuTitle(itemInfoDTO.getItemTitle());
        planOrderDetailItemVO.setSpecification(itemInfoDTO.getSpecification());
        planOrderDetailItemVO.setSpecificationUnit(itemInfoDTO.getSpecificationUnit());
        planOrderDetailItemVO.setMainPicture(itemInfoDTO.getMainPicture());
        planOrderDetailItemVO.setItemCode(itemInfoDTO.getItemCode());
        planOrderDetailItemVO.setGoodsType(itemInfoDTO.getGoodsType());
        planOrderDetailItemVO.setSupplierName(itemInfoDTO.getSupplierName());
        planOrderDetailItemVO.setOnSale(itemInfoDTO.getOnSale());
        planOrderDetailItemVO.setMiniOrderQuantity(itemInfoDTO.getMiniOrderQuantity());
        planOrderDetailItemVO.setBuyMultiple(itemInfoDTO.getBuyMultiple());
        planOrderDetailItemVO.setDeleteFlag(itemInfoDTO.getDeleteFlag());

        return planOrderDetailItemVO;
    }


    public static PlanOrderQueryParam convert2PlanOrderQueryParam(PlanOrderListQueryInput queryInput) {
        if (queryInput == null) {
            return null;
        }
        PlanOrderQueryParam planOrderQueryParam = new PlanOrderQueryParam();
        planOrderQueryParam.setPageIndex(queryInput.getPageIndex());
        planOrderQueryParam.setPageSize(queryInput.getPageSize());
        planOrderQueryParam.setPlanOrderStatus(queryInput.getPlanOrderStatus());
        planOrderQueryParam.setAgentOrderNo(queryInput.getAgentOrderNo());
        planOrderQueryParam.setPlanOrderNo(queryInput.getPlanOrderNo());
        planOrderQueryParam.setOrderNo(queryInput.getOrderNo());
        planOrderQueryParam.setPlanType(queryInput.getPlanType());
        if (Objects.nonNull(queryInput.getItemId())) {
            planOrderQueryParam.setItemIds(Collections.singletonList(queryInput.getItemId()));
        }
        // 排序
        if (!CollectionUtils.isEmpty(queryInput.getSortList())) {
            PageSortInput sortInput = queryInput.getSortList().get(0);
            if (AgentOrderConstant.ORDER_FIELD_LIST.contains(sortInput.getSortBy()) && AgentOrderConstant.ORDER_SORT_LIST.contains(sortInput.getOrderBy())) {
                planOrderQueryParam.setOrderBy(AgentOrderConstant.ORDER_FIELD_MAP.get(sortInput.getSortBy()) + " " + sortInput.getOrderBy());
            }
        }
        // 时间字段处理
        if (Objects.nonNull(queryInput.getPlanCreateStartDate()) && Objects.nonNull(queryInput.getPlanCreateEndDate())) {
            planOrderQueryParam.setPlanCreateStartTime(DateUtil.startOfDay(queryInput.getPlanCreateStartDate()));
            planOrderQueryParam.setPlanCreateEndTime(DateUtil.endOfDay(queryInput.getPlanCreateEndDate()));
        }
        if (Objects.nonNull(queryInput.getPlanCancelStartDate()) && Objects.nonNull(queryInput.getPlanCancelEndDate())) {
            planOrderQueryParam.setPlanCancelStartTime(DateUtil.startOfDay(queryInput.getPlanCancelStartDate()));
            planOrderQueryParam.setPlanCancelEndTime(DateUtil.endOfDay(queryInput.getPlanCancelEndDate()));
        }
        if (Objects.nonNull(queryInput.getOrderCreateStartDate()) && Objects.nonNull(queryInput.getOrderCreateEndDate())) {
            planOrderQueryParam.setOrderCreateStartTime(DateUtil.startOfDay(queryInput.getOrderCreateStartDate()));
            planOrderQueryParam.setOrderCreateEndTime(DateUtil.endOfDay(queryInput.getOrderCreateEndDate()));
        }
        return planOrderQueryParam;
    }

    public static PlanOrderQueryListVO convert2PlanOrderPageList(PlanOrder planOrder) {
        if (planOrder == null) {
            return null;
        }
        PlanOrderQueryListVO planOrderQueryListVO = new PlanOrderQueryListVO();
        planOrderQueryListVO.setAgentOrderNo(planOrder.getAgentOrderNo());
        planOrderQueryListVO.setPlanOrderId(planOrder.getId());
        planOrderQueryListVO.setPlanOrderNo(planOrder.getPlanOrderNo());
        planOrderQueryListVO.setStoreId(planOrder.getStoreId());
        planOrderQueryListVO.setPlanOrderCreateTime(planOrder.getCreateTime());
        planOrderQueryListVO.setLastNotifyTime(planOrder.getPlanConfirmNotifyTime());
        planOrderQueryListVO.setPlanType(planOrder.getPlanType());
        return planOrderQueryListVO;
    }

    public static PlanOrderDetailVO convert2PlanOrderDetailVo(PlanOrder planOrder) {
        if (planOrder == null) {
            return null;
        }
        PlanOrderDetailVO planOrderDetailVO = new PlanOrderDetailVO();
        planOrderDetailVO.setPlanOrderId(planOrder.getId());
        planOrderDetailVO.setAgentOrderNo(planOrder.getAgentOrderNo());
        planOrderDetailVO.setPlanOrderNo(planOrder.getPlanOrderNo());
        planOrderDetailVO.setPlanOrderStatus(planOrder.getStatus());
        planOrderDetailVO.setRecommendReason(planOrder.getRecommendReason());
        planOrderDetailVO.setCancelReason(planOrder.getCancelRemark());
        planOrderDetailVO.setPlanOrderCreateTime(planOrder.getCreateTime());
        planOrderDetailVO.setAgentOperatorAuthId(planOrder.getCreatorUserId());
        planOrderDetailVO.setLastNotifyTime(planOrder.getPlanConfirmNotifyTime());
        planOrderDetailVO.setPlanType(planOrder.getPlanType());

        return planOrderDetailVO;
    }

    public static PlanOrderDetailItemVO convert2ItemVo(OrderItemVO vo) {
        if (vo == null) {
            return null;
        }
        PlanOrderDetailItemVO planOrderDetailItemVO = new PlanOrderDetailItemVO();
        planOrderDetailItemVO.setItemId(vo.getItemId());
        planOrderDetailItemVO.setSpuTitle(vo.getTitle());
        planOrderDetailItemVO.setSpecification(vo.getSpecification());
        planOrderDetailItemVO.setSpecificationUnit(vo.getSpecificationUnit());
        planOrderDetailItemVO.setMainPicture(vo.getMainPicture());
        planOrderDetailItemVO.setItemCode(vo.getItemCode());
        planOrderDetailItemVO.setGoodsType(vo.getGoodsType());
        planOrderDetailItemVO.setSupplierName(vo.getSupplierName());
        planOrderDetailItemVO.setItemPrice(vo.getPrice());
        planOrderDetailItemVO.setItemAmount(vo.getAmount());
        planOrderDetailItemVO.setItemTotalAmount(vo.getTotalPrice());
        return planOrderDetailItemVO;
    }
}
