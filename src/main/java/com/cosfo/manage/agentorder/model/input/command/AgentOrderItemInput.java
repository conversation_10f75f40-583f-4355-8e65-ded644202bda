package com.cosfo.manage.agentorder.model.input.command;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @author: monna.chen
 * @Date: 2024/2/5 15:08
 * @Description:
 */
@Data
public class AgentOrderItemInput implements Serializable {
    private static final long serialVersionUID = 936195741622865691L;

    /**
     * 商品ID
     */
    @NotNull(message = "商品列表-商品ID不可为空")
    private Long itemId;

    /**
     * 商品数量
     */
    @NotNull(message = "商品列表-商品数量不可为空")
    @Min(value = 1, message = "商品列表-商品数量不能小于1")
    @Max(value = 99999, message = "商品列表-商品数量不能大于99999")
    private Integer itemAmount;
}
