package com.cosfo.manage.agentorder.model.dto;

import com.cosfo.manage.common.context.AgentOrderEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 *
 *
 * @author: xiaowk
 * @date: 2024/2/27 上午11:42
 */
@Data
public class PlanOrderCreateOrderFailDto {


    /**
     * 订划单ID
     */
    private Long planOrderId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 下单类型
     */
    private AgentOrderEnum.PlanTypeEnum planTypeEnum;

    /**
     * 创建订单失败原因
     */
    private String failReason;

    /**
     * 商品快照
     */
    private String itemSnapshot;

    /**
     * 创建订单时间,
     * 计划单失败，创建了订单，为当前时间；未创建订单，为空
     *
     */
    private LocalDateTime orderTime;
}
