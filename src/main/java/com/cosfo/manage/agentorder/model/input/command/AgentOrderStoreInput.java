package com.cosfo.manage.agentorder.model.input.command;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @author: monna.chen
 * @Date: 2024/2/5 15:13
 * @Description:
 */
@Data
public class AgentOrderStoreInput implements Serializable {
    private static final long serialVersionUID = 7272445378007280079L;

    /**
     * 门店ID
     */
    @NotNull(message = "门店列表-门店ID不可为空")
    private Long storeId;

    /**
     * 门店编号
     */
    private String storeNo;

    /**
     * 下单方式
     */
    @NotNull(message = "门店列表-下单方式不可为空")
    private String planType;
}
