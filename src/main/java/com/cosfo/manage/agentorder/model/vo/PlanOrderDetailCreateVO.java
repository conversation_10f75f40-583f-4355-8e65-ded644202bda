package com.cosfo.manage.agentorder.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @author: monna.chen
 * @Date: 2024/2/5 17:42
 * @Description:
 */
@Data
public class PlanOrderDetailCreateVO implements Serializable {
    private static final long serialVersionUID = -2473901624060960178L;

    /**
     * 创建订单的角色
     */
    private String createOrderRole;

    /**
     * 门店操作人 -accountId
     */
    private Long storeOperatorAuthId;

    /**
     * 门店操作人 - 名称
     */
    private String storeOperatorAuthName;

    /**
     * 门店操作人-手机号
     */
    private String storeOperatorAuthPhone;

    /**
     * 创建订单数
     */
    private Integer createOrderCount;

    /**
     * 创建订单商品数（不同商品的个数）
     */
    private Integer createItemCount;

    /**
     * 创建订单商品件数（不同商品个数*每件商品下单件数）
     */
    private Integer createItemTotalAmount;

    /**
     * 下单失败的商品数 （不同商品的个数）
     */
    private Integer failItemCount;

    /**
     * 取消订单的角色
     */
    private String cancelOrderRole;

    /**
     * 订单取消时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cancelOrderTime;
}
