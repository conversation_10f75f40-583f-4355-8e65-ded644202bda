package com.cosfo.manage.agentorder.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *
 * @author: xiaowk
 * @date: 2024/10/16 下午6:12
 */
@Data
public class AgentOrderCheckVO implements Serializable {
    private static final long serialVersionUID = -5419740645272431244L;

    /**
     * 门店id
     */
    private Long storeId;


    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店校验失败原因。（门店校验失败后，不会校验商品）
     */
    private String storeCheckFailMsg;

    /**
     * 商品校验结果列表
     */
    private List<ItemCheckResult> itemCheckResultList;


    @Data
    public static class ItemCheckResult implements Serializable {

        private Long itemId;

        /**
         * marketId
         */
        private Long marketId;

        /**
         * 商品数量
         */
        private Integer itemQuantity;

        /**
         * 检查成功状态 true-检查通过 false-检查失败
         */
        private boolean checkFlag;

        /**
         * 检查失败原因
         */
        private String failMsg;
    }
}
