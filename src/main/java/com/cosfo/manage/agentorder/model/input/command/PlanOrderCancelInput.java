package com.cosfo.manage.agentorder.model.input.command;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @author: monna.chen
 * @Date: 2024/2/5 17:01
 * @Description:
 */
@Data
public class PlanOrderCancelInput implements Serializable {
    private static final long serialVersionUID = -2618931869441687347L;

    /**
     * 计划单ID
     */
    @NotNull(message = "计划单ID不可为空")
    private Long planOrderId;

    /**
     * 取消原因
     */
    @NotNull(message = "取消原因不可为空")
    @Length(max = 255, message = "取消原因最长255个字符")
    private String cancelRemark;
}
