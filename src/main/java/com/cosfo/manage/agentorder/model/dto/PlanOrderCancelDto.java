package com.cosfo.manage.agentorder.model.dto;

import com.cosfo.manage.common.context.AgentOrderEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: monna.chen
 * @Date: 2024/2/22 11:18
 * @Description:
 */
@Data
@Slf4j
public class PlanOrderCancelDto {

    /**
     * 订划单ID
     */
    private Long planOrderId;

    /**
     * 计划单单号
     */
    private String planOrderNo;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 操作人ID -
     * 来源为租户品牌方时 = authUserId
     * 来源为门店       = accountID
     */
    private Long operatorId;

    /**
     * 来源
     */
    private AgentOrderEnum.CancelOrderRoleEnum operatorSource;

    /**
     * 取消原因
     */
    private String cancelRemark;
}
