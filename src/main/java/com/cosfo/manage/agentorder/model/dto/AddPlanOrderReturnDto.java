package com.cosfo.manage.agentorder.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2024/2/28 11:41
 * @Description: 创建计划单返回
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AddPlanOrderReturnDto {

    /**
     * 代下单号
     */
    private String agentOrderNo;

    /**
     * 计划单号列表
     */
    private List<Long> planOrderIds;

}
