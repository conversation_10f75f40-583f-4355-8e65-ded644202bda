package com.cosfo.manage.agentorder.model.input.query;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * @author: monna.chen
 * @Date: 2024/2/4 17:45
 * @Description:
 */
@Data
public class PlanOrderListQueryInput extends BasePageInput implements Serializable {
    private static final long serialVersionUID = 1818576460513550074L;


    /**
     * 计划单状态 100-待门店确认; 105-下单中; 200-下单成功; 300-下单失败; 400-已取消
     */
    @NotNull(message = "计划单状态不可为空")
    private Integer planOrderStatus;

    /**
     * 代下单编号
     */
    private String agentOrderNo;

    /**
     * 计划单编号
     */
    private String planOrderNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 代下单人手机号
     */
    private String agentOperatorPhone;

    /**
     * 门店编号
     */
    private String storeNo;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 店长手机号
     */
    private String storeManagerPhone;

    /**
     * 商品编码
     */
    private Long itemId;

    /**
     * 计划单创建开始时间
     */
    @JSONField(format = "yyyy-MM-dd")
    private LocalDate planCreateStartDate;

    /**
     * 计划单创建结束时间
     */
    @JSONField(format = "yyyy-MM-dd")
    private LocalDate planCreateEndDate;

    /**
     * 订单创建开始时间
     */
    @JSONField(format = "yyyy-MM-dd")
    private LocalDate orderCreateStartDate;

    /**
     * 订单创建结束时间
     */
    @JSONField(format = "yyyy-MM-dd")
    private LocalDate orderCreateEndDate;

    /**
     * 计划单取消开始时间
     */
    @JSONField(format = "yyyy-MM-dd")
    private LocalDate planCancelStartDate;

    /**
     * 计划单取消结束时间
     */
    @JSONField(format = "yyyy-MM-dd")
    private LocalDate planCancelEndDate;

    /**
     * 计划下单方式 create_plan_order-生成计划单(配货单) create_order-创建订单 create_force_plan_order-计划单强制下单(铺货单)
     */
    private String planType;

}
