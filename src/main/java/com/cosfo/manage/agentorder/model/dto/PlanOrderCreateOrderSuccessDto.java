package com.cosfo.manage.agentorder.model.dto;

import com.cosfo.manage.common.context.AgentOrderEnum;
import lombok.Data;

import java.util.List;

/**
 *
 *
 * @author: xiaowk
 * @date: 2024/2/27 上午11:42
 */
@Data
public class PlanOrderCreateOrderSuccessDto {


    /**
     * 订划单ID
     */
    private Long planOrderId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 下单类型
     */
    private AgentOrderEnum.PlanTypeEnum planTypeEnum;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 创建订单成功订单编号
     */
    private List<String> orderNoList;

    /**
     * 商品快照
     */
    private String itemSnapshot;
}
