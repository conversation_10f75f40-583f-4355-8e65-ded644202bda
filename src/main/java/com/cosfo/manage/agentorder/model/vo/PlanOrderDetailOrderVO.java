package com.cosfo.manage.agentorder.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2024/2/5 17:26
 * @Description:
 */
@Data
public class PlanOrderDetailOrderVO implements Serializable {
    private static final long serialVersionUID = -772042584422005150L;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单创建时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime orderCreateTime;

    /**
     * 订单支付时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime orderPayTime;

    /**
     * 店长手机号
     */
    private String storeManagerPhone;

    /**
     * 失败原因 可能为空
     */
    private String failReason;

    /**
     * 订单状态的标签 成功:SUCCESS 失败 FAIL
     */
    private String orderStatus;

    /**
     * 订单具体状态的枚举，同订单管理状态
     * 原状态枚举，未经转换
     */
    private Integer orderStatusOriEnum;

    /**
     * 订单具体状态的枚举，同订单管理状态
     */
    private Integer orderStatusEnum;

    /**
     * 订单具体状态，同订单管理状态
     */
    private String orderStatusStr;

    /**
     * 订单的商品列表
     */
    private List<PlanOrderDetailItemVO> itemVOList;
}
