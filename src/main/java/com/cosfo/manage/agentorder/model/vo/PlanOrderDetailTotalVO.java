package com.cosfo.manage.agentorder.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2024/2/5 17:39
 * @Description:
 */
@Data
public class PlanOrderDetailTotalVO implements Serializable {
    private static final long serialVersionUID = 3889074716325313192L;

    /**
     * 代下单详情
     */
    private PlanOrderDetailVO planOrderDetailVO;

    /**
     * 门店详情
     */
    private PlanOrderDetailStoreVO planOrderDetailStoreVO;

    /**
     * 代下单信息 （与创建订单相关）
     */
    private PlanOrderDetailCreateVO planOrderDetailCreateVO;

    /**
     * 下单成功的金额，包含商品价格与运费
     */
    private BigDecimal orderTotalPrice;

    /**
     * 已生成订单的订单列表，各状态通用
     */
    private List<PlanOrderDetailOrderVO> orderDetailList;

    /**
     * 未成订单的商品金额，不含运费
     */
    private BigDecimal unOrderItemTotalPrice;

    /**
     * 商品列表,各状态通用
     */
    private List<PlanOrderDetailItemVO> allItemList;
}
