package com.cosfo.manage.agentorder.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2024/2/5 15:53
 * @Description: 代下单/计划单 - 再来一单的详情页
 */
@Data
public class AgentOrderAgainDetailVO implements Serializable {
    private static final long serialVersionUID = -5419740645272431244L;

    /**
     * 推荐理由
     */
    private String recommendReason;

    /**
     * 商品列表
     */
    private List<AgentOrderAgainItemVO> itemVOList;

    /**
     * 门店列表
     */
    private List<AgentOrderAgainStoreVO> storeVOList;
}
