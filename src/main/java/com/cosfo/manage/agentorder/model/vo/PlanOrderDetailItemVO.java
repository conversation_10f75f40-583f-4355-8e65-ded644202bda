package com.cosfo.manage.agentorder.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: monna.chen
 * @Date: 2024/2/5 17:19
 * @Description:
 */
@Data
public class PlanOrderDetailItemVO implements Serializable {
    private static final long serialVersionUID = 8547079556119797133L;

    /**
     * 商品编码
     */
    private Long itemId;

    /**
     * 商品标题
     */
    private String spuTitle;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 商品图片
     */
    private String mainPicture;

    /**
     * 自有编码
     */
    private String itemCode;

    /**
     * 商品类型
     */
    private Integer goodsType;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 商品单价
     */
    private BigDecimal itemPrice;

    /**
     * 数量
     */
    private Integer itemAmount;

    /**
     * 商品总价
     */
    private BigDecimal itemTotalAmount;

    /**
     * 门店端可见状态,可能为空
     */
    private String storeShowStatus;

    /**
     * 下单结果
     */
    private String orderResult;

    /**
     * 上下架状态
     */
    private Integer onSale;

    /**
     * 库存
     */
    private Integer stockAmount;
    /**
     * 最小起订量 - 与库存比较
     */
    private Integer miniOrderQuantity;
    /**
     * 倍数订货 倍数值 - 与库存比较
     */
    private Integer buyMultiple;

    /**
     * 删除标识 0、已删除 1、正在使用
     */
    private Integer deleteFlag;

}
