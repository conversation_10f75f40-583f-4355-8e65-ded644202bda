package com.cosfo.manage.agentorder.model.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * @author: monna.chen
 * @Date: 2024/2/5 16:24
 * @Description:
 */
@Data
public class AgentOrderAgainStoreVO implements Serializable {
    private static final long serialVersionUID = -5426831365826796924L;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 门店编号
     */
    private String storeNo;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店类型
     */
    private Integer storeType;

    /**
     * 账期权限
     */
    private Integer billSwitch;

    /**
     * 下单方式 CREATE_PLAN_ORDER-创建计划单  CREATE_ORDER-创建订单
     */
    private String planType;

}
