package com.cosfo.manage.product.convert;

import com.cosfo.erp.client.category.req.CategoryQueryReq;
import com.cosfo.erp.client.category.resp.CategoryDetailResultResp;
import com.cosfo.erp.client.category.resp.CategoryLevelResultResp;
import com.cosfo.manage.facade.dto.CategoryDTO;
import com.cosfo.manage.product.model.dto.ProductCategoryDTO;
import com.cosfo.manage.product.model.dto.ProductCategoryTreeDTO;
import com.cosfo.manage.product.model.po.ProductCategory;
import com.cosfo.manage.product.model.vo.CategoryVO;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date : 2023/1/30 17:10
 */
public class CategoryConverter {

    public static ProductCategoryTreeDTO toCategoryTreeDTO(CategoryDetailResultResp cateGoryDetailResultResp) {

        if (cateGoryDetailResultResp == null) {
            return null;
        }
        ProductCategoryTreeDTO productCategoryTreeDTO = new ProductCategoryTreeDTO();
        productCategoryTreeDTO.setId(cateGoryDetailResultResp.getId());
        productCategoryTreeDTO.setName(cateGoryDetailResultResp.getName());
        productCategoryTreeDTO.setParentId(cateGoryDetailResultResp.getParentId());
// Not mapped TO fields:
// childList
// Not mapped FROM fields:
// check
// cateGoryDetailResultRespList
        if (!CollectionUtils.isEmpty(cateGoryDetailResultResp.getCategoryDetailResultRespList())){
            List<ProductCategoryTreeDTO> categoryTreeDTOList = cateGoryDetailResultResp.getCategoryDetailResultRespList().stream().map(CategoryConverter::toCategoryTreeDTO).collect(Collectors.toList());
            productCategoryTreeDTO.setChildList(categoryTreeDTOList);
        }
        return productCategoryTreeDTO;
    }


    public static List<CategoryVO> cateResultRespList2CateGoryVO(List<CategoryDetailResultResp> resultRespList){

        if (resultRespList == null) {
            return Collections.emptyList();
        }
        List<CategoryVO> categoryVOList = new ArrayList<>();
        for (CategoryDetailResultResp cateGoryDetailResultResp : resultRespList) {
            categoryVOList.add(toCategoryVO(cateGoryDetailResultResp));
        }
        return categoryVOList;
    }

    public static CategoryVO toCategoryVO(CategoryDetailResultResp cateGoryDetailResultResp) {
        if (cateGoryDetailResultResp == null) {
            return null;
        }
        CategoryVO categoryVO = new CategoryVO();
        categoryVO.setId(cateGoryDetailResultResp.getId());
        categoryVO.setName(cateGoryDetailResultResp.getName());
        categoryVO.setParentId(cateGoryDetailResultResp.getParentId());
        categoryVO.setCheck(cateGoryDetailResultResp.getCheck());
// Not mapped TO fields:
// categoryVOS
// Not mapped FROM fields:
// cateGoryDetailResultRespList
        if (!CollectionUtils.isEmpty(cateGoryDetailResultResp.getCategoryDetailResultRespList())){
            List<CategoryVO> categoryVOList = cateResultRespList2CateGoryVO(cateGoryDetailResultResp.getCategoryDetailResultRespList());
            categoryVO.setCategoryVOS(categoryVOList);
        }
        return categoryVO;
    }

    public static ProductCategoryDTO toProductCategoryDTO(CategoryLevelResultResp cateGoryLevelResultResp){

        if (cateGoryLevelResultResp == null) {
            return null;
        }
        ProductCategoryDTO productCategoryDTO = new ProductCategoryDTO();
        productCategoryDTO.setFirstCategoryId(cateGoryLevelResultResp.getFirstCategoryId());
        productCategoryDTO.setFirstCategoryName(cateGoryLevelResultResp.getFirstCategoryName());
        productCategoryDTO.setSecondCategoryId(cateGoryLevelResultResp.getSecondCategoryId());
        productCategoryDTO.setSecondCategoryName(cateGoryLevelResultResp.getSecondCategoryName());
        productCategoryDTO.setThirdCategoryId(cateGoryLevelResultResp.getThirdCategoryId());
        productCategoryDTO.setThirdCategoryName(cateGoryLevelResultResp.getThirdCategoryName());
        productCategoryDTO.setCategoryStr(cateGoryLevelResultResp.getCategoryStr());
        return productCategoryDTO;
    }

    public static ProductCategory toProductCategory(CategoryDetailResultResp cateGoryDetailResultResp){

        if (cateGoryDetailResultResp == null) {
            return null;
        }
        ProductCategory productCategory = new ProductCategory();
        productCategory.setId(cateGoryDetailResultResp.getId());
        productCategory.setName(cateGoryDetailResultResp.getName());
        productCategory.setParentId(cateGoryDetailResultResp.getParentId());
        productCategory.setCreateTime(cateGoryDetailResultResp.getCreateTime());
        productCategory.setUpdateTime(cateGoryDetailResultResp.getUpdateTime());
// Not mapped FROM fields:
// check
// cateGoryDetailResultRespList
        return productCategory;
    }

    /**
     * 转化为CategoryQueryReq
     *
     * @param categoryDTO
     * @return
     */
    public static CategoryQueryReq convertToCategoryQueryReq(CategoryDTO categoryDTO){

        if (categoryDTO == null) {
            return null;
        }
        CategoryQueryReq categoryQueryReq = new CategoryQueryReq();
        categoryQueryReq.setId(Long.valueOf(categoryDTO.getId()));
        categoryQueryReq.setName(categoryDTO.getCategory());
        if(Objects.nonNull(categoryQueryReq.getParentId())) {
            categoryQueryReq.setParentId(Long.valueOf(categoryQueryReq.getParentId()));
        }else {
            categoryQueryReq.setParentId(0L);
        }

        return categoryQueryReq;
    }
}
