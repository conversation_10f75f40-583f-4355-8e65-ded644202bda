package com.cosfo.manage.product.convert;

import com.cosfo.manage.client.product.req.SummerFarmSynchronizedSkuReq;
import com.cosfo.manage.client.product.resp.SummerFarmSynchronizedSkuResp;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.common.context.AgentTypeEnum;
import com.cosfo.manage.common.context.ApproveStatusEnum;
import com.cosfo.manage.common.context.SkuUseFlagEnum;
import com.cosfo.manage.good.model.dto.ProductSkuExcelDataInput;
import com.cosfo.manage.product.model.dto.*;
import com.cosfo.manage.product.model.po.ProductAgentApplication;
import com.cosfo.manage.product.model.po.ProductAgentApplicationItem;
import com.cosfo.manage.product.model.po.ProductSku;
import com.cosfo.manage.product.model.po.ProductSpu;
import com.cosfo.manage.product.model.vo.*;
import net.xianmu.common.exception.BizException;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 商品域转换器
 * <AUTHOR>
 * @date 2022/11/15 21:33
 */
public class ProductConverter {

    private ProductConverter () {
    }

    /**
     * 转换为DTO对象
     * @param item
     * @return
     */
    public static ProductAgentApplicationDTO convertToProductAgentApplicationDTO(ProductAgentApplication item) {
        if (item == null) {
            return null;
        }
        ProductAgentApplicationDTO result = new ProductAgentApplicationDTO();
        result.setId(item.getId());
        result.setCategoryId(item.getCategoryId());
        result.setTitle(item.getTitle());
        result.setStorageTemperature(item.getStorageTemperature());
        result.setStorageLocation(item.getStorageLocation());
        result.setGuaranteePeriod(item.getGuaranteePeriod());
        result.setGuaranteeUnit(item.getGuaranteeUnit());
        result.setSupplierSpuId(item.getSupplierSpuId());
        return result;
    }

    /**
     * 转换为DTO
     * @param item
     * @return
     */
    public static ProductAgentApplicationQueryDTO convertToProductAgentApplicationQueryDTO(ProductAgentApplicationQueryVO item) {
        if (item == null) {
            return null;
        }
        ProductAgentApplicationQueryDTO result = new ProductAgentApplicationQueryDTO();
        result.setTitle(item.getTitle());
        result.setCategoryIds(item.getCategoryIds());
        result.setStatus(item.getStatus());
        result.setPageIndex(item.getPageIndex());
        result.setPageSize(item.getPageSize());
        return result;
    }

    /**
     * 转换成VO对象
     * @param item
     * @return
     */
    public static ProductAgentApplicationVO convertToProductAgentApplicationVO(ProductAgentApplicationDTO item) {
        if (item == null) {
            return null;
        }
        ProductAgentApplicationVO result = new ProductAgentApplicationVO();
        result.setId(item.getId());
        result.setTitle(item.getTitle());
        result.setCategoryId(item.getCategoryId());
        result.setCategoryStr(item.getCategoryStr());
        result.setStorageLocation(item.getStorageLocation());
        result.setStorageTemperature(item.getStorageTemperature());
        result.setGuaranteePeriod(item.getGuaranteePeriod());
        result.setGuaranteeUnit(item.getGuaranteeUnit());
        result.setItemList(item.getItemList());
        return result;
    }

    /**
     * 转换成DTO对象
     * @param item
     * @return
     */
    public static ProductAgentApplicationDTO convertToProductAgentApplicationDTO(ProductAgentApplicationVO item) {
        if (item == null) {
            return null;
        }
        ProductAgentApplicationDTO result = new ProductAgentApplicationDTO();
        result.setId(item.getId());
        result.setTitle(item.getTitle());
        result.setStorageLocation(item.getStorageLocation());
        result.setCategoryId(item.getCategoryId());
        result.setStorageTemperature(item.getStorageTemperature());
        result.setGuaranteeUnit(item.getGuaranteeUnit());
        result.setGuaranteePeriod(item.getGuaranteePeriod());
        result.setItemList(item.getItemList());
        return result;
    }

    /**
     * 转换为PO对象
     * @param item
     * @return
     */
    public static ProductAgentApplication convertToProductAgentApplication(ProductAgentApplicationDTO item) {
        if (item == null) {
            return null;
        }
        ProductAgentApplication result = new ProductAgentApplication();
        result.setId(item.getId());
        result.setTenantId(item.getTenantId());
        result.setTitle(item.getTitle());
        result.setCategoryId(item.getCategoryId());
        result.setStorageLocation(item.getStorageLocation());
        result.setStorageTemperature(item.getStorageTemperature());
        result.setGuaranteePeriod(item.getGuaranteePeriod());
        result.setGuaranteeUnit(item.getGuaranteeUnit());
        result.setSupplierSpuId(item.getSupplierSkuId());
        result.setCreateTime(item.getCreateTime());
        result.setUpdateTime(item.getUpdateTime());
        return result;
    }

    /**
     * 转换为 DTO
     * @param item
     * @return
     */
    public static ProductAgentApplicationItemDTO convertToProductAgentApplicationItemDTO(ProductAgentApplicationItemVO item) {
        if (item == null) {
            return null;
        }
        ProductAgentApplicationItemDTO result = new ProductAgentApplicationItemDTO();
        result.setId(item.getId());
        result.setApplicationId(item.getApplicationId());
        result.setSpecification(item.getSpecification());
        result.setSpecificationUnit(item.getSpecificationUnit());
        result.setWeightNum(item.getWeightNum());
        result.setDomesticFlag(item.getDomesticFlag());
        result.setStatus(item.getStatus());
        result.setRefuseReason(item.getRefuseReason());
        result.setSupplierSkuId(item.getSupplierSkuId());
        result.setAuditTime(item.getAuditTime());
        result.setVolume(item.getVolume());
        return result;
    }

    /**
     * 转换为 PO
     * @param item
     * @return
     */
    public static ProductAgentApplicationItem convertToProductAgentApplicationItem(ProductAgentApplicationItemDTO item) {
        if (item == null) {
            return null;
        }
        ProductAgentApplicationItem result = new ProductAgentApplicationItem();
        result.setId(item.getId());
        result.setTenantId(item.getTenantId());
        result.setApplicationId(item.getApplicationId());
        result.setSpecification(item.getSpecification());
        result.setSpecificationUnit(item.getSpecificationUnit());
        result.setDomesticFlag(item.getDomesticFlag());
        result.setWeightNum(item.getWeightNum());
        result.setStatus(item.getStatus());
        result.setRefuseReason(item.getRefuseReason());
        result.setSupplierSkuId(item.getSupplierSkuId());
        result.setAuditTime(item.getAuditTime());
        result.setVolume(item.getVolume());
        return result;
    }

    /**
     * 转化为VO
     *
     * @param productSkuDto
     * @return
     */
    public static ProductSkuDetailVO convertToProductSkuVO(ProductSkuDTO productSkuDto){
        if(productSkuDto == null){
            return null;
        }

        ProductSkuDetailVO productSkuVo = new ProductSkuDetailVO();
        productSkuVo.setId(productSkuDto.getId());
        productSkuVo.setSupplySkuId(productSkuDto.getId());
        productSkuVo.setSku(productSkuDto.getSku());
        productSkuVo.setTenantId(productSkuDto.getTenantId());
        productSkuVo.setSpuId(productSkuDto.getSpuId());
        productSkuVo.setSpecification(productSkuDto.getSpecification());
        productSkuVo.setSpecificationUnit(productSkuDto.getSpecificationUnit());
        productSkuVo.setPrice(productSkuDto.getPrice());
        productSkuVo.setPriceStr(productSkuDto.getPriceStr());
        productSkuVo.setBrandName(productSkuDto.getBrandName());
        productSkuVo.setTitle(productSkuDto.getTitle());
        productSkuVo.setMainPicture(productSkuDto.getMainPicture());
        productSkuVo.setStartTime(productSkuDto.getStartTime());
        productSkuVo.setEndTime(productSkuDto.getEndTime());
        productSkuVo.setThirdCategory(productSkuDto.getThirdCategory());
        productSkuVo.setSecondaryCategory(productSkuDto.getSecondCategory());
        productSkuVo.setCategory(productSkuDto.getFirstCategory());
//        productSkuVo.setBrandId(productSkuDto.getBrandId());
        productSkuVo.setBrandName(productSkuDto.getBrandName());
        productSkuVo.setCityNum(productSkuDto.getCityNum());
        productSkuVo.setMinPrice(productSkuDto.getMinPrice());
        productSkuVo.setMaxPrice(productSkuDto.getMaxPrice());
        productSkuVo.setOrigin(productSkuDto.getOrigin());
        productSkuVo.setStorageLocation(productSkuDto.getStorageLocation());
        productSkuVo.setStorageTemperature(productSkuDto.getStorageTemperature());
        productSkuVo.setGuaranteePeriod(productSkuDto.getGuaranteePeriod());
        productSkuVo.setGuaranteeUnit(productSkuDto.getGuaranteeUnit());
        productSkuVo.setEnabledTotalQuantity(productSkuDto.getEnabledTotalQuantity());
        productSkuVo.setProductAgentWarehouseDataVOS(productSkuDto.getProductAgentWarehouseDataVOS());
        productSkuVo.setWarehouseDataVos(productSkuDto.getWarehouseDataVos());
        productSkuVo.setProductSupplyPriceId(productSkuDto.getProductSupplyPriceId());
        productSkuVo.setAgentStatus(productSkuDto.getAgentStatus());
        return productSkuVo;
    }

    /**
     * 转化为ProductSpu
     *
     * @param productSkuExcelDataInput
     * @return
     */
    public static ProductSpu convertToProductSpu(ProductSkuExcelDataInput productSkuExcelDataInput, Long tenantId){
        if (productSkuExcelDataInput == null) {
            return null;
        }

        ProductSpu productSpu = new ProductSpu();
        productSpu.setTenantId(tenantId);
        productSpu.setCategoryId(productSkuExcelDataInput.getCategoryId());
        productSpu.setTitle(productSkuExcelDataInput.getTitle());
        productSpu.setStorageLocation(productSkuExcelDataInput.getStorageLocation());
        productSpu.setStorageTemperature(productSkuExcelDataInput.getStorageTemperature());
        productSpu.setGuaranteePeriod(Integer.valueOf(productSkuExcelDataInput.getGuaranteePeriod()));
        productSpu.setGuaranteeUnit(productSkuExcelDataInput.getGuaranteeUnit());
        productSpu.setOrigin(productSkuExcelDataInput.getOrigin());
        productSpu.setBrandName(productSkuExcelDataInput.getBrandName());
        return productSpu;
    }

    /**
     * 转化为ProductSku
     *
     * @param productSkuExcelDataInput
     * @param tenantId
     * @return
     */
    public static ProductSku convertToProductSku(ProductSkuExcelDataInput productSkuExcelDataInput, Long tenantId){
        if (productSkuExcelDataInput == null) {
            return null;
        }

        ProductSku productSku = new ProductSku();
        productSku.setTenantId(tenantId);
        productSku.setSpecification(productSkuExcelDataInput.getSpecification());
        productSku.setSpecificationUnit(productSkuExcelDataInput.getSpecificationUnit());
        productSku.setSpecificationType(productSkuExcelDataInput.getSpecificationType());
        productSku.setApproveStatus(ApproveStatusEnum.PASS.getCode());
        productSku.setPlaceType(productSkuExcelDataInput.getPlaceType());
        if (Objects.nonNull(productSkuExcelDataInput.getLength()) && Objects.nonNull(productSkuExcelDataInput.getWidth()) && Objects.nonNull(productSkuExcelDataInput.getHeight())) {
            String length = String.valueOf(new BigDecimal(productSkuExcelDataInput.getLength()).divide(new BigDecimal(100)));
            String width = String.valueOf(new BigDecimal(productSkuExcelDataInput.getWidth()).divide(new BigDecimal(100)));
            String height = String.valueOf(new BigDecimal(productSkuExcelDataInput.getHeight()).divide(new BigDecimal(100)));
            productSku.setVolume(length + StringConstants.STARS + width + StringConstants.STARS + height);
        }

        if(Objects.nonNull(productSkuExcelDataInput.getVolumeUnit())) {
            productSku.setVolumeUnit(productSkuExcelDataInput.getVolumeUnit());
        }

        if(Objects.nonNull(productSkuExcelDataInput.getWeight())) {
            productSku.setWeight(Double.valueOf(productSkuExcelDataInput.getWeight()));
        }
        AgentTypeEnum agentTypeEnum = AgentTypeEnum.getByValue(productSkuExcelDataInput.getAgentApplication());
        if (Objects.isNull(agentTypeEnum)){
            throw new BizException("是否代仓类型错误！");
        }
        productSku.setAgentType(AgentTypeEnum.NO.getCode());
        productSku.setCustomSkuCode(productSkuExcelDataInput.getCustomSkuCode());
        return productSku;
    }

    /**
     * 转换为SummerFarmSynchronizedSkuDTO
     *
     * @param summerFarmSynchronizedSkuReq
     * @return
     */
    public static SummerFarmSynchronizedSkuDTO convertToSummerFarmSynchronized(SummerFarmSynchronizedSkuReq summerFarmSynchronizedSkuReq){

        if (summerFarmSynchronizedSkuReq == null) {
            return null;
        }
        SummerFarmSynchronizedSkuDTO summerFarmSynchronizedSkuDTO = new SummerFarmSynchronizedSkuDTO();
        summerFarmSynchronizedSkuDTO.setSpuId(summerFarmSynchronizedSkuReq.getSpuId());
        summerFarmSynchronizedSkuDTO.setTitle(summerFarmSynchronizedSkuReq.getTitle());
        summerFarmSynchronizedSkuDTO.setFirstCategoryId(summerFarmSynchronizedSkuReq.getFirstCategoryId());
        summerFarmSynchronizedSkuDTO.setFirstCategoryName(summerFarmSynchronizedSkuReq.getFirstCategoryName());
        summerFarmSynchronizedSkuDTO.setSecondCategoryId(summerFarmSynchronizedSkuReq.getSecondCategoryId());
        summerFarmSynchronizedSkuDTO.setSecondCategoryName(summerFarmSynchronizedSkuReq.getSecondCategoryName());
        summerFarmSynchronizedSkuDTO.setThirdCategoryId(summerFarmSynchronizedSkuReq.getThirdCategoryId());
        summerFarmSynchronizedSkuDTO.setThirdCategoryName(summerFarmSynchronizedSkuReq.getThirdCategoryName());
        summerFarmSynchronizedSkuDTO.setSubTitle(summerFarmSynchronizedSkuReq.getSubTitle());
        summerFarmSynchronizedSkuDTO.setMainPicture(summerFarmSynchronizedSkuReq.getMainPicture());
        summerFarmSynchronizedSkuDTO.setDetailPicture(summerFarmSynchronizedSkuReq.getDetailPicture());
        summerFarmSynchronizedSkuDTO.setStorageLocation(summerFarmSynchronizedSkuReq.getStorageLocation());
        summerFarmSynchronizedSkuDTO.setGuaranteePeriod(summerFarmSynchronizedSkuReq.getGuaranteePeriod());
        summerFarmSynchronizedSkuDTO.setGuaranteeUnit(summerFarmSynchronizedSkuReq.getGuaranteeUnit());
        summerFarmSynchronizedSkuDTO.setOrigin(summerFarmSynchronizedSkuReq.getOrigin());
        summerFarmSynchronizedSkuDTO.setSkuId(summerFarmSynchronizedSkuReq.getSkuId());
        summerFarmSynchronizedSkuDTO.setSku(summerFarmSynchronizedSkuReq.getSku());
        summerFarmSynchronizedSkuDTO.setSpecification(summerFarmSynchronizedSkuReq.getSpecification());
        summerFarmSynchronizedSkuDTO.setSpecificationUnit(summerFarmSynchronizedSkuReq.getSpecificationUnit());
        summerFarmSynchronizedSkuDTO.setAdminId(summerFarmSynchronizedSkuReq.getAdminId());
        summerFarmSynchronizedSkuDTO.setType(summerFarmSynchronizedSkuReq.getType());
        summerFarmSynchronizedSkuDTO.setBrandName(summerFarmSynchronizedSkuReq.getBrandName());
        summerFarmSynchronizedSkuDTO.setStorageTemperature(summerFarmSynchronizedSkuReq.getStorageTemperature());
        summerFarmSynchronizedSkuDTO.setSkuIds(summerFarmSynchronizedSkuReq.getSkuIds());
        summerFarmSynchronizedSkuDTO.setWeightNum(summerFarmSynchronizedSkuReq.getWeightNum());
        summerFarmSynchronizedSkuDTO.setVolume(summerFarmSynchronizedSkuReq.getVolume());
        summerFarmSynchronizedSkuDTO.setSpu(summerFarmSynchronizedSkuReq.getSpu());
        summerFarmSynchronizedSkuDTO.setSkuPicture(summerFarmSynchronizedSkuReq.getSkuPicture());
        summerFarmSynchronizedSkuDTO.setSkuTitle(summerFarmSynchronizedSkuReq.getSkuTitle());
        summerFarmSynchronizedSkuDTO.setCreator(summerFarmSynchronizedSkuReq.getCreator());
        summerFarmSynchronizedSkuDTO.setCreateType(summerFarmSynchronizedSkuReq.getCreateType());
        summerFarmSynchronizedSkuDTO.setUseFlag(SkuUseFlagEnum.convertByXmOutdated(summerFarmSynchronizedSkuReq.getOutdated()));
        summerFarmSynchronizedSkuDTO.setSubAgentType(summerFarmSynchronizedSkuReq.getSubType());
        summerFarmSynchronizedSkuDTO.setPlaceType(summerFarmSynchronizedSkuReq.getPlaceType());
        return summerFarmSynchronizedSkuDTO;
    }

    /**
     * 转化为SummerFarmSynchronizedSkuResp
     *
     * @param summerFarmSynchronizedSkuVo
     * @return
     */
    public static SummerFarmSynchronizedSkuResp convertToSummerFarmSynchronizedSkuResp(SummerFarmSynchronizedSkuVO summerFarmSynchronizedSkuVo){
        if (summerFarmSynchronizedSkuVo == null) {
            return null;
        }
        SummerFarmSynchronizedSkuResp summerFarmSynchronizedSkuResp = new SummerFarmSynchronizedSkuResp();
        summerFarmSynchronizedSkuResp.setSkuId(summerFarmSynchronizedSkuVo.getSkuId());
        summerFarmSynchronizedSkuResp.setSku(summerFarmSynchronizedSkuVo.getSku());
        summerFarmSynchronizedSkuResp.setInvId(summerFarmSynchronizedSkuVo.getInvId());
        return summerFarmSynchronizedSkuResp;
    }

    public static SummerFarmSynchronizedSkuDTO convertSummerFarmSynchronizedSkuDTO(net.summerfarm.manage.client.saas.resp.SummerFarmSynchronizedSkuResp summerFarmSynchronizedSkuResp){
        if (summerFarmSynchronizedSkuResp == null) {
            return null;
        }

        SummerFarmSynchronizedSkuDTO summerFarmSynchronizedSkuDTO = new SummerFarmSynchronizedSkuDTO();
        summerFarmSynchronizedSkuDTO.setSpuId(summerFarmSynchronizedSkuResp.getSpuId());
        summerFarmSynchronizedSkuDTO.setTitle(summerFarmSynchronizedSkuResp.getTitle());
        summerFarmSynchronizedSkuDTO.setFirstCategoryId(summerFarmSynchronizedSkuResp.getFirstCategoryId());
        summerFarmSynchronizedSkuDTO.setFirstCategoryName(summerFarmSynchronizedSkuResp.getFirstCategoryName());
        summerFarmSynchronizedSkuDTO.setSecondCategoryId(summerFarmSynchronizedSkuResp.getSecondCategoryId());
        summerFarmSynchronizedSkuDTO.setSecondCategoryName(summerFarmSynchronizedSkuResp.getSecondCategoryName());
        summerFarmSynchronizedSkuDTO.setThirdCategoryId(summerFarmSynchronizedSkuResp.getThirdCategoryId());
        summerFarmSynchronizedSkuDTO.setThirdCategoryName(summerFarmSynchronizedSkuResp.getThirdCategoryName());
        summerFarmSynchronizedSkuDTO.setSubTitle(summerFarmSynchronizedSkuResp.getSubTitle());
        summerFarmSynchronizedSkuDTO.setMainPicture(summerFarmSynchronizedSkuResp.getMainPicture());
        summerFarmSynchronizedSkuDTO.setDetailPicture(summerFarmSynchronizedSkuResp.getDetailPicture());
        summerFarmSynchronizedSkuDTO.setStorageLocation(summerFarmSynchronizedSkuResp.getStorageLocation());
        summerFarmSynchronizedSkuDTO.setGuaranteePeriod(summerFarmSynchronizedSkuResp.getGuaranteePeriod());
        summerFarmSynchronizedSkuDTO.setGuaranteeUnit(summerFarmSynchronizedSkuResp.getGuaranteeUnit());
        summerFarmSynchronizedSkuDTO.setOrigin(summerFarmSynchronizedSkuResp.getOrigin());
        summerFarmSynchronizedSkuDTO.setSkuId(summerFarmSynchronizedSkuResp.getSkuId());
        summerFarmSynchronizedSkuDTO.setSku(summerFarmSynchronizedSkuResp.getSku());
        summerFarmSynchronizedSkuDTO.setSpecification(summerFarmSynchronizedSkuResp.getSpecification());
        summerFarmSynchronizedSkuDTO.setSpecificationUnit(summerFarmSynchronizedSkuResp.getSpecificationUnit());
        summerFarmSynchronizedSkuDTO.setAdminId(summerFarmSynchronizedSkuResp.getAdminId());
        summerFarmSynchronizedSkuDTO.setType(summerFarmSynchronizedSkuResp.getType());
        summerFarmSynchronizedSkuDTO.setBrandName(summerFarmSynchronizedSkuResp.getBrandName());
        summerFarmSynchronizedSkuDTO.setStorageTemperature(summerFarmSynchronizedSkuResp.getStorageTemperature());
        summerFarmSynchronizedSkuDTO.setSkuIds(summerFarmSynchronizedSkuResp.getSkuIds());
        summerFarmSynchronizedSkuDTO.setWeightNum(summerFarmSynchronizedSkuResp.getWeightNum());
        summerFarmSynchronizedSkuDTO.setVolume(summerFarmSynchronizedSkuResp.getVolume());
        summerFarmSynchronizedSkuDTO.setSpu(summerFarmSynchronizedSkuResp.getSpu());
        summerFarmSynchronizedSkuDTO.setSkuPicture(summerFarmSynchronizedSkuResp.getSkuPicture());
        summerFarmSynchronizedSkuDTO.setSkuTitle(summerFarmSynchronizedSkuResp.getSkuTitle());
        summerFarmSynchronizedSkuDTO.setCreator(summerFarmSynchronizedSkuResp.getCreator());
        summerFarmSynchronizedSkuDTO.setCreateType(summerFarmSynchronizedSkuResp.getCreateType());
        return summerFarmSynchronizedSkuDTO;
    }

    public static ProductSpu copySpu(ProductSpu spu){
        if (spu == null) {
            return null;
        }
        ProductSpu productSpu = new ProductSpu();
        productSpu.setId(spu.getId());
        productSpu.setTenantId(spu.getTenantId());
        productSpu.setCategoryId(spu.getCategoryId());
        productSpu.setBrandId(spu.getBrandId());
        productSpu.setTitle(spu.getTitle());
        productSpu.setSubTitle(spu.getSubTitle());
        productSpu.setMainPicture(spu.getMainPicture());
        productSpu.setDetailPicture(spu.getDetailPicture());
        productSpu.setStorageLocation(spu.getStorageLocation());
        productSpu.setStorageTemperature(spu.getStorageTemperature());
        productSpu.setGuaranteePeriod(spu.getGuaranteePeriod());
        productSpu.setGuaranteeUnit(spu.getGuaranteeUnit());
        productSpu.setOrigin(spu.getOrigin());
        productSpu.setBrandName(spu.getBrandName());
        productSpu.setAgentType(spu.getAgentType());
        productSpu.setCreateTime(spu.getCreateTime());
        productSpu.setUpdateTime(spu.getUpdateTime());
        productSpu.setCustomSpuCode(spu.getCustomSpuCode());
        return productSpu;
    }

}
