package com.cosfo.manage.product.repository.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.good.model.dto.ProductSkuQueryInput;
import com.cosfo.manage.product.mapper.ProductSkuMapper;
import com.cosfo.manage.product.model.po.ProductSku;
import com.cosfo.manage.product.repository.ProductSkuRepository;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Service
public class ProductSkuRepositoryImpl extends ServiceImpl<ProductSkuMapper, ProductSku> implements ProductSkuRepository {
    @Override
    public List<ProductSku> listBySpuIds(List<Long> spuIds, Long tenantId) {
        LambdaQueryWrapper<ProductSku> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(ProductSku::getSpuId, spuIds)
            .eq(ProductSku::getTenantId, tenantId)
            .orderByDesc(ProductSku::getCreateTime)
            .orderByDesc(ProductSku::getUseFlag);
        return list(lambdaQueryWrapper);
    }

    @Override
    public List<ProductSku> listBySpuIds(List<Long> spuIds, Long tenantId, Integer associated, Integer useFlag) {
        if (CollectionUtils.isEmpty(spuIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ProductSku> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(ProductSku::getSpuId, spuIds)
            .eq(ProductSku::getTenantId, tenantId)
            .eq(Objects.nonNull(associated), ProductSku::getAssociated, associated)
            .eq(Objects.nonNull(useFlag), ProductSku::getUseFlag, useFlag)
            .orderByDesc(ProductSku::getId);
        return list(lambdaQueryWrapper);
    }

    @Override
    public ProductSku getByIdAndTenantId(Long skuId, Long tenantId) {
        LambdaQueryWrapper<ProductSku> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductSku::getId, skuId)
            .eq(ProductSku::getTenantId, tenantId);
        return getOne(lambdaQueryWrapper);
    }

    @Override
    public List<ProductSku> listByCondition(ProductSkuQueryInput skuQuery, Long tenantId) {
        LambdaQueryWrapper<ProductSku> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(CollectionUtil.isNotEmpty(skuQuery.getSpuIds()), ProductSku::getSpuId, skuQuery.getSpuIds())
            .eq(ProductSku::getTenantId, tenantId)
            .eq(!Objects.isNull(skuQuery.getSkuId()), ProductSku::getId, skuQuery.getSkuId())
            .eq(!Objects.isNull(skuQuery.getStatus()), ProductSku::getApproveStatus, skuQuery.getStatus())
            .eq(!Objects.isNull(skuQuery.getAssociated()), ProductSku::getAssociated, skuQuery.getAssociated())
            .in(CollectionUtil.isNotEmpty(skuQuery.getSkuIds()), ProductSku::getId, skuQuery.getSkuIds())
            .eq(Objects.nonNull(skuQuery.getUseFlag()),ProductSku::getUseFlag,skuQuery.getUseFlag())
            .eq(Objects.nonNull(skuQuery.getAgentType()),ProductSku::getAgentType,skuQuery.getAgentType())
            .in(CollectionUtil.isNotEmpty(skuQuery.getSpuIds()),ProductSku::getSpuId,skuQuery.getSpuIds())
            .orderByDesc(ProductSku::getId);
        return list(lambdaQueryWrapper);
    }

    @Override
    public void saveBatch(List<ProductSku> skuList) {
        getBaseMapper().saveBatch(skuList);
    }
}
