package com.cosfo.manage.product.repository;

import com.cosfo.manage.product.model.dto.ProductAgentApplicationDTO;
import com.cosfo.manage.product.model.dto.ProductAgentApplicationQueryDTO;
import com.cosfo.manage.product.model.po.ProductAgentApplication;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 代仓商品申请表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-15
 */
public interface ProductAgentApplicationRepository extends IService<ProductAgentApplication> {

    /**
     * 根据条件查询所有
     * @param productAgentApplicationQueryDto
     * @return
     */
    List<ProductAgentApplicationDTO> listAll(ProductAgentApplicationQueryDTO productAgentApplicationQueryDto);

}
