package com.cosfo.manage.product.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.manage.product.model.dto.ProductAgentApplicationItemQueryDTO;
import com.cosfo.manage.product.model.dto.ProductAgentApplicationQueryDTO;
import com.cosfo.manage.product.model.po.ProductAgentApplicationItem;
import com.cosfo.manage.product.mapper.ProductAgentApplicationItemMapper;
import com.cosfo.manage.product.repository.ProductAgentApplicationItemRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 代仓商品申请item表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-15
 */
@Service
public class ProductAgentApplicationItemRepositoryImpl extends ServiceImpl<ProductAgentApplicationItemMapper, ProductAgentApplicationItem> implements ProductAgentApplicationItemRepository {

}
