package com.cosfo.manage.product.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.product.model.dto.ProductStockWarningQueryDTO;
import com.cosfo.manage.product.model.po.ProductStockForewarningReport;
import com.cosfo.manage.report.model.dto.ProductStockForewarningReportDTO;
import com.cosfo.manage.report.model.dto.ProductStockWarnInput;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * <p>
 * 库存预警表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-31
 */
public interface ProductStockForewarningReportRepository extends IService<ProductStockForewarningReport> {

    PageInfo<ProductStockForewarningReportDTO> queryProductWarnList(ProductStockWarnInput input);

    /**
     * 分页查询所有的库存预警记录
     * @param tenantId
     * @param pageSize
     * @param maxId
     * @return
     */
    PageInfo<ProductStockForewarningReportDTO> queryAllProductWarnPage(Long tenantId, Integer pageSize, Long maxId);

    ProductStockForewarningReport selectByTenantAndSkuAndWarehouseNo(Long tenantId, String skucode, Integer warehouseNo);

    List<ProductStockForewarningReport> selectByTenantAndSkuId(Long tenantId, Long skuId);

    boolean deleteByTenantIdAndSkuId(Long tenantId, Long skuId);

    boolean deleteByTenantIdAndSkuIdAndWarehouseNos(Long tenantId, Long skuId, List<Integer> warehouseNos);

    boolean insertOrUpdateBatch(List<ProductStockForewarningReport> list);

    boolean updateUseFlagBySkuId(Long tenantId, Long skuId, Integer useFlag);

    /**
     * 根据租户id和状态查询库存预警表
     *
     * @param productStockWarningQueryDTO
     * @return 库存预警表
     */
    List<ProductStockForewarningReport> queryWarningSummary(ProductStockWarningQueryDTO productStockWarningQueryDTO);

    /**
     * 根据租户id查询库存预警的商品数
     *
     * @param tenantId 租户id
     * @return 库存预警表条数
     */
    long queryWarningSkuCnt(Long tenantId);
}
