package com.cosfo.manage.product.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.common.result.ResultDTOEnum;
import com.cosfo.manage.common.util.AssertCheckParams;
import com.cosfo.manage.good.model.dto.ProductQueryInput;
import com.cosfo.manage.product.mapper.ProductSpuMapper;
import com.cosfo.manage.product.model.po.ProductSpu;
import com.cosfo.manage.product.repository.ProductSpuRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class ProductSpuRepositoryImpl extends ServiceImpl <ProductSpuMapper, ProductSpu> implements ProductSpuRepository {
    @Override
    public ProductSpu getByIdAndTenantId(Long id,Long tenantId) {
        LambdaQueryWrapper<ProductSpu> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(ProductSpu::getId,id)
                .eq(ProductSpu::getTenantId,tenantId);
        return getOne(lambdaQueryWrapper);
    }

    @Override
    public List<ProductSpu> listByCondition(ProductQueryInput productQueryInput,Long tenantId) {
        return getBaseMapper().listByCondition(productQueryInput, tenantId);
    }

    @Override
    public List<ProductSpu> getByTitleAndTenantId(Long tenantId, String title) {
        LambdaQueryWrapper<ProductSpu> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(ProductSpu::getTenantId, tenantId);
        lambdaQueryWrapper.eq(ProductSpu::getTitle, title);
        return list(lambdaQueryWrapper);
    }
}
