package com.cosfo.manage.product.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.manage.good.model.dto.ProductQueryInput;
import com.cosfo.manage.product.model.dto.ProductAgentApplicationItemQueryDTO;
import com.cosfo.manage.product.model.dto.ProductAgentApplicationQueryDTO;
import com.cosfo.manage.product.model.po.ProductAgentApplicationItem;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.product.model.po.ProductSpu;

import java.util.List;

/**
 * <p>
 * spu 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-15
 */
public interface ProductSpuRepository extends IService<ProductSpu> {
    ProductSpu getByIdAndTenantId(Long spuId,Long tenantId);

    List<ProductSpu> listByCondition(ProductQueryInput productQueryInput,Long tenantId);

    /**
     * 根据名称和租户Id查询
     *
     * @param tenantId
     * @param title
     * @return
     */
    List<ProductSpu> getByTitleAndTenantId(Long tenantId, String title);
}
