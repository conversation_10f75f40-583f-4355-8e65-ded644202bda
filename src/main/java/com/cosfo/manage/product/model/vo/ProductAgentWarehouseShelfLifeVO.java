package com.cosfo.manage.product.model.vo;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class ProductAgentWarehouseShelfLifeVO implements Serializable {
    /**
     * sku编码
     */
    private Long skuId;
    /**
     * sku编码
     */
    private String sku;
    /**
     * 库存仓编号
     */
    private Integer warehouseNo;
    /**
     * 采购批次
     */
    private String batch;
    /**
     * 批次库存
     */
    private Integer storeQuantity;
    /**
     * 生产日期
     */
    private LocalDate productionDate;
    /**
     * 到期时间
     */
    private LocalDate qualityDate;
    /**
     * 保质期
     */
    private Integer shelfLife;
    /**
     * 剩余保质期
     */
    private Integer leftShelfLife;
}
