package com.cosfo.manage.product.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * @description
 * <AUTHOR>
 * @date 2022/5/12 11:18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProductSpuVO {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 类目id
     */
    private Long categoryId;
    /**
     * 品牌id
     */
    private Long brandId;
    /**
     * 品牌名
     */
    private String brandName;
    /**
     * 主标题
     */
    private String title;
    /**
     * 副标题
     */
    private String subTitle;
    /**
     * 主图
     */
    private String mainPicture;
    /**
     * 详情图
     */
    private String detailPicture;
    /**
     * 储存区域 0、常温 1、冷藏 2、冷冻
     */
    private Integer storageLocation;
    /**
     * 存储温度
     */
    private String storageTemperature;
    /**
     * 保质期
     */
    private Integer guaranteePeriod;
    /**
     * 0 天 1 月 2 年
     */
    private Integer guaranteeUnit;
    /**
     * 产地
     */
    private String origin;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 0 非代仓商品 1 代仓商品
     */
    private Integer productType;

    /**
     * 类目Id
     */
    private Long classificationId;
}
