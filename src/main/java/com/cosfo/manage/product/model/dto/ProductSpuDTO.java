package com.cosfo.manage.product.model.dto;

import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import com.cosfo.manage.product.model.po.ProductSku;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description
 * <AUTHOR>
 * @date 2022/5/12 11:23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProductSpuDTO {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 类目id
     */
    private Long categoryId;
    /**
     * 品牌id
     */
    private Long brandId;
    /**
     * 主标题
     */
    private String title;
    /**
     * 副标题
     */
    private String subTitle;
    /**
     * 主图
     */
    private String mainPicture;
    /**
     * 详情图
     */
    private String detailPicture;
    /**
     * 储存区域 0、常温 1、冷藏 2、冷冻
     */
    private Integer storageLocation;

    /**
     * 存储区域描述
     */
    private String storageLocationDesc;
    /**
     * 存储温度
     */
    private String storageTemperature;
    /**
     * 保质期
     */
    private Integer guaranteePeriod;
    /**
     * 0 天 1 月 2 年
     */
    private Integer guaranteeUnit;
    /**
     * 保质期单位描述
     */
    private String guaranteeUnitDesc;
    /**
     * 产地
     */
    private String origin;

    /**
     * sku个数
     */
    private Integer skuAmount;

    /**
     * 在售sku个数
     */
    private Integer onSaleSkuAmount;

    /**
     * 最低售价
     */
    private BigDecimal lowestPrice;

    /**
     * 最高售价
     */
    private BigDecimal highestPrice;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 三级类目名称
     */
    private String thirdCategory;

    /**
     * 二级类目名称
     */
    private String secondaryCategory;

    /**
     * 一级类目名称
     */
    private String category;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * sku 集合
     */
    private List<ProductSkuDTO> skuList;

    /**
     * 类目Id 集合
     */
    private List<Long> categoryIds;
    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 0 非代仓商品 1 代仓商品
     */
    private Integer productType;

    /**
     * 一级类目
     */
    private String firstCategoryName;

    /**
     * 二级类目
     */
    private String secondCategoryName;

    /**
     * 三级类目
     */
    private String thirdCategoryName;

    /**
     * 类目Id
     */
    private Long classificationId;
}
