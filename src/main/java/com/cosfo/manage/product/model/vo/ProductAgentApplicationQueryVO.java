package com.cosfo.manage.product.model.vo;

import com.cosfo.manage.product.model.dto.ProductAgentApplicationDTO;
import com.cosfo.manage.product.model.dto.ProductAgentApplicationQueryDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/11/11 11:48
 */
@Data
public class ProductAgentApplicationQueryVO {

    /**
     * 商品名称
     */
    private String title;

    /**
     * 类目ids
     */
    private List<Long> categoryIds;

    /**
     * 审核状态 0、审核中 1、审核通过 2、审核拒绝
     */
    private Integer status;

    /**
     * 页码
     */
    private Integer pageIndex;

    /**
     * 页大小
     */
    private Integer pageSize;
}
