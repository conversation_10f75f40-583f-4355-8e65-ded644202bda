package com.cosfo.manage.product.model.po;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description
 * <AUTHOR>
 * @date 2022/5/12 12:44
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProductCategory implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 类目名称
     */
    private String name;

    /**
     * 上级类目id
     */
    private Long parentId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}
