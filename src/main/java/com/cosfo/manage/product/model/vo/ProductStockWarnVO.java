package com.cosfo.manage.product.model.vo;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-05-16
 * @Description:
 */
@Data
public class ProductStockWarnVO implements Serializable {

    /**
     * skuId(product_sku的id)
     */
    private Long skuId;

    /**
     * 代理商skuCode
     */
    private String agentSkuCode;

    /**
     * 供应城市
     */
    private String cityName;

    /**
     * 仓库ID列表
     */
    private List<Integer> warehouseIdList;

    /**
     * 商品图片
     */
    private String mainPicture;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 商品规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 是否售罄 0否:1是
     */
    private Integer saleOut;

    /**
     * 上下架状态 1上架 0下架
     */
    private Integer onSale;

    /**
     * 预计补货时间
     */
    private String replenishmentTime;

    /**
     * 近七天平均销量
     */
    private Integer sevenAverageSales;

    /**
     * 是否低于平均销量
     */
    private Boolean belowAverage;
}
