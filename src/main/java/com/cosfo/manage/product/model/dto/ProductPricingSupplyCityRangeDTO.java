package com.cosfo.manage.product.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/9/20
 */
@Data
public class ProductPricingSupplyCityRangeDTO {
    /**
     * 报价单主键Id
     */
    private Long productPricingSupplyId;
    /**
     * 有效开始时间
     */
    private LocalDateTime startTime;
    /**
     * 有效结束时间
     */
    private LocalDateTime endTime;
    /**
     * 最小价格
     */
    private BigDecimal minPrice;
    /**
     * 最大价格
     */
    private BigDecimal maxPrice;
    /**
     * 供应城市数
     */
    private Integer supplyCityCount;
    /**
     * 城市数
     */
    private Integer cityNum;
}
