package com.cosfo.manage.product.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/5/9
 */
@Data
public class SummerFarmSynchronizedSkuDTO implements Serializable {
    /**
     * 商品spuId
     */
    private Long spuId;
    /**
     * products.pd_name
     */
    private String title;
    /** spu编码 **/
    private String spu;
    /**
     * 一级类目Id
     */
    private Long firstCategoryId;
    /**
     * 一级类目名称
     */
    private String firstCategoryName;
    /**
     * 二级类目Id
     */
    private Long secondCategoryId;
    /**
     * 二级类目名称
     */
    private String secondCategoryName;
    /**
     * 三级类目Id
     */
    private Long thirdCategoryId;
    /**
     * 三级类目名称
     */
    private String thirdCategoryName;
    /**
     * products.pddetail
     */
    private String subTitle;
    /**
     * 主图片
     */
    private String mainPicture;
    /**
     * 详细图片
     */
    private String detailPicture;
    /**
     * 储存区域
     */
    private Integer storageLocation;
    /**
     * 保质期
     */
    private Integer guaranteePeriod;
    /**
     * 保质期单位
     */
    private Integer guaranteeUnit;
    /**
     * 产地
     */
    private String origin;

    /* -------------------------------------- */

    /**
     * sku编码
     */
    private Long skuId;

    /**
     * 鲜沐sku
     */
    private String sku;

    /** sku图片 **/
    private String skuPicture;
    /** sku名称 **/
    private String skuTitle;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 大客户adminId
     */
    private Long adminId;

    /**
     * 是否代仓 0自营 1代仓
     */
    private Integer type;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 存储温度
     */
    private String storageTemperature;
    /**
     * 同租户 同spu下 skuIds
     */
    private List<Long> skuIds;
    /**
     * 重量
     */
    private BigDecimal weightNum;
    /**
     * 体积
     */
    private String volume;
    /** 创建类型 **/
    private Integer createType;
    /** 创建人 **/
    private Integer creator;

    /** 状态： 0、停用 1、启用 **/
    private Integer useFlag;

    /** 货品二级性质，1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-经销 **/
    private Integer subAgentType;
    /** 地点类型0进口1国产 **/
    private Integer placeType;
}
