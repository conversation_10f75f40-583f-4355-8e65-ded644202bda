package com.cosfo.manage.product.model.vo;

import com.cosfo.manage.good.model.vo.SelfGoodWarehouseDataVO;
import com.cosfo.manage.report.model.vo.ProductAgentWarehouseDataVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/20
 */
@Data
public class ProductSkuDetailVO implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 鲜沐sku
     */
    private String sku;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * spu id
     */
    private Long spuId;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 供价
     */
    private BigDecimal price;

    /**
     * 供应价区间
     */
    private String priceStr;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 主标题
     */
    private String title;

    /**
     * 图片
     */
    private String mainPicture;

    /**
     * 生效时间
     */
    private LocalDateTime startTime;

    /**
     * 失效时间
     */
    private LocalDateTime endTime;

    /**
     * 三级类目名称
     */
    private String thirdCategory;

    /**
     * 二级类目名称
     */
    private String secondaryCategory;
    /**
     * 一级类目名称
     */
    private String category;

    /**
     * 品牌id
     */
    private Long brandId;

    /**
     * 类目id
     */
    private Long categoryId;
    /**
     * 供应城市数
     */
    private Integer cityNum;

    /**x
     * 最小金额
     */
    private BigDecimal minPrice;

    /**
     * 最大金额
     */
    private BigDecimal maxPrice;

    /**
     * 产地
     */
    private String origin;

    /**
     * 储存区域 0、常温 1、冷藏 2、冷冻
     */
    private Integer storageLocation;
    /**
     * 存储温度
     */
    private String storageTemperature;
    /**
     * 保质期
     */
    private Integer guaranteePeriod;
    /**
     * 0 天 1 月 2 年
     */
    private Integer guaranteeUnit;
    /**
     * 可用库存
     */
    private Integer enabledTotalQuantity;
    /**
     * 库存列表
     */
    private List<ProductAgentWarehouseDataVO> productAgentWarehouseDataVOS;
    /**
     * 报价单主键Id
     */
    private Long productSupplyPriceId;


    /**
     * 自营货品-仓库列表
     */
    private List<SelfGoodWarehouseDataVO> warehouseDataVos;
    /**
     * 售后单位
     */
    private String afterSaleUnit;

    /**
     * 最大售后数量
     */
    private Integer maxAfterSaleAmount;
    /**
     * 自有编码
     */
    private String customSkuCode;

    /**
     * 供应skuId
     */
    private Long supplySkuId;

    /**
     * 代仓申请状态  0-审核中 1-通过 2-拒绝 3-未申请
     */
    private Integer agentStatus;
}
