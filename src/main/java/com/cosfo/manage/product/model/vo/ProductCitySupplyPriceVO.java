package com.cosfo.manage.product.model.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/9/15
 */
@Data
public class ProductCitySupplyPriceVO {
    /**
     * 城市Id
     */
    private Long cityId;
    /**
     * 供应城市
     */
    private String cityName;
    /**
     * 供应价
     */
    private BigDecimal price;
    /**
     * 在售状态 0在售1补货中
     */
    private Integer supplyStatus;
    /**
     * 生效开始时间
     */
    private LocalDateTime startTime;
    /**
     * 生效结束时间
     */
    private LocalDateTime endTime;
}
