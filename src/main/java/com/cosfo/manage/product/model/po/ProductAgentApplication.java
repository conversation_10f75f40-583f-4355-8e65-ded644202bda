package com.cosfo.manage.product.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.cosfo.manage.product.model.dto.ProductAgentApplicationDTO;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 代仓商品申请表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-15
 */
@Getter
@Setter
@TableName("product_agent_application")
public class ProductAgentApplication implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 商品名称
     */
    @TableField("title")
    private String title;

    /**
     * 类目id
     */
    @TableField("category_id")
    private Long categoryId;

    /**
     * 储存区域 0、常温 1、冷藏 2、冷冻
     */
    @TableField("storage_location")
    private Integer storageLocation;

    /**
     * 存储温度
     */
    @TableField("storage_temperature")
    private String storageTemperature;

    /**
     * 保质期
     */
    @TableField("guarantee_period")
    private Integer guaranteePeriod;

    /**
     * 0 天 1 月 2 年
     */
    @TableField("guarantee_unit")
    private Integer guaranteeUnit;

    /**
     * 供应商spuId
     */
    @TableField("supplier_spu_id")
    private Long supplierSpuId;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

}
