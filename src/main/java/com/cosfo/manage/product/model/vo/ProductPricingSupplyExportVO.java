package com.cosfo.manage.product.model.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/6/10
 */
@Data
public class ProductPricingSupplyExportVO {
    /**
     * 主键Id
     */
    private Long id;
    /**
     * 城市报价主键Id
     */
    private Long citySupplyPriceId;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * supplyTenantId
     */
    private Long supplyTenantId;
    /**
     * skuId
     */
    private Long supplySkuId;
    /**
     * sku
     */
    private String supplySku;
    /**
     * 商品名称
     */
    private String title;
    /**
     * 主图片
     */
    private String mainPicture;
    /**
     * 规格
     */
    private String specification;
    /**
     * 规格单位
     */
    private String specificationUnit;
    /**
     * 类目
     */
    private String categoryName;
    /**
     * 品牌
     */
    private String brandName;
    /**
     * 报价方式 0指定价 1商城价
     */
    private Integer type;
    /**
     * 报价方式
     */
    private String typeStr;
    /**
     * 供应价
     */
    private BigDecimal price;
    /**
     * 供应价区间
     */
    private String priceStr;
    /**
     * 城市Id
     */
    private Long cityId;
    /**
     * 城市名称
     */
    private String cityName;
    /**
     * 最小价格
     */
    private BigDecimal minPrice;
    /**
     * 最大价格
     */
    private BigDecimal maxPrice;

    /**
     * 采用状态
     */
    private String usingStatusStr;

    /**
     * 有效开始时间
     */
    private LocalDateTime startTime;

    /**
     * 有效结束时间
     */
    private LocalDateTime endTime;

    /**
     * 城市数量
     */
    private Integer cityCount;

    private String supplierName;
}
