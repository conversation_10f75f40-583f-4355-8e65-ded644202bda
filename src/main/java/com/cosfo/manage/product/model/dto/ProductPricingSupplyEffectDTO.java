package com.cosfo.manage.product.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 供价 DTO
 * <AUTHOR>
 * @date 2022/5/12 17:57:15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProductPricingSupplyEffectDTO implements Serializable {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 供应SKU id
     */
    private Long supplySkuId;

    /**
     * 生效开始时间
     */
    private LocalDateTime startTime;

    /**
     * 生效结束时间
     */
    private LocalDateTime endTime;

    /**
     * 供应城市数量
     */
    private Integer supplyCityCount;

    /**
     * 是否关联销售商品 0未关联 1已关联
     */
    private Integer havingRelated;





//    /**
//     * 报价方式 0、指定价 1、鲜沐商城同价
//     */
//    private Integer type;
//
//    /**
//     * 0、成本供价 1、报价单供价
//     */
//    private Integer supplyType;
//    /**
//     * 最低供应价
//     */
//    private BigDecimal minSupplyPrice;
//
//    /**
//     * 最高供应价
//     */
//    private BigDecimal maxSupplyPrice;
}
