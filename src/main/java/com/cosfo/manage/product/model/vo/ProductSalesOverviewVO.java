package com.cosfo.manage.product.model.vo;

import com.cosfo.manage.report.model.po.ChartLine;
import com.cosfo.manage.product.model.po.ProductSalesOverview;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/10/10 9:25
 */
@Data
public class ProductSalesOverviewVO {

    /**
     * 支付成功商品件数
     */
    private Integer paySuccessNum;

    /**
     * 上周期支付成功商品件数
     */
    private Integer lastSuccessNum;

    /**
     * 支付成功件数环比
     */
    private String paySuccessNumChain;

    /**
     * 支付成功金额
     */
    private BigDecimal paySuccessPrice;

    /**
     * 上周期支付成功金额
     */
    private BigDecimal lastPaySuccessPrice;

    /**
     * 支付成功金额环比
     */
    private String paySuccessPriceChain;

    /**
     * 退款金额比
     */
    private String refundRate;

    /**
     * 上周期退款金额比
     */
    private String lastRefundRate;

    /**
     * 退款金额环比
     */
    private String refundRateChain;

    /**
     * 退款金额
     */
    private BigDecimal refundPrice;

    /**
     * 上周期退款金额
     */
    private BigDecimal lastRefundPrice;

    /**
     * 退款金额环比
     */
    private String refundPriceChain;

    /**
     * 折线图数据
     */
    private ChartLine chartLine;
}
