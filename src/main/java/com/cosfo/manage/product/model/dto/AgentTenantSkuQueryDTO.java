package com.cosfo.manage.product.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-05-20
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AgentTenantSkuQueryDTO {

    /**
     * 代理商租户ID
     */
    private Long agentTenantId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 报价单ID列表
     */
    private List<Long> supplyIdList;

    /**
     * 城市名称列表
     */
    private List<String> cityNames;
}
