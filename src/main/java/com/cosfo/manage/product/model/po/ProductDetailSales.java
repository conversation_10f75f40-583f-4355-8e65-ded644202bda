package com.cosfo.manage.product.model.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * product_detail_sales
 * <AUTHOR>
@Data
public class ProductDetailSales implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 时间标签 yyyyMMdd
     */
    private String timeTag;

    /**
     * sku id
     */
    private Long skuId;

    /**
     * 商品类型 0 自营 1 经销
     */
    @Deprecated
    private Integer warehouseType;

    /**
     * 0 自营仓 1 三方仓
     */
    @Deprecated
    private Integer deliveryType;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 规格
     */
    private String specification;

    /**
     * 类目
     */
    private String category;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 供应价
     */
    private BigDecimal supplyPrice;

    /**
     * 售价
     */
    private BigDecimal price;

    /**
     * 销售数量
     */
    private Integer salesNum;

    /**
     * 销售额
     */
    private BigDecimal salesPrice;

    /**
     * 售后数量
     */
    private Integer afterSaleNum;

    /**
     * 售后金额
     */
    private BigDecimal afterSalePrice;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 类目ID
     */
    private Long categoryId;
    /**
     * 门店类型：0、直营店 1、加盟店 2、托管店
     */
    private Integer storeType;
    /**
     * 门店名
     */
    private String storeName;
    /**
     * 地址信息
     */
    private String address;
    /**
     * 商品编码
     */
    private Long itemId;

    /**
     * 货品类型 0虚拟货品 1报价货品 2自营货品
     */
    private String goodsTypeDesc;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 售后单位
     */
    private String afterSaleUnit;

    /**
     * 供应商申请金额
     */
    private BigDecimal afterSaleApplyPrice;

    /**
     * 供应商实退金额
     */
    private BigDecimal afterSaleTotalPrice;

    private static final long serialVersionUID = 1L;
}
