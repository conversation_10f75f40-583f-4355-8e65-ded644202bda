package com.cosfo.manage.product.model.vo;

import lombok.Data;
import net.summerfarm.wms.saleinventory.dto.dto.SkuQuantityChangeRecordDTO;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/2/27 11:15
 */
@Data
public class ProductStockChangeRecordVO {
    /**
     * 鲜沐skuId
     */
    private Long skuId;
    /**
     * sku编码
     */
    private String sku;
    /**
     * 商品名称
     */
    private String pdName;
    /**
     * 仓库编码
     */
    private Integer warehouseNo;
    /**
     * 仓名称
     */
    private String warehouseName;
    /**
     * 规格
     */
    private String spec;
    /**
     * 仓库库存
     */
    private Integer quantity;
    /**
     * 冻结库存
     */
    private Integer lockQuantity;
    /**
     * 在途库存
     */
    private Integer roadQuantity;
    /**
     * 安全库存
     */
    private Integer safeQuantity;
    /**
     * 虚拟库存
     */
    private Integer onlineQuantity;
    /**
     * 变动类型
     */
    private String changeTypeDesc;
    /**
     * 变动数量
     */
    private List<SkuQuantityChangeRecordDTO.ChangeQuantityDTO> changeQuantityList;
    /**
     * 记录人
     */
    private String recorder;
    /**
     * 记录时间
     */
    private LocalDateTime recordTime;
    /**
     * 备注
     */
    private String remark;

    @Data
    public static class ChangeQuantityDTO implements Serializable {

        /**
         * 变动数量
         */
        private Integer changeQuantity;
        /**
         * 变更类型描述
         */
        private String quantityType;

    }
}
