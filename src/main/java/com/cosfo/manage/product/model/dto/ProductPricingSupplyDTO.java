package com.cosfo.manage.product.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 供价 DTO
 * <AUTHOR>
 * @date 2022/5/12 17:57:15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProductPricingSupplyDTO implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * sku id
     */
    private Long skuId;

    /**
     * 供应SKU id
     */
    private Long supplySkuId;

    /**
     * 供应商sku
     */
    private String supplySku;

    /**
     * 供应价
     */
    private BigDecimal price;

    /**
     * 供应价区间
     */
    private String priceStr;

    /**
     * 供应商tenantId
     */
    private Long supplyTenantId;

    /**
     * 报价方式 0、指定价 1、鲜沐商城同价
     */
    private Integer type;

    /**
     * 0、成本供价 1、报价单供价
     */
    private Integer supplyType;

    /**
     * 生效开始时间
     */
    private LocalDateTime startTime;

    /**
     * 生效结束时间
     */
    private LocalDateTime endTime;

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 三级类目名称
     */
    private String thirdCategory;

    /**
     * 二级类目名称
     */
    private String secondaryCategory;

    /**
     * 一级类目名称
     */
    private String category;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 主图
     */
    private String mainPicture;

    /**
     * 品牌id
     */
    private Long brandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 第三方仓名称
     */
    private String warehouseName;

    /**
     * 子类目集合
     */
    private List<Long> categoryIds;

    /**
     * 是否删除
     */
    private Integer deleted;

    /**
     * 城市数量
     */
    private Integer cityNum;

    /**
     * 最小价格
     */
    private BigDecimal minPrice;
    /**
     * 最大价格
     */
    private BigDecimal maxPrice;

    /**
     * 供应sku规格
     */
    private String specification;

    /**
     * 供应sku规格单位
     */
    private String specificationUnit;

    /**
     * sku
     */
    private String sku;

    /**
     * 副标题
     */
    private String subTitle;

    /**
     * 详情图
     */
    private String detailPicture;

    /**
     * 一级类目
     */
    private String firstCategoryName;

    /**
     * 二级类目
     */
    private String secondCategoryName;

    /**
     * 三级类目
     */
    private String thirdCategoryName;

    /**
     * 最低供应价
     */
    private BigDecimal minSupplyPrice;

    /**
     * 最高供应价
     */
    private BigDecimal maxSupplyPrice;

    /**
     * 供应城市数量
     */
    private Integer supplyCityCount;

    /**
     * 是否与商品关联 0 没关联 1 已关联
     */
    private Integer isAssociateProduct;

    /**
     * 页大小
     */
    private Integer pageSize;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 城市id
     */
    private Long cityId;

    /**
     * 省id
     */
    private Long provinceId;

    /**
     * 0 非代仓商品 1 代仓商品
     */
    private Integer productType;

    /**
     * 供应商标题
     */
    private String supplierName;

    /**
     * 储存区域 0、常温 1、冷藏 2、冷冻
     */
    private Integer storageLocation;
    /**
     * 存储温度
     */
    private String storageTemperature;
    /**
     * 保质期
     */
    private Integer guaranteePeriod;
    /**
     * 0 天 1 月 2 年
     */
    private Integer guaranteeUnit;
    /**
     * 产地
     */
    private String origin;
    /**
     * spuId
     */
    private Long spuId;

    /**
     * 是否关联销售商品 0未关联 1已关联
     */
    private Integer havingRelated;


    /**
     * 售后单位
     */
    private String afterSaleUnit;

    /**
     * 最大售后数量
     */
    private Integer maxAfterSaleAmount;

    private Long agentSkuId;
}
