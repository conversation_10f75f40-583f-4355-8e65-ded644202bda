package com.cosfo.manage.product.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Discription 鲜沐报价货品城市库存同步实体
 * <AUTHOR>
 * @date 2023/5/18
 */
@Getter
@Setter
@TableName("product_city_stock")
public class ProductCityStock implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * saas侧skuId
     */
    @TableField("sku_id")
    private Long skuId;

    /**
     * 城市id
     */
    @TableField("city_id")
    private Long cityId;

    /**
     * 城市库存数
     */
    @TableField("quantity")
    private Long quantity;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

}
