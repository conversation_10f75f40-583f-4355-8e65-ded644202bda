package com.cosfo.manage.product.model.po;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * brand_category_mapping
 * <AUTHOR>
@Data
public class ProductBrandCategoryMapping implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 品牌Id
     */
    private Long brandId;

    /**
     * 类目Id
     */
    private Long categoryId;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}
