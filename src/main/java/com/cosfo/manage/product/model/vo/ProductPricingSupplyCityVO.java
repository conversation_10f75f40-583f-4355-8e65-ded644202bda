package com.cosfo.manage.product.model.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/9/20
 */
@Data
public class ProductPricingSupplyCityVO {
    /**
     * 城市名称
     */
    private String cityName;

    private Long id;

    private Long productPricingSupplyId;

    private Long cityId;

    private Integer type;

    private Integer supplyType;

    private BigDecimal price;

    /**
     * 浮动值
     */
    private BigDecimal strategyValue;

    private String priceStr;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private Integer supplyStatus;

    private String mainPicture;

    private String title;

    private String specification;

    private String specificationUnit;

    private Long skuId;

    private String sku;

    /**
     * 生效状态 0已失效 1生效中 2未生效
     */
    private Integer expireStatus;

    /**
     * 省心定价格
     */
    private BigDecimal productSkuPreferentialCostPrice;

    /**
     * 省心定生效开始时间
     */
    private LocalDateTime productSkuPreferentialCostPriceStartTime;

    /**
     * 省心定生效结束时间
     */
    private LocalDateTime productSkuPreferentialCostPriceEndTime;
}
