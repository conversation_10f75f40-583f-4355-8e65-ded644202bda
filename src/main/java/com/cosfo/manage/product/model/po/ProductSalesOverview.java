package com.cosfo.manage.product.model.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.cosfo.manage.report.model.po.ChartLine;
import lombok.Data;

/**
 * product_sales_overview
 * <AUTHOR>
@Data
public class ProductSalesOverview implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 时间标签 yyyyMMdd
     */
    private String timeTag;

    /**
     * 1、日 2、周 3、月
     */
    private Integer type;

    /**
     * 支付成功商品件数
     */
    private Integer paySuccessNum;

    /**
     * 上周期支付成功商品件数
     */
    private Integer lastSuccessNum;

    /**
     * 支付成功件数环比
     */
    private String paySuccessNumChain;

    /**
     * 支付成功金额
     */
    private BigDecimal paySuccessPrice;

    /**
     * 上周期支付成功金额
     */
    private BigDecimal lastPaySuccessPrice;

    /**
     * 支付成功金额环比
     */
    private String paySuccessPriceChain;

    /**
     * 退款件数
     */
    private Integer refundNum;

    /**
     * 上周期退款数
     */
    private Integer lastRefundNum;

    /**
     * 退款数环比
     */
    private String refundNumChain;

    /**
     * 退款金额
     */
    private BigDecimal refundPrice;

    /**
     * 上周期退款金额
     */
    private BigDecimal lastRefundPrice;

    /**
     * 退款金额环比
     */
    private String refundPriceChain;

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 门店类型：0、直营店 1、加盟店 2、托管店
     */
    private Integer storeType;

    /**
     * 门店名
     */
    private String storeName;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 商品类型 0自营1三方
     */
    private Integer warehouseType;

    /**
     * 配送方式 0品牌方配送1三方配送
     */
    private Integer deliveryType;

    private static final long serialVersionUID = 1L;

    /**
     * 构造方法
     */
    public ProductSalesOverview() {

    }

    /**
     * 构造方法
     * @param paySuccessNum
     * @param lastSuccessNum
     * @param paySuccessNumChain
     * @param paySuccessPrice
     * @param lastPaySuccessPrice
     * @param paySuccessPriceChain
     * @param refundNum
     * @param lastRefundNum
     * @param refundNumChain
     * @param refundPrice
     * @param lastRefundPrice
     * @param refundPriceChain
     * @param chartLine
     */
    public ProductSalesOverview(Integer paySuccessNum, Integer lastSuccessNum, String paySuccessNumChain, BigDecimal paySuccessPrice, BigDecimal lastPaySuccessPrice, String paySuccessPriceChain, Integer refundNum, Integer lastRefundNum, String refundNumChain, BigDecimal refundPrice, BigDecimal lastRefundPrice, String refundPriceChain, ChartLine chartLine) {
        this.paySuccessNum = paySuccessNum;
        this.lastSuccessNum = lastSuccessNum;
        this.paySuccessNumChain = paySuccessNumChain;
        this.paySuccessPrice = paySuccessPrice;
        this.lastPaySuccessPrice = lastPaySuccessPrice;
        this.paySuccessPriceChain = paySuccessPriceChain;
        this.refundNum = refundNum;
        this.lastRefundNum = lastRefundNum;
        this.refundNumChain = refundNumChain;
        this.refundPrice = refundPrice;
        this.lastRefundPrice = lastRefundPrice;
        this.refundPriceChain = refundPriceChain;
    }
}
