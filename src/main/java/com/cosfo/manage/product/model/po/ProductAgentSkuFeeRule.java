package com.cosfo.manage.product.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 代仓商品收费规则
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-10
 */
@Getter
@Setter
@TableName("product_agent_sku_fee_rule")
public class ProductAgentSkuFeeRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户Id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 规则类型0按比例1按件数
     */
    @TableField("type")
    private Integer type;

    /**
     * 规则
     */
    @TableField("rule")
    private String rule;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 自动加价标识 0 关闭 1 开启
     */
    @TableField("automatic_increase_price_flag")
    private Integer automaticIncreasePriceFlag;


}
