package com.cosfo.manage.product.model.vo;

import lombok.Data;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/6/9
 */
@Data
public class CategoryVO {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 类目名称
     */
    private String name;

    /**
     * 上级类目id
     */
    private Long parentId;

    /**
     * 是否选中
     */
    private Boolean check = false;
    /** 是否删除 0=启用，1=删除  不传默认为启用 **/
    private Integer outdated;

    /**
     * 子类集合
     */
    private List<CategoryVO> categoryVOS;
}
