package com.cosfo.manage.product.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 库存预警表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-31
 */
@Getter
@Setter
@TableName("product_stock_forewarning_report")
public class ProductStockForewarningReport implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * skuId
     */
    @TableField("sku_id")
    private Long skuId;

    /**
     * skucode
     */
    @TableField("sku_code")
    private String skuCode;

    /**
     * 仓库
     */
    @TableField("warehouse_no")
    private Integer warehouseNo;

    /**
     * 货品类型 0虚拟货品 1报价货品 2自营货品
     */
    @TableField("goods_type")
    private Integer goodsType;

    /**
     * 销售天数
     */
    @TableField("sale_days")
    private Integer saleDays;

    /**
     * 累计销量
     */
    @TableField("sale_quantity")
    private Integer saleQuantity;

    /**
     * 库存数
     */
    @TableField("quantity")
    private Integer quantity;

    /**
     * 预警状态 0-正常 1-预警 2-售罄
     */
    @TableField("forewarning_status")
    private Integer forewarningStatus;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 货品停用状态 0停用1启用
     */
    @TableField("use_flag")
    private Integer useFlag;


}
