package com.cosfo.manage.product.model.vo;

import com.cosfo.manage.product.model.dto.WarehouseSkuFenceAreaDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *
 *
 * @author: xiaowk
 * @date: 2023/10/30 下午2:28
 */
@Data
public class ProductStockWarningVO implements Serializable {
    
    /**
     * skuId(product_sku的id)
     */
    private Long skuId;

    /**
     * 代理商skuCode
     */
    private String skuCode;

    /**
     * 停用状态 0-停用 1-启用
     */
    private Integer useFlag;

    /**
     * 货品停用状态描述
     */
    private String useFlagDesc;

    /**
     * 货品类型 0无货商品 1报价货品 2自营货品
     */
    private Integer goodsType;

    /**
     * 商品图片
     */
    private String mainPicture;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 商品规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;


    /**
     * 一级类目
     */
    private String firstCategory;
    /**
     * 二级类目
     */
    private String secondCategory;
    /**
     * 三级类目
     */
    private String thirdCategory;



    /**
     * 仓所属租户
     */
    private Long warehouseTenantId;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * 仓库服务商
     */
    private String warehouseProvider;

    /**
     * 城市数
     */
    private Integer cityNum;
    /**
     * 近X天平均/累计销量
     */
    private Integer saleAmount;
    /**
     * 仓库库存
     */
    private Integer quantity;

    /**
     * warehouseSkuFenceAreaDTOList
     */
    private List<WarehouseSkuFenceAreaDTO> warehouseSkuFenceAreaDTOList;

    /**
     * 上下架 0上架1下架
     */
    private Integer onSale;

    /**
     * 上下架描述
     */
    private String onSaleDesc;


    /**
     * 预计补货时间
     */
    private String replenishmentTime;

    /**
     *  预警状态 0正常 1预警 2售罄
     */
    private Integer forewarningStatus;

    private String forewarningStatusDesc;

    private String forewarningMsg;


}
