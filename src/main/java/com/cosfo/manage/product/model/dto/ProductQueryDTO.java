package com.cosfo.manage.product.model.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/9/26 21:21
 */
@Data
public class ProductQueryDTO {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 类目id
     */
    private Long categoryId;
    /**
     * 品牌
     */
    private String brandName;
    /**
     * 主标题
     */
    private String title;
    /**
     * SKU ID
     */
    private Long skuId;

    /**
     * 页index
     */
    private Integer pageIndex;

    /**
     * 页大小
     */
    private Integer pageSize;

    /**
     * 类目ids
     */
    private List<Long> categoryIds;

    // TODO: 2022/9/27 不应该放在这个DTO里  待优化
    /**
     * 文件下载id
     */
    private Long fileDownloadRecordId;
}
