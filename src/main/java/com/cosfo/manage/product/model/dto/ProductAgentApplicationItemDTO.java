package com.cosfo.manage.product.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 申请项 DTO
 * <AUTHOR>
 * @date 2022/11/16 21:46
 */
@Data
public class ProductAgentApplicationItemDTO {

    /**
     * id
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 申请id
     */
    private Long applicationId;

    /**
     * 规格名称
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 体重
     */
    private BigDecimal weightNum;

    /**
     * 0 进口 1 国产
     */
    private Integer domesticFlag;

    /**
     * 审核状态 0、审核中 1、审核通过 2、审核拒绝
     */
    private Integer status;

    /**
     * 拒绝原因
     */
    private String refuseReason;

    /**
     * 供应商skuId
     */
    private Long supplierSkuId;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 体积
     */
    private String volume;

}
