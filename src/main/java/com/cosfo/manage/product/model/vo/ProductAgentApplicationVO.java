package com.cosfo.manage.product.model.vo;

import com.cosfo.manage.product.model.po.ProductAgentApplicationItem;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/11/11 11:47
 */
@Data
public class ProductAgentApplicationVO {

    /**
     * id
     */
    private Long id;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 类目层级字符串
     */
    private String categoryStr;

    /**
     * 储存区域 0、常温 1、冷藏 2、冷冻
     */
    private Integer storageLocation;

    /**
     * 存储温度
     */
    private String storageTemperature;

    /**
     * 保质期
     */
    private Integer guaranteePeriod;

    /**
     * 0 天 1 月 2 年
     */
    private Integer guaranteeUnit;


    /**
     * 申请item项
     */
    private List<ProductAgentApplicationItem> itemList;
}
