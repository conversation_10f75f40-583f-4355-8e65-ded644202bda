package com.cosfo.manage.product.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Discription 实物商品SKU VO
 * <AUTHOR>
 * @date 2022/5/12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProductSkuVO implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 鲜沐sku
     */
    private String sku;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * spu id
     */
    private Long spuId;

    /**
     * 规格
     */
    @NotNull(message = "规格不能为空")
    private String specification;

    /**
     * 规格单位
     */
    @NotNull(message = "规格单位不能空")
    private String specificationUnit;

    /**
     * 分类id
     */
    @NotNull(message = "分类不能为空")
    private Long classificationId;

    /**
     * 0自营仓 1三方仓
     */
    @NotNull(message = "仓库类型不能为空")
    private Integer warehouseType;

    /**
     * 配送类型 0品牌仓配送1三方配送
     */
    private Integer deliveryType;

    /**
     * 库存
     */
    private Integer quantity;

    /**
     * 变更库存数量
     */
    private Integer changeQuantity;

    /**
     * 上下架 0 下架 1 上架
     */
    @NotNull(message = "上下架类型不能为空")
    private Integer onSale;

    /**
     * 第三方报价单id / 代理Id
     */
    private Long supplyId;

    /**
     * 代理Id
     */
    private Long agentId;

    /**
     * 供价
     */
    private BigDecimal price;

    /**
     * 库存
     */
    private Integer amount;

    /**
     * 最小起订量
     */
    @Min(1)
    private Integer miniOrderQuantity;

    /**
     * 价格类型 0所有门店展示并统一定价 1所有门店展示单差异化定价 2部分门店展示且差异化定价
     */
    private Integer priceType;

    /**
     * 供应价区间
     */
    private String priceStr;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 主标题
     */
    private String title;

    /**
     * 图片
     */
    private String mainPicture;
}
