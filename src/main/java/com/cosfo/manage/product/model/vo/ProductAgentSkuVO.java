package com.cosfo.manage.product.model.vo;

import com.cosfo.manage.good.model.vo.SelfGoodWarehouseDataVO;
import com.cosfo.manage.report.model.vo.ProductAgentWarehouseDataVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/18
 */
@Data
public class ProductAgentSkuVO {
    /**
     * 代理Id
     */
    private Long id;
    /**
     * skuId
     */
    private Long skuId;
    /**
     * 商品信息
     */
    private String title;
    /**
     * 主图
     */
    private String mainPicture;
    /**
     * sku
     */
    private Long supplySkuId;

    /**
     * 代理skucode
     */
    private String supplySkuCode;
    /**
     * 代理sku编码
     */
    private Long supplySku;
    /**
     * 代理供应商Id
     */
    private Long supplyTenantId;
    /**
     * 代理供应商名称
     */
    private String agentTenantName;
    /**
     * 规格
     */
    private String specification;
    /**
     * 规格单位
     */
    private String specificationUnit;


    /**
     * 三级类目名称
     */
    private String thirdCategory;

    /**
     * 二级类目名称
     */
    private String secondaryCategory;
    /**
     * 一级类目名称
     */
    private String category;

    /**
     * 类目Id
     */
    private Long categoryId;

    /**
     * 品牌
     */
    private String brandName;

    /**
     * 产地
     */
    private String origin;
    /**
     * 储存区域
     */
    private Integer storageLocation;
    /**
     * 存储温度
     */
    private String storageTemperature;
    /**
     * 保质期
     */
    private Integer guaranteePeriod;
    /**
     * 0 天 1 月 2 年
     */
    private Integer guaranteeUnit;
    /**
     * 库存数据
     */
    private List<ProductAgentWarehouseDataVO> productAgentWarehouseDataVOS;

    /**
     * 自营货品-仓库列表
     */
    private List<SelfGoodWarehouseDataVO> warehouseDataVos;
    /**
     * 自有编码
     */
    private String customSkuCode;

    /**
     * 最小供应价格
     */
    private BigDecimal minPrice;
    /**
     * 最大供应价格
     */
    private BigDecimal maxPrice;
    /**
     * 供应价区间
     */
    private String priceStr;

    /**
     * 页大小
     */
    private Integer pageSize;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 代仓申请状态  0-审核中 1-通过 2-拒绝 3-未申请
     */
    private Integer agentStatus;
}
