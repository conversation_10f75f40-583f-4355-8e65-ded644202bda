package com.cosfo.manage.product.model.dto;

import com.cosfo.manage.good.model.vo.SelfGoodWarehouseDataVO;
import com.cosfo.manage.market.model.dto.MarketAreaItemMappingDTO;
import com.cosfo.manage.market.model.vo.MarketAreaItemMappingVO;
import com.cosfo.manage.product.model.vo.ProductAgentSkuVO;
import com.cosfo.manage.report.model.vo.ProductAgentWarehouseDataVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.goods.client.resp.ProductsMappingResp;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Discription 实物商品SKU DTO
 * <AUTHOR>
 * @date 2022/5/12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProductSkuDTO implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 鲜沐sku
     */
    private String sku;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * spu id
     */
    private Long spuId;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 规格类型 0容量*数量 1区间
     */
    private Integer specificationType;

    /**
     * 分类id
     */
    private Long classificationId;

    /**
     * 0自营仓 1三方仓
     */
    @Deprecated
    private Integer warehouseType;

    /**
     * 配送方式 0 品牌方配送 1 三方配送
     */
    @Deprecated
    private Integer deliveryType;

    /**
     * 库存
     */
    private Integer amount;

    /**
     * 变更库存数量
     */
    private Integer changeQuantity;

    /**
     * 上下架 0 下架 1 上架
     */
    private Integer onSale;

    /**
     * 第三方报价单id
     */
    private Long supplyId;

    /**
     * 代理Id
     */
    private Long agentId;

    /**
     * 供价
     */
    private BigDecimal price;

    /**
     * 供应价区间
     */
    private String priceStr;

    /**
     * 品牌Id
     */
    private Long brandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 主标题
     */
    private String title;

    /**
     * 图片
     */
    private String mainPicture;

    /**
     * 第三方仓
     */
    private String warehouse;

    /**
     * 生效时间
     */
    private LocalDateTime startTime;

    /**
     * 失效时间
     */
    private LocalDateTime endTime;

    /**
     * 供价数据
     */
    private ProductPricingSupplyDTO supplyDTO;

    /**
     * 代仓商品信息
     */
    private ProductAgentSkuVO productAgentSkuVO;

    /**
     * 类目id
     */
    private Long categoryId;
    /**
     * 供应城市数
     */
    private Integer cityNum;

    /**
     * 最小金额
     */
    private BigDecimal minPrice;

    /**
     * 最大金额
     */
    private BigDecimal maxPrice;

    /**
     * 副标题
     */
    private String subTitle;

    /**
     * 产地
     */
    private String origin;

    /**
     * 储存区域 0、常温 1、冷藏 2、冷冻
     */
    private Integer storageLocation;
    /**
     * 存储温度
     */
    private String storageTemperature;
    /**
     * 保质期
     */
    private Integer guaranteePeriod;
    /**
     * 0 天 1 月 2 年
     */
    private Integer guaranteeUnit;

    /**
     * 最小起订量
     */
    private Integer miniOrderQuantity;

    /**
     * 定价策略
     */
    private List<MarketAreaItemMappingVO> marketAreaItemMappingVos;

    /**
     * 价格类型 0所有门店展示并统一定价 1所有门店展示单差异化定价 2部分门店展示且差异化定价
     */
    private Integer priceType;

    /**
     * 价格策略
     */
    private List<MarketAreaItemMappingDTO> marketAreaItemMappingDtoList;
    /**
     * 代仓品skuId
     */
    private Long agentSkuId;
    /**
     * 代仓品skuCode
     */
    private String agentSkuCode;
    /**
     * 可用库存
     */
    private Integer enabledTotalQuantity;
    /**
     * 库存列表
     */
    private List<ProductAgentWarehouseDataVO> productAgentWarehouseDataVOS;

    /**
     * 自营货品-仓库列表
     */
    private List<SelfGoodWarehouseDataVO> warehouseDataVos;

    /**
     * 类目Id
     */
    private Long firstCategoryId;
    /**
     * 一级类目
     */
    private String firstCategory;
    /**
     * 二级类目Id
     */
    private Long secondCategoryId;
    /**
     * 二级类目
     */
    private String secondCategory;
    /**
     * 三级类目Id
     */
    private Long thirdCategoryId;
    /**
     * 三级类目
     */
    private String thirdCategory;

    /**
     * @see com.cosfo.manage.common.context.ProductAutomaticIncreasePriceFlagEnum
     */
    private Integer automaticIncreasePriceFlag;

    /**
     * 报价单Id
     */
    private Long productSupplyPriceId;

    /**
     * 税率
     */
    private BigDecimal taxRateValue;

    /**
     * 是否关联商品
     */
    private Integer associated;

    /**
     * 产地类型 0进口1国产
     */
    private Integer placeType;

    /**
     * 体积
     */
    private String volume;

    /**
     * 体积单位
     */
    private Long volumeUnit;

    /**
     * 重量
     */
    private Double weight;

    /**
     * 重量备注
     */
    private String weightNotes;

    /**
     * 售后单位
     */
    private String afterSaleUnit;

    /**
     * 最大售后数量
     */
    private Integer maxAfterSaleAmount;

    /**
     * 自有编码
     */
    private String customSkuCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 城市库存数
     */
    private Long quantity;
    /**
     * 映射关系
     **/
    private ProductsMappingResp skuMapping;

    /**
     * 停用状态 0-停用 1-启用
     */
    private Integer useFlag;


    /**
     * 代仓申请状态  0-审核中 1-通过 2-拒绝 3-未申请
     */
    private Integer agentStatus;
}
