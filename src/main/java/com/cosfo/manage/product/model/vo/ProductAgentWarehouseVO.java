package com.cosfo.manage.product.model.vo;

import com.cosfo.manage.product.model.po.ProductAgentWarehouse;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/11/4
 */
@Data
public class ProductAgentWarehouseVO extends ProductAgentWarehouse {
    /**
     * 主键Id
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 仓库Id
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 状态0使用1未使用
     */
    private Integer status;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;
}
