package com.cosfo.manage.product.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.cosfo.common.excel.easyexcel.LargeDataSetExporter;
import com.cosfo.manage.product.model.dto.*;
import com.cosfo.manage.product.model.po.ProductPricingSupply;
import com.cosfo.manage.product.model.vo.ProductPricingSupplyCityMappingDTO;
import com.cosfo.manage.product.model.vo.ProductPricingSupplyExportVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @discription 供价持久层
 * <AUTHOR>
 * @date 2022/5/12
 */
@Repository
public interface ProductPricingSupplyMapper {

    /**
     * 删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(ProductPricingSupply record);

    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(ProductPricingSupply record);

    /**
     * 根据主键查询
     * @param id
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    ProductPricingSupply selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(ProductPricingSupply record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(ProductPricingSupply record);

    /**
     * 查询所有
     * @param pricingSupplyDTO
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<ProductPricingSupplyDTO> listQuotation(ProductPricingSupplyDTO pricingSupplyDTO);

    /**
     * 根据租户id和skuId查询
     * @param tenantId
     * @param skuId
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    ProductPricingSupplyDTO selectByTenantAndSkuId(@Param("tenantId") Long tenantId, @Param("skuId") Long skuId);


    /**
     * 批量查询报价单信息
     *
     * @param tenantId
     * @param skuIds
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<ProductPricingSupplyDTO> batchQueryByTenantAndSkuIds(@Param("tenantId") Long tenantId, @Param("skuIds") List<Long> skuIds);

    /**
     * 查询经销
     * @param query
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<ProductPricingSupplyDTO> queryAll(ProductPricingSupplyQueryDTO query);
    /**
     * 查询报价
     * @param query
     * @return
     */
    List<ProductPricingSupplyEffectDTO> queryEffectSkuInfo(ProductPricingSupplyEffectQueryDTO query);

    /**
     * 查询生效时间
     * @param tenantId
     * @param supplyIds
     * @return
     */
    List<ProductPricingSupplyDTO> queryEffectTime(@Param("tenantId") Long tenantId, @Param("supplyIds") List<Long> supplyIds);

    /**
     * 查询供应商的SKU在这个租户的数据
     * @param tenantId
     * @param id
     * @return
     */
    ProductPricingSupply selectBySupplierSkuId(@Param("tenantId") Long tenantId, @Param("supplierSkuId") Long id);

    /**
     * 查询供应城市
     *
     * @param supplySkuId
     * @param tenantId
     * @return
     */
    List<ProductPricingSupplyCityMappingDTO> querySupplyCity(@Param("supplySkuId") Long supplySkuId,
                                                             @Param("tenantId") Long tenantId);

    /**
     * 查询供应城市
     *
     * @param skuIds
     * @param tenantId
     * @return
     */
    List<ProductPricingSupplyCityMappingDTO> querySupplyCityBySkuId(@Param("skuIds") List<Long> skuIds,
                                                             @Param("tenantId") Long tenantId);

    /**
     * 更新是否关联商品字段
     *
     * @param supplySkuId
     * @param tenantId
     * @param associated
     */
    void updateAssociated(@Param("supplySkuId") Long supplySkuId,
                          @Param("tenantId") Long tenantId,
                          @Param("associated") Integer associated);

    void exportList(@Param("supplyQueryDTO") ProductPricingSupplyQueryDTO supplyQueryDTO, LargeDataSetExporter<ProductPricingSupplyExportVO, ProductPricingSupplyExportVO> handler);

    /**
     * 批量查询货品生效城市
     * @param tenantId
     * @return
     */
    List<SkuSupplyCityCountDTO> querySupplyCityCountBySkuIds(@Param("supplySkuIds") List<Long> supplySkuIds,
                                                             @Param("tenantId") Long tenantId);



    List<ProductPricingSupply> queryBySupplierSkuId(@Param("supplyTenantId") Long supplyTenantId, @Param("supplierSkuId") Long supplierSkuId);

    /**
     * 查询报价货品 指定城市，报给哪些租户
     * @param supplyTenantId
     * @param supplySkuId
     * @param cityNames
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<ProductPricingSupplyCityMappingDTO> querySupplyCityBySkuIdAndSupplyTenantId(@Param("supplyTenantId") Long supplyTenantId, @Param("supplySkuId") Long supplySkuId, @Param("cityNames") List<String> cityNames);


    /**
     * 查询供应城市
     *
     * @param skuIds
     * @param tenantId
     * @return
     */
    List<ProductPricingSupplyCityMappingDTO> queryEffectSupplyCityBySkuId(@Param("skuIds") List<Long> skuIds,
                                                                    @Param("tenantId") Long tenantId);

    /**
     * 查询鲜沐skuIds
     * @param supplySkuIds
     * @param tenantId
     * @return
     */
    List<ProductPricingSupply> queryXianmuSkuBySupplySkuIds(@Param("supplySkuIds") List<Long> supplySkuIds, @Param("tenantId") Long tenantId);
}
