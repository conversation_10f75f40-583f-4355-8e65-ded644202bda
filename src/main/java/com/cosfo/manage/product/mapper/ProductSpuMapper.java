package com.cosfo.manage.product.mapper;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.manage.good.model.dto.ProductQueryInput;
import com.cosfo.manage.product.model.dto.ProductSpuDTO;
import com.cosfo.manage.product.model.dto.ProductQueryDTO;
import com.cosfo.manage.product.model.po.ProductSku;
import com.cosfo.manage.product.model.po.ProductSpu;
import com.cosfo.manage.product.model.vo.ProductSpuVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Discription 实物商品SKU持久层
 * <AUTHOR>
 * @date 2022/5/9
 */
public interface ProductSpuMapper extends BaseMapper<ProductSpu> {

    /**
     * 通过ID查询单条数据
     *
     * @param id
     * @return 实例对象
     */
    @InterceptorIgnore(tenantLine = "on")
    ProductSpu selectByPrimaryKey(Long id);

    /**
     * 通过ID批量查询
     *
     * @param ids
     * @return 实例对象
     */
    @InterceptorIgnore(tenantLine = "on")
    List<ProductSpu> selectBatchByPrimaryKey(@Param("ids") List<Long> ids);


    /**
     * 新增数据
     *
     * @param productSpu 实例对象
     * @return 影响行数
     */
    int insert(ProductSpu productSpu);

    /**
     * 新增
     *
     * @param record
     * @return
     */
    int insertSelective(ProductSpu record);

    /**
     * 修改数据
     *
     * @param productSpu 实例对象
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(ProductSpu productSpu);

    /**
     * 修改数据
     * @param productSpu
     * @return
     */
    int update(ProductSpu productSpu);

    /**
     * 通过主键删除数据
     *
     * @param id
     * @return 影响行数
     */
    @InterceptorIgnore(tenantLine = "on")
    int deleteById(Long id);

    /**
     * 查询所有
     * @param productQueryDTO
     * @return
     */
    List<ProductSpuDTO> listAll(ProductQueryDTO productQueryDTO);

    /**
     * 根据SPU统计上架数量、售价范围等
     * @param id
     * @return
     */
    ProductSpuDTO querySummary(Long id);

    /**
     * 根据条件查询
     *
     * @param productQueryInput
     * @param tenantId
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<ProductSpu> listByCondition(@Param("productQueryInput") ProductQueryInput productQueryInput,
                                     @Param("tenantId") Long tenantId);

    List<ProductSpuVO> queryBySupplySkuIds(List<Long> supplySkuIds);
}

