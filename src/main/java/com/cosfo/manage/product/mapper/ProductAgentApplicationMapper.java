package com.cosfo.manage.product.mapper;

import com.cosfo.manage.product.model.dto.ProductAgentApplicationDTO;
import com.cosfo.manage.product.model.dto.ProductAgentApplicationQueryDTO;
import com.cosfo.manage.product.model.po.ProductAgentApplication;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 代仓商品申请表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-15
 */
@Mapper
public interface ProductAgentApplicationMapper extends BaseMapper<ProductAgentApplication> {

    /**
     * 根据条件查询所有
     * @param productAgentApplicationQueryDTO
     * @return
     */
    List<ProductAgentApplicationDTO> listAll(ProductAgentApplicationQueryDTO productAgentApplicationQueryDTO);
}
