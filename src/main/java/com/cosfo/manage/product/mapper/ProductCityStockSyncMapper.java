package com.cosfo.manage.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.manage.product.model.dto.ProductCityStockDTO;
import com.cosfo.manage.product.model.po.ProductCityStock;
import com.cosfo.manage.product.model.po.ProductSku;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Discription 实物商品SKU持久层
 * @date 2022/5/9
 */
@Mapper
public interface ProductCityStockSyncMapper extends BaseMapper<ProductCityStock> {

    /**
     * 插入数据
     *
     * @param record
     * @return
     */
    int insert(ProductCityStockDTO record);

    /**
     * 根据城市ID、skuId获取数据
     *
     * @param cityId
     * @param skuId
     * @return
     */
    ProductCityStock selectOneByParam(@Param("cityId") Long cityId, @Param("skuId") Long skuId);

    /**
     * 根据主键更新
     * @param productCityStockDTO
     * @return
     */
    int updateByPrimaryKey(ProductCityStockDTO productCityStockDTO);

    /**
     * 根据唯一索引更新
     * @param productCityStockDTO
     * @return
     */
    int updateByUniqueKey(ProductCityStockDTO productCityStockDTO);

    /**
     * 根据 productCityStockDtoList 批量获取数据
     *
     * @param productCityStockDtoList
     * @return
     */
    List<ProductCityStock> batchSelectByParam(@Param("productCityStockDtoList") List<ProductCityStockDTO> productCityStockDtoList);

    /**
     * 根据 productCityStockDtoList 批量执行更新
     *
     * @param productCityStockDtoList
     * @return
     */
    int batchUpdate(@Param("productCityStockDtoList") List<ProductCityStockDTO> productCityStockDtoList);
}
