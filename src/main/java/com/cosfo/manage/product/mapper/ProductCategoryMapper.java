package com.cosfo.manage.product.mapper;

import com.cosfo.manage.product.model.po.ProductCategory;
import com.cosfo.manage.product.model.vo.CategoryVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description
 * <AUTHOR>
 * @date 2022/5/12 12:44
 */
@Repository
public interface ProductCategoryMapper {

    /**
     * 删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(ProductCategory record);

    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(ProductCategory record);

    /**
     * 根据主键查询
     * @param id
     * @return
     */
    ProductCategory selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(ProductCategory record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(ProductCategory record);

    /**
     * 查询所有
     * @return
     */
    List<ProductCategory> selectAll();

    /**
     * 查询子集类目ID集合
     *
     * @param categoryIds
     * @return
     */
    List<CategoryVO> selectByChildCategory(@Param("categoryIds") List<Long> categoryIds);

    /**
     * 根据父级id和名称查询
     * @param parentId
     * @param name
     * @return
     */
    ProductCategory selectByParentIdAndName(@Param("parentId") Long parentId, @Param("name") String name);
}
