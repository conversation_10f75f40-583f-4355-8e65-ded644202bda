package com.cosfo.manage.product.mapper;

import com.cosfo.manage.product.model.dto.ProductPricingSupplyCityRangeDTO;
import com.cosfo.manage.product.model.po.ProductPricingSupplyCityMapping;
import com.cosfo.manage.product.model.vo.ProductPricingSupplyCityMappingDTO;
import com.cosfo.manage.product.model.vo.ProductPricingSupplyCityVO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ProductPricingSupplyCityMappingMapper {

    /**
     * 查询
     * @param id
     * @return
     */
    ProductPricingSupplyCityMapping selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(ProductPricingSupplyCityMapping record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(ProductPricingSupplyCityMapping record);


    /**
     * 查询城市价范围
     *
     * @param productPricingSupplyId
     * @return
     */
    ProductPricingSupplyCityRangeDTO queryCitySupplyPriceRange(@Param("productPricingSupplyId") Long productPricingSupplyId);


    /**
     * 根据Id和城市名称查询
     *
     * @param productPricingSupplyId
     * @param cityName
     * @return
     */
    List<ProductPricingSupplyCityVO> queryByIdAndCityName(@Param("productPricingSupplyId") Long productPricingSupplyId,
                                                          @Param("cityName") String cityName);

    /**
     * 查询供应价对应城市
     *
     * @param tenantId
     * @param supplySkuIds
     * @return
     */
    List<ProductPricingSupplyCityMappingDTO> queryByTenantIdAndSupplySkuId(@Param("tenantId") Long tenantId,
                                                                           @Param("supplySkuIds")List<Long> supplySkuIds);

    List<ProductPricingSupplyCityMapping> queryExpiredSupplyCityByTime(@Param("expiredStartTime") LocalDateTime expiredStartTime, @Param("expiredEndTime") LocalDateTime expiredEndTime);
}
