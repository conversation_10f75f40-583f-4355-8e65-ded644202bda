package com.cosfo.manage.product.mapper;

import com.cosfo.manage.product.model.po.ProductBrand;
import com.cosfo.manage.product.model.vo.ProductBrandVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description 品牌持久层
 * <AUTHOR>
 * @date 2022/5/11 10:42
 */
@Repository
public interface ProductBrandMapper {

    /**
     * 删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);


    /**
     * 插入
     * @param record
     * @return
     */
    int insert(ProductBrand record);


    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(ProductBrand record);

    /**
     * 根据主键查询
     * @param id
     * @return
     */
    ProductBrand selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(ProductBrand record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(ProductBrand record);

    /**
     * 查询所有品牌
     * @param productBrand
     * @return
     */
    List<ProductBrand> listAll(ProductBrand productBrand);

    /**
     * 根据类目Id查询品牌
     *
     * @param categoryId
     * @return
     */
    List<ProductBrandVO> listByCategoryId(@Param("categoryId") Long categoryId);

    /**
     * 根据名称模糊搜索
     * @param brandName
     * @return
     */
    List<ProductBrand> fuzzyByName(String brandName);

    /**
     * 根据名称查询
     * @param name
     * @return
     */
    ProductBrand selectByName(String name);
}
