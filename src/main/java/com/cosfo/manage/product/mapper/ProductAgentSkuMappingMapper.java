package com.cosfo.manage.product.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.manage.product.model.dto.ProductAgentSkuDTO;
import com.cosfo.manage.product.model.dto.ProductAgentSkuQueryDTO;
import com.cosfo.manage.product.model.po.ProductAgentSkuMapping;
import com.cosfo.manage.product.model.vo.ProductAgentSkuVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface ProductAgentSkuMappingMapper extends BaseMapper<ProductAgentSkuMapping> {

    /**
     * 删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(ProductAgentSkuMapping record);

    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(ProductAgentSkuMapping record);

    /**
     * 查询
     * @param id
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    ProductAgentSkuMapping selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(ProductAgentSkuMapping record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(ProductAgentSkuMapping record);

    /**
     * 查询代理sku信息
     *
     * @param productAgentSkuQueryDTO
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<ProductAgentSkuVO> listAll(ProductAgentSkuQueryDTO productAgentSkuQueryDTO);

    /**
     * 查询sku对应代仓sku信息
     *
     * @param tenantId
     * @param skuId
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    ProductAgentSkuMapping selectByTenantIdAndSkuId(@Param("tenantId") Long tenantId,@Param("skuId") Long skuId);

    /**
     * 查询代仓商品信息
     *
     * @param tenantId
     * @param skuId
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    ProductAgentSkuVO queryAgentSkuInfo(@Param("tenantId") Long tenantId,@Param("skuId") Long skuId);

    /**
     * 查询所有
     * @param productAgentSkuQueryDTO
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<ProductAgentSkuDTO> queryAll(ProductAgentSkuQueryDTO productAgentSkuQueryDTO);

    /**
     * 查询供应商的SKU在某个租户下的数据
     * @param tenantId
     * @param id
     * @return
     */
    ProductAgentSkuMapping selectByAgentSkuId(@Param("tenantId") Long tenantId, @Param("agentSkuId") Long id);

    @InterceptorIgnore(tenantLine = "on")
    ProductAgentSkuMapping selectByAgentSkuIdAndAgentTenantIdAndTenantId(@Param("agentTenantId") Long agentTenantId, @Param("agentSkuId") Long agentSkuId, @Param("tenantId") Long tenantId);

    /**
     * 查询品牌方已映射代仓品
     *
     * @param tenantId
     * @return
     */
    List<ProductAgentSkuMapping> selectHavingMappingByTenantId(@Param("tenantId") Long tenantId);

    /**
     * 处理未同步代仓货品
     *
     * @return
     */
    List<ProductAgentSkuMapping> selectNoSynchronizedAgentSku();

    /**
     * 根据agentSkuId查询skuId
     * 固定排除tenantId=1
     * @param agentSkuIds
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<ProductAgentSkuMapping> batchQueryByAgentSkuId(List<Long> agentSkuIds);

    /**
     * 根据skuId 查鲜沐skuId
     * @param skuIds
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<ProductAgentSkuMapping> batchQueryBySkuIds(List<Long> skuIds);

    /**
     * 根据货品id查询
     *
     * @param skuIds
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<ProductAgentSkuMapping> selectBySkuIds(@Param("skuIds") List<Long> skuIds);

    /**
     * 根据代仓skuId查询代仓sku信息
     *
     * @param agentSkuIds
     * @Param agentTenantId
     * @param tenantId
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<ProductAgentSkuMapping> queryAgentSkuInfoByAgentSkuIds(@Param("agentSkuIds") List<Long> agentSkuIds,
                                                                @Param("agentTenantId") Long agentTenantId,
                                                                @Param("tenantId") Long tenantId);

    @InterceptorIgnore(tenantLine = "on")
    List<ProductAgentSkuMapping> queryAgentSkuInfoByAgentSkuCodes(@Param("agentSkuCodes") List<String> agentSkuCodes);

    /**
     * 查询代理商的SKU在报价单的数据
     * @param agentTenantId
     * @param agentSkuCode
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    ProductAgentSkuMapping selectByAgentTenantInfo(@Param("agentTenantId") Long agentTenantId, @Param("agentSkuCode") String agentSkuCode);

    /**
     * 查询代理商的sku映射信息
     * @param agentTenantId
     * @param agentSkuCode
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    ProductAgentSkuMapping queryByAgentTenantIdAndSku(@Param("agentTenantId") Long agentTenantId, @Param("agentSkuCode") String agentSkuCode);

    @InterceptorIgnore(tenantLine = "on")
    List<ProductAgentSkuMapping> selectBySkuCodes(@Param("skuCodes") List<String> skuCodes);

    /**
     * 仅初始化数据使用，后续可删除
     * @param tenantId
     * @return
     */
    List<ProductAgentSkuMapping> selectSupplySkuByTenantId(@Param("tenantId") Long tenantId);
}
