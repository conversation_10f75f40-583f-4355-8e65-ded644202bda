package com.cosfo.manage.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.manage.product.model.dto.ProductSkuOrderSummaryDayDTO;
import com.cosfo.manage.product.model.po.ProductSkuOrderSummary;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * sku下单汇总 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-01
 */
@Mapper
public interface ProductSkuOrderSummaryMapper extends BaseMapper<ProductSkuOrderSummary> {


    int updateAddOrderQuantity(@Param("id") Long id, @Param("orderQuantity") Integer orderQuantity);

    List<ProductSkuOrderSummaryDayDTO> querySaleAmountByTenantId(@Param("tenantId") Long tenantId, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    List<ProductSkuOrderSummaryDayDTO> querySaleAmountByTenantIdAndSkuId(@Param("tenantId") Long tenantId, @Param("skuIds") List<Long> skuIds, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
}
