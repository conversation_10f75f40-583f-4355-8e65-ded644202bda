package com.cosfo.manage.product.service;

import com.cosfo.manage.product.model.po.ProductAgentSkuMapping;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @desc
 * <AUTHOR>
 * @date 2023/1/30 11:49
 */
public interface ProductAgentSkuMappingService {

    /**
     * 根据代理skuId查询对应的skuId
     * @param agentSkuId
     * @return
     */
    List<ProductAgentSkuMapping> batchQueryByAgentSkuId(List<Long> agentSkuId);

    /**
     * 根据saas skuIds 获取 鲜沐skuIds
     * @param skuIds
     * @return
     */
    List<ProductAgentSkuMapping> batchQueryBySaasSkuIds(List<Long> skuIds);
}
