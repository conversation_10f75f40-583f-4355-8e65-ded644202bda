package com.cosfo.manage.product.service;


import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.product.model.dto.ProductAgentSkuDTO;
import com.cosfo.manage.product.model.dto.ProductAgentSkuQueryDTO;
import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import com.cosfo.manage.product.model.vo.ProductAgentSkuVO;
import com.cosfo.manage.report.model.dto.ProductAgentWarehouseDateQueryDTO;
import com.cosfo.manage.report.model.vo.ProductAgentWarehouseDataVO;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/18
 */
public interface ProductAgentSkuService {

    /**
     * 查询可以代仓的sku信息
     *
     * @param pageIndex
     * @param pageSize
     * @param productAgentSkuQueryDTO
     * @param loginContextInfoDTO
     * @return
     */
    ResultDTO<PageInfo<ProductAgentSkuVO>> listAllOld(Integer pageIndex, Integer pageSize, ProductAgentSkuQueryDTO productAgentSkuQueryDTO, LoginContextInfoDTO loginContextInfoDTO);
    PageInfo<ProductAgentSkuVO> listAll(Integer pageIndex, Integer pageSize, ProductAgentSkuQueryDTO productAgentSkuQueryDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 代仓市场列表
     * @param queryDTO
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult<PageInfo<ProductAgentSkuDTO>> listDistributionMarket(ProductAgentSkuQueryDTO queryDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 查询品牌方代仓品
     *
     * @param productAgentWarehouseDateQueryDTO
     * @param loginContextInfoDTO
     * @return
     */
    List<ProductSkuDTO> queryTenantAgentSku(ProductAgentWarehouseDateQueryDTO productAgentWarehouseDateQueryDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 查询代仓货品库存数据
     *
     * @param skuIds
     * @param tenantId
     * @return
     */
    List<ProductAgentWarehouseDataVO> queryAgentProductWarehouseData(List<Long> skuIds, Long tenantId);
}
