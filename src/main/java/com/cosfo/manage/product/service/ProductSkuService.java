package com.cosfo.manage.product.service;

import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import com.cosfo.manage.product.model.dto.ProductSkuQueryConditionDTO;
import com.cosfo.manage.product.model.dto.SummerFarmSynchronizedSkuDTO;
import com.cosfo.manage.product.model.vo.SummerFarmSynchronizedSkuVO;
import com.cosfo.summerfarm.model.dto.SummerFarmSkuMsgDTO;
import com.cosfo.summerfarm.model.input.SummerfarmSkuInput;

import java.util.List;

/**
 * @description product 业务层
 * <AUTHOR>
 * @date 2022/5/12 15:27
 */
public interface ProductSkuService {

//    /**
//     * 保存商品SKU
//     * 1、新建货品SKU 2、新建商品item 3、供价 4、销售策略（定价、上下架、库存） 5、定价策略配置 6、分类关系映射
//     * @param productSkuDTO
//     * @param contextInfoDTO
//     * @Param isCheckPrice
//     * @return
//     */
//    ResultDTO<Long> saveSku(ProductSkuDTO productSkuDTO, LoginContextInfoDTO contextInfoDTO, Boolean isCheckPrice);

    /**
     * 操作上下架
     * @param productSkuDTO
     * @return
     */
    //ResultDTO changOnSale(ProductSkuDTO productSkuDTO);

//    /**
//     * 更新商品SKU
//     * @param productSkuDTO
//     * @param contextInfoDTO
//     * @return
//     */
//    ResultDTO updateSku(ProductSkuDTO productSkuDTO, LoginContextInfoDTO contextInfoDTO);

    /**
     * 查询供应商sku商品信息
     *
     * @param supplySkuIds
     * @return
     */
    List<ProductSkuDTO> querySupplySkuInfo(List<Long> supplySkuIds);

    /**
     * 查询商品商城价
     *
     * @param skuList
     * @param tenantId
     * @return
     */
    //List<ProductSkuDTO> querySkuMallPrice(List<ProductSku> skuList, Long tenantId);

    /**
     * 查询SKU信息
     * @param skuId
     * @return
     */
    @Deprecated
    ProductSkuDTO querySkuInfo(Long skuId);


    /**
     * 根据skuId查询所属分类
     * @param tenantId
     * @param id
     * @return
     */
    //MarketClassificationDTO queryClassification(Long tenantId, Long id);

    /**
     * 删除门店价
     *
     * @param productSkuPriceDTO
     * @param loginContextInfoDTO
     * @return
     */
    //CommonResult deleteStorePrice(ProductSkuPriceDTO productSkuPriceDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 查询sku详情
     *
     * @param productSkuDto
     * @param loginContextInfoDto
     * @return
     */
    //CommonResult<ProductSkuDTO> queryDetail(ProductSkuDTO productSkuDto, LoginContextInfoDTO loginContextInfoDto);

    /**
     * 更新是否关联商品字段
     *
     * @param skuId
     * @param tenantId
     * @param associated
     */
    void updateAssociated(Long skuId, Long tenantId, Integer associated);

//    /**
//     * 查询鲜沐商品信息
//     * @param supplyIds
//     * @return
//     */
//    Map<Long, SummerfarmProductInfoDTO> callSummerfarmQuerySkuInfo(List<Long> supplyIds);

    /**
     * 接收鲜沐商品信息消息
     * @param summerFarmSkuMsgDTO
     */
    void receiveSummerfarmProductInfo(SummerFarmSkuMsgDTO summerFarmSkuMsgDTO);

    /**
     * 根据条件查询
     *
     * @param productSkuQueryConditionDTO
     * @return
     */
    List<ProductSkuDTO> queryByConditionOld(ProductSkuQueryConditionDTO productSkuQueryConditionDTO);
    List<ProductSkuDTO> queryByCondition(ProductSkuQueryConditionDTO productSkuQueryConditionDTO);

    /**
     * 同步货品
     *
     * @param summerFarmSynchronizedSkuDTO
     */
    SummerFarmSynchronizedSkuVO doneSkuSynchronized(SummerFarmSynchronizedSkuDTO summerFarmSynchronizedSkuDTO, Long tenantId);

    /**
     * 同步供应商sku信息
     *
     * @param summerfarmSkuInput
     */
    void synchronizedSupplySku(SummerfarmSkuInput summerfarmSkuInput, Long tenantId);

    ProductSkuDTO selectProductSkuDetailById(Long skuId);
}
