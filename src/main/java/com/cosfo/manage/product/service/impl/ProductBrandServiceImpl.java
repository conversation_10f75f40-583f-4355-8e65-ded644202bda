package com.cosfo.manage.product.service.impl;

import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.common.result.ResultDTOEnum;
import com.cosfo.manage.common.util.AssertCheckParams;
import com.cosfo.manage.facade.MarketFacade;
import com.cosfo.manage.facade.MarketItemFacade;
import com.cosfo.manage.product.mapper.ProductBrandMapper;
import com.cosfo.manage.product.model.dto.ProductBrandDTO;
import com.cosfo.manage.product.model.po.ProductBrand;
import com.cosfo.manage.product.model.vo.ProductBrandVO;
import com.cosfo.manage.product.service.ProductBrandService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * @description 品牌Service层
 * <AUTHOR>
 * @date 2022/5/11 10:42
 */
@Service
public class ProductBrandServiceImpl implements ProductBrandService {

    @Resource
    private ProductBrandMapper productBrandMapper;

    @Resource
    private MarketFacade marketFacade;


    @Override
    public ResultDTO listAll(ProductBrandDTO productBrandDTO) {
        ProductBrand query = new ProductBrand();
        query.setName(productBrandDTO.getName());
        List<ProductBrand> brandList =  productBrandMapper.listAll(query);
        return ResultDTO.success(brandList);
    }

    @Override
    public ResultDTO listByCategoryId(Long categoryId,Long brandId) {
        AssertCheckParams.notNull(categoryId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "类目Id不能为空");
        List<ProductBrandVO> productBrandVOS = productBrandMapper.listByCategoryId(categoryId);
        // 查询商品绑定品牌Id
        if(brandId != null && brandId != 0) {
            ProductBrand productBrand = productBrandMapper.selectByPrimaryKey(brandId);
            boolean disabled = productBrandVOS.stream().anyMatch(productBrandVO -> {
                boolean equals = productBrandVO.getId().equals(productBrand.getId());
                return equals;
            });

            if (!disabled) {
                ProductBrandVO productBrandVO = new ProductBrandVO();
                BeanUtils.copyProperties(productBrand, productBrandVO);
                productBrandVO.setDisabled(disabled);
                productBrandVOS.add(productBrandVO);
            }
        }

        return ResultDTO.success(productBrandVOS);
    }

    @Override
    public ProductBrandDTO selectByName(String brandName) {
        ProductBrand productBrand = productBrandMapper.selectByName(brandName);
        if(Objects.isNull(productBrand)){
            return null;
        }

        ProductBrandDTO productBrandDTO = new ProductBrandDTO();
        productBrandDTO.setId(productBrand.getId());
        productBrandDTO.setName(productBrand.getName());
        return productBrandDTO;
    }

    @Override
    public Set<String> all(Long tenantId) {
        return marketFacade.queryBrandNameByTenantId (tenantId);
    }
}
