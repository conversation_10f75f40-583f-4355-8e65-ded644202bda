package com.cosfo.manage.product.service;

import com.cofso.preferential.client.resp.ProductSkuPreferentialCostPriceRangeResp;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.product.model.dto.*;
import com.cosfo.manage.product.model.vo.ProductPricingSupplyCityMappingDTO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.cosfo.manage.product.model.vo.ProductPricingSupplyCityVO;
import com.cosfo.manage.product.model.vo.ProductPricingSupplyVO;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;

/**
 * @description 供价服务层
 * <AUTHOR>
 * @date 2022/5/12 19:46
 */
public interface ProductPricingSupplyService {

    PageInfo<ProductPricingSupplyDTO> listDistributionMarket(ProductPricingSupplyQueryDTO supplyQueryDTO, LoginContextInfoDTO loginContextInfoDTO);


    /**
     * 查询城市供应价信息
     *
     * @param productPricingSupplyCityQueryDTO
     * @return
     */
    ResultDTO<PageInfo<ProductPricingSupplyCityVO>> queryCitySupplyPriceDetail(ProductPricingSupplyCityQueryDTO productPricingSupplyCityQueryDTO);

    /**
     * 查询商品供应价
     *
     * @param supplySkuIds
     * @param tenantId
     * @return
     */
    List<ProductPricingSupplyCityMappingDTO> querySkuSupplyPrice(List<Long> supplySkuIds, Long tenantId);
    /**
     * key = skuid
     * @param supplySkuIds
     * @param tenantId
     * @return
     */
    Map<Long, List<ProductPricingSupplyCityMappingDTO>> querySupplyPriceSkuIdMap(List<Long> supplySkuIds, Long tenantId);
    /**
     * 查询报价品的省心定价格区间
     *
     * @param tenantId
     * @param preferentialCostPriceRangeQueryMap
     * @return
     */
    Map<Long, ProductSkuPreferentialCostPriceRangeResp> queryPreferentialCostPriceRange(Long tenantId, Map<Long, List<Long>> preferentialCostPriceRangeQueryMap);

    CommonResult<ProductPricingSupplyVO> queryMarketDetail(Long id, Integer productType, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 查询供应城市
     *
     * @param supplySkuId
     * @param tenantId
     * @return
     */
    List<ProductPricingSupplyCityMappingDTO> querySupplyCity(Long supplySkuId, Long tenantId);

    /**
     * 查询供应城市
     *
     * @param skuIds
     * @param tenantId
     * @return
     */
    List<ProductPricingSupplyCityMappingDTO> querySupplyCityBySkuId(List<Long> skuIds, Long tenantId);

    /**
     * 查询城市价和范围
     *
     * @param productPricingSupplyId
     * @return
     */
    ProductPricingSupplyCityRangeDTO queryCitySupplyPriceRange(Long productPricingSupplyId);

    /**
     * 更新是否关联商品字段
     *
     * @param skuId
     * @param tenantId
     * @param associated
     */
    void updateAssociated(Long skuId, Long tenantId, Integer associated);

    Object exportList(LoginContextInfoDTO contextInfoDTO,ProductPricingSupplyQueryDTO supplyQueryDTO);

    BigDecimal getMaxPrice(BigDecimal maxPrice, ProductSkuPreferentialCostPriceRangeResp productSkuPreferentialCostPriceRange);

    BigDecimal getMinPrice(BigDecimal minPrice, ProductSkuPreferentialCostPriceRangeResp productSkuPreferentialCostPriceRange);
}
