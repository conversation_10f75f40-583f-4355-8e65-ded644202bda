package com.cosfo.manage.product.service;

import com.cosfo.manage.common.model.po.CommonLocationCity;
import com.cosfo.manage.system.model.dto.CommonLocationCityDTO;

import java.util.List;
import java.util.Set;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/9/20
 */
public interface LocationCityService {
    /**
     * 根据城市Id查询
     *
     * @return
     */
    List<CommonLocationCityDTO> queryByCityIds(List<Long> cityIds);
    /**
     * 根据城市名称查询
     *
     * @return
     */
    List<CommonLocationCityDTO> queryByCityNames(Set<String> cityNames);

    /**
     * 根据主键
     * @param cityId
     * @return
     */
    CommonLocationCity selectByPrimaryKey(Long cityId);

    /**
     * 查询所有城市名称
     * @return
     */
    List<String> queryAllCity();
}
