package com.cosfo.manage.product.controller;

import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.context.ExcelTemplateConstant;
import com.cosfo.manage.common.model.dto.ExcelImportResDTO;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.product.model.dto.ProductSpuDTO;
import com.cosfo.manage.product.model.dto.ProductQueryDTO;
import com.cosfo.manage.product.model.vo.ProductSpuVO;
import com.cosfo.manage.product.service.ProductSpuService;
import com.cosfo.manage.system.model.po.SystemParameters;
import com.cosfo.manage.system.service.SystemParametersService;
import com.cosfo.summerfarm.model.input.SummerfarmSkuInput;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * @description 商品SPU Controller层
 * <AUTHOR>
 * @date 2022/5/12 10:42
 */
@RestController
@RequestMapping("/productSpu")
public class ProductSpuController extends BaseController {

    @Resource
    private ProductSpuService productSpuService;
    @Resource
    private SystemParametersService systemParametersService;

    /**
     * 保存SPU
     * @param productSpuVO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/saveSpu", method = RequestMethod.POST)
    public ResultDTO saveSpu(@RequestBody ProductSpuVO productSpuVO) {
        ProductSpuDTO productSpuDTO = new ProductSpuDTO();
        BeanUtils.copyProperties(productSpuVO, productSpuDTO);
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        return productSpuService.saveSpu(productSpuDTO, contextInfoDTO);
    }

    /**
     * 更新SPU
     * @param productSpuVO
     * @return
     */
    /*@RequestMapping(value = "/updateSpu", method = RequestMethod.POST)
    public ResultDTO updateSpu(@RequestBody ProductSpuVO productSpuVO) {
        ProductSpuDTO productSpuDTO = new ProductSpuDTO();
        BeanUtils.copyProperties(productSpuVO, productSpuDTO);
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        return productSpuService.updateSpu(productSpuDTO, contextInfoDTO);
    }*/

    /**
     * 商品列表
     * @param pageIndex
     * @param pageSize
     * @param productSpuVO
     * @return
     */
    /*@RequestMapping(value = "/listAll/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public ResultDTO listAll(@PathVariable Integer pageIndex, @PathVariable Integer pageSize, ProductSpuVO productSpuVO) {
        ProductQueryDTO productQueryDTO = new ProductQueryDTO();
        BeanUtils.copyProperties(productSpuVO, productQueryDTO);
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        productQueryDTO.setPageIndex(pageIndex);
        productQueryDTO.setPageSize(pageSize);
        return productSpuService.listAll(pageIndex, pageSize, productQueryDTO, contextInfoDTO);
    }*/

//    /**
//     * 商品详情
//     * @param id
//     * @return
//     */
//    @RequestMapping(value = "/detail/{id}", method = RequestMethod.GET)
//    public ResultDTO<ProductSpuDTO> selectDetail(@PathVariable Long id) {
//        return productSpuService.selectDetail(id);
//    }

    /**
     *  根据经销模板创建商品
     * @param productSpuVO
     * @return
     */
    /*@RequestMapping(value = "/template-create-product", method = RequestMethod.POST)
    public CommonResult templateCreateProduct(@RequestBody ProductSpuVO productSpuVO) {
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        ProductSpuDTO productSpuDTO = new ProductSpuDTO();
        BeanUtils.copyProperties(productSpuVO, productSpuDTO);
        return productSpuService.templateCreateProduct(productSpuDTO, loginContextInfoDTO);
    }*/

    /**
     * 商品模板导入url
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping(value = "/query/import-template")
    public CommonResult<String> queryImportTemplate() {
        SystemParameters parameters = systemParametersService.selectByKey(ExcelTemplateConstant.PRODUCT_IMPORT_TEMPLATE);
        return CommonResult.ok(parameters.getParamValue());
    }

    /**
     * 商品导入
     * @param file
     * @return
     */
    /*@PostMapping(value = "/import")
    public CommonResult<ExcelImportResDTO> importProduct(@RequestBody MultipartFile file) throws IOException {
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        ExcelImportResDTO excelImportResDTO = productSpuService.importProduct(file, loginContextInfoDTO);
        return CommonResult.ok(excelImportResDTO);
    }*/

    /**
     * 商品导出
     * @param productSpuVO
     * @return
     */
    /*@PostMapping(value = "/export")
    public CommonResult export(@RequestBody ProductSpuVO productSpuVO) {
        ProductQueryDTO productQueryDTO = new ProductQueryDTO();
        BeanUtils.copyProperties(productSpuVO, productQueryDTO);
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        productSpuService.export(productQueryDTO, loginContextInfoDTO);
        return CommonResult.ok();
    }*/
}
