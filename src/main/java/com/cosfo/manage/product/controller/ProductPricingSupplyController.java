package com.cosfo.manage.product.controller;

import com.cosfo.manage.common.config.GrayReleaseConfig;
import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.PageResultDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.product.model.dto.ProductPricingSupplyCityQueryDTO;
import com.cosfo.manage.product.model.dto.ProductPricingSupplyDTO;
import com.cosfo.manage.product.model.dto.ProductPricingSupplyQueryDTO;
import com.cosfo.manage.product.model.vo.ProductPricingSupplyCityVO;
import com.cosfo.manage.product.model.vo.ProductPricingSupplyVO;
import com.cosfo.manage.product.service.ProductPricingSupplyService;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @description 供价
 * <AUTHOR>
 * @date 2022/5/12 20:44
 */
@RestController
@RequestMapping("/productPricingSupply")
public class ProductPricingSupplyController extends BaseController {

    @Resource
    private ProductPricingSupplyService productPricingSupplyService;

    @Resource
    private GrayReleaseConfig grayReleaseConfig;
    /**
     * 查询所有报价
     * @param pageIndex
     * @param pageSize
     * @param productPricingSupplyVO
     * @return
     */
    @RequestMapping(value = "/listAll/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public ResultDTO<PageInfo<ProductPricingSupplyDTO>> listAll(@PathVariable Integer pageIndex, @PathVariable Integer pageSize, ProductPricingSupplyVO productPricingSupplyVO) {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        ProductPricingSupplyQueryDTO pricingSupplyDTO = new ProductPricingSupplyQueryDTO();
        BeanUtils.copyProperties(productPricingSupplyVO, pricingSupplyDTO);
        pricingSupplyDTO.setPageNum (pageIndex);
        pricingSupplyDTO.setPageSize (pageSize);
        pricingSupplyDTO.setWithPreferentailFlag (true);
        return PageResultDTO.success(productPricingSupplyService.listDistributionMarket(pricingSupplyDTO, contextInfoDTO));
    }

    /**
     * 城市报价详情
     * @param productPricingSupplyCityQueryDTO
     * @return
     */
    @PostMapping("/query/city-detail")
    public ResultDTO<PageInfo<ProductPricingSupplyCityVO>> queryCitySupplyPriceDetail(@RequestBody ProductPricingSupplyCityQueryDTO productPricingSupplyCityQueryDTO){
        return productPricingSupplyService.queryCitySupplyPriceDetail(productPricingSupplyCityQueryDTO);
    }

    /**
     * 报价货品列表
     * @param productPricingSupplyVO
     * @return
     */
    @PostMapping("/query/distribution-market-list")
    public CommonResult<PageInfo<ProductPricingSupplyDTO>> listDistributionMarket(@RequestBody ProductPricingSupplyVO productPricingSupplyVO) {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        ProductPricingSupplyQueryDTO supplyQueryDTO = new ProductPricingSupplyQueryDTO();
        BeanUtils.copyProperties(productPricingSupplyVO, supplyQueryDTO);
        return CommonResult.ok (productPricingSupplyService.listDistributionMarket (supplyQueryDTO, contextInfoDTO));
    }
    /**
     * 报价货品导出
     * @param productPricingSupplyVO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/export-list")
    public CommonResult exportList(@RequestBody ProductPricingSupplyVO productPricingSupplyVO) {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        ProductPricingSupplyQueryDTO supplyQueryDTO = new ProductPricingSupplyQueryDTO();
        BeanUtils.copyProperties(productPricingSupplyVO, supplyQueryDTO);
        return CommonResult.ok(productPricingSupplyService.exportList(contextInfoDTO,supplyQueryDTO));
    }
    /**
     * 经销详情
     * @param productPricingSupplyVO
     * @return
     */
    @PostMapping("/query/distribution-market-detail")
    public CommonResult<ProductPricingSupplyVO> queryDistributionMarketDetail(@RequestBody ProductPricingSupplyVO productPricingSupplyVO) {
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        return productPricingSupplyService.queryMarketDetail (productPricingSupplyVO.getId(), productPricingSupplyVO.getProductType(), loginContextInfoDTO);
    }
}
