package com.cosfo.manage.product.controller;

import com.cosfo.manage.common.config.GrayReleaseConfig;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.product.service.ProductCategoryService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description
 * <AUTHOR>
 * @date 2022/5/12 12:44
 */
@RestController
@RequestMapping("/category")
public class ProductCategoryController extends BaseController {

    @Resource
    private ProductCategoryService productCategoryService;

    /**
     * 类目树
     * @return
     */
    @RequestMapping(value = "/listAll", method = RequestMethod.GET)
    public ResultDTO categoryTree() {
        return productCategoryService.listCategoryTree ();
    }
}
