package com.cosfo.manage.product.controller;


import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.product.model.dto.ProductAgentSkuFeeRuleDTO;
import com.cosfo.manage.product.model.po.ProductAgentSkuFeeRule;
import com.cosfo.manage.product.model.vo.ProductAgentSkuFeeRuleVO;
import com.cosfo.manage.product.service.ProductAgentSkuFeeRuleService;
import net.xianmu.authentication.common.aspect.TenantPrivilegesPermission;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 代仓商品收费规则 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-10
 */
@RestController
@RequestMapping("/product/product-agent-sku-fee-rule")
public class ProductAgentSkuFeeRuleController extends BaseController {

    @Resource
    private ProductAgentSkuFeeRuleService productAgentSkuFeeRuleService;

    /**
     * 查询代仓收费规则
     * @return
     */
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:mall-product-agent-rule:query")
    @PostMapping("/query/rule")
    public CommonResult<ProductAgentSkuFeeRuleVO> queryRule() {
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        return productAgentSkuFeeRuleService.queryRule(loginContextInfoDTO.getTenantId());
    }

    /**
     * 更新是否开启自动加价标识
     * @param productAgentSkuFeeRuleDto
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:mall-product-agent-rule:update", expireError = true)
    @PostMapping("/upsert/automatic-increase-price-flag")
    public CommonResult updateAutomaticIncreasePriceFlag(@RequestBody ProductAgentSkuFeeRuleDTO productAgentSkuFeeRuleDto) {
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        return productAgentSkuFeeRuleService.updateAutomaticIncreasePriceFlag(productAgentSkuFeeRuleDto, loginContextInfoDTO);
    }
}
