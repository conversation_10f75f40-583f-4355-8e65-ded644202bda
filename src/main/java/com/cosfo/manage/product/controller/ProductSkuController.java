package com.cosfo.manage.product.controller;

import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import com.cosfo.manage.product.model.dto.ProductSkuPriceDTO;
import com.cosfo.manage.product.model.vo.ProductSkuVO;
import com.cosfo.manage.product.service.ProductSkuService;
import net.xianmu.common.result.CommonResult;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 商品SKU Controller层
 *
 * <AUTHOR>
 * @date 2022/5/12 15:42
 */
@RestController
@RequestMapping("/productSku")
public class ProductSkuController extends BaseController {

    @Resource
    private ProductSkuService productSkuService;

//    /**
//     * 保存SKU
//     * @param productSkuVO
//     * @param bindingResult
//     * @return
//     */
//    @RequestMapping(value = "/saveSku", method = RequestMethod.POST)
//    public ResultDTO saveSku(@Validated @RequestBody ProductSkuVO productSkuVO, BindingResult bindingResult) {
//        if (bindingResult.hasErrors()) {
//            return ResultDTO.fail(bindingResult.getFieldError().getDefaultMessage());
//        }
//        ProductSkuDTO productSkuDTO = new ProductSkuDTO();
//        BeanUtils.copyProperties(productSkuVO, productSkuDTO);
//        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
//        return productSkuService.saveSku(productSkuDTO, contextInfoDTO, Boolean.TRUE);
//    }

  /*  *//**
     * 上下架
     * @param productSkuVO
     * @return
     *//*
    @RequestMapping(value = "/changOnSale", method = RequestMethod.POST)
    public ResultDTO changOnSale(@RequestBody ProductSkuVO productSkuVO) {
        ProductSkuDTO productSkuDTO = new ProductSkuDTO();
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        BeanUtils.copyProperties(productSkuVO, productSkuDTO);
        productSkuDTO.setTenantId(contextInfoDTO.getTenantId());
        return productSkuService.changOnSale(productSkuDTO);
    }*/

//    /**
//     * 更新SKU
//     * @param productSkuVO
//     * @param bindingResult
//     * @return
//     */
//    @RequestMapping(value = "/updateSku", method = RequestMethod.POST)
//    public ResultDTO updateSku(@Validated @RequestBody ProductSkuVO productSkuVO, BindingResult bindingResult) {
//        if (bindingResult.hasErrors()) {
//            return ResultDTO.fail(bindingResult.getFieldError().getDefaultMessage());
//        }
//        ProductSkuDTO productSkuDTO = new ProductSkuDTO();
//        BeanUtils.copyProperties(productSkuVO, productSkuDTO);
//        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
//        return productSkuService.updateSku(productSkuDTO, contextInfoDTO);
//    }

    /**
     * 删除商品定价策略
     *
     * @param productSkuPriceDTO
     * @return
     */
   /* @PostMapping("/upsert/delete-store-price")
    public CommonResult deleteStorePrice(@RequestBody ProductSkuPriceDTO productSkuPriceDTO){
        return productSkuService.deleteStorePrice(productSkuPriceDTO, getMerchantInfoDTO());
    }*/

    /**
     * 查询sku详情
     *
     * @param productSkuDTO
     * @return
     */
    /*@PostMapping("/query/detail")
    public CommonResult detail(@RequestBody ProductSkuDTO productSkuDTO){
        return productSkuService.queryDetail(productSkuDTO, getMerchantInfoDTO());
    }*/
}
