package com.cosfo.manage.product.controller;

import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.product.model.dto.ProductBrandDTO;
import com.cosfo.manage.product.model.vo.ProductBrandVO;
import com.cosfo.manage.product.service.ProductBrandService;
import net.xianmu.common.result.CommonResult;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import java.util.Set;

import javax.annotation.Resource;

/**
 * @description 品牌 Controller层
 * <AUTHOR>
 * @date 2022/5/11 10:42
 */
@RestController
@RequestMapping("/brand")
public class ProductBrandController extends BaseController {

    @Resource
    private ProductBrandService productBrandService;

    /**
     * 查询所有品牌
     * @param productBrandVO
     * @return
     */
    @RequestMapping(value = "/listAll", method = RequestMethod.GET)
    public ResultDTO listAll(ProductBrandVO productBrandVO) {
        ProductBrandDTO productBrandDTO = new ProductBrandDTO();
        BeanUtils.copyProperties(productBrandVO, productBrandDTO);
        return productBrandService.listAll(productBrandDTO);
    }
    /**
     * 查询所有品牌
     * @return
     */
    @RequestMapping(value = "/all", method = RequestMethod.GET)
    public CommonResult<Set<String>> all() {
        return CommonResult.ok (productBrandService.all(getMerchantInfoDTO ().getTenantId ()));
    }

    /**
     * 根据类目树查询绑定的品牌列表
     *
     * @param categoryId
     * @return
     */
    @RequestMapping(value = "/listByCategoryId", method = RequestMethod.GET)
    public ResultDTO listByCategoryId(Long categoryId,Long brandId){
        return productBrandService.listByCategoryId(categoryId,brandId);
    }
}
