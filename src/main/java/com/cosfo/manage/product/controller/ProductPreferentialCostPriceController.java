package com.cosfo.manage.product.controller;

import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.product.model.dto.PreferentialCostPriceQueryDTO;
import com.cosfo.manage.product.model.vo.ProductPreferentialCostPriceBasicDataVO;
import com.cosfo.manage.product.model.vo.ProductPreferentialCostPriceVO;
import com.cosfo.manage.product.service.ProductPreferentialCostPriceService;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 省心定 详情
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "preferential_cost_price")
public class ProductPreferentialCostPriceController extends BaseController {

    @Resource
    private ProductPreferentialCostPriceService productPreferentialCostPriceService;

    /**
     * 查询品牌方省心定价格列表
     *
     * @return
     */
    @PostMapping("/query/list")
    public CommonResult<PageInfo<ProductPreferentialCostPriceVO>> listPreferentialCostPrice(@Valid @RequestBody PreferentialCostPriceQueryDTO queryDTO) {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        queryDTO.setTenantId(contextInfoDTO.getTenantId());
        PageInfo<ProductPreferentialCostPriceVO> pageInfo = productPreferentialCostPriceService.listPreferentialCostPrice(queryDTO);
        return CommonResult.ok(pageInfo);
    }

    /**
     * 查询品牌方省心定 基础数据
     *
     * @return
     */
    @PostMapping("/query/basic_data")
    public CommonResult<ProductPreferentialCostPriceBasicDataVO> queryBasicData(@Valid @RequestBody PreferentialCostPriceQueryDTO queryDTO) {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        queryDTO.setTenantId(contextInfoDTO.getTenantId());
        ProductPreferentialCostPriceBasicDataVO basicDataVO = productPreferentialCostPriceService.queryBasicData(queryDTO);
        return CommonResult.ok(basicDataVO);
    }
}
