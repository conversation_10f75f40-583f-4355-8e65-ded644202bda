package com.cosfo.manage.market.model.po;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 城市销售商品实体类
 * <AUTHOR>
 * @date 2022/5/12 17:57:15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MarketAreaItem implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 所属租户id
     */
    private Long tenantId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * item表id
     */
    private Long itemId;

    /**
     * 0 下架 1 上架
     */
    private Integer onSale;

    /**
     * 价格类型 0所有门店展示并统一定价 1所有门店展示单差异化定价 2部分门店展示且差异化定价
     */
    private Integer priceType;

    /**
     * 0 自营仓 1 第三方仓
     */
    @Deprecated
    private Integer warehouseType;

    /**
     * 配送方式 0品牌方配送 1三方配送
     */
    @Deprecated
    private Integer deliveryType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 最小起订量
     */
    private Integer miniOrderQuantity;

    private static final long serialVersionUID = 1L;
}
