package com.cosfo.manage.wangdiantong.sdk.api.sales;

import com.cosfo.manage.wangdiantong.sdk.Pager;
import com.cosfo.manage.wangdiantong.sdk.api.sales.dto.*;
import com.cosfo.manage.wangdiantong.sdk.impl.Api;

import java.util.List;

public interface LogisticsSyncAPI {
    @Api(value = "sales.LogisticsSync.getSyncListExt", paged = true)
    LogisticsSyncGetResponse get(LogisticsSyncGetRequest request, Pager pager);

    @Api(value = "sales.LogisticsSync.getSpecialOids")
    LogisticsSyncSpecialOidsGetResponse getSpecialOids(int tradeId, byte platformId, String tid, String oids);

    @Api(value = "sales.LogisticsSync.update")
    LogisticsSyncUpdateResponse update(List<LogisticsSyncUpdateDto> request);
}
