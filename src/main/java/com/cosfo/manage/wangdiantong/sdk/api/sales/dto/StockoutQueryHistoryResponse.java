package com.cosfo.manage.wangdiantong.sdk.api.sales.dto;

import com.google.gson.annotations.SerializedName;

import java.math.BigDecimal;
import java.util.List;

public class StockoutQueryHistoryResponse {

    @SerializedName("total_count")
    private Integer totalCount;
    @SerializedName("order")
    private List<Order> orders;

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public List<Order> getOrders() {
        return orders;
    }

    public void setOrders(List<Order> orders) {
        this.orders = orders;
    }

    public static class Order {
        @SerializedName("post_amount")
        private BigDecimal postAmount;
        @SerializedName("fenxiao_nick")
        private String fenxiaoNick;
        @SerializedName("trade_time")
        private Long tradeTime;
        @SerializedName("error_info")
        private String errorInfo;
        @SerializedName("bad_reason")
        private Integer badReason;
        @SerializedName("discount")
        private BigDecimal discount;
        @SerializedName("picker_name")
        private String pickerName;
        @SerializedName("trade_id")
        private Integer tradeId;
        @SerializedName("trade_label")
        private String tradeLabel;
        @SerializedName("receiver_country")
        private Integer receiverCountry;
        @SerializedName("refund_status")
        private Integer refundStatus;
        @SerializedName("receiver_province")
        private Integer receiverProvince;
        @SerializedName("buyer_message")
        private String buyerMessage;
        @SerializedName("logistics_code")
        private String logisticsCode;
        @SerializedName("shop_platform_id")
        private Integer shopPlatformId;
        @SerializedName("shop_id")
        private Integer shopId;
        @SerializedName("warehouse_name")
        private String warehouseName;
        @SerializedName("nick_name")
        private String nickName;
        @SerializedName("id_card_type")
        private Integer idCardType;
        @SerializedName("status")
        private Integer status;
        @SerializedName("package_fee")
        private BigDecimal packageFee;
        @SerializedName("src_trade_no")
        private String srcTradeNo;
        @SerializedName("post_fee")
        private BigDecimal postFee;
        @SerializedName("custom_type")
        private Integer customType;
        @SerializedName("sub_platform_id")
        private Integer subPlatformId;
        @SerializedName("goods_count")
        private BigDecimal goodsCount;
        @SerializedName("stockout_id")
        private Integer stockoutId;
        @SerializedName("src_order_no")
        private String srcOrderNo;
        @SerializedName("invoice_content")
        private String invoiceContent;
        @SerializedName("receiver_name")
        private String receiverName;
        @SerializedName("currency")
        private String currency;
        @SerializedName("sendbill_template_id")
        private Integer sendbillTemplateId;
        @SerializedName("picklist_no")
        private String picklistNo;
        @SerializedName("examiner_name")
        private String examinerName;
        @SerializedName("logistics_type")
        private Integer logisticsType;
        @SerializedName("trade_from")
        private Integer tradeFrom;
        @SerializedName("delivery_term")
        private Integer deliveryTerm;
        @SerializedName("logistics_no")
        private String logisticsNo;
        @SerializedName("consigner_name")
        private String consignerName;
        @SerializedName("receiver_district")
        private Integer receiverDistrict;
        @SerializedName("goods_total_amount")
        private BigDecimal goodsTotalAmount;
        @SerializedName("receivable")
        private Integer receivable;
        @SerializedName("receiver_mobile")
        private String receiverMobile;
        @SerializedName("stock_check_time")
        private Long stockCheckTime;
        @SerializedName("cs_remark")
        private String csRemark;
        @SerializedName("receiver_address")
        private String receiverAddress;
        @SerializedName("customer_name")
        private String customerName;
        @SerializedName("printer_name")
        private String printerName;
        @SerializedName("customer_id")
        private Integer customerId;
        @SerializedName("warehouse_id")
        private Integer warehouseId;
        @SerializedName("logistics_name")
        private String logisticsName;
        @SerializedName("details_list")
        private List<Detail> detailList;
        @SerializedName("consign_time")
        private String consignTime;
        @SerializedName("warehouse_type")
        private Integer warehouseType;
        @SerializedName("receiver_dtb")
        private String receiverDtb;
        @SerializedName("operator_id")
        private Integer operatorId;
        @SerializedName("print_remark")
        private String printRemark;
        @SerializedName("employee_no")
        private String employeeNo;
        @SerializedName("tax_rate")
        private BigDecimal taxRate;
        @SerializedName("shop_remark")
        private String shopRemark;
        @SerializedName("outer_no")
        private String outerNo;
        @SerializedName("invoice_id")
        private Integer invoiceId;
        @SerializedName("modified")
        private String modified;
        @SerializedName("order_type")
        private Integer orderType;
        @SerializedName("shop_no")
        private String shopNo;
        @SerializedName("picklist_seq")
        private Integer picklistSeq;
        @SerializedName("seq_no")
        private Integer seqNo;
        @SerializedName("receiver_area")
        private String receiverArea;
        @SerializedName("customer_no")
        private String customerNo;
        @SerializedName("created")
        private Long created;
        @SerializedName("weight")
        private BigDecimal weight;
        @SerializedName("block_reason")
        private Integer blockReason;
        @SerializedName("tax")
        private BigDecimal tax;
        @SerializedName("shop_name")
        private String shopName;
        @SerializedName("pay_time")
        private Long payTime;
        @SerializedName("goods_total_cost")
        private BigDecimal goodsTotalCost;
        @SerializedName("trade_no")
        private String tradeNo;
        @SerializedName("consign_status")
        private Integer consignStatus;
        @SerializedName("order_no")
        private String orderNo;
        @SerializedName("receiver_city")
        private Integer receiverCity;
        @SerializedName("invoice_title")
        private String invoiceTitle;
        @SerializedName("goods_type_count")
        private Integer goodsTypeCount;
        @SerializedName("id_card")
        private String idCard;
        @SerializedName("remark")
        private String remark;
        @SerializedName("calc_post_cost")
        private BigDecimal calcPostCost;
        @SerializedName("cod_amount")
        private BigDecimal codAmount;
        @SerializedName("flag_name")
        private String flagName;
        @SerializedName("logistics_id")
        private Integer logisticsId;
        @SerializedName("warehouse_no")
        private String warehouseNo;
        @SerializedName("receiver_telno")
        private String receiverTelno;
        @SerializedName("receiver_zip")
        private String receiverZip;
        @SerializedName("trade_status")
        private Integer tradeStatus;
        @SerializedName("invoice_type")
        private Integer invoiceType;
        @SerializedName("batch_no")
        private String batchNo;
        @SerializedName("packager_name")
        private String packagerName;
        @SerializedName("salesman_no")
        private String salesmanNo;
        @SerializedName("platform_id")
        private Integer platformId;
        @SerializedName("paid")
        private BigDecimal paid;
        @SerializedName("trade_type")
        private Integer tradeType;
        @SerializedName("logistics_print_status")
        private Integer logisticsPrintStatus;
        @SerializedName("fullname")
        private String fullname;
        @SerializedName("logistics_list")
        private List<Logistics> logisticsList;

        public static class Detail {
            @SerializedName("rec_id")
            private Integer recId;
            @SerializedName("stockout_id")
            private Integer stockoutId;
            @SerializedName("spec_id")
            private Integer specId;
            @SerializedName("goods_count")
            private BigDecimal goodsCount;
            @SerializedName("goods_amount")
            private BigDecimal goodsAmount;
            @SerializedName("paid")
            private BigDecimal paid;
            @SerializedName("sell_price")
            private BigDecimal sellPrice;
            @SerializedName("remark")
            private String remark;
            @SerializedName("goods_name")
            private String goodsName;
            @SerializedName("goods_no")
            private String goodsNo;
            @SerializedName("spec_name")
            private String specName;
            @SerializedName("spec_code")
            private String specCode;
            @SerializedName("spec_no")
            private String specNo;
            @SerializedName("cost_price")
            private BigDecimal costPrice;
            @SerializedName("weight")
            private BigDecimal weight;
            @SerializedName("total_amount")
            private BigDecimal totalAmount;
            @SerializedName("goods_id")
            private Integer goodsId;
            @SerializedName("prop1")
            private String prop1;
            @SerializedName("prop2")
            private String prop2;
            @SerializedName("prop3")
            private String prop3;
            @SerializedName("prop4")
            private String prop4;
            @SerializedName("prop5")
            private String prop5;
            @SerializedName("prop6")
            private String prop6;
            @SerializedName("platform_id")
            private Integer platformId;
            @SerializedName("refund_status")
            private Integer refundStatus;
            @SerializedName("market_price")
            private BigDecimal marketPrice;
            @SerializedName("discount")
            private BigDecimal discount;
            @SerializedName("share_amount")
            private BigDecimal shareAmount;
            @SerializedName("tax_rate")
            private BigDecimal taxRate;
            @SerializedName("barcode")
            private String barcode;
            @SerializedName("unit_name")
            private String unitName;
            @SerializedName("sale_order_id")
            private Integer saleOrderId;
            @SerializedName("gift_type")
            private Integer giftType;
            @SerializedName("src_oid")
            private String srcOid;
            @SerializedName("src_tid")
            private String srcTid;
            @SerializedName("from_mask")
            private Integer fromMask;
            @SerializedName("goods_type")
            private Integer goodsType;
            @SerializedName("batch_no")
            private String batchNo;
            @SerializedName("good_prop1")
            private String goodProp1;
            @SerializedName("good_prop2")
            private String goodProp2;
            @SerializedName("good_prop3")
            private String goodProp3;
            @SerializedName("good_prop4")
            private String goodProp4;
            @SerializedName("good_prop5")
            private String goodProp5;
            @SerializedName("good_prop6")
            private String goodProp6;
            @SerializedName("src_order_detail_id")
            private Integer srcOrderDetailId;
            @SerializedName("suite_no")
            private String suiteNo;
            @SerializedName("share_post_amount")
            private BigDecimal sharePostAmount;
            @SerializedName("brand_no")
            private String brandNo;
            @SerializedName("brand_name")
            private String brandName;
            @SerializedName("src_order_type")
            private Integer srcOrderType;
            @SerializedName("base_unit_id")
            private Integer baseUnitId;
            @SerializedName("unit_id")
            private Integer unitId;
            @SerializedName("unit_ratio")
            private BigDecimal unitRatio;
            @SerializedName("is_package")
            private Boolean isPackage;
            @SerializedName("num2")
            private BigDecimal num2;
            @SerializedName("batch_id")
            private Integer batchId;
            @SerializedName("is_examined")
            private Integer isExamined;
            @SerializedName("scan_type")
            private Integer scanType;
            @SerializedName("modified_date")
            private String modifiedDate;
            @SerializedName("created_date")
            private String createdDate;
            @SerializedName("share_price")
            private Integer sharePrice;
            @SerializedName("position_details_list")
            private List<PositionDetail> positionDetailsList;
            @SerializedName("sn_list")
            private String snList;

            public static class PositionDetail {
                @SerializedName("rec_id")
                private Integer recId;
                @SerializedName("stockout_detail_id")
                private Integer stockoutDetailId;
                @SerializedName("position_id")
                private Integer positionId;
                @SerializedName("position_no")
                private String positionNo;
                @SerializedName("batch_no")
                private String batchNo;
                @SerializedName("expire_date")
                private String expireDate;
                @SerializedName("position_goods_count")
                private BigDecimal positionGoodsCount;

                public Integer getRecId() {
                    return recId;
                }

                public void setRecId(Integer recId) {
                    this.recId = recId;
                }

                public Integer getStockoutDetailId() {
                    return stockoutDetailId;
                }

                public void setStockoutDetailId(Integer stockoutDetailId) {
                    this.stockoutDetailId = stockoutDetailId;
                }

                public Integer getPositionId() {
                    return positionId;
                }

                public void setPositionId(Integer positionId) {
                    this.positionId = positionId;
                }

                public String getPositionNo() {
                    return positionNo;
                }

                public void setPositionNo(String positionNo) {
                    this.positionNo = positionNo;
                }

                public String getBatchNo() {
                    return batchNo;
                }

                public void setBatchNo(String batchNo) {
                    this.batchNo = batchNo;
                }

                public String getExpireDate() {
                    return expireDate;
                }

                public void setExpireDate(String expireDate) {
                    this.expireDate = expireDate;
                }

                public BigDecimal getPositionGoodsCount() {
                    return positionGoodsCount;
                }

                public void setPositionGoodsCount(BigDecimal positionGoodsCount) {
                    this.positionGoodsCount = positionGoodsCount;
                }
            }

            public Integer getRecId() {
                return recId;
            }

            public void setRecId(Integer recId) {
                this.recId = recId;
            }

            public Integer getStockoutId() {
                return stockoutId;
            }

            public void setStockoutId(Integer stockoutId) {
                this.stockoutId = stockoutId;
            }

            public Integer getSpecId() {
                return specId;
            }

            public void setSpecId(Integer specId) {
                this.specId = specId;
            }

            public BigDecimal getGoodsCount() {
                return goodsCount;
            }

            public void setGoodsCount(BigDecimal goodsCount) {
                this.goodsCount = goodsCount;
            }

            public BigDecimal getGoodsAmount() {
                return goodsAmount;
            }

            public void setGoodsAmount(BigDecimal goodsAmount) {
                this.goodsAmount = goodsAmount;
            }

            public BigDecimal getPaid() {
                return paid;
            }

            public void setPaid(BigDecimal paid) {
                this.paid = paid;
            }

            public BigDecimal getSellPrice() {
                return sellPrice;
            }

            public void setSellPrice(BigDecimal sellPrice) {
                this.sellPrice = sellPrice;
            }

            public String getRemark() {
                return remark;
            }

            public void setRemark(String remark) {
                this.remark = remark;
            }

            public String getGoodsName() {
                return goodsName;
            }

            public void setGoodsName(String goodsName) {
                this.goodsName = goodsName;
            }

            public String getGoodsNo() {
                return goodsNo;
            }

            public void setGoodsNo(String goodsNo) {
                this.goodsNo = goodsNo;
            }

            public String getSpecName() {
                return specName;
            }

            public void setSpecName(String specName) {
                this.specName = specName;
            }

            public String getSpecCode() {
                return specCode;
            }

            public void setSpecCode(String specCode) {
                this.specCode = specCode;
            }

            public String getSpecNo() {
                return specNo;
            }

            public void setSpecNo(String specNo) {
                this.specNo = specNo;
            }

            public BigDecimal getCostPrice() {
                return costPrice;
            }

            public void setCostPrice(BigDecimal costPrice) {
                this.costPrice = costPrice;
            }

            public BigDecimal getWeight() {
                return weight;
            }

            public void setWeight(BigDecimal weight) {
                this.weight = weight;
            }

            public BigDecimal getTotalAmount() {
                return totalAmount;
            }

            public void setTotalAmount(BigDecimal totalAmount) {
                this.totalAmount = totalAmount;
            }

            public Integer getGoodsId() {
                return goodsId;
            }

            public void setGoodsId(Integer goodsId) {
                this.goodsId = goodsId;
            }

            public String getProp1() {
                return prop1;
            }

            public void setProp1(String prop1) {
                this.prop1 = prop1;
            }

            public String getProp2() {
                return prop2;
            }

            public void setProp2(String prop2) {
                this.prop2 = prop2;
            }

            public String getProp3() {
                return prop3;
            }

            public void setProp3(String prop3) {
                this.prop3 = prop3;
            }

            public String getProp4() {
                return prop4;
            }

            public void setProp4(String prop4) {
                this.prop4 = prop4;
            }

            public String getProp5() {
                return prop5;
            }

            public void setProp5(String prop5) {
                this.prop5 = prop5;
            }

            public String getProp6() {
                return prop6;
            }

            public void setProp6(String prop6) {
                this.prop6 = prop6;
            }

            public Integer getPlatformId() {
                return platformId;
            }

            public void setPlatformId(Integer platformId) {
                this.platformId = platformId;
            }

            public Integer getRefundStatus() {
                return refundStatus;
            }

            public void setRefundStatus(Integer refundStatus) {
                this.refundStatus = refundStatus;
            }

            public BigDecimal getMarketPrice() {
                return marketPrice;
            }

            public void setMarketPrice(BigDecimal marketPrice) {
                this.marketPrice = marketPrice;
            }

            public BigDecimal getDiscount() {
                return discount;
            }

            public void setDiscount(BigDecimal discount) {
                this.discount = discount;
            }

            public BigDecimal getShareAmount() {
                return shareAmount;
            }

            public void setShareAmount(BigDecimal shareAmount) {
                this.shareAmount = shareAmount;
            }

            public BigDecimal getTaxRate() {
                return taxRate;
            }

            public void setTaxRate(BigDecimal taxRate) {
                this.taxRate = taxRate;
            }

            public String getBarcode() {
                return barcode;
            }

            public void setBarcode(String barcode) {
                this.barcode = barcode;
            }

            public String getUnitName() {
                return unitName;
            }

            public void setUnitName(String unitName) {
                this.unitName = unitName;
            }

            public Integer getSaleOrderId() {
                return saleOrderId;
            }

            public void setSaleOrderId(Integer saleOrderId) {
                this.saleOrderId = saleOrderId;
            }

            public Integer getGiftType() {
                return giftType;
            }

            public void setGiftType(Integer giftType) {
                this.giftType = giftType;
            }

            public String getSrcOid() {
                return srcOid;
            }

            public void setSrcOid(String srcOid) {
                this.srcOid = srcOid;
            }

            public String getSrcTid() {
                return srcTid;
            }

            public void setSrcTid(String srcTid) {
                this.srcTid = srcTid;
            }

            public Integer getFromMask() {
                return fromMask;
            }

            public void setFromMask(Integer fromMask) {
                this.fromMask = fromMask;
            }

            public Integer getGoodsType() {
                return goodsType;
            }

            public void setGoodsType(Integer goodsType) {
                this.goodsType = goodsType;
            }

            public String getBatchNo() {
                return batchNo;
            }

            public void setBatchNo(String batchNo) {
                this.batchNo = batchNo;
            }

            public String getGoodProp1() {
                return goodProp1;
            }

            public void setGoodProp1(String goodProp1) {
                this.goodProp1 = goodProp1;
            }

            public String getGoodProp2() {
                return goodProp2;
            }

            public void setGoodProp2(String goodProp2) {
                this.goodProp2 = goodProp2;
            }

            public String getGoodProp3() {
                return goodProp3;
            }

            public void setGoodProp3(String goodProp3) {
                this.goodProp3 = goodProp3;
            }

            public String getGoodProp4() {
                return goodProp4;
            }

            public void setGoodProp4(String goodProp4) {
                this.goodProp4 = goodProp4;
            }

            public String getGoodProp5() {
                return goodProp5;
            }

            public void setGoodProp5(String goodProp5) {
                this.goodProp5 = goodProp5;
            }

            public String getGoodProp6() {
                return goodProp6;
            }

            public void setGoodProp6(String goodProp6) {
                this.goodProp6 = goodProp6;
            }

            public Integer getSrcOrderDetailId() {
                return srcOrderDetailId;
            }

            public void setSrcOrderDetailId(Integer srcOrderDetailId) {
                this.srcOrderDetailId = srcOrderDetailId;
            }

            public String getSuiteNo() {
                return suiteNo;
            }

            public void setSuiteNo(String suiteNo) {
                this.suiteNo = suiteNo;
            }

            public BigDecimal getSharePostAmount() {
                return sharePostAmount;
            }

            public void setSharePostAmount(BigDecimal sharePostAmount) {
                this.sharePostAmount = sharePostAmount;
            }

            public String getBrandNo() {
                return brandNo;
            }

            public void setBrandNo(String brandNo) {
                this.brandNo = brandNo;
            }

            public String getBrandName() {
                return brandName;
            }

            public void setBrandName(String brandName) {
                this.brandName = brandName;
            }

            public Integer getSrcOrderType() {
                return srcOrderType;
            }

            public void setSrcOrderType(Integer srcOrderType) {
                this.srcOrderType = srcOrderType;
            }

            public Integer getBaseUnitId() {
                return baseUnitId;
            }

            public void setBaseUnitId(Integer baseUnitId) {
                this.baseUnitId = baseUnitId;
            }

            public Integer getUnitId() {
                return unitId;
            }

            public void setUnitId(Integer unitId) {
                this.unitId = unitId;
            }

            public BigDecimal getUnitRatio() {
                return unitRatio;
            }

            public void setUnitRatio(BigDecimal unitRatio) {
                this.unitRatio = unitRatio;
            }

            public Boolean getPackage() {
                return isPackage;
            }

            public void setPackage(Boolean aPackage) {
                isPackage = aPackage;
            }

            public BigDecimal getNum2() {
                return num2;
            }

            public void setNum2(BigDecimal num2) {
                this.num2 = num2;
            }

            public Integer getBatchId() {
                return batchId;
            }

            public void setBatchId(Integer batchId) {
                this.batchId = batchId;
            }

            public Integer getIsExamined() {
                return isExamined;
            }

            public void setIsExamined(Integer isExamined) {
                this.isExamined = isExamined;
            }

            public Integer getScanType() {
                return scanType;
            }

            public void setScanType(Integer scanType) {
                this.scanType = scanType;
            }

            public String getModifiedDate() {
                return modifiedDate;
            }

            public void setModifiedDate(String modifiedDate) {
                this.modifiedDate = modifiedDate;
            }

            public String getCreatedDate() {
                return createdDate;
            }

            public void setCreatedDate(String createdDate) {
                this.createdDate = createdDate;
            }

            public Integer getSharePrice() {
                return sharePrice;
            }

            public void setSharePrice(Integer sharePrice) {
                this.sharePrice = sharePrice;
            }

            public List<PositionDetail> getPositionDetailsList() {
                return positionDetailsList;
            }

            public void setPositionDetailsList(List<PositionDetail> positionDetailsList) {
                this.positionDetailsList = positionDetailsList;
            }

            public String getSnList() {
                return snList;
            }

            public void setSnList(String snList) {
                this.snList = snList;
            }
        }

        public static class Logistics {
            @SerializedName("rec_id")
            private Integer recId;
            @SerializedName("stockout_id")
            private Integer stockoutId;
            @SerializedName("logistics_id")
            private Integer logisticsId;
            @SerializedName("logistics_no")
            private String logisticsNo;
            @SerializedName("calc_weight")
            private BigDecimal calcWeight;
            @SerializedName("weight")
            private BigDecimal weight;
            @SerializedName("postage")
            private BigDecimal postage;
            @SerializedName("remark")
            private String remark;
            @SerializedName("length")
            private BigDecimal length;
            @SerializedName("width")
            private BigDecimal width;
            @SerializedName("height")
            private BigDecimal height;
            @SerializedName("package_name")
            private String packageName;
            @SerializedName("logistics_name")
            private String logisticsName;
            @SerializedName("volume")
            private BigDecimal volume;

            public Integer getRecId() {
                return recId;
            }

            public void setRecId(Integer recId) {
                this.recId = recId;
            }

            public Integer getStockoutId() {
                return stockoutId;
            }

            public void setStockoutId(Integer stockoutId) {
                this.stockoutId = stockoutId;
            }

            public Integer getLogisticsId() {
                return logisticsId;
            }

            public void setLogisticsId(Integer logisticsId) {
                this.logisticsId = logisticsId;
            }

            public String getLogisticsNo() {
                return logisticsNo;
            }

            public void setLogisticsNo(String logisticsNo) {
                this.logisticsNo = logisticsNo;
            }

            public BigDecimal getCalcWeight() {
                return calcWeight;
            }

            public void setCalcWeight(BigDecimal calcWeight) {
                this.calcWeight = calcWeight;
            }

            public BigDecimal getWeight() {
                return weight;
            }

            public void setWeight(BigDecimal weight) {
                this.weight = weight;
            }

            public BigDecimal getPostage() {
                return postage;
            }

            public void setPostage(BigDecimal postage) {
                this.postage = postage;
            }

            public String getRemark() {
                return remark;
            }

            public void setRemark(String remark) {
                this.remark = remark;
            }

            public BigDecimal getLength() {
                return length;
            }

            public void setLength(BigDecimal length) {
                this.length = length;
            }

            public BigDecimal getWidth() {
                return width;
            }

            public void setWidth(BigDecimal width) {
                this.width = width;
            }

            public BigDecimal getHeight() {
                return height;
            }

            public void setHeight(BigDecimal height) {
                this.height = height;
            }

            public String getPackageName() {
                return packageName;
            }

            public void setPackageName(String packageName) {
                this.packageName = packageName;
            }

            public String getLogisticsName() {
                return logisticsName;
            }

            public void setLogisticsName(String logisticsName) {
                this.logisticsName = logisticsName;
            }

            public BigDecimal getVolume() {
                return volume;
            }

            public void setVolume(BigDecimal volume) {
                this.volume = volume;
            }
        }

        public BigDecimal getPostAmount() {
            return postAmount;
        }

        public void setPostAmount(BigDecimal postAmount) {
            this.postAmount = postAmount;
        }

        public String getFenxiaoNick() {
            return fenxiaoNick;
        }

        public void setFenxiaoNick(String fenxiaoNick) {
            this.fenxiaoNick = fenxiaoNick;
        }

        public Long getTradeTime() {
            return tradeTime;
        }

        public void setTradeTime(Long tradeTime) {
            this.tradeTime = tradeTime;
        }

        public String getErrorInfo() {
            return errorInfo;
        }

        public void setErrorInfo(String errorInfo) {
            this.errorInfo = errorInfo;
        }

        public Integer getBadReason() {
            return badReason;
        }

        public void setBadReason(Integer badReason) {
            this.badReason = badReason;
        }

        public BigDecimal getDiscount() {
            return discount;
        }

        public void setDiscount(BigDecimal discount) {
            this.discount = discount;
        }

        public String getPickerName() {
            return pickerName;
        }

        public void setPickerName(String pickerName) {
            this.pickerName = pickerName;
        }

        public Integer getTradeId() {
            return tradeId;
        }

        public void setTradeId(Integer tradeId) {
            this.tradeId = tradeId;
        }

        public String getTradeLabel() {
            return tradeLabel;
        }

        public void setTradeLabel(String tradeLabel) {
            this.tradeLabel = tradeLabel;
        }

        public Integer getReceiverCountry() {
            return receiverCountry;
        }

        public void setReceiverCountry(Integer receiverCountry) {
            this.receiverCountry = receiverCountry;
        }

        public Integer getRefundStatus() {
            return refundStatus;
        }

        public void setRefundStatus(Integer refundStatus) {
            this.refundStatus = refundStatus;
        }

        public Integer getReceiverProvince() {
            return receiverProvince;
        }

        public void setReceiverProvince(Integer receiverProvince) {
            this.receiverProvince = receiverProvince;
        }

        public String getBuyerMessage() {
            return buyerMessage;
        }

        public void setBuyerMessage(String buyerMessage) {
            this.buyerMessage = buyerMessage;
        }

        public String getLogisticsCode() {
            return logisticsCode;
        }

        public void setLogisticsCode(String logisticsCode) {
            this.logisticsCode = logisticsCode;
        }

        public Integer getShopPlatformId() {
            return shopPlatformId;
        }

        public void setShopPlatformId(Integer shopPlatformId) {
            this.shopPlatformId = shopPlatformId;
        }

        public Integer getShopId() {
            return shopId;
        }

        public void setShopId(Integer shopId) {
            this.shopId = shopId;
        }

        public String getWarehouseName() {
            return warehouseName;
        }

        public void setWarehouseName(String warehouseName) {
            this.warehouseName = warehouseName;
        }

        public String getNickName() {
            return nickName;
        }

        public void setNickName(String nickName) {
            this.nickName = nickName;
        }

        public Integer getIdCardType() {
            return idCardType;
        }

        public void setIdCardType(Integer idCardType) {
            this.idCardType = idCardType;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public BigDecimal getPackageFee() {
            return packageFee;
        }

        public void setPackageFee(BigDecimal packageFee) {
            this.packageFee = packageFee;
        }

        public String getSrcTradeNo() {
            return srcTradeNo;
        }

        public void setSrcTradeNo(String srcTradeNo) {
            this.srcTradeNo = srcTradeNo;
        }

        public BigDecimal getPostFee() {
            return postFee;
        }

        public void setPostFee(BigDecimal postFee) {
            this.postFee = postFee;
        }

        public Integer getCustomType() {
            return customType;
        }

        public void setCustomType(Integer customType) {
            this.customType = customType;
        }

        public Integer getSubPlatformId() {
            return subPlatformId;
        }

        public void setSubPlatformId(Integer subPlatformId) {
            this.subPlatformId = subPlatformId;
        }

        public BigDecimal getGoodsCount() {
            return goodsCount;
        }

        public void setGoodsCount(BigDecimal goodsCount) {
            this.goodsCount = goodsCount;
        }

        public Integer getStockoutId() {
            return stockoutId;
        }

        public void setStockoutId(Integer stockoutId) {
            this.stockoutId = stockoutId;
        }

        public String getSrcOrderNo() {
            return srcOrderNo;
        }

        public void setSrcOrderNo(String srcOrderNo) {
            this.srcOrderNo = srcOrderNo;
        }

        public String getInvoiceContent() {
            return invoiceContent;
        }

        public void setInvoiceContent(String invoiceContent) {
            this.invoiceContent = invoiceContent;
        }

        public String getReceiverName() {
            return receiverName;
        }

        public void setReceiverName(String receiverName) {
            this.receiverName = receiverName;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }

        public Integer getSendbillTemplateId() {
            return sendbillTemplateId;
        }

        public void setSendbillTemplateId(Integer sendbillTemplateId) {
            this.sendbillTemplateId = sendbillTemplateId;
        }

        public String getPicklistNo() {
            return picklistNo;
        }

        public void setPicklistNo(String picklistNo) {
            this.picklistNo = picklistNo;
        }

        public String getExaminerName() {
            return examinerName;
        }

        public void setExaminerName(String examinerName) {
            this.examinerName = examinerName;
        }

        public Integer getLogisticsType() {
            return logisticsType;
        }

        public void setLogisticsType(Integer logisticsType) {
            this.logisticsType = logisticsType;
        }

        public Integer getTradeFrom() {
            return tradeFrom;
        }

        public void setTradeFrom(Integer tradeFrom) {
            this.tradeFrom = tradeFrom;
        }

        public Integer getDeliveryTerm() {
            return deliveryTerm;
        }

        public void setDeliveryTerm(Integer deliveryTerm) {
            this.deliveryTerm = deliveryTerm;
        }

        public String getLogisticsNo() {
            return logisticsNo;
        }

        public void setLogisticsNo(String logisticsNo) {
            this.logisticsNo = logisticsNo;
        }

        public String getConsignerName() {
            return consignerName;
        }

        public void setConsignerName(String consignerName) {
            this.consignerName = consignerName;
        }

        public Integer getReceiverDistrict() {
            return receiverDistrict;
        }

        public void setReceiverDistrict(Integer receiverDistrict) {
            this.receiverDistrict = receiverDistrict;
        }

        public BigDecimal getGoodsTotalAmount() {
            return goodsTotalAmount;
        }

        public void setGoodsTotalAmount(BigDecimal goodsTotalAmount) {
            this.goodsTotalAmount = goodsTotalAmount;
        }

        public Integer getReceivable() {
            return receivable;
        }

        public void setReceivable(Integer receivable) {
            this.receivable = receivable;
        }

        public String getReceiverMobile() {
            return receiverMobile;
        }

        public void setReceiverMobile(String receiverMobile) {
            this.receiverMobile = receiverMobile;
        }

        public Long getStockCheckTime() {
            return stockCheckTime;
        }

        public void setStockCheckTime(Long stockCheckTime) {
            this.stockCheckTime = stockCheckTime;
        }

        public String getCsRemark() {
            return csRemark;
        }

        public void setCsRemark(String csRemark) {
            this.csRemark = csRemark;
        }

        public String getReceiverAddress() {
            return receiverAddress;
        }

        public void setReceiverAddress(String receiverAddress) {
            this.receiverAddress = receiverAddress;
        }

        public String getCustomerName() {
            return customerName;
        }

        public void setCustomerName(String customerName) {
            this.customerName = customerName;
        }

        public String getPrinterName() {
            return printerName;
        }

        public void setPrinterName(String printerName) {
            this.printerName = printerName;
        }

        public Integer getCustomerId() {
            return customerId;
        }

        public void setCustomerId(Integer customerId) {
            this.customerId = customerId;
        }

        public Integer getWarehouseId() {
            return warehouseId;
        }

        public void setWarehouseId(Integer warehouseId) {
            this.warehouseId = warehouseId;
        }

        public String getLogisticsName() {
            return logisticsName;
        }

        public void setLogisticsName(String logisticsName) {
            this.logisticsName = logisticsName;
        }

        public List<Detail> getDetailList() {
            return detailList;
        }

        public void setDetailList(List<Detail> detailList) {
            this.detailList = detailList;
        }

        public String getConsignTime() {
            return consignTime;
        }

        public void setConsignTime(String consignTime) {
            this.consignTime = consignTime;
        }

        public Integer getWarehouseType() {
            return warehouseType;
        }

        public void setWarehouseType(Integer warehouseType) {
            this.warehouseType = warehouseType;
        }

        public String getReceiverDtb() {
            return receiverDtb;
        }

        public void setReceiverDtb(String receiverDtb) {
            this.receiverDtb = receiverDtb;
        }

        public Integer getOperatorId() {
            return operatorId;
        }

        public void setOperatorId(Integer operatorId) {
            this.operatorId = operatorId;
        }

        public String getPrintRemark() {
            return printRemark;
        }

        public void setPrintRemark(String printRemark) {
            this.printRemark = printRemark;
        }

        public String getEmployeeNo() {
            return employeeNo;
        }

        public void setEmployeeNo(String employeeNo) {
            this.employeeNo = employeeNo;
        }

        public BigDecimal getTaxRate() {
            return taxRate;
        }

        public void setTaxRate(BigDecimal taxRate) {
            this.taxRate = taxRate;
        }

        public String getShopRemark() {
            return shopRemark;
        }

        public void setShopRemark(String shopRemark) {
            this.shopRemark = shopRemark;
        }

        public String getOuterNo() {
            return outerNo;
        }

        public void setOuterNo(String outerNo) {
            this.outerNo = outerNo;
        }

        public Integer getInvoiceId() {
            return invoiceId;
        }

        public void setInvoiceId(Integer invoiceId) {
            this.invoiceId = invoiceId;
        }

        public String getModified() {
            return modified;
        }

        public void setModified(String modified) {
            this.modified = modified;
        }

        public Integer getOrderType() {
            return orderType;
        }

        public void setOrderType(Integer orderType) {
            this.orderType = orderType;
        }

        public String getShopNo() {
            return shopNo;
        }

        public void setShopNo(String shopNo) {
            this.shopNo = shopNo;
        }

        public Integer getPicklistSeq() {
            return picklistSeq;
        }

        public void setPicklistSeq(Integer picklistSeq) {
            this.picklistSeq = picklistSeq;
        }

        public Integer getSeqNo() {
            return seqNo;
        }

        public void setSeqNo(Integer seqNo) {
            this.seqNo = seqNo;
        }

        public String getReceiverArea() {
            return receiverArea;
        }

        public void setReceiverArea(String receiverArea) {
            this.receiverArea = receiverArea;
        }

        public String getCustomerNo() {
            return customerNo;
        }

        public void setCustomerNo(String customerNo) {
            this.customerNo = customerNo;
        }

        public Long getCreated() {
            return created;
        }

        public void setCreated(Long created) {
            this.created = created;
        }

        public BigDecimal getWeight() {
            return weight;
        }

        public void setWeight(BigDecimal weight) {
            this.weight = weight;
        }

        public Integer getBlockReason() {
            return blockReason;
        }

        public void setBlockReason(Integer blockReason) {
            this.blockReason = blockReason;
        }

        public BigDecimal getTax() {
            return tax;
        }

        public void setTax(BigDecimal tax) {
            this.tax = tax;
        }

        public String getShopName() {
            return shopName;
        }

        public void setShopName(String shopName) {
            this.shopName = shopName;
        }

        public Long getPayTime() {
            return payTime;
        }

        public void setPayTime(Long payTime) {
            this.payTime = payTime;
        }

        public BigDecimal getGoodsTotalCost() {
            return goodsTotalCost;
        }

        public void setGoodsTotalCost(BigDecimal goodsTotalCost) {
            this.goodsTotalCost = goodsTotalCost;
        }

        public String getTradeNo() {
            return tradeNo;
        }

        public void setTradeNo(String tradeNo) {
            this.tradeNo = tradeNo;
        }

        public Integer getConsignStatus() {
            return consignStatus;
        }

        public void setConsignStatus(Integer consignStatus) {
            this.consignStatus = consignStatus;
        }

        public String getOrderNo() {
            return orderNo;
        }

        public void setOrderNo(String orderNo) {
            this.orderNo = orderNo;
        }

        public Integer getReceiverCity() {
            return receiverCity;
        }

        public void setReceiverCity(Integer receiverCity) {
            this.receiverCity = receiverCity;
        }

        public String getInvoiceTitle() {
            return invoiceTitle;
        }

        public void setInvoiceTitle(String invoiceTitle) {
            this.invoiceTitle = invoiceTitle;
        }

        public Integer getGoodsTypeCount() {
            return goodsTypeCount;
        }

        public void setGoodsTypeCount(Integer goodsTypeCount) {
            this.goodsTypeCount = goodsTypeCount;
        }

        public String getIdCard() {
            return idCard;
        }

        public void setIdCard(String idCard) {
            this.idCard = idCard;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public BigDecimal getCalcPostCost() {
            return calcPostCost;
        }

        public void setCalcPostCost(BigDecimal calcPostCost) {
            this.calcPostCost = calcPostCost;
        }

        public BigDecimal getCodAmount() {
            return codAmount;
        }

        public void setCodAmount(BigDecimal codAmount) {
            this.codAmount = codAmount;
        }

        public String getFlagName() {
            return flagName;
        }

        public void setFlagName(String flagName) {
            this.flagName = flagName;
        }

        public Integer getLogisticsId() {
            return logisticsId;
        }

        public void setLogisticsId(Integer logisticsId) {
            this.logisticsId = logisticsId;
        }

        public String getWarehouseNo() {
            return warehouseNo;
        }

        public void setWarehouseNo(String warehouseNo) {
            this.warehouseNo = warehouseNo;
        }

        public String getReceiverTelno() {
            return receiverTelno;
        }

        public void setReceiverTelno(String receiverTelno) {
            this.receiverTelno = receiverTelno;
        }

        public String getReceiverZip() {
            return receiverZip;
        }

        public void setReceiverZip(String receiverZip) {
            this.receiverZip = receiverZip;
        }

        public Integer getTradeStatus() {
            return tradeStatus;
        }

        public void setTradeStatus(Integer tradeStatus) {
            this.tradeStatus = tradeStatus;
        }

        public Integer getInvoiceType() {
            return invoiceType;
        }

        public void setInvoiceType(Integer invoiceType) {
            this.invoiceType = invoiceType;
        }

        public String getBatchNo() {
            return batchNo;
        }

        public void setBatchNo(String batchNo) {
            this.batchNo = batchNo;
        }

        public String getPackagerName() {
            return packagerName;
        }

        public void setPackagerName(String packagerName) {
            this.packagerName = packagerName;
        }

        public String getSalesmanNo() {
            return salesmanNo;
        }

        public void setSalesmanNo(String salesmanNo) {
            this.salesmanNo = salesmanNo;
        }

        public Integer getPlatformId() {
            return platformId;
        }

        public void setPlatformId(Integer platformId) {
            this.platformId = platformId;
        }

        public BigDecimal getPaid() {
            return paid;
        }

        public void setPaid(BigDecimal paid) {
            this.paid = paid;
        }

        public Integer getTradeType() {
            return tradeType;
        }

        public void setTradeType(Integer tradeType) {
            this.tradeType = tradeType;
        }

        public Integer getLogisticsPrintStatus() {
            return logisticsPrintStatus;
        }

        public void setLogisticsPrintStatus(Integer logisticsPrintStatus) {
            this.logisticsPrintStatus = logisticsPrintStatus;
        }

        public String getFullname() {
            return fullname;
        }

        public void setFullname(String fullname) {
            this.fullname = fullname;
        }

        public List<Logistics> getLogisticsList() {
            return logisticsList;
        }

        public void setLogisticsList(List<Logistics> logisticsList) {
            this.logisticsList = logisticsList;
        }
    }
}
