package com.cosfo.manage.wangdiantong.sdk.api.sales.dto;

import com.google.gson.annotations.SerializedName;

import java.math.BigDecimal;
import java.util.List;

public class TradeQueryHistoryResponse {

    @SerializedName("total_count")
    private Integer totalCount;
    @SerializedName("order")
    private List<Order> orders;

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public List<Order> getOrders() {
        return orders;
    }

    public void setOrders(List<Order> orders) {
        this.orders = orders;
    }

    public static class Order {
        @SerializedName("fenxiao_nick")
        private String fenxiaoNick;
        @SerializedName("post_amount")
        private BigDecimal postAmount;
        @SerializedName("estimate_consign_time")
        private String estimateConsignTime;
        @SerializedName("trade_time")
        private Long tradeTime;
        @SerializedName("receiver_ring")
        private String receiverRing;
        @SerializedName("remark_flag")
        private Integer remarkFlag;
        @SerializedName("detail_list")
        private List<Detail> detailList;
        @SerializedName("bad_reason")
        private Integer badReason;
        @SerializedName("discount")
        private BigDecimal discount;
        @SerializedName("package_id")
        private Integer packageId;
        @SerializedName("raw_goods_count")
        private BigDecimal rawGoodsCount;
        @SerializedName("fchecker_name")
        private String fcheckerName;
        @SerializedName("trade_id")
        private Integer tradeId;
        @SerializedName("trade_label")
        private String tradeLabel;
        @SerializedName("checker_name")
        private String checkerName;
        @SerializedName("large_type")
        private Integer largeType;
        @SerializedName("refund_status")
        private Integer refundStatus;
        @SerializedName("receiver_province")
        private Integer receiverProvince;
        @SerializedName("buyer_message")
        private String buyerMessage;
        @SerializedName("logistics_code")
        private String logisticsCode;
        @SerializedName("shop_platform_id")
        private Integer shopPlatformId;
        @SerializedName("trade_mask")
        private Integer tradeMask;
        @SerializedName("src_tids")
        private String srcTids;
        @SerializedName("shop_id")
        private Integer shopId;
        @SerializedName("checkouter_name")
        private String checkouterName;
        @SerializedName("id_card_type")
        private Integer idCardType;
        @SerializedName("freeze_reason")
        private String freezeReason;
        @SerializedName("single_spec_no")
        private String singleSpecNo;
        @SerializedName("sub_platform_id")
        private Integer subPlatformId;
        @SerializedName("goods_count")
        private BigDecimal goodsCount;
        @SerializedName("invoice_content")
        private String invoiceContent;
        @SerializedName("post_cost")
        private BigDecimal postCost;
        @SerializedName("commission")
        private Integer commission;
        @SerializedName("currency")
        private String currency;
        @SerializedName("trade_from")
        private Integer tradeFrom;
        @SerializedName("delivery_term")
        private Integer deliveryTerm;
        @SerializedName("gift_mask")
        private Integer giftMask;
        @SerializedName("logistics_no")
        private String logisticsNo;
        @SerializedName("goods_cost")
        private BigDecimal goodsCost;
        @SerializedName("receiver_district")
        private Integer receiverDistrict;
        @SerializedName("receivable")
        private Integer receivable;
        @SerializedName("cs_remark")
        private String csRemark;
        @SerializedName("ext_cod_fee")
        private BigDecimal extCodFee;
        @SerializedName("customer_id")
        private Integer customerId;
        @SerializedName("warehouse_id")
        private Integer warehouseId;
        @SerializedName("logistics_name")
        private String logisticsName;
        @SerializedName("warehouse_type")
        private Integer warehouseType;
        @SerializedName("consign_time")
        private Long consignTime;
        @SerializedName("raw_goods_type_count")
        private Integer rawGoodsTypeCount;
        @SerializedName("receiver_dtb")
        private String receiverDtb;
        @SerializedName("other_cost")
        private BigDecimal otherCost;
        @SerializedName("print_remark")
        private String printRemark;
        @SerializedName("other_amount")
        private BigDecimal otherAmount;
        @SerializedName("tax_rate")
        private BigDecimal taxRate;
        @SerializedName("to_deliver_time")
        private String toDeliverTime;
        @SerializedName("shop_remark")
        private String shopRemark;
        @SerializedName("invoice_id")
        private Integer invoiceId;
        @SerializedName("modified")
        private String modified;
        @SerializedName("shop_no")
        private String shopNo;
        @SerializedName("receiver_area")
        private String receiverArea;
        @SerializedName("customer_no")
        private String customerNo;
        @SerializedName("created")
        private Long created;
        @SerializedName("weight")
        private BigDecimal weight;
        @SerializedName("tax")
        private BigDecimal tax;
        @SerializedName("shop_name")
        private String shopName;
        @SerializedName("pay_time")
        private String payTime;
        @SerializedName("volume")
        private BigDecimal volume;
        @SerializedName("is_sealed")
        private Boolean isSealed;
        @SerializedName("trade_no")
        private String tradeNo;
        @SerializedName("check_time")
        private String checkTime;
        @SerializedName("customer_type")
        private Integer customerType;
        @SerializedName("salesman_name")
        private String salesmanName;
        @SerializedName("receiver_city")
        private Integer receiverCity;
        @SerializedName("invoice_title")
        private String invoiceTitle;
        @SerializedName("goods_type_count")
        private Integer goodsTypeCount;
        @SerializedName("cod_amount")
        private BigDecimal codAmount;
        @SerializedName("flag_name")
        private String flagName;
        @SerializedName("logistics_id")
        private Integer logisticsId;
        @SerializedName("receiver_zip")
        private String receiverZip;
        @SerializedName("warehouse_no")
        private String warehouseNo;
        @SerializedName("trade_status")
        private Integer tradeStatus;
        @SerializedName("invoice_type")
        private Integer invoiceType;
        @SerializedName("profit")
        private BigDecimal profit;
        @SerializedName("goods_amount")
        private BigDecimal goodsAmount;
        @SerializedName("stockout_no")
        private String stockoutNo;
        @SerializedName("version_id")
        private Integer versionId;
        @SerializedName("cancel_reason")
        private String cancelReason;
        @SerializedName("fenxiao_type")
        private Integer fenxiaoType;
        @SerializedName("revert_reason")
        private String revertReason;
        @SerializedName("platform_id")
        private Integer platformId;
        @SerializedName("package_name")
        private String packageName;
        @SerializedName("paid")
        private BigDecimal paid;
        @SerializedName("trade_type")
        private Integer tradeType;
        @SerializedName("logistics_type_name")
        private String logisticsTypeName;
        @SerializedName("created_date")
        private String createdDate;
        @SerializedName("delay_to_time")
        private String delayToTime;

        public String getFenxiaoNick() {
            return fenxiaoNick;
        }

        public void setFenxiaoNick(String fenxiaoNick) {
            this.fenxiaoNick = fenxiaoNick;
        }

        public BigDecimal getPostAmount() {
            return postAmount;
        }

        public void setPostAmount(BigDecimal postAmount) {
            this.postAmount = postAmount;
        }

        public String getEstimateConsignTime() {
            return estimateConsignTime;
        }

        public void setEstimateConsignTime(String estimateConsignTime) {
            this.estimateConsignTime = estimateConsignTime;
        }

        public Long getTradeTime() {
            return tradeTime;
        }

        public void setTradeTime(Long tradeTime) {
            this.tradeTime = tradeTime;
        }

        public String getReceiverRing() {
            return receiverRing;
        }

        public void setReceiverRing(String receiverRing) {
            this.receiverRing = receiverRing;
        }

        public Integer getRemarkFlag() {
            return remarkFlag;
        }

        public void setRemarkFlag(Integer remarkFlag) {
            this.remarkFlag = remarkFlag;
        }

        public List<Detail> getDetailList() {
            return detailList;
        }

        public void setDetailList(List<Detail> detailList) {
            this.detailList = detailList;
        }

        public Integer getBadReason() {
            return badReason;
        }

        public void setBadReason(Integer badReason) {
            this.badReason = badReason;
        }

        public BigDecimal getDiscount() {
            return discount;
        }

        public void setDiscount(BigDecimal discount) {
            this.discount = discount;
        }

        public Integer getPackageId() {
            return packageId;
        }

        public void setPackageId(Integer packageId) {
            this.packageId = packageId;
        }

        public BigDecimal getRawGoodsCount() {
            return rawGoodsCount;
        }

        public void setRawGoodsCount(BigDecimal rawGoodsCount) {
            this.rawGoodsCount = rawGoodsCount;
        }

        public String getFcheckerName() {
            return fcheckerName;
        }

        public void setFcheckerName(String fcheckerName) {
            this.fcheckerName = fcheckerName;
        }

        public Integer getTradeId() {
            return tradeId;
        }

        public void setTradeId(Integer tradeId) {
            this.tradeId = tradeId;
        }

        public String getTradeLabel() {
            return tradeLabel;
        }

        public void setTradeLabel(String tradeLabel) {
            this.tradeLabel = tradeLabel;
        }

        public String getCheckerName() {
            return checkerName;
        }

        public void setCheckerName(String checkerName) {
            this.checkerName = checkerName;
        }

        public Integer getLargeType() {
            return largeType;
        }

        public void setLargeType(Integer largeType) {
            this.largeType = largeType;
        }

        public Integer getRefundStatus() {
            return refundStatus;
        }

        public void setRefundStatus(Integer refundStatus) {
            this.refundStatus = refundStatus;
        }

        public Integer getReceiverProvince() {
            return receiverProvince;
        }

        public void setReceiverProvince(Integer receiverProvince) {
            this.receiverProvince = receiverProvince;
        }

        public String getBuyerMessage() {
            return buyerMessage;
        }

        public void setBuyerMessage(String buyerMessage) {
            this.buyerMessage = buyerMessage;
        }

        public String getLogisticsCode() {
            return logisticsCode;
        }

        public void setLogisticsCode(String logisticsCode) {
            this.logisticsCode = logisticsCode;
        }

        public Integer getShopPlatformId() {
            return shopPlatformId;
        }

        public void setShopPlatformId(Integer shopPlatformId) {
            this.shopPlatformId = shopPlatformId;
        }

        public Integer getTradeMask() {
            return tradeMask;
        }

        public void setTradeMask(Integer tradeMask) {
            this.tradeMask = tradeMask;
        }

        public String getSrcTids() {
            return srcTids;
        }

        public void setSrcTids(String srcTids) {
            this.srcTids = srcTids;
        }

        public Integer getShopId() {
            return shopId;
        }

        public void setShopId(Integer shopId) {
            this.shopId = shopId;
        }

        public String getCheckouterName() {
            return checkouterName;
        }

        public void setCheckouterName(String checkouterName) {
            this.checkouterName = checkouterName;
        }

        public Integer getIdCardType() {
            return idCardType;
        }

        public void setIdCardType(Integer idCardType) {
            this.idCardType = idCardType;
        }

        public String getFreezeReason() {
            return freezeReason;
        }

        public void setFreezeReason(String freezeReason) {
            this.freezeReason = freezeReason;
        }

        public String getSingleSpecNo() {
            return singleSpecNo;
        }

        public void setSingleSpecNo(String singleSpecNo) {
            this.singleSpecNo = singleSpecNo;
        }

        public Integer getSubPlatformId() {
            return subPlatformId;
        }

        public void setSubPlatformId(Integer subPlatformId) {
            this.subPlatformId = subPlatformId;
        }

        public BigDecimal getGoodsCount() {
            return goodsCount;
        }

        public void setGoodsCount(BigDecimal goodsCount) {
            this.goodsCount = goodsCount;
        }

        public String getInvoiceContent() {
            return invoiceContent;
        }

        public void setInvoiceContent(String invoiceContent) {
            this.invoiceContent = invoiceContent;
        }

        public BigDecimal getPostCost() {
            return postCost;
        }

        public void setPostCost(BigDecimal postCost) {
            this.postCost = postCost;
        }

        public Integer getCommission() {
            return commission;
        }

        public void setCommission(Integer commission) {
            this.commission = commission;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }

        public Integer getTradeFrom() {
            return tradeFrom;
        }

        public void setTradeFrom(Integer tradeFrom) {
            this.tradeFrom = tradeFrom;
        }

        public Integer getDeliveryTerm() {
            return deliveryTerm;
        }

        public void setDeliveryTerm(Integer deliveryTerm) {
            this.deliveryTerm = deliveryTerm;
        }

        public Integer getGiftMask() {
            return giftMask;
        }

        public void setGiftMask(Integer giftMask) {
            this.giftMask = giftMask;
        }

        public String getLogisticsNo() {
            return logisticsNo;
        }

        public void setLogisticsNo(String logisticsNo) {
            this.logisticsNo = logisticsNo;
        }

        public BigDecimal getGoodsCost() {
            return goodsCost;
        }

        public void setGoodsCost(BigDecimal goodsCost) {
            this.goodsCost = goodsCost;
        }

        public Integer getReceiverDistrict() {
            return receiverDistrict;
        }

        public void setReceiverDistrict(Integer receiverDistrict) {
            this.receiverDistrict = receiverDistrict;
        }

        public Integer getReceivable() {
            return receivable;
        }

        public void setReceivable(Integer receivable) {
            this.receivable = receivable;
        }

        public String getCsRemark() {
            return csRemark;
        }

        public void setCsRemark(String csRemark) {
            this.csRemark = csRemark;
        }

        public BigDecimal getExtCodFee() {
            return extCodFee;
        }

        public void setExtCodFee(BigDecimal extCodFee) {
            this.extCodFee = extCodFee;
        }

        public Integer getCustomerId() {
            return customerId;
        }

        public void setCustomerId(Integer customerId) {
            this.customerId = customerId;
        }

        public Integer getWarehouseId() {
            return warehouseId;
        }

        public void setWarehouseId(Integer warehouseId) {
            this.warehouseId = warehouseId;
        }

        public String getLogisticsName() {
            return logisticsName;
        }

        public void setLogisticsName(String logisticsName) {
            this.logisticsName = logisticsName;
        }

        public Integer getWarehouseType() {
            return warehouseType;
        }

        public void setWarehouseType(Integer warehouseType) {
            this.warehouseType = warehouseType;
        }

        public Long getConsignTime() {
            return consignTime;
        }

        public void setConsignTime(Long consignTime) {
            this.consignTime = consignTime;
        }

        public Integer getRawGoodsTypeCount() {
            return rawGoodsTypeCount;
        }

        public void setRawGoodsTypeCount(Integer rawGoodsTypeCount) {
            this.rawGoodsTypeCount = rawGoodsTypeCount;
        }

        public String getReceiverDtb() {
            return receiverDtb;
        }

        public void setReceiverDtb(String receiverDtb) {
            this.receiverDtb = receiverDtb;
        }

        public BigDecimal getOtherCost() {
            return otherCost;
        }

        public void setOtherCost(BigDecimal otherCost) {
            this.otherCost = otherCost;
        }

        public String getPrintRemark() {
            return printRemark;
        }

        public void setPrintRemark(String printRemark) {
            this.printRemark = printRemark;
        }

        public BigDecimal getOtherAmount() {
            return otherAmount;
        }

        public void setOtherAmount(BigDecimal otherAmount) {
            this.otherAmount = otherAmount;
        }

        public BigDecimal getTaxRate() {
            return taxRate;
        }

        public void setTaxRate(BigDecimal taxRate) {
            this.taxRate = taxRate;
        }

        public String getToDeliverTime() {
            return toDeliverTime;
        }

        public void setToDeliverTime(String toDeliverTime) {
            this.toDeliverTime = toDeliverTime;
        }

        public String getShopRemark() {
            return shopRemark;
        }

        public void setShopRemark(String shopRemark) {
            this.shopRemark = shopRemark;
        }

        public Integer getInvoiceId() {
            return invoiceId;
        }

        public void setInvoiceId(Integer invoiceId) {
            this.invoiceId = invoiceId;
        }

        public String getModified() {
            return modified;
        }

        public void setModified(String modified) {
            this.modified = modified;
        }

        public String getShopNo() {
            return shopNo;
        }

        public void setShopNo(String shopNo) {
            this.shopNo = shopNo;
        }

        public String getReceiverArea() {
            return receiverArea;
        }

        public void setReceiverArea(String receiverArea) {
            this.receiverArea = receiverArea;
        }

        public String getCustomerNo() {
            return customerNo;
        }

        public void setCustomerNo(String customerNo) {
            this.customerNo = customerNo;
        }

        public Long getCreated() {
            return created;
        }

        public void setCreated(Long created) {
            this.created = created;
        }

        public BigDecimal getWeight() {
            return weight;
        }

        public void setWeight(BigDecimal weight) {
            this.weight = weight;
        }

        public BigDecimal getTax() {
            return tax;
        }

        public void setTax(BigDecimal tax) {
            this.tax = tax;
        }

        public String getShopName() {
            return shopName;
        }

        public void setShopName(String shopName) {
            this.shopName = shopName;
        }

        public String getPayTime() {
            return payTime;
        }

        public void setPayTime(String payTime) {
            this.payTime = payTime;
        }

        public BigDecimal getVolume() {
            return volume;
        }

        public void setVolume(BigDecimal volume) {
            this.volume = volume;
        }

        public Boolean getSealed() {
            return isSealed;
        }

        public void setSealed(Boolean sealed) {
            isSealed = sealed;
        }

        public String getTradeNo() {
            return tradeNo;
        }

        public void setTradeNo(String tradeNo) {
            this.tradeNo = tradeNo;
        }

        public String getCheckTime() {
            return checkTime;
        }

        public void setCheckTime(String checkTime) {
            this.checkTime = checkTime;
        }

        public Integer getCustomerType() {
            return customerType;
        }

        public void setCustomerType(Integer customerType) {
            this.customerType = customerType;
        }

        public String getSalesmanName() {
            return salesmanName;
        }

        public void setSalesmanName(String salesmanName) {
            this.salesmanName = salesmanName;
        }

        public Integer getReceiverCity() {
            return receiverCity;
        }

        public void setReceiverCity(Integer receiverCity) {
            this.receiverCity = receiverCity;
        }

        public String getInvoiceTitle() {
            return invoiceTitle;
        }

        public void setInvoiceTitle(String invoiceTitle) {
            this.invoiceTitle = invoiceTitle;
        }

        public Integer getGoodsTypeCount() {
            return goodsTypeCount;
        }

        public void setGoodsTypeCount(Integer goodsTypeCount) {
            this.goodsTypeCount = goodsTypeCount;
        }

        public BigDecimal getCodAmount() {
            return codAmount;
        }

        public void setCodAmount(BigDecimal codAmount) {
            this.codAmount = codAmount;
        }

        public String getFlagName() {
            return flagName;
        }

        public void setFlagName(String flagName) {
            this.flagName = flagName;
        }

        public Integer getLogisticsId() {
            return logisticsId;
        }

        public void setLogisticsId(Integer logisticsId) {
            this.logisticsId = logisticsId;
        }

        public String getReceiverZip() {
            return receiverZip;
        }

        public void setReceiverZip(String receiverZip) {
            this.receiverZip = receiverZip;
        }

        public String getWarehouseNo() {
            return warehouseNo;
        }

        public void setWarehouseNo(String warehouseNo) {
            this.warehouseNo = warehouseNo;
        }

        public Integer getTradeStatus() {
            return tradeStatus;
        }

        public void setTradeStatus(Integer tradeStatus) {
            this.tradeStatus = tradeStatus;
        }

        public Integer getInvoiceType() {
            return invoiceType;
        }

        public void setInvoiceType(Integer invoiceType) {
            this.invoiceType = invoiceType;
        }

        public BigDecimal getProfit() {
            return profit;
        }

        public void setProfit(BigDecimal profit) {
            this.profit = profit;
        }

        public BigDecimal getGoodsAmount() {
            return goodsAmount;
        }

        public void setGoodsAmount(BigDecimal goodsAmount) {
            this.goodsAmount = goodsAmount;
        }

        public String getStockoutNo() {
            return stockoutNo;
        }

        public void setStockoutNo(String stockoutNo) {
            this.stockoutNo = stockoutNo;
        }

        public Integer getVersionId() {
            return versionId;
        }

        public void setVersionId(Integer versionId) {
            this.versionId = versionId;
        }

        public String getCancelReason() {
            return cancelReason;
        }

        public void setCancelReason(String cancelReason) {
            this.cancelReason = cancelReason;
        }

        public Integer getFenxiaoType() {
            return fenxiaoType;
        }

        public void setFenxiaoType(Integer fenxiaoType) {
            this.fenxiaoType = fenxiaoType;
        }

        public String getRevertReason() {
            return revertReason;
        }

        public void setRevertReason(String revertReason) {
            this.revertReason = revertReason;
        }

        public Integer getPlatformId() {
            return platformId;
        }

        public void setPlatformId(Integer platformId) {
            this.platformId = platformId;
        }

        public String getPackageName() {
            return packageName;
        }

        public void setPackageName(String packageName) {
            this.packageName = packageName;
        }

        public BigDecimal getPaid() {
            return paid;
        }

        public void setPaid(BigDecimal paid) {
            this.paid = paid;
        }

        public Integer getTradeType() {
            return tradeType;
        }

        public void setTradeType(Integer tradeType) {
            this.tradeType = tradeType;
        }

        public String getLogisticsTypeName() {
            return logisticsTypeName;
        }

        public void setLogisticsTypeName(String logisticsTypeName) {
            this.logisticsTypeName = logisticsTypeName;
        }

        public String getCreatedDate() {
            return createdDate;
        }

        public void setCreatedDate(String createdDate) {
            this.createdDate = createdDate;
        }

        public String getDelayToTime() {
            return delayToTime;
        }

        public void setDelayToTime(String delayToTime) {
            this.delayToTime = delayToTime;
        }

        public static class Detail {
            @SerializedName("rec_id")
            private Integer recId;
            @SerializedName("trade_id")
            private Integer tradeId;
            @SerializedName("platform_id")
            private Integer platformId;
            @SerializedName("src_oid")
            private String srcOid;
            @SerializedName("src_tid")
            private String srcTid;
            @SerializedName("gift_type")
            private Integer giftType;
            @SerializedName("refund_status")
            private Integer refundStatus;
            @SerializedName("guarantee_mode")
            private Integer guaranteeMode;
            @SerializedName("delivery_term")
            private Integer deliveryTerm;
            @SerializedName("num")
            private BigDecimal num;
            @SerializedName("price")
            private BigDecimal price;
            @SerializedName("refund_num")
            private BigDecimal refundNum;
            @SerializedName("order_price")
            private BigDecimal orderPrice;
            @SerializedName("share_price")
            private BigDecimal sharePrice;
            @SerializedName("adjust")
            private BigDecimal adjust;
            @SerializedName("discount")
            private BigDecimal discount;
            @SerializedName("share_amount")
            private BigDecimal shareAmount;
            @SerializedName("paid")
            private BigDecimal paid;
            @SerializedName("goods_name")
            private String goodsName;
            @SerializedName("tax_rate")
            private BigDecimal taxRate;
            @SerializedName("goods_no")
            private String goodsNo;
            @SerializedName("spec_name")
            private String specName;
            @SerializedName("spec_no")
            private String specNo;
            @SerializedName("spec_code")
            private String specCode;
            @SerializedName("suite_no")
            private String suiteNo;
            @SerializedName("suite_name")
            private String suiteName;
            @SerializedName("suite_num")
            private BigDecimal suiteNum;
            @SerializedName("suite_amount")
            private BigDecimal suiteAmount;
            @SerializedName("suite_discount")
            private BigDecimal suiteDiscount;
            @SerializedName("api_goods_name")
            private String apiGoodsName;
            @SerializedName("api_spec_name")
            private String apiSpecName;
            @SerializedName("commission")
            private BigDecimal commission;
            @SerializedName("goods_type")
            private Integer goodsType;
            @SerializedName("from_mask")
            private Integer fromMask;
            @SerializedName("remark")
            private String remark;
            @SerializedName("modified")
            private Long modified;
            @SerializedName("img_url")
            private String imgUrl;
            @SerializedName("platform_status")
            private Integer platformStatus;
            @SerializedName("created")
            private Long created;
            @SerializedName("prop2")
            private String prop2;
            @SerializedName("weight")
            private BigDecimal weight;
            @SerializedName("api_goods_id")
            private String apiGoodsId;
            @SerializedName("api_spec_id")
            private String apiSpecId;
            @SerializedName("goods_id")
            private Integer goodsId;
            @SerializedName("spec_id")
            private Integer specId;
            @SerializedName("prop1")
            private String prop1;
            @SerializedName("actual_num")
            private BigDecimal actualNum;
            @SerializedName("barcode")
            private String barcode;
            @SerializedName("suite_id")
            private Integer suiteId;
            @SerializedName("bind_oid")
            private String bindOid;
            @SerializedName("print_suite_mode")
            private Integer printSuiteMode;
            @SerializedName("flag")
            private Integer flag;
            @SerializedName("stock_state")
            private Integer stockState;
            @SerializedName("is_consigned")
            private Boolean isConsigned;
            @SerializedName("is_received")
            private Integer isReceived;
            @SerializedName("cid")
            private Integer cid;
            @SerializedName("share_post_price")
            private BigDecimal sharePostPrice;

            public Integer getRecId() {
                return recId;
            }

            public void setRecId(Integer recId) {
                this.recId = recId;
            }

            public Integer getTradeId() {
                return tradeId;
            }

            public void setTradeId(Integer tradeId) {
                this.tradeId = tradeId;
            }

            public Integer getPlatformId() {
                return platformId;
            }

            public void setPlatformId(Integer platformId) {
                this.platformId = platformId;
            }

            public String getSrcOid() {
                return srcOid;
            }

            public void setSrcOid(String srcOid) {
                this.srcOid = srcOid;
            }

            public String getSrcTid() {
                return srcTid;
            }

            public void setSrcTid(String srcTid) {
                this.srcTid = srcTid;
            }

            public Integer getGiftType() {
                return giftType;
            }

            public void setGiftType(Integer giftType) {
                this.giftType = giftType;
            }

            public Integer getRefundStatus() {
                return refundStatus;
            }

            public void setRefundStatus(Integer refundStatus) {
                this.refundStatus = refundStatus;
            }

            public Integer getGuaranteeMode() {
                return guaranteeMode;
            }

            public void setGuaranteeMode(Integer guaranteeMode) {
                this.guaranteeMode = guaranteeMode;
            }

            public Integer getDeliveryTerm() {
                return deliveryTerm;
            }

            public void setDeliveryTerm(Integer deliveryTerm) {
                this.deliveryTerm = deliveryTerm;
            }

            public BigDecimal getNum() {
                return num;
            }

            public void setNum(BigDecimal num) {
                this.num = num;
            }

            public BigDecimal getPrice() {
                return price;
            }

            public void setPrice(BigDecimal price) {
                this.price = price;
            }

            public BigDecimal getRefundNum() {
                return refundNum;
            }

            public void setRefundNum(BigDecimal refundNum) {
                this.refundNum = refundNum;
            }

            public BigDecimal getOrderPrice() {
                return orderPrice;
            }

            public void setOrderPrice(BigDecimal orderPrice) {
                this.orderPrice = orderPrice;
            }

            public BigDecimal getSharePrice() {
                return sharePrice;
            }

            public void setSharePrice(BigDecimal sharePrice) {
                this.sharePrice = sharePrice;
            }

            public BigDecimal getAdjust() {
                return adjust;
            }

            public void setAdjust(BigDecimal adjust) {
                this.adjust = adjust;
            }

            public BigDecimal getDiscount() {
                return discount;
            }

            public void setDiscount(BigDecimal discount) {
                this.discount = discount;
            }

            public BigDecimal getShareAmount() {
                return shareAmount;
            }

            public void setShareAmount(BigDecimal shareAmount) {
                this.shareAmount = shareAmount;
            }

            public BigDecimal getPaid() {
                return paid;
            }

            public void setPaid(BigDecimal paid) {
                this.paid = paid;
            }

            public String getGoodsName() {
                return goodsName;
            }

            public void setGoodsName(String goodsName) {
                this.goodsName = goodsName;
            }

            public BigDecimal getTaxRate() {
                return taxRate;
            }

            public void setTaxRate(BigDecimal taxRate) {
                this.taxRate = taxRate;
            }

            public String getGoodsNo() {
                return goodsNo;
            }

            public void setGoodsNo(String goodsNo) {
                this.goodsNo = goodsNo;
            }

            public String getSpecName() {
                return specName;
            }

            public void setSpecName(String specName) {
                this.specName = specName;
            }

            public String getSpecNo() {
                return specNo;
            }

            public void setSpecNo(String specNo) {
                this.specNo = specNo;
            }

            public String getSpecCode() {
                return specCode;
            }

            public void setSpecCode(String specCode) {
                this.specCode = specCode;
            }

            public String getSuiteNo() {
                return suiteNo;
            }

            public void setSuiteNo(String suiteNo) {
                this.suiteNo = suiteNo;
            }

            public String getSuiteName() {
                return suiteName;
            }

            public void setSuiteName(String suiteName) {
                this.suiteName = suiteName;
            }

            public BigDecimal getSuiteNum() {
                return suiteNum;
            }

            public void setSuiteNum(BigDecimal suiteNum) {
                this.suiteNum = suiteNum;
            }

            public BigDecimal getSuiteAmount() {
                return suiteAmount;
            }

            public void setSuiteAmount(BigDecimal suiteAmount) {
                this.suiteAmount = suiteAmount;
            }

            public BigDecimal getSuiteDiscount() {
                return suiteDiscount;
            }

            public void setSuiteDiscount(BigDecimal suiteDiscount) {
                this.suiteDiscount = suiteDiscount;
            }

            public String getApiGoodsName() {
                return apiGoodsName;
            }

            public void setApiGoodsName(String apiGoodsName) {
                this.apiGoodsName = apiGoodsName;
            }

            public String getApiSpecName() {
                return apiSpecName;
            }

            public void setApiSpecName(String apiSpecName) {
                this.apiSpecName = apiSpecName;
            }

            public BigDecimal getCommission() {
                return commission;
            }

            public void setCommission(BigDecimal commission) {
                this.commission = commission;
            }

            public Integer getGoodsType() {
                return goodsType;
            }

            public void setGoodsType(Integer goodsType) {
                this.goodsType = goodsType;
            }

            public Integer getFromMask() {
                return fromMask;
            }

            public void setFromMask(Integer fromMask) {
                this.fromMask = fromMask;
            }

            public String getRemark() {
                return remark;
            }

            public void setRemark(String remark) {
                this.remark = remark;
            }

            public Long getModified() {
                return modified;
            }

            public void setModified(Long modified) {
                this.modified = modified;
            }

            public String getImgUrl() {
                return imgUrl;
            }

            public void setImgUrl(String imgUrl) {
                this.imgUrl = imgUrl;
            }

            public Integer getPlatformStatus() {
                return platformStatus;
            }

            public void setPlatformStatus(Integer platformStatus) {
                this.platformStatus = platformStatus;
            }

            public Long getCreated() {
                return created;
            }

            public void setCreated(Long created) {
                this.created = created;
            }

            public String getProp2() {
                return prop2;
            }

            public void setProp2(String prop2) {
                this.prop2 = prop2;
            }

            public BigDecimal getWeight() {
                return weight;
            }

            public void setWeight(BigDecimal weight) {
                this.weight = weight;
            }

            public String getApiGoodsId() {
                return apiGoodsId;
            }

            public void setApiGoodsId(String apiGoodsId) {
                this.apiGoodsId = apiGoodsId;
            }

            public String getApiSpecId() {
                return apiSpecId;
            }

            public void setApiSpecId(String apiSpecId) {
                this.apiSpecId = apiSpecId;
            }

            public Integer getGoodsId() {
                return goodsId;
            }

            public void setGoodsId(Integer goodsId) {
                this.goodsId = goodsId;
            }

            public Integer getSpecId() {
                return specId;
            }

            public void setSpecId(Integer specId) {
                this.specId = specId;
            }

            public String getProp1() {
                return prop1;
            }

            public void setProp1(String prop1) {
                this.prop1 = prop1;
            }

            public BigDecimal getActualNum() {
                return actualNum;
            }

            public void setActualNum(BigDecimal actualNum) {
                this.actualNum = actualNum;
            }

            public String getBarcode() {
                return barcode;
            }

            public void setBarcode(String barcode) {
                this.barcode = barcode;
            }

            public Integer getSuiteId() {
                return suiteId;
            }

            public void setSuiteId(Integer suiteId) {
                this.suiteId = suiteId;
            }

            public String getBindOid() {
                return bindOid;
            }

            public void setBindOid(String bindOid) {
                this.bindOid = bindOid;
            }

            public Integer getPrintSuiteMode() {
                return printSuiteMode;
            }

            public void setPrintSuiteMode(Integer printSuiteMode) {
                this.printSuiteMode = printSuiteMode;
            }

            public Integer getFlag() {
                return flag;
            }

            public void setFlag(Integer flag) {
                this.flag = flag;
            }

            public Integer getStockState() {
                return stockState;
            }

            public void setStockState(Integer stockState) {
                this.stockState = stockState;
            }

            public Boolean getConsigned() {
                return isConsigned;
            }

            public void setConsigned(Boolean consigned) {
                isConsigned = consigned;
            }

            public Integer getIsReceived() {
                return isReceived;
            }

            public void setIsReceived(Integer isReceived) {
                this.isReceived = isReceived;
            }

            public Integer getCid() {
                return cid;
            }

            public void setCid(Integer cid) {
                this.cid = cid;
            }

            public BigDecimal getSharePostPrice() {
                return sharePostPrice;
            }

            public void setSharePostPrice(BigDecimal sharePostPrice) {
                this.sharePostPrice = sharePostPrice;
            }
        }
    }
}
