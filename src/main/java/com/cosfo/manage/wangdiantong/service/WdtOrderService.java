package com.cosfo.manage.wangdiantong.service;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.cosfo.manage.facade.ordercenter.OrderAddressQueryFacade;
import com.cosfo.manage.facade.ordercenter.OrderItemQueryFacade;
import com.cosfo.manage.facade.ordercenter.OrderQueryFacade;
import com.cosfo.manage.wangdiantong.sdk.Client;
import com.cosfo.manage.wangdiantong.sdk.api.sales.RawTradeAPI;
import com.cosfo.manage.wangdiantong.sdk.api.sales.dto.PushSelfRequest;
import com.cosfo.manage.wangdiantong.sdk.api.sales.dto.PushSelfResponse;
import com.cosfo.manage.wangdiantong.sdk.impl.ApiFactory;
import com.cosfo.manage.wangdiantong.sdk.impl.DefaultClient;
import com.cosfo.ordercenter.client.common.WarehouseTypeEnum;
import com.cosfo.ordercenter.client.resp.order.OrderAddressResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 旺店通订单api对接服务类
 *
 * @author: xiaowk
 * @time: 2024/6/25 下午3:32
 */
@Service
@Slf4j
public class WdtOrderService {

    /**
     * 接口账号，接口请求的公共参数appkey
     */
    @NacosValue(value = "${wdt.appKey:depiao3-test}", autoRefreshed = true)
    private String appKey;

    /**
     * 接口秘钥，接口请求的参数appsecret ，用于计算签名
     */
    @NacosValue(value = "${wdt.appSecret:e741d52ed:357572692283515062637953e4f490de}", autoRefreshed = true)
    private String appSecret;

    /**
     * 卖家账号，接口请求的公共参数sid
     */
    @NacosValue(value = "${wdt.sid:wdtapi3}", autoRefreshed = true)
    private String sid;

    /**
     * 店铺编号，接口请求时的业务参数shop_no
     */
    @NacosValue(value = "${wdt.shopNo:depiao3-test}", autoRefreshed = true)
    private String shopNo;

    @NacosValue(value = "${wdt.url:http://47.92.239.46/}", autoRefreshed = true)
    private String url;

    @NacosValue(value = "${wdt.tenantId:2}", autoRefreshed = true)
    private Long tenantIdForWdt;

    /**
     * 供应商id
     */
    @NacosValue(value = "${wdt.supplierId:3294}", autoRefreshed = true)
    private Long supplierId;

    @NacosValue(value = "${wdt.itemIds:21733,21735}", autoRefreshed = true)
    private List<Long> itemIds;

    @NacosValue(value = "${wdt.supplyPriceDiscount:0.86}", autoRefreshed = true)
    private String supplyPriceDiscount;


    @Resource
    private OrderQueryFacade orderQueryFacade;
    @Resource
    private OrderItemQueryFacade orderItemQueryFacade;
    @Resource
    private OrderAddressQueryFacade orderAddressQueryFacade;


    /**
     * 检查能否推送订单到旺店通
     * @param orderNo
     * @param tenantId
     * @param warehouseType
     * @return
     */
    public boolean canPushOrder(String orderNo, Long tenantId, Integer warehouseType){
        if (!WarehouseTypeEnum.PROPRIETARY.getCode().equals(warehouseType)) {
            log.error("orderNo={}, 不是无仓订单，不支持旺店通推单", orderNo);
            return false;
        }

        if (!tenantIdForWdt.equals(tenantId)) {
            log.warn("orderNo={}, tenantId={}, 未对接旺店通", orderNo, tenantId);
            return false;
        }

        return true;
    }


    /**
     * 根据订单号给旺店通推送订单
     * @param orderNo
     */
    public void pushOrder(String orderNo) {
        OrderResp orderResp = orderQueryFacade.queryByNo(orderNo);
        if (orderResp == null) {
            log.error("orderNo={}, 订单不存在", orderNo);
            return;
        }

        if(!canPushOrder(orderNo, orderResp.getTenantId(), orderResp.getWarehouseType())){
            return;
        }

        OrderAddressResp orderAddressResp = orderAddressQueryFacade.queryByOrderId(orderResp.getTenantId(), orderResp.getId());
        if (orderAddressResp == null) {
            log.error("orderNo={}, 下单地址信息不存在", orderNo);
            return;
        }

        // 查询订单项快照信息
        List<OrderItemAndSnapshotResp> orderItemAndSnapshotResps = orderItemQueryFacade.queryByOrderId(orderResp.getId());
        if (CollectionUtils.isEmpty(orderItemAndSnapshotResps)) {
            log.error("orderNo={}, 订单项快照信息不存在", orderNo);
            return;
        }

        orderItemAndSnapshotResps = orderItemAndSnapshotResps.stream()
                .filter(e -> supplierId.equals(e.getSupplierTenantId()) && itemIds.contains(e.getItemId()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(orderItemAndSnapshotResps)) {
            log.error("orderNo={}, 订单项快照信息不存在，目标供应商supplierId={}, 商品itemIds={}", orderNo, supplierId, itemIds);
            return;
        }


        List<PushSelfRequest.RawTrade> rawTradeList = new ArrayList<>();
        List<PushSelfRequest.RawTradeOrder> tradeOrderList = new ArrayList<>();
        List<PushSelfRequest.RawTradeDiscount> tradeDiscountList = new ArrayList<>();

        buildRequest(rawTradeList, tradeOrderList, tradeDiscountList, orderResp, orderItemAndSnapshotResps, orderAddressResp);
        Client client = DefaultClient.get(sid, url, appKey, appSecret);
        RawTradeAPI rawTradeAPI = ApiFactory.get(client, RawTradeAPI.class);
        PushSelfResponse response = rawTradeAPI.pushSelf(shopNo, rawTradeList, tradeOrderList, tradeDiscountList);
    }


    public void buildRequest(List<PushSelfRequest.RawTrade> rawTradeList,
                             List<PushSelfRequest.RawTradeOrder> tradeOderList,
                             List<PushSelfRequest.RawTradeDiscount> tradeDiscountList,
                             OrderResp orderResp,
                             List<OrderItemAndSnapshotResp> orderItemAndSnapshotResps,
                             OrderAddressResp orderAddressResp) {

        PushSelfRequest.RawTrade rawTrade = new PushSelfRequest.RawTrade();
        rawTrade.setAutoWms(false);
        // rawTrade.setWarehouseNo("pos_inner"); // 非自流转订单不需要设置此字段.

        // 邮费
        rawTrade.setPostAmount(BigDecimal.valueOf(0.0000D));
        // 其他费用
        rawTrade.setOtherAmount(BigDecimal.valueOf(0.0000D));

        // 优惠金额
        rawTrade.setDiscount(BigDecimal.valueOf(0.0000D));
        // 平台费用
        rawTrade.setPlatformCost(BigDecimal.valueOf(0.0000D));
        // 货到付款金额, 若delivery_term=2，则为应付金额，否则则为0
        rawTrade.setCodAmount(BigDecimal.valueOf(0.0000D));
        // 已从平台收款的金额
        rawTrade.setReceived(BigDecimal.valueOf(0.0000D));

        rawTrade.setTid(orderResp.getOrderNo());
        // 原始单包含的子订单数，rawTradeOrderList节点下所有子单数之和
        rawTrade.setOrderCount(orderItemAndSnapshotResps.size());

        rawTrade.setProcessStatus(PushSelfRequest.RawTrade.PROCESS_STATUS_WAIT_DELIVERY);
        rawTrade.setTradeStatus(PushSelfRequest.RawTrade.TRADE_STATUS_WAIT_CONSIGN);
        rawTrade.setRefundStatus(PushSelfRequest.RawTrade.REFUND_NONE);
        rawTrade.setPayStatus(PushSelfRequest.RawTrade.PAY_STATUS_FULL_PAID);
        // 1在线转帐
        // 2现金，3银行转账，4邮局汇款
        // 5预付款
        // 6刷卡
        rawTrade.setPayMethod(PushSelfRequest.RawTrade.PAY_METHOD_ONLINE);

        rawTrade.setTradeTime(LocalDateTimeUtil.format(orderResp.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
        rawTrade.setPayTime(LocalDateTimeUtil.format(orderResp.getPayTime(), "yyyy-MM-dd HH:mm:ss"));
        rawTrade.setEndTime(LocalDateTimeUtil.format(orderResp.getPayTime(), "yyyy-MM-dd HH:mm:ss"));

        // 买家昵称/客户网名
        rawTrade.setBuyerNick(orderAddressResp.getContactName());
        // 买家备注
        rawTrade.setBuyerMessage(orderResp.getRemark());
        // 买家邮箱
//        rawTrade.setBuyerEmail("<EMAIL>");
        // 买家地区
//        rawTrade.setBuyerArea("test_area");


        StringBuilder areaSb = new StringBuilder();
        areaSb.append(orderAddressResp.getProvince()).append(" ")
                .append(orderAddressResp.getCity()).append(" ")
                .append(orderAddressResp.getArea());

        // 收件人姓名
        rawTrade.setReceiverName(orderAddressResp.getContactName());
        // 收件人手机
        rawTrade.setReceiverMobile(orderAddressResp.getContactPhone());
        rawTrade.setReceiverTelno("");
        // 省市区空格分隔，示例【北京 北京市 朝阳区】，
        rawTrade.setReceiverArea(areaSb.toString());
        rawTrade.setReceiverAddress(orderAddressResp.getAddress());
        // 收件人邮编，若无则传‘’
        rawTrade.setReceiverZip("");

        rawTrade.setInvoiceType(PushSelfRequest.RawTrade.INVOICE_TYPE_NONE);
        rawTrade.setInvoiceTitle("");
        rawTrade.setInvoiceContent("");

        // rawTrade.logistics_type=-1;
        rawTrade.setConsignInterval(0);
        rawTrade.setDeliveryTerm(PushSelfRequest.RawTrade.DELIVERY_TERM_DAP);
        rawTrade.setToDeliverTime("");
        rawTrade.setPayId("");
        rawTrade.setPayAccount("");
        rawTrade.setRemark("");
        rawTrade.setRemarkFlag(0);

        for (OrderItemAndSnapshotResp orderItemAndSnapshotResp : orderItemAndSnapshotResps) {
            PushSelfRequest.RawTradeOrder tradeOrder = new PushSelfRequest.RawTradeOrder();

            tradeOrder.setTid(rawTrade.getTid());
            tradeOrder.setOid(rawTrade.getTid() + "_" + orderItemAndSnapshotResp.getItemId());
            tradeOrder.setOrderType(PushSelfRequest.RawTradeOrder.ORDER_TYPE_NORMAL);

            tradeOrder.setStatus(PushSelfRequest.RawTradeOrder.PLATFORM_STATUS);
            tradeOrder.setRefundStatus(PushSelfRequest.RawTradeOrder.REFUND_STATUS_NONE);

            // 平台货品ID
            tradeOrder.setGoodsId(String.valueOf(orderItemAndSnapshotResp.getItemId()));
            // 平台规格ID
            tradeOrder.setSpecId(orderItemAndSnapshotResp.getSpecification());
            tradeOrder.setGoodsNo(orderItemAndSnapshotResp.getItemCode());
            tradeOrder.setSpecNo(orderItemAndSnapshotResp.getItemCode());
            // 货品名称
            tradeOrder.setGoodsName(orderItemAndSnapshotResp.getTitle());

            // 数量
            tradeOrder.setNum(BigDecimal.valueOf(orderItemAndSnapshotResp.getAmount()));
            // 单价
            tradeOrder.setPrice(orderItemAndSnapshotResp.getSupplyPrice().multiply(new BigDecimal(supplyPriceDiscount)));
            // 手工调整的优惠金额
            tradeOrder.setAdjustAmount(BigDecimal.valueOf(0.0000D));
            // 退款金额
            tradeOrder.setRefundAmount(BigDecimal.valueOf(0.0000D));
            tradeOrder.setDiscount(BigDecimal.valueOf(0.0000D)); // 优惠金额
            tradeOrder.setShareDiscount(BigDecimal.valueOf(0.0000D));// 分摊优惠金额
            // 总价格
            tradeOrder.setTotalAmount(NumberUtil.mul(tradeOrder.getNum(), tradeOrder.getPrice()));
            // $tradeOrder->total_amount - $tradeOrder->share_discount ;

            tradeOrder.setCid("");
            tradeOrder.setRemark("");
            tradeOrder.setJson("");

            // 处理主单部分数据 货品总数量
            rawTrade.setGoodsCount(rawTrade.getGoodsCount().add(tradeOrder.getNum()));
            // 应收金额
            rawTrade.setReceivable(rawTrade.getReceivable().add(tradeOrder.getTotalAmount()));
            // rawTrade.receivable += tradeOrder.getShareAmount();
            tradeOderList.add(tradeOrder);
        }

        // 优惠信息 不需要优惠明细可不传,优惠明细显示在订单管理界面的优惠明细Tab页
//        for (int k = 0; k < tradeOderList.size(); k++) {
//            PushSelfRequest.RawTradeDiscount tradeDiscount = new PushSelfRequest.RawTradeDiscount();
//
//            tradeDiscount.setTid(rawTrade.getTid());
//            tradeDiscount.setOid(tradeOderList.get(k).getOid());
//            tradeDiscount.setType("api_type_" + k);
//            tradeDiscount.setSn("api_sn_20201120" + k);
//            tradeDiscount.setName("api_discount_" + k);
//            tradeDiscount.setIsBonus(Byte.valueOf((byte) 0));
//            tradeDiscount.setDetail("raw_discount");
//            tradeDiscount.setAmount(BigDecimal.valueOf(0.20000D));
//
//            tradeDiscountList.add(tradeDiscount);
//        }

        rawTradeList.add(rawTrade);
    }


}
