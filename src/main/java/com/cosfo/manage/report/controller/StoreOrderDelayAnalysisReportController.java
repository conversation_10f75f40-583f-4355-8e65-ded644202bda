package com.cosfo.manage.report.controller;

import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.product.model.vo.StoreOrderDelayAnalysisVO;
import com.cosfo.manage.report.model.dto.StoreOrderDelayAnalysisInput;
import com.cosfo.manage.report.service.StoreOrderDelayAnalysisReportService;
import com.github.pagehelper.PageInfo;
import net.xianmu.authentication.common.aspect.TenantPrivilegesPermission;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 数据-商品分析-门店滞叫分析
 *
 * @author: xiaowk
 * @date: 2023/10/30 下午1:47
 */
@RestController
@RequestMapping("/store/order/delay-analysis")
public class StoreOrderDelayAnalysisReportController extends BaseController {

    @Resource
    private StoreOrderDelayAnalysisReportService storeOrderDelayAnalysisReportService;

    /**
     * 查询门店滞叫分析表
     *
     * @return
     */
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:delay-calls-analysis:query", expireError = true)
    @PostMapping("/query/list")
    public CommonResult<PageInfo<StoreOrderDelayAnalysisVO>> queryList(@RequestBody @Valid StoreOrderDelayAnalysisInput storeOrderDelayAnalysisInput) {
        storeOrderDelayAnalysisInput.setTenantId(getMerchantInfoDTO().getTenantId());
        return CommonResult.ok(storeOrderDelayAnalysisReportService.queryList(storeOrderDelayAnalysisInput));
    }

    /**
     * 导出门店滞叫分析表
     *
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/export/list")
    public CommonResult<Long> exportList(@RequestBody StoreOrderDelayAnalysisInput storeOrderDelayAnalysisInput) {
        storeOrderDelayAnalysisInput.setTenantId(getMerchantInfoDTO().getTenantId());
        Long resId = storeOrderDelayAnalysisReportService.exportList(storeOrderDelayAnalysisInput);
        return CommonResult.ok(resId);
    }

}
