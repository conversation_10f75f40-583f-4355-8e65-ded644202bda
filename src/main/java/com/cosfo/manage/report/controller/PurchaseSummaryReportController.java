package com.cosfo.manage.report.controller;

import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.report.model.dto.PurchaseSummarySkuQueryDTO;
import com.cosfo.manage.report.model.dto.PurchaseSummarySupplierQueryDTO;
import com.cosfo.manage.report.model.vo.PurchaseSummarySkuAggVO;
import com.cosfo.manage.report.model.vo.PurchaseSummarySkuVO;
import com.cosfo.manage.report.model.vo.PurchaseSummarySupplierAggVO;
import com.cosfo.manage.report.model.vo.PurchaseSummarySupplierVO;
import com.cosfo.manage.report.service.PurchaseSummaryReportService;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 采购汇总报表
 *
 * @author: xiaowk
 * @date: 2024/1/17 下午4:24
 */
@RestController
@RequestMapping("/purchase/summary")
public class PurchaseSummaryReportController extends BaseController {

    @Resource
    private PurchaseSummaryReportService purchaseSummaryReportService;

    /**
     * 采购汇总 - 商品维度 合计
     *
     * @param purchaseSummarySkuQueryDTO
     * @return
     */
    @PostMapping("/query/sku-agg")
    public CommonResult<PurchaseSummarySkuAggVO> queryPurchaseSummaryAggBySku(@RequestBody PurchaseSummarySkuQueryDTO purchaseSummarySkuQueryDTO) {
        purchaseSummarySkuQueryDTO.setTenantId(getMerchantInfoDTO().getTenantId());
        return CommonResult.ok(purchaseSummaryReportService.queryPurchaseSummaryAggBySku(purchaseSummarySkuQueryDTO));
    }



    /**
     * 采购汇总 - 商品维度 列表
     *
     * @param purchaseSummarySkuQueryDTO
     * @return
     */
    @PostMapping("/query/sku-list-all")
    public CommonResult<PageInfo<PurchaseSummarySkuVO>> queryPurchaseSummaryListBySku(@RequestBody PurchaseSummarySkuQueryDTO purchaseSummarySkuQueryDTO) {
        purchaseSummarySkuQueryDTO.setTenantId(getMerchantInfoDTO().getTenantId());

        return CommonResult.ok(purchaseSummaryReportService.queryPurchaseSummaryListBySku(purchaseSummarySkuQueryDTO));
    }

    /**
     * 采购汇总 - 商品维度 导出
     *
     * @param purchaseSummarySkuQueryDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/export/export-sku")
    public CommonResult exportSku(@RequestBody PurchaseSummarySkuQueryDTO purchaseSummarySkuQueryDTO) {
        purchaseSummarySkuQueryDTO.setTenantId(getMerchantInfoDTO().getTenantId());
        Long resId = purchaseSummaryReportService.exportSku(purchaseSummarySkuQueryDTO);
        return CommonResult.ok(resId);
    }


    /**
     * 采购汇总 - 供应商维度 合计
     *
     * @param purchaseSummarySupplierQueryDTO
     * @return
     */
    @PostMapping("/query/supplier-agg")
    public CommonResult<PurchaseSummarySupplierAggVO> queryPurchaseSummaryAggBySupplier(@RequestBody PurchaseSummarySupplierQueryDTO purchaseSummarySupplierQueryDTO) {
        purchaseSummarySupplierQueryDTO.setTenantId(getMerchantInfoDTO().getTenantId());

        return CommonResult.ok(purchaseSummaryReportService.queryPurchaseSummaryAggBySupplier(purchaseSummarySupplierQueryDTO));
    }

    /**
     * 采购汇总 - 供应商维度 列表
     *
     * @param purchaseSummarySupplierQueryDTO
     * @return
     */
    @PostMapping("/query/supplier-list-all")
    public CommonResult<PageInfo<PurchaseSummarySupplierVO>> queryPurchaseSummaryListBySupplier(@RequestBody PurchaseSummarySupplierQueryDTO purchaseSummarySupplierQueryDTO) {
        purchaseSummarySupplierQueryDTO.setTenantId(getMerchantInfoDTO().getTenantId());

        return CommonResult.ok(purchaseSummaryReportService.queryPurchaseSummaryListBySupplier(purchaseSummarySupplierQueryDTO));
    }

    /**
     * 采购汇总 - 供应商维度 导出
     *
     * @param purchaseSummarySupplierQueryDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/export/export-supplier")
    public CommonResult exportSupplier(@RequestBody PurchaseSummarySupplierQueryDTO purchaseSummarySupplierQueryDTO) {
        purchaseSummarySupplierQueryDTO.setTenantId(getMerchantInfoDTO().getTenantId());
        Long resId = purchaseSummaryReportService.exportSupplier(purchaseSummarySupplierQueryDTO);
        return CommonResult.ok(resId);
    }
}
