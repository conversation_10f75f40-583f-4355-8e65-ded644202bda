package com.cosfo.manage.report.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.report.model.po.OrderAfterSaleInvertedSummary;
import org.apache.ibatis.session.ResultHandler;

import java.time.LocalDateTime;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/25
 */
public interface OrderAfterSaleInvertedSummaryRepository extends IService<OrderAfterSaleInvertedSummary> {
    /**
     * 根据条件查询
     *
     * @param tenantId
     * @param startTime
     * @param endTime
     * @param resultHandler
     */
    void queryByConditionWithHandler(Long tenantId, Long supplierId, LocalDateTime startTime, LocalDateTime endTime, ResultHandler<?> resultHandler);
}
