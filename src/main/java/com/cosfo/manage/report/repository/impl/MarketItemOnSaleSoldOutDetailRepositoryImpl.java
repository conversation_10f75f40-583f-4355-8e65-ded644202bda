package com.cosfo.manage.report.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.market.model.po.MarketItemOnSaleSoldOutDetail;
import com.cosfo.manage.report.mapper.MarketItemOnSaleSoldOutDetailMapper;
import com.cosfo.manage.report.repository.MarketItemOnSaleSoldOutDetailRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 商品上架售罄汇总表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
@Service
public class MarketItemOnSaleSoldOutDetailRepositoryImpl extends ServiceImpl<MarketItemOnSaleSoldOutDetailMapper, MarketItemOnSaleSoldOutDetail> implements MarketItemOnSaleSoldOutDetailRepository {

    @Resource
    private MarketItemOnSaleSoldOutDetailMapper mapper;

    @Override
    public MarketItemOnSaleSoldOutDetail querySummary(Long tenantId, String startTime, String endTime) {
        return mapper.querySummary(tenantId, startTime, endTime);
    }

    @Override
    public List<MarketItemOnSaleSoldOutDetail> selectByTenantIdAndTimeTag(Long tenantId, String startTime, String endTime) {

        return this.lambdaQuery()
                .eq(MarketItemOnSaleSoldOutDetail::getTenantId, tenantId)
                .ge(MarketItemOnSaleSoldOutDetail::getTimeTag, startTime)
                .le(MarketItemOnSaleSoldOutDetail::getTimeTag, endTime)
                .orderByDesc(MarketItemOnSaleSoldOutDetail::getSoldOutTime)
                .list();
    }
}
