package com.cosfo.manage.report.repository;

import com.cosfo.manage.report.model.po.SupplierMetricsSummary;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 供应商指标汇总 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
public interface SupplierMetricsSummaryRepository extends IService<SupplierMetricsSummary> {

    /**
     * 根据时间标签查询
     * @param tenantId 租户id
     * @param timeTag 时间标签
     * @return
     */
    List<SupplierMetricsSummary> listByTenantIdAndTimeTag(Long tenantId, String timeTag, String sortField);
}
