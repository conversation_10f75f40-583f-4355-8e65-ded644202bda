package com.cosfo.manage.report.repository;

import com.cosfo.manage.merchant.model.po.MerchantStorePurchaseActivityDetail;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 门店采购活跃明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
public interface MerchantStorePurchaseActivityDetailRepository extends IService<MerchantStorePurchaseActivityDetail> {

    List<MerchantStorePurchaseActivityDetail> selectByTenantIdAndTimeTag(Long tenantId, String startTime, String endTime);

}
