package com.cosfo.manage.report.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.report.mapper.OrderItemStatementAnalysisMapper;
import com.cosfo.manage.report.model.dto.OrderItemStatementAnalysisInput;
import com.cosfo.manage.report.model.po.OrderItemStatementAnalysis;
import com.cosfo.manage.report.repository.OrderItemStatementAnalysisRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <p>
 * 订单明细对账分析 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-26
 */
@Service
public class OrderItemStatementAnalysisRepositoryImpl extends ServiceImpl<OrderItemStatementAnalysisMapper, OrderItemStatementAnalysis> implements OrderItemStatementAnalysisRepository {


    @Override
    public Page<OrderItemStatementAnalysis> queryListByPage(OrderItemStatementAnalysisInput input) {
        LambdaQueryWrapper<OrderItemStatementAnalysis> reportLambdaQueryWrapper = new LambdaQueryWrapper<>();
        reportLambdaQueryWrapper.eq(OrderItemStatementAnalysis::getTenantId, input.getTenantId());
        reportLambdaQueryWrapper.eq(StringUtils.isNotBlank(input.getStoreNo()), OrderItemStatementAnalysis::getStoreNo, input.getStoreNo());
        reportLambdaQueryWrapper.like(StringUtils.isNotBlank(input.getStoreName()), OrderItemStatementAnalysis::getStoreName, input.getStoreName());

        reportLambdaQueryWrapper.eq(StringUtils.isNotBlank(input.getStoreNo()), OrderItemStatementAnalysis::getStoreNo, input.getStoreNo());
        reportLambdaQueryWrapper.like(StringUtils.isNotBlank(input.getStoreName()), OrderItemStatementAnalysis::getStoreName, input.getStoreName());
        reportLambdaQueryWrapper.in(!CollectionUtils.isEmpty(input.getStoreIds()), OrderItemStatementAnalysis::getStoreId, input.getStoreIds());
        reportLambdaQueryWrapper.in(!CollectionUtils.isEmpty(input.getMerchantStoreGroupIds()), OrderItemStatementAnalysis::getStoreGroupId, input.getMerchantStoreGroupIds());
        reportLambdaQueryWrapper.eq(StringUtils.isNotBlank(input.getOrderNo()), OrderItemStatementAnalysis::getOrderNo, input.getOrderNo());
        reportLambdaQueryWrapper.eq(input.getOrderSource() != null, OrderItemStatementAnalysis::getOrderSource, input.getOrderSource());
        reportLambdaQueryWrapper.eq(input.getPayType() != null, OrderItemStatementAnalysis::getPayType, input.getPayType());
        reportLambdaQueryWrapper.eq(input.getItemId() != null, OrderItemStatementAnalysis::getItemId, input.getItemId());
        reportLambdaQueryWrapper.eq(input.getSkuId() != null, OrderItemStatementAnalysis::getSkuId, input.getSkuId());
        reportLambdaQueryWrapper.eq(input.getWarehouseType() != null, OrderItemStatementAnalysis::getWarehouseType, input.getWarehouseType());
        reportLambdaQueryWrapper.eq(input.getWarehouseNo() != null, OrderItemStatementAnalysis::getWarehouseNo, input.getWarehouseNo());
        reportLambdaQueryWrapper.eq(input.getSupplierId() != null, OrderItemStatementAnalysis::getSupplierId, input.getSupplierId());
        reportLambdaQueryWrapper.eq(StringUtils.isNotBlank(input.getOutboundBatchNo()), OrderItemStatementAnalysis::getOutboundBatchNo, input.getOutboundBatchNo());
        reportLambdaQueryWrapper.eq(StringUtils.isNotBlank(input.getPaymentNo()), OrderItemStatementAnalysis::getPaymentNo, input.getPaymentNo());
        reportLambdaQueryWrapper.eq(StringUtils.isNotBlank(input.getTransactionId()), OrderItemStatementAnalysis::getTransactionId, input.getTransactionId());

        if(input.getTimeQueryType() != null && input.getStartTime() != null && input.getEndTime() != null) {
            if(input.getTimeQueryType() == 1){
                reportLambdaQueryWrapper.between(OrderItemStatementAnalysis::getOrderTime, input.getStartTime(), input.getEndTime());
            }else if(input.getTimeQueryType() == 2){
                reportLambdaQueryWrapper.between(OrderItemStatementAnalysis::getPayTime, input.getStartTime(), input.getEndTime());
            }else if(input.getTimeQueryType() == 3){
                reportLambdaQueryWrapper.between(OrderItemStatementAnalysis::getDeliveryTime, input.getStartTime(), input.getEndTime());
            }else if(input.getTimeQueryType() == 4){
                reportLambdaQueryWrapper.between(OrderItemStatementAnalysis::getFinishedTime, input.getStartTime(), input.getEndTime());
            }else if(input.getTimeQueryType() == 5){
                reportLambdaQueryWrapper.between(OrderItemStatementAnalysis::getOutboundTime, input.getStartTime(), input.getEndTime());
            }
        }

        reportLambdaQueryWrapper.orderByDesc(OrderItemStatementAnalysis::getOrderNo);
        Page<OrderItemStatementAnalysis> page = page(new Page<>(input.getPageIndex(), input.getPageSize()), reportLambdaQueryWrapper);
        return page;
    }
}
