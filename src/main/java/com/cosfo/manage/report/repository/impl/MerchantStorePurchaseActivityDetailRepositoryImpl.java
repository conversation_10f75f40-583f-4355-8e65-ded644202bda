package com.cosfo.manage.report.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.merchant.model.po.MerchantStorePurchaseActivityDetail;
import com.cosfo.manage.report.mapper.MerchantStorePurchaseActivityDetailMapper;
import com.cosfo.manage.report.repository.MerchantStorePurchaseActivityDetailRepository;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 门店采购活跃明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
@Service
public class MerchantStorePurchaseActivityDetailRepositoryImpl extends ServiceImpl<MerchantStorePurchaseActivityDetailMapper, MerchantStorePurchaseActivityDetail> implements MerchantStorePurchaseActivityDetailRepository {

    @Override
    public List<MerchantStorePurchaseActivityDetail> selectByTenantIdAndTimeTag(Long tenantId, String startTime, String endTime) {

        return this.lambdaQuery()
                .eq(MerchantStorePurchaseActivityDetail::getTenantId, tenantId)
                .ge(MerchantStorePurchaseActivityDetail::getTimeTag, startTime)
                .le(MerchantStorePurchaseActivityDetail::getTimeTag, endTime)
                .orderByDesc(MerchantStorePurchaseActivityDetail::getStoreId)
                .list();
    }
}