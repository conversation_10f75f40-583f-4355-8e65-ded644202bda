package com.cosfo.manage.report.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.bill.model.dto.TenantBillQueryDTO;
import com.cosfo.manage.report.model.dto.OrderPaymentStatistics;
import com.cosfo.manage.report.model.dto.OrderSummaryDTO;
import com.cosfo.manage.report.model.po.OrderItemDetailSummary;
import org.apache.ibatis.session.ResultHandler;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 订单明细详情汇总 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
public interface OrderItemDetailSummaryRepository extends IService<OrderItemDetailSummary> {

    /**
     * 根据起止日查询
     * @param tenantId
     * @param supplierId
     * @param startTime
     * @param endTime
     * @return
     */
    void queryByConditionWithHandler(Long tenantId, Long supplierId, LocalDateTime startTime, LocalDateTime endTime, Integer goodsType, ResultHandler<?> resultHandler);

    /**
     * 查询去重itemCount
     * @param tenantId
     * @param startTime
     * @param endTime
     * @return
     */
    Integer queryItemCount(Long tenantId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询支付数 - 门店
     * @param tenantId
     * @param startTime
     * @param endTime
     * @param payType
     * @return
     */
    Integer queryStoreCountByPayType(Long tenantId, LocalDateTime startTime, LocalDateTime endTime, Integer payType);


    /**
     * 查询存储数
     *
     * @param tenantBillQueryDTO 租户账单查询dto
     * @return {@link List}<{@link OrderPaymentStatistics}>
     */
    List<OrderPaymentStatistics> queryStoreCounts(TenantBillQueryDTO tenantBillQueryDTO);


    /**
     * 查询汇总
     *
     * @param tenantBillQueryDTO 租户账单查询dto
     * @return {@link OrderSummaryDTO}
     */
    OrderSummaryDTO querySummary(TenantBillQueryDTO tenantBillQueryDTO);

    /**
     * 根据条件查询
     * @param tenantId
     * @param supplierId
     * @param goodsType
     * @param orderNos
     * @return
     */
    List<OrderItemDetailSummary> queryByCondition(Long tenantId, Long supplierId, Integer goodsType, List<String> orderNos);
}
