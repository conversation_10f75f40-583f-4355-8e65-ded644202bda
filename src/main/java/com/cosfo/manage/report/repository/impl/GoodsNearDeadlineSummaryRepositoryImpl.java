package com.cosfo.manage.report.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.report.mapper.GoodsNearDeadlineSummaryMapper;
import com.cosfo.manage.report.model.po.GoodsNearDeadlineSummary;
import com.cosfo.manage.report.repository.GoodsNearDeadlineSummaryRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 临期货品汇总 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@Service
public class GoodsNearDeadlineSummaryRepositoryImpl extends ServiceImpl<GoodsNearDeadlineSummaryMapper, GoodsNearDeadlineSummary> implements GoodsNearDeadlineSummaryRepository {

    @Resource
    private GoodsNearDeadlineSummaryMapper goodsNearDeadlineSummaryMapper;
    @Override
    public List<GoodsNearDeadlineSummary> listByTenantAndTimeTag(Long tenantId, String timeTag) {
        QueryWrapper<GoodsNearDeadlineSummary> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id",tenantId)
                .eq("time_tag",timeTag)
                .orderByDesc("enter_deadline_date");
        return goodsNearDeadlineSummaryMapper.selectList(queryWrapper);
    }
}
