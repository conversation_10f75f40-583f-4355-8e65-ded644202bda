package com.cosfo.manage.report.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cosfo.manage.report.model.po.StockTurnoverSummary;
import com.cosfo.manage.report.mapper.StockTurnoverSummaryMapper;
import com.cosfo.manage.report.repository.StockTurnoverSummaryRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 近30天库存周转天数汇总 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
@Service
public class StockTurnoverSummaryRepositoryImpl extends ServiceImpl<StockTurnoverSummaryMapper, StockTurnoverSummary> implements StockTurnoverSummaryRepository {

    @Resource
    private StockTurnoverSummaryMapper stockTurnoverSummaryMapper;
    @Override
    public List<StockTurnoverSummary> listByTenantAndTimeTag(Long tenantId, String timeTag) {
        QueryWrapper<StockTurnoverSummary> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", tenantId).eq("time_tag", timeTag);
        return stockTurnoverSummaryMapper.selectList(queryWrapper);
    }
}
