package com.cosfo.manage.report.repository;

import com.cosfo.manage.report.model.po.GoodsNoSaleSummary;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 滞销货品汇总 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
public interface GoodsNoSaleSummaryRepository extends IService<GoodsNoSaleSummary> {

    /**
     * 根据时间标签查询
     * @param tenantId
     * @param timeTag
     * @return
     */
    List<GoodsNoSaleSummary> listByTenantAndTimeTag(Long tenantId, String timeTag);
}
