package com.cosfo.manage.report.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.report.mapper.MerchantStoreOrderHysteresisAnalysisMapper;
import com.cosfo.manage.report.model.dto.StoreOrderDelayAnalysisInput;
import com.cosfo.manage.report.model.po.MerchantStoreOrderHysteresisAnalysis;
import com.cosfo.manage.report.repository.MerchantStoreOrderHysteresisAnalysisRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 门店滞叫分析表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-15
 */
@Service
public class MerchantStoreOrderHysteresisAnalysisRepositoryImpl extends ServiceImpl<MerchantStoreOrderHysteresisAnalysisMapper, MerchantStoreOrderHysteresisAnalysis> implements MerchantStoreOrderHysteresisAnalysisRepository {

    @Override
    public Page<MerchantStoreOrderHysteresisAnalysis> queryListByPage(StoreOrderDelayAnalysisInput input) {
        LambdaQueryWrapper<MerchantStoreOrderHysteresisAnalysis> reportLambdaQueryWrapper = new LambdaQueryWrapper<>();
        reportLambdaQueryWrapper.eq(MerchantStoreOrderHysteresisAnalysis::getTenantId, input.getTenantId());
        reportLambdaQueryWrapper.eq(StringUtils.isNotBlank(input.getStoreCode()), MerchantStoreOrderHysteresisAnalysis::getStoreCode, input.getStoreCode());
        reportLambdaQueryWrapper.like(StringUtils.isNotBlank(input.getStoreName()), MerchantStoreOrderHysteresisAnalysis::getStoreName, input.getStoreName());
        reportLambdaQueryWrapper.eq(input.getStoreType() != null, MerchantStoreOrderHysteresisAnalysis::getStoreType, input.getStoreType());
        reportLambdaQueryWrapper.eq(input.getStoreStatus() != null, MerchantStoreOrderHysteresisAnalysis::getStoreStatus, input.getStoreStatus());
        reportLambdaQueryWrapper.like(StringUtils.isNotBlank(input.getTitle()), MerchantStoreOrderHysteresisAnalysis::getTitle, input.getTitle());
        reportLambdaQueryWrapper.eq(input.getItemId() != null, MerchantStoreOrderHysteresisAnalysis::getItemId, input.getItemId());
        reportLambdaQueryWrapper.ge(input.getDelayDays() != null, MerchantStoreOrderHysteresisAnalysis::getDelayDays, input.getDelayDays());
        reportLambdaQueryWrapper.orderByDesc(MerchantStoreOrderHysteresisAnalysis::getStoreId, MerchantStoreOrderHysteresisAnalysis::getDelayDays);
        Page<MerchantStoreOrderHysteresisAnalysis> page = page(new Page<>(input.getPageIndex(), input.getPageSize()), reportLambdaQueryWrapper);
        return page;
    }
}
