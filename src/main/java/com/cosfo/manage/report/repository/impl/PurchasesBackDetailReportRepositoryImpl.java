package com.cosfo.manage.report.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.report.mapper.PurchasesBackDetailReportMapper;
import com.cosfo.manage.report.model.dto.ReportCommonQueryDTO;
import com.cosfo.manage.report.model.po.PurchasesBackDetailReport;
import com.cosfo.manage.report.model.vo.PurchaseDetailAggVO;
import com.cosfo.manage.report.repository.PurchasesBackDetailReportRepository;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <p>
 * 采购退货明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-13
 */
@Service
public class PurchasesBackDetailReportRepositoryImpl extends ServiceImpl<PurchasesBackDetailReportMapper, PurchasesBackDetailReport> implements PurchasesBackDetailReportRepository {

    @Resource
    private PurchasesBackDetailReportMapper purchasesBackDetailReportMapper;

    @Override
    public PurchaseDetailAggVO getPurchaseBackDetailAgg(ReportCommonQueryDTO queryDTO) {
        PurchaseDetailAggVO purchaseDetailAggVO = new PurchaseDetailAggVO();
        // 采购数量和金额
        QueryWrapper<PurchasesBackDetailReport> purchasesBackDetailReportQueryWrapper = new QueryWrapper<>();
        purchasesBackDetailReportQueryWrapper.select("`back_type` as backType, COALESCE(sum(back_quantity),0) as backQuantitySum, COALESCE(sum(back_amount),0) as backAmountSum");
        LambdaQueryWrapper<PurchasesBackDetailReport> purchaseDetailQuery = purchasesBackDetailReportQueryWrapper.lambda();
        purchaseDetailQuery.eq(PurchasesBackDetailReport::getTenantId, queryDTO.getTenantId());
        purchaseDetailQuery.between(StringUtils.isNotBlank(queryDTO.getStartTime()) && StringUtils.isNotBlank(queryDTO.getEndTime()),PurchasesBackDetailReport::getBackDate, queryDTO.getStartTime(), queryDTO.getEndTime());
        purchaseDetailQuery.eq(queryDTO.getWarehouseId() != null, PurchasesBackDetailReport::getBackWarehouseId, queryDTO.getWarehouseId());
        purchaseDetailQuery.eq(queryDTO.getSkuId() != null, PurchasesBackDetailReport::getSkuId, queryDTO.getSkuId());
        purchaseDetailQuery.likeRight(StringUtils.isNotBlank(queryDTO.getName()), PurchasesBackDetailReport::getName, queryDTO.getName());
        purchaseDetailQuery.in(!CollectionUtils.isEmpty(queryDTO.getCategoryIds()), PurchasesBackDetailReport::getCategoryId, queryDTO.getCategoryIds());
        purchaseDetailQuery.orderByDesc(PurchasesBackDetailReport::getBackDate);
        purchaseDetailQuery.groupBy(PurchasesBackDetailReport::getBackType);
        List<Map<String, Object>> mapList = listMaps(purchaseDetailQuery);
        // 初始值
        purchaseDetailAggVO.setInboundQuantity(0);
        purchaseDetailAggVO.setInboundAmount(BigDecimal.ZERO);
        purchaseDetailAggVO.setUnboundQuantity(0);
        purchaseDetailAggVO.setUnboundAmount(BigDecimal.ZERO);

        if (!CollectionUtils.isEmpty(mapList)) {
            mapList.stream().forEach(map->{
                Integer backType = (Integer) map.get("backType");
                if (backType == 0){
                    // 未入库
                    purchaseDetailAggVO.setUnboundAmount((BigDecimal) map.get("backAmountSum"));
                    purchaseDetailAggVO.setUnboundQuantity(((BigDecimal) map.get("backQuantitySum")).intValue());
                } else if (backType == 1) {
                    //已入库
                    purchaseDetailAggVO.setInboundAmount((BigDecimal) map.get("backAmountSum"));
                    purchaseDetailAggVO.setInboundQuantity(((BigDecimal) map.get("backQuantitySum")).intValue());
                }
            });
        }
        purchaseDetailAggVO.setBackAmount(purchaseDetailAggVO.getUnboundAmount().add(purchaseDetailAggVO.getInboundAmount()));
        purchaseDetailAggVO.setBackQuantity(purchaseDetailAggVO.getUnboundQuantity() + purchaseDetailAggVO.getInboundQuantity());
        return purchaseDetailAggVO;
    }


    @Override
    public Page<PurchasesBackDetailReport> queryPurchaseBackDetailPage(ReportCommonQueryDTO queryDTO) {
        LambdaQueryWrapper<PurchasesBackDetailReport> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PurchasesBackDetailReport::getTenantId, queryDTO.getTenantId());
        queryWrapper.between(StringUtils.isNotBlank(queryDTO.getStartTime()) && StringUtils.isNotBlank(queryDTO.getEndTime()),PurchasesBackDetailReport::getBackDate, queryDTO.getStartTime(), queryDTO.getEndTime());
        queryWrapper.eq(queryDTO.getWarehouseId() != null, PurchasesBackDetailReport::getBackWarehouseId, queryDTO.getWarehouseId());
        queryWrapper.eq(queryDTO.getSkuId() != null, PurchasesBackDetailReport::getSkuId, queryDTO.getSkuId());
        queryWrapper.likeRight(StringUtils.isNotBlank(queryDTO.getName()), PurchasesBackDetailReport::getName, queryDTO.getName());
        queryWrapper.in(!CollectionUtils.isEmpty(queryDTO.getCategoryIds()), PurchasesBackDetailReport::getCategoryId, queryDTO.getCategoryIds());
        queryWrapper.orderByDesc(PurchasesBackDetailReport::getBackDate);
        Page<PurchasesBackDetailReport> page = page(new Page<>(queryDTO.getPageIndex(),queryDTO.getPageSize()), queryWrapper);
        return page;
    }

    @Override
    public void listPurchaseBackDetailForExport(ReportCommonQueryDTO reportCommonQueryDTO, ResultHandler<?> resultHandler) {
        LambdaQueryWrapper<PurchasesBackDetailReport> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PurchasesBackDetailReport::getTenantId, reportCommonQueryDTO.getTenantId());
        queryWrapper.between(StringUtils.isNotBlank(reportCommonQueryDTO.getStartTime()) && StringUtils.isNotBlank(reportCommonQueryDTO.getEndTime()),PurchasesBackDetailReport::getBackDate, reportCommonQueryDTO.getStartTime(), reportCommonQueryDTO.getEndTime());
        queryWrapper.eq(reportCommonQueryDTO.getWarehouseId() != null, PurchasesBackDetailReport::getBackWarehouseId, reportCommonQueryDTO.getWarehouseId());
        queryWrapper.eq(reportCommonQueryDTO.getSkuId() != null, PurchasesBackDetailReport::getSkuId, reportCommonQueryDTO.getSkuId());
        queryWrapper.likeRight(StringUtils.isNotBlank(reportCommonQueryDTO.getName()), PurchasesBackDetailReport::getName, reportCommonQueryDTO.getName());
        queryWrapper.in(!CollectionUtils.isEmpty(reportCommonQueryDTO.getCategoryIds()), PurchasesBackDetailReport::getCategoryId, reportCommonQueryDTO.getCategoryIds());
        queryWrapper.orderByDesc(PurchasesBackDetailReport::getBackDate);
//        queryWrapper.last("limit 3000");
        purchasesBackDetailReportMapper.listPurchaseBackDetailForExport(queryWrapper, resultHandler);
    }
}
