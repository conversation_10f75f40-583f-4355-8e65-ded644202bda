package com.cosfo.manage.report.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.report.mapper.PurchaseDetailReportMapper;
import com.cosfo.manage.report.model.dto.PurchaseSummarySkuQueryDTO;
import com.cosfo.manage.report.model.dto.PurchaseSummarySupplierQueryDTO;
import com.cosfo.manage.report.model.dto.ReportCommonQueryDTO;
import com.cosfo.manage.report.model.po.PurchaseDetailReport;
import com.cosfo.manage.report.model.vo.PurchaseDetailAggVO;
import com.cosfo.manage.report.model.vo.PurchaseSummarySkuAggVO;
import com.cosfo.manage.report.model.vo.PurchaseSummarySkuVO;
import com.cosfo.manage.report.model.vo.PurchaseSummarySupplierAggVO;
import com.cosfo.manage.report.model.vo.PurchaseSummarySupplierVO;
import com.cosfo.manage.report.repository.PurchaseDetailReportRepository;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Map;

/**
 * <p>
 * 采购明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-13
 */
@Service
public class PurchaseDetailReportRepositoryImpl extends ServiceImpl<PurchaseDetailReportMapper, PurchaseDetailReport> implements PurchaseDetailReportRepository {

    @Resource
    private PurchaseDetailReportMapper purchaseDetailReportMapper;

    @Override
    public Page<PurchaseDetailReport> queryPurchaseDetailPage(ReportCommonQueryDTO queryDTO) {
        LambdaQueryWrapper<PurchaseDetailReport> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PurchaseDetailReport::getTenantId, queryDTO.getTenantId());
        queryWrapper.between(StringUtils.isNotBlank(queryDTO.getStartTime()) && StringUtils.isNotBlank(queryDTO.getEndTime()), PurchaseDetailReport::getPurchaseDate, queryDTO.getStartTime(), queryDTO.getEndTime());
        queryWrapper.eq(queryDTO.getWarehouseId() != null, PurchaseDetailReport::getWarehouseId, queryDTO.getWarehouseId());
        queryWrapper.eq(queryDTO.getSkuId() != null, PurchaseDetailReport::getSkuId, queryDTO.getSkuId());
        queryWrapper.likeRight(StringUtils.isNotBlank(queryDTO.getName()), PurchaseDetailReport::getName, queryDTO.getName());
        queryWrapper.in(!CollectionUtils.isEmpty(queryDTO.getCategoryIds()), PurchaseDetailReport::getCategoryId, queryDTO.getCategoryIds());
        queryWrapper.orderByDesc(PurchaseDetailReport::getPurchaseDate);
        Page<PurchaseDetailReport> page = page(new Page<>(queryDTO.getPageIndex(), queryDTO.getPageSize()), queryWrapper);
        return page;
    }

    @Override
    public void listPurchaseDetailForExport(ReportCommonQueryDTO queryDTO, ResultHandler<?> resultHandler) {
        LambdaQueryWrapper<PurchaseDetailReport> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PurchaseDetailReport::getTenantId, queryDTO.getTenantId());
        queryWrapper.between(StringUtils.isNotBlank(queryDTO.getStartTime()) && StringUtils.isNotBlank(queryDTO.getEndTime()), PurchaseDetailReport::getPurchaseDate, queryDTO.getStartTime(), queryDTO.getEndTime());
        queryWrapper.eq(queryDTO.getWarehouseId() != null, PurchaseDetailReport::getWarehouseId, queryDTO.getWarehouseId());
        queryWrapper.eq(queryDTO.getSkuId() != null, PurchaseDetailReport::getSkuId, queryDTO.getSkuId());
        queryWrapper.likeRight(queryDTO.getName() != null, PurchaseDetailReport::getName, queryDTO.getName());
        queryWrapper.in(!CollectionUtils.isEmpty(queryDTO.getCategoryIds()), PurchaseDetailReport::getCategoryId, queryDTO.getCategoryIds());
        queryWrapper.orderByDesc(PurchaseDetailReport::getPurchaseDate);
//        queryWrapper.last("limit 3000");
        purchaseDetailReportMapper.listPurchaseDetailForExport(queryWrapper, resultHandler);
    }

    @Override
    public PurchaseDetailAggVO getPurchaseDetailAgg(ReportCommonQueryDTO queryDTO) {
        PurchaseDetailAggVO purchaseDetailAggVO = new PurchaseDetailAggVO();
        // 采购数量和金额
        QueryWrapper<PurchaseDetailReport> detailReportQueryWrapper = new QueryWrapper<>();
        detailReportQueryWrapper.select("COALESCE(sum(purchase_quantity),0) as purchaseQuantitySum, COALESCE(sum(purchase_amount),0) as purchaseAmountSum");
        LambdaQueryWrapper<PurchaseDetailReport> purchaseDetailQuery = detailReportQueryWrapper.lambda();
        purchaseDetailQuery.eq(PurchaseDetailReport::getTenantId, queryDTO.getTenantId());
        purchaseDetailQuery.between(StringUtils.isNotBlank(queryDTO.getStartTime()) && StringUtils.isNotBlank(queryDTO.getEndTime()), PurchaseDetailReport::getPurchaseDate, queryDTO.getStartTime(), queryDTO.getEndTime());
        purchaseDetailQuery.eq(queryDTO.getWarehouseId() != null, PurchaseDetailReport::getWarehouseId, queryDTO.getWarehouseId());
        purchaseDetailQuery.eq(queryDTO.getSkuId() != null, PurchaseDetailReport::getSkuId, queryDTO.getSkuId());
        purchaseDetailQuery.likeRight(StringUtils.isNotBlank(queryDTO.getName()), PurchaseDetailReport::getName, queryDTO.getName());
        purchaseDetailQuery.in(!CollectionUtils.isEmpty(queryDTO.getCategoryIds()), PurchaseDetailReport::getCategoryId, queryDTO.getCategoryIds());
        purchaseDetailQuery.orderByDesc(PurchaseDetailReport::getPurchaseDate);
        Map<String, Object> map = getMap(purchaseDetailQuery);
        purchaseDetailAggVO.setPurchaseQuantity(((BigDecimal) map.get("purchaseQuantitySum")).intValue());
        purchaseDetailAggVO.setPurchaseAmount((BigDecimal) map.get("purchaseAmountSum"));
        return purchaseDetailAggVO;
    }


    @Override
    public PurchaseSummarySkuAggVO getPurchaseSummarySkuAgg(PurchaseSummarySkuQueryDTO queryDTO) {
        PurchaseSummarySkuAggVO skuAggVO = new PurchaseSummarySkuAggVO();
        // 采购数量和金额
        QueryWrapper<PurchaseDetailReport> detailReportQueryWrapper = new QueryWrapper<>();
        detailReportQueryWrapper.select("COALESCE(sum(purchase_quantity),0) as purchaseQuantitySum",
                "COALESCE(sum(purchase_amount),0) as purchaseAmountSum",
                "COALESCE(sum(back_quantity),0) as backQuantitySum",
                "COALESCE(sum(back_amount),0) as backAmountSum",
                "(COALESCE(sum(purchase_amount),0)-COALESCE(sum(back_amount),0)) as totalAmount");

        LambdaQueryWrapper<PurchaseDetailReport> purchaseDetailQuery = detailReportQueryWrapper.lambda();
        purchaseDetailQuery.eq(PurchaseDetailReport::getTenantId, queryDTO.getTenantId());
        purchaseDetailQuery.eq(PurchaseDetailReport::getInboundStatus, "全部入库");
        purchaseDetailQuery.between(StringUtils.isNotBlank(queryDTO.getStartTime()) && StringUtils.isNotBlank(queryDTO.getEndTime()), PurchaseDetailReport::getPurchaseDate, queryDTO.getStartTime(), queryDTO.getEndTime());
        purchaseDetailQuery.eq(queryDTO.getSkuId() != null, PurchaseDetailReport::getSkuId, queryDTO.getSkuId());
        purchaseDetailQuery.like(StringUtils.isNotBlank(queryDTO.getName()), PurchaseDetailReport::getName, queryDTO.getName());
        purchaseDetailQuery.like(StringUtils.isNotBlank(queryDTO.getBrandName()), PurchaseDetailReport::getBrandName, queryDTO.getBrandName());
        purchaseDetailQuery.in(!CollectionUtils.isEmpty(queryDTO.getCategoryIds()), PurchaseDetailReport::getCategoryId, queryDTO.getCategoryIds());
        Map<String, Object> map = getMap(purchaseDetailQuery);

        skuAggVO.setPurchaseQuantity(((BigDecimal) map.get("purchaseQuantitySum")).intValue());
        skuAggVO.setPurchaseAmount((BigDecimal) map.get("purchaseAmountSum"));
        skuAggVO.setBackQuantity(((BigDecimal) map.get("backQuantitySum")).intValue());
        skuAggVO.setBackAmount((BigDecimal) map.get("backAmountSum"));
        skuAggVO.setTotalAmount((BigDecimal) map.get("totalAmount"));
        return skuAggVO;
    }

    @Override
    public PurchaseSummarySupplierAggVO getPurchaseSummarySupplierAgg(PurchaseSummarySupplierQueryDTO queryDTO) {
        PurchaseSummarySupplierAggVO supplierAggVO = new PurchaseSummarySupplierAggVO();
        // 采购数量和金额
        QueryWrapper<PurchaseDetailReport> detailReportQueryWrapper = new QueryWrapper<>();
        detailReportQueryWrapper
                .select("(COALESCE(sum(purchase_amount),0)-COALESCE(sum(back_amount),0)) as totalAmount",
                        "(COALESCE(sum(purchase_quantity),0)-COALESCE(sum(back_quantity),0)) as totalQuantity",
                        "count(distinct(purchase_no)) as purchaseOrderNum");

        LambdaQueryWrapper<PurchaseDetailReport> purchaseDetailQuery = detailReportQueryWrapper.lambda();
        purchaseDetailQuery.eq(PurchaseDetailReport::getTenantId, queryDTO.getTenantId());
        purchaseDetailQuery.eq(PurchaseDetailReport::getInboundStatus, "全部入库");
        purchaseDetailQuery.between(StringUtils.isNotBlank(queryDTO.getStartTime()) && StringUtils.isNotBlank(queryDTO.getEndTime()), PurchaseDetailReport::getPurchaseDate, queryDTO.getStartTime(), queryDTO.getEndTime());
        purchaseDetailQuery.eq(queryDTO.getSkuId() != null, PurchaseDetailReport::getSkuId, queryDTO.getSkuId());
        purchaseDetailQuery.like(StringUtils.isNotBlank(queryDTO.getName()), PurchaseDetailReport::getName, queryDTO.getName());
        purchaseDetailQuery.like(StringUtils.isNotBlank(queryDTO.getBrandName()), PurchaseDetailReport::getBrandName, queryDTO.getBrandName());
        purchaseDetailQuery.in(!CollectionUtils.isEmpty(queryDTO.getCategoryIds()), PurchaseDetailReport::getCategoryId, queryDTO.getCategoryIds());
        purchaseDetailQuery.eq(StringUtils.isNotBlank(queryDTO.getSupplierId()), PurchaseDetailReport::getSupplierId, NumberUtils.toInt(queryDTO.getSupplierId()));
        Map<String, Object> map = getMap(purchaseDetailQuery);
        supplierAggVO.setPurchaseOrderNum(((Long) map.get("purchaseOrderNum")).intValue());
        supplierAggVO.setTotalQuantity(((BigDecimal) map.get("totalQuantity")).intValue());
        supplierAggVO.setTotalAmount((BigDecimal) map.get("totalAmount"));
        return supplierAggVO;
    }

    @Override
    public Page<PurchaseSummarySkuVO> queryPurchaseSummaryListBySku(PurchaseSummarySkuQueryDTO queryDTO) {
        Page<PurchaseSummarySkuVO> page = new Page<>(queryDTO.getPageIndex(), queryDTO.getPageSize());
        return getBaseMapper().queryPurchaseSummaryListBySku(page, queryDTO);
    }

    @Override
    public Page<PurchaseSummarySupplierVO> queryPurchaseSummaryListBySupplier(PurchaseSummarySupplierQueryDTO queryDTO) {
        Page<PurchaseSummarySupplierVO> page = new Page<>(queryDTO.getPageIndex(), queryDTO.getPageSize());
        return getBaseMapper().queryPurchaseSummaryListBySupplier(page, queryDTO);
    }
}
