package com.cosfo.manage.report.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.common.excel.easyexcel.ExcelLargeDataSetExporter;
import com.cosfo.manage.report.model.dto.MarketItemSummaryQueryDTO;
import com.cosfo.manage.report.model.po.MarketItemSalesSummary;
import com.cosfo.manage.report.mapper.MarketItemSalesSummaryMapper;
import com.cosfo.manage.report.model.po.OrderItemDetailSummary;
import com.cosfo.manage.report.repository.MarketItemSalesRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

/**
 * <p>
 * 商品销售汇总 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Service
public class MarketItemSalesSummaryRepositoryImpl extends ServiceImpl<MarketItemSalesSummaryMapper, MarketItemSalesSummary> implements MarketItemSalesRepository {

    @Resource
    private MarketItemSalesSummaryMapper marketItemSalesSummaryMapper;

    public void queryByConditionWithHandler(MarketItemSummaryQueryDTO marketItemSummaryQueryDTO, ResultHandler<?> resultHandler) {
        marketItemSalesSummaryMapper.queryByConditionWithHandler(marketItemSummaryQueryDTO, resultHandler);
    }
}
