package com.cosfo.manage.report.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.report.model.dto.StoreOrderDelayAnalysisInput;
import com.cosfo.manage.report.model.po.MerchantStoreOrderHysteresisAnalysis;

/**
 * <p>
 * 门店滞叫分析表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-15
 */
public interface MerchantStoreOrderHysteresisAnalysisRepository extends IService<MerchantStoreOrderHysteresisAnalysis> {


    Page<MerchantStoreOrderHysteresisAnalysis> queryListByPage(StoreOrderDelayAnalysisInput input);
}
