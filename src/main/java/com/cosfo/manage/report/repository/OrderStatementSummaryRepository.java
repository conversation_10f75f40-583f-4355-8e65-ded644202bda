package com.cosfo.manage.report.repository;

import com.cosfo.manage.report.model.po.OrderStatementSummary;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 订单对账单概要 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
public interface OrderStatementSummaryRepository extends IService<OrderStatementSummary> {

    /**
     * 根据条件查询
     * @param tenantId
     * @param startTime
     * @param endTime
     * @return
     */
    OrderStatementSummary queryByConditionSum(Long tenantId, LocalDateTime startTime, LocalDateTime endTime);
}
