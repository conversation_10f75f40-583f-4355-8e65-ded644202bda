package com.cosfo.manage.report.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.report.mapper.OrderAfterSaleDetailSummaryMapper;
import com.cosfo.manage.report.model.dto.OrderAfterSaleDetailSummaryQueryDTO;
import com.cosfo.manage.report.model.po.OrderAfterSaleDetailSummary;
import com.cosfo.manage.report.repository.OrderAfterSaleDetailSummaryRepository;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 售后订单汇总 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Service
public class OrderAfterSaleDetailSummaryRepositoryImpl extends ServiceImpl<OrderAfterSaleDetailSummaryMapper, OrderAfterSaleDetailSummary> implements OrderAfterSaleDetailSummaryRepository {

    @Resource
    private OrderAfterSaleDetailSummaryMapper orderAfterSaleDetailSummaryMapper;

    @Override
    public void queryByConditionWithHandler(Long tenantId, Long supplierId, LocalDateTime startTime, LocalDateTime endTime, Integer goodsType, ResultHandler<?> resultHandler) {
        LambdaQueryWrapper<OrderAfterSaleDetailSummary> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OrderAfterSaleDetailSummary::getTenantId, tenantId);
        lambdaQueryWrapper.between(OrderAfterSaleDetailSummary::getTimeTag, startTime, endTime);
        lambdaQueryWrapper.eq(Objects.nonNull(supplierId), OrderAfterSaleDetailSummary::getSupplierId, supplierId);
        lambdaQueryWrapper.eq(OrderAfterSaleDetailSummary::getGoodsType, goodsType);
        orderAfterSaleDetailSummaryMapper.queryByConditionWithHandler(lambdaQueryWrapper, resultHandler);
    }

    @Override
    public OrderAfterSaleDetailSummary queryByConditionSum(OrderAfterSaleDetailSummaryQueryDTO orderAfterSaleDetailSummaryQueryDTO) {
        return orderAfterSaleDetailSummaryMapper.queryByConditionSum(orderAfterSaleDetailSummaryQueryDTO);
    }

    @Override
    public List<OrderAfterSaleDetailSummary> queryByCondition(OrderAfterSaleDetailSummaryQueryDTO orderAfterSaleDetailSummaryQueryDTO) {
        LambdaQueryWrapper<OrderAfterSaleDetailSummary> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OrderAfterSaleDetailSummary::getTenantId, orderAfterSaleDetailSummaryQueryDTO.getTenantId());
        lambdaQueryWrapper.between(!orderAfterSaleDetailSummaryQueryDTO.isSpecialFlag(), OrderAfterSaleDetailSummary::getTimeTag, orderAfterSaleDetailSummaryQueryDTO.getStartTime(), orderAfterSaleDetailSummaryQueryDTO.getEndTime());
        lambdaQueryWrapper.between(orderAfterSaleDetailSummaryQueryDTO.isSpecialFlag(), OrderAfterSaleDetailSummary::getSpecialTimeTag, orderAfterSaleDetailSummaryQueryDTO.getStartTime(), orderAfterSaleDetailSummaryQueryDTO.getEndTime());
        lambdaQueryWrapper.eq(Objects.nonNull(orderAfterSaleDetailSummaryQueryDTO.getSupplierId()), OrderAfterSaleDetailSummary::getSupplierId, orderAfterSaleDetailSummaryQueryDTO.getSupplierId());
        lambdaQueryWrapper.eq(OrderAfterSaleDetailSummary::getGoodsType, orderAfterSaleDetailSummaryQueryDTO.getGoodsType());
        lambdaQueryWrapper.notIn(!CollectionUtils.isEmpty(orderAfterSaleDetailSummaryQueryDTO.getExcludeOrderNoList()), OrderAfterSaleDetailSummary::getOrderNo, orderAfterSaleDetailSummaryQueryDTO.getExcludeOrderNoList());
        lambdaQueryWrapper.select(OrderAfterSaleDetailSummary::getOrderNo);
        return orderAfterSaleDetailSummaryMapper.selectList(lambdaQueryWrapper);
    }
}
