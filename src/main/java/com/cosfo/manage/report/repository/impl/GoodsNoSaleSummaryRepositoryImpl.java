package com.cosfo.manage.report.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cosfo.manage.report.model.po.GoodsNoSaleSummary;
import com.cosfo.manage.report.mapper.GoodsNoSaleSummaryMapper;
import com.cosfo.manage.report.repository.GoodsNoSaleSummaryRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 滞销货品汇总 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
@Service
public class GoodsNoSaleSummaryRepositoryImpl extends ServiceImpl<GoodsNoSaleSummaryMapper, GoodsNoSaleSummary> implements GoodsNoSaleSummaryRepository {

    @Resource
    private GoodsNoSaleSummaryMapper goodsNoSaleSummaryMapper;
    @Override
    public List<GoodsNoSaleSummary> listByTenantAndTimeTag(Long tenantId, String timeTag) {
        QueryWrapper<GoodsNoSaleSummary> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id",tenantId)
               .eq("time_tag",timeTag);
        return goodsNoSaleSummaryMapper.selectList(queryWrapper);
    }
}
