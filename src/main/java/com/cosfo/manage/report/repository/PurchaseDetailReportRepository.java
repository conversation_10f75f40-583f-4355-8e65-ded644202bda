package com.cosfo.manage.report.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.report.model.dto.PurchaseSummarySkuQueryDTO;
import com.cosfo.manage.report.model.dto.PurchaseSummarySupplierQueryDTO;
import com.cosfo.manage.report.model.dto.ReportCommonQueryDTO;
import com.cosfo.manage.report.model.po.PurchaseDetailReport;
import com.cosfo.manage.report.model.vo.PurchaseDetailAggVO;
import com.cosfo.manage.report.model.vo.PurchaseSummarySkuAggVO;
import com.cosfo.manage.report.model.vo.PurchaseSummarySkuVO;
import com.cosfo.manage.report.model.vo.PurchaseSummarySupplierAggVO;
import com.cosfo.manage.report.model.vo.PurchaseSummarySupplierVO;
import org.apache.ibatis.session.ResultHandler;

/**
 * <p>
 * 采购明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-13
 */
public interface PurchaseDetailReportRepository extends IService<PurchaseDetailReport> {

    /**
     * 查询所有
     * @param queryDTO
     * @return
     */
    Page<PurchaseDetailReport> queryPurchaseDetailPage(ReportCommonQueryDTO queryDTO);


    /**
     * 获取采购明细导出列表，限制3000条
     *
     * @param reportCommonQueryDTO
     * @return
     */
    void listPurchaseDetailForExport(ReportCommonQueryDTO reportCommonQueryDTO, ResultHandler<?> resultHandler);

    /**
     * 获取采购报表汇总信息
     * @param queryDTO
     * @return
     */
    PurchaseDetailAggVO getPurchaseDetailAgg(ReportCommonQueryDTO queryDTO);

    /**
     * 采购汇总表 - 商品维度 - 合计信息
     * @param queryDTO
     * @return
     */
    PurchaseSummarySkuAggVO getPurchaseSummarySkuAgg(PurchaseSummarySkuQueryDTO queryDTO);

    /**
     * 采购汇总表 - 供应商维度 - 合计信息
     * @param queryDTO
     * @return
     */
    PurchaseSummarySupplierAggVO getPurchaseSummarySupplierAgg(PurchaseSummarySupplierQueryDTO queryDTO);

    /**
     * 采购汇总表 - 商品维度 列表查询
     * @param queryDTO
     * @return
     */
    Page<PurchaseSummarySkuVO> queryPurchaseSummaryListBySku(PurchaseSummarySkuQueryDTO queryDTO);

    /**
     * 采购汇总表 - 供应商维度 - 列表查询
     * @param queryDTO
     * @return
     */
    Page<PurchaseSummarySupplierVO> queryPurchaseSummaryListBySupplier(PurchaseSummarySupplierQueryDTO queryDTO);
}
