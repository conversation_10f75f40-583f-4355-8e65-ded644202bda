package com.cosfo.manage.report.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.report.mapper.GoodsExpirationSummaryMapper;
import com.cosfo.manage.report.model.po.GoodsExpirationSummary;
import com.cosfo.manage.report.repository.GoodsExpirationSummaryRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 货品过期汇总 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@Service
public class GoodsExpirationSummaryRepositoryImpl extends ServiceImpl<GoodsExpirationSummaryMapper, GoodsExpirationSummary> implements GoodsExpirationSummaryRepository {

    @Resource
    private GoodsExpirationSummaryMapper goodsExpirationSummaryMapper;

    @Override
    public List<GoodsExpirationSummary> listByTenantAndTimeTag(Long tenantId, String timeTag) {
        QueryWrapper<GoodsExpirationSummary> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id",tenantId)
                .eq("time_tag",timeTag)
                .orderByDesc("expiration_date");
        return goodsExpirationSummaryMapper.selectList(queryWrapper);
    }
}
