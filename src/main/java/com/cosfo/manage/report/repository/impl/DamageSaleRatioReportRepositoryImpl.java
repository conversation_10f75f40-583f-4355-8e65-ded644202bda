package com.cosfo.manage.report.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.report.mapper.DamageSaleRatioReportMapper;
import com.cosfo.manage.report.model.dto.ReportCommonQueryDTO;
import com.cosfo.manage.report.model.po.DamageSaleRatioReport;
import com.cosfo.manage.report.model.vo.PurchaseDetailAggVO;
import com.cosfo.manage.report.repository.DamageSaleRatioReportRepository;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <p>
 * 损售比 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-13
 */
@Service
public class DamageSaleRatioReportRepositoryImpl extends ServiceImpl<DamageSaleRatioReportMapper, DamageSaleRatioReport> implements DamageSaleRatioReportRepository {

    @Resource
    private DamageSaleRatioReportMapper damageSaleRatioReportMapper;

    @Override
    public Page<DamageSaleRatioReport> queryDamageSaleRatioDetailPage(ReportCommonQueryDTO reportCommonQueryDTO) {
        LambdaQueryWrapper<DamageSaleRatioReport> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DamageSaleRatioReport::getTenantId, reportCommonQueryDTO.getTenantId());
        queryWrapper.between(StringUtils.isNotBlank(reportCommonQueryDTO.getStartTime()) && StringUtils.isNotBlank(reportCommonQueryDTO.getEndTime()), DamageSaleRatioReport::getDamageDate, reportCommonQueryDTO.getStartTime(), reportCommonQueryDTO.getEndTime());
        queryWrapper.eq(reportCommonQueryDTO.getWarehouseId() != null, DamageSaleRatioReport::getWarehouseId, reportCommonQueryDTO.getWarehouseId());
        queryWrapper.eq(reportCommonQueryDTO.getSkuId() != null, DamageSaleRatioReport::getSkuId, reportCommonQueryDTO.getSkuId());
        queryWrapper.likeRight(StringUtils.isNotBlank(reportCommonQueryDTO.getName()), DamageSaleRatioReport::getName, reportCommonQueryDTO.getName());
        queryWrapper.in(!CollectionUtils.isEmpty(reportCommonQueryDTO.getCategoryIds()), DamageSaleRatioReport::getCategoryId, reportCommonQueryDTO.getCategoryIds());
        queryWrapper.orderByDesc(DamageSaleRatioReport::getDamageDate);
        Page<DamageSaleRatioReport> page = page(new Page<>(reportCommonQueryDTO.getPageIndex(), reportCommonQueryDTO.getPageSize()), queryWrapper);
        return page;
    }

    @Override
    public void listDamageSaleRatioReportForExport(ReportCommonQueryDTO reportCommonQueryDTO, ResultHandler<?> resultHandler) {
        LambdaQueryWrapper<DamageSaleRatioReport> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DamageSaleRatioReport::getTenantId, reportCommonQueryDTO.getTenantId());
        queryWrapper.between(StringUtils.isNotBlank(reportCommonQueryDTO.getStartTime()) && StringUtils.isNotBlank(reportCommonQueryDTO.getEndTime()), DamageSaleRatioReport::getDamageDate, reportCommonQueryDTO.getStartTime(), reportCommonQueryDTO.getEndTime());
        queryWrapper.eq(reportCommonQueryDTO.getWarehouseId() != null, DamageSaleRatioReport::getWarehouseId, reportCommonQueryDTO.getWarehouseId());
        queryWrapper.eq(reportCommonQueryDTO.getSkuId() != null, DamageSaleRatioReport::getSkuId, reportCommonQueryDTO.getSkuId());
        queryWrapper.likeRight(StringUtils.isNotBlank(reportCommonQueryDTO.getName()), DamageSaleRatioReport::getName, reportCommonQueryDTO.getName());
        queryWrapper.in(!CollectionUtils.isEmpty(reportCommonQueryDTO.getCategoryIds()), DamageSaleRatioReport::getCategoryId, reportCommonQueryDTO.getCategoryIds());
        queryWrapper.orderByDesc(DamageSaleRatioReport::getDamageDate);
//        queryWrapper.last("limit 3000");
        damageSaleRatioReportMapper.listDamageSaleRatioReportForExport(queryWrapper, resultHandler);
    }

    @Override
    public PurchaseDetailAggVO getDamageSaleRatioDetailAgg(ReportCommonQueryDTO queryDTO) {
        PurchaseDetailAggVO purchaseDetailAggVO = new PurchaseDetailAggVO();
        // 采购数量和金额
        QueryWrapper<DamageSaleRatioReport> damageSaleRatioReportQueryWrapper = new QueryWrapper<>();
        damageSaleRatioReportQueryWrapper.select("COALESCE(sum(damage_quantity),0) as damageQuantitySum, COALESCE(sum(damage_amount),0) as damageAmountSum, COALESCE(sum(back_quantity),0) as backQuantitySum, COALESCE(sum(back_amount),0) as backAmountSum, COALESCE(sum(outbound_quantity),0) as outboundQuantitySum, COALESCE(sum(outbound_amount),0) as outboundAmountSum");
        LambdaQueryWrapper<DamageSaleRatioReport> purchaseDetailQuery = damageSaleRatioReportQueryWrapper.lambda();
        purchaseDetailQuery.eq(DamageSaleRatioReport::getTenantId, queryDTO.getTenantId());
        purchaseDetailQuery.between(StringUtils.isNotBlank(queryDTO.getStartTime()) && StringUtils.isNotBlank(queryDTO.getEndTime()), DamageSaleRatioReport::getDamageDate, queryDTO.getStartTime(), queryDTO.getEndTime());
        purchaseDetailQuery.eq(queryDTO.getWarehouseId() != null, DamageSaleRatioReport::getWarehouseId, queryDTO.getWarehouseId());
        purchaseDetailQuery.eq(queryDTO.getSkuId() != null, DamageSaleRatioReport::getSkuId, queryDTO.getSkuId());
        purchaseDetailQuery.likeRight(StringUtils.isNotBlank(queryDTO.getName()), DamageSaleRatioReport::getName, queryDTO.getName());
        purchaseDetailQuery.in(!CollectionUtils.isEmpty(queryDTO.getCategoryIds()), DamageSaleRatioReport::getCategoryId, queryDTO.getCategoryIds());
        purchaseDetailQuery.orderByDesc(DamageSaleRatioReport::getDamageDate);
        Map<String, Object> map = getMap(purchaseDetailQuery);
        purchaseDetailAggVO.setDamageAmount(BigDecimal.ZERO);
        purchaseDetailAggVO.setDamageBackAmount(BigDecimal.ZERO);
        purchaseDetailAggVO.setOutboundAmount(BigDecimal.ZERO);
        // 货损
        purchaseDetailAggVO.setDamageQuantity(((BigDecimal) map.get("damageQuantitySum")).intValue());
        purchaseDetailAggVO.setDamageAmount((BigDecimal) map.get("damageAmountSum"));
        // 退货
        purchaseDetailAggVO.setDamageBackAmount((BigDecimal) map.get("backAmountSum"));
        purchaseDetailAggVO.setDamageBackQuantity(((BigDecimal) map.get("backQuantitySum")).intValue());
        // 销售出库
        purchaseDetailAggVO.setOutboundAmount((BigDecimal) map.get("outboundAmountSum"));
        purchaseDetailAggVO.setOutboundQuantity(((BigDecimal) map.get("outboundQuantitySum")).intValue());
        // 损售比
        purchaseDetailAggVO.setDamageSaleRatio(purchaseDetailAggVO.getOutboundAmount().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : purchaseDetailAggVO.getDamageAmount().add(purchaseDetailAggVO.getDamageBackAmount()).multiply(BigDecimal.valueOf(100)).divide(purchaseDetailAggVO.getOutboundAmount(), 2, RoundingMode.HALF_UP));
        return purchaseDetailAggVO;
    }
}
