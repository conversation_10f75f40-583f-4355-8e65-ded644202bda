package com.cosfo.manage.report.service;


import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.report.model.dto.MerchantStorePurchaseReportQueryDTO;
import com.cosfo.manage.report.model.vo.MerchantStorePurchaseReportResultVO;
import com.cosfo.manage.report.model.vo.MerchantStorePurchaseReportVO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * <p>
 * 门店采购明细报表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-02
 */
public interface MerchantStorePurchaseReportService {
    PageInfo<MerchantStorePurchaseReportResultVO> listAll(MerchantStorePurchaseReportVO merchantStorePurchaseReportVO);

    void export(LoginContextInfoDTO requestContextInfoDTO, MerchantStorePurchaseReportVO merchantStorePurchaseReportVO);
}
