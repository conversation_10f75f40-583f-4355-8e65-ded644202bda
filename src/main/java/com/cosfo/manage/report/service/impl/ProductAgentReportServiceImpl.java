package com.cosfo.manage.report.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cofso.item.client.resp.MarketItemOnSaleSimple4StoreResp;
import com.cosfo.common.excel.easyexcel.converter.EasyExcelLocalDateConverter;
import com.cosfo.common.excel.easyexcel.converter.LocalDateTimeConverter;
import com.cosfo.common.util.TimeUtils;
import com.cosfo.manage.common.config.GrayReleaseConfig;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.context.ExcelTypeEnum;
import com.cosfo.manage.common.context.FileDownloadTypeEnum;
import com.cosfo.manage.common.context.OnSaleTypeEnum;
import com.cosfo.manage.common.context.ProductWarehouseDataEnums;
import com.cosfo.manage.common.converter.ProductAgentWarehouseShelfLifeMapper;
import com.cosfo.manage.common.exception.DefaultServiceException;
import com.cosfo.manage.common.executor.ExecutorFactory;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTOEnum;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.common.util.ExcelUtils;
import com.cosfo.manage.common.util.PageInfoHelper;
import com.cosfo.manage.facade.*;
import com.cosfo.manage.facade.dto.ProductSkuWarehouseQueryDTO;
import com.cosfo.manage.facade.input.ProductStockChangeRecordQueryInput;
import com.cosfo.manage.file.service.FileDownloadRecordService;
import com.cosfo.manage.market.service.MarketItemBusinessService;
import com.cosfo.manage.order.model.dto.OrderSkuSaleDTO;
import com.cosfo.manage.order.service.OrderBusinessService;
import com.cosfo.manage.product.dao.ProductAgentSkuMappingDao;
import com.cosfo.manage.product.mapper.ProductAgentSkuMappingMapper;
import com.cosfo.manage.product.mapper.ProductSkuOrderSummaryMapper;
import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import com.cosfo.manage.product.model.dto.ProductSkuOrderSummaryDayDTO;
import com.cosfo.manage.product.model.dto.ProductStockChangeRecordQueryDTO;
import com.cosfo.manage.product.model.dto.WarehouseSkuFenceAreaDTO;
import com.cosfo.manage.product.model.po.ProductAgentSkuMapping;
import com.cosfo.manage.product.model.vo.ProductAgentWarehouseShelfLifeVO;
import com.cosfo.manage.product.model.vo.ProductAgentWarehouseVO;
import com.cosfo.manage.product.model.vo.ProductStockChangeRecordVO;
import com.cosfo.manage.product.service.ProductAgentSkuService;
import com.cosfo.manage.product.service.ProductAgentWarehouseService;
import com.cosfo.manage.report.converter.Convert;
import com.cosfo.manage.report.converter.SaleInventoryConverter;
import com.cosfo.manage.report.model.dto.ProductAgentShelfLifeQueryDTO;
import com.cosfo.manage.report.model.dto.ProductAgentWarehouseDateQueryDTO;
import com.cosfo.manage.report.model.dto.ProductAgentWarehouseOverviewQueryDTO;
import com.cosfo.manage.report.model.vo.ProductAgentWarehouseAggregationVO;
import com.cosfo.manage.report.model.vo.ProductAgentWarehouseDataVO;
import com.cosfo.manage.report.model.vo.ProductAgentWarehouseOverviewVO;
import com.cosfo.manage.report.service.ProductAgentReportService;
import com.cosfo.manage.tenant.model.dto.TenantDTO;
import com.cosfo.manage.tenant.service.TenantService;
import com.cosfo.summerfarm.model.input.SummerfarmAgentSkuWarehouseDataInput;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Maps;
import com.google.common.collect.Table;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.goods.client.resp.ProductsMappingResp;
import net.summerfarm.wms.saleinventory.dto.dto.MinShelfLifePurchaseBatchDTO;
import net.summerfarm.wms.saleinventory.dto.dto.SkuAggregateInventoryDTO;
import net.summerfarm.wms.saleinventory.dto.dto.SkuQuantityChangeRecordDTO;
import net.summerfarm.wms.saleinventory.dto.req.BatchQueryMinShelfLifePurchaseBatchReq;
import net.summerfarm.wms.saleinventory.dto.req.QueryAggregateInventoryRequest;
import net.summerfarm.wms.saleinventory.dto.req.QuerySkuAggregateInventoryRequest;
import net.summerfarm.wms.saleinventory.dto.res.PageQuerySkuQuantityChangeRecordResp;
import net.summerfarm.wms.saleinventory.dto.res.QueryAggregateInventoryResp;
import net.summerfarm.wms.saleinventory.dto.res.QueryInventoryResp;
import net.summerfarm.wnc.client.enums.WarehouseSourceEnum;
import net.summerfarm.wnc.client.req.WarehouseSkuFenceReq;
import net.summerfarm.wnc.client.resp.WarehouseSkuFenceAreaResp;
import net.summerfarm.wnc.client.resp.WarehouseSkuFenceResp;
import net.summerfarm.wnc.client.resp.WarehouseSkuFenceStorageResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import net.xianmu.scp.client.req.replenishment.ReplenishmentStockConfigBaseQueryInput;
import net.xianmu.scp.client.resp.replenishment.ReplenishmentStockConfigDTO;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/11/4
 */
@Service
@Slf4j
public class ProductAgentReportServiceImpl implements ProductAgentReportService {
    @Resource
    private ProductAgentWarehouseService productAgentWarehouseService;
    @Resource
    private ProductAgentSkuService productAgentSkuService;
    @Resource
    private TenantService tenantService;
    @Resource
    private OrderBusinessService orderBusinessService;
    @Resource
    private FileDownloadRecordService fileDownloadRecordService;
    @Resource
    private CommonService commonService;
    @Resource
    private SaasInventoryFacade saasInventoryFacade;
    @Resource
    private ProductAgentSkuMappingMapper productAgentSkuMappingMapper;
    @Resource
    private ProductAgentSkuMappingDao productAgentSkuMappingDao;
    @Resource
    private SaleInventoryServiceFacade saleInventoryServiceFacade;
    @Resource
    private WarehouseStorageFenceQueryFacade warehouseStorageFenceQueryFacade;
    @Resource
    private MarketItemBusinessService marketItemBusinessService;
    @Resource
    private GrayReleaseConfig grayReleaseConfig;
    @Resource
    private ProductFacade productFacade;
    @Resource
    private ProductSkuOrderSummaryMapper productSkuOrderSummaryMapper;
    @Resource
    private ScpFacade scpFacade;



    @Override
    public CommonResult<List<ProductAgentWarehouseVO>> queryWarehouseList(LoginContextInfoDTO loginContextInfoDTO) {
        List<ProductAgentWarehouseVO> productAgentWarehouseVos = productAgentWarehouseService.queryByTenantId(loginContextInfoDTO.getTenantId());
        return CommonResult.ok(productAgentWarehouseVos);
    }

    @Override
    public CommonResult<PageInfo<ProductAgentWarehouseDataVO>> warehouseDataList(ProductAgentWarehouseDateQueryDTO productAgentWarehouseDateQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        if (grayReleaseConfig.executeProductCenterGray(loginContextInfoDTO.getTenantId ())) {
            PageInfo pageInfo = convertWarehouseData (productAgentWarehouseDateQueryDTO, loginContextInfoDTO);
            return CommonResult.ok (pageInfo);
        }else {
            PageInfo pageInfo = PageInfoHelper.createPageInfo (new ArrayList<> (), productAgentWarehouseDateQueryDTO.getPageSize ());
            // 查询商品名称
            pageInfo = convertWarehouseDataOld (productAgentWarehouseDateQueryDTO, loginContextInfoDTO, pageInfo);

            return CommonResult.ok (pageInfo);
        }
    }

    private PageInfo convertWarehouseData(ProductAgentWarehouseDateQueryDTO warehouseDateQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        SummerfarmAgentSkuWarehouseDataInput summerfarmAgentSkuWarehouseDataInput = packagingQueryCondition(warehouseDateQueryDTO, loginContextInfoDTO);
        ProductSkuWarehouseQueryDTO productSkuWarehouseQueryDTO = transferProductSkuWarehouseQueryDTO(warehouseDateQueryDTO.getSync(), warehouseDateQueryDTO.getInOutRecord());
        if (CollectionUtils.isEmpty(summerfarmAgentSkuWarehouseDataInput.getWarehouseIds()) ) {
            return PageInfoHelper.createPageInfo(Collections.EMPTY_LIST, warehouseDateQueryDTO.getPageSize());
        }
        // 查询商品名称
        List<ProductSkuDTO> productSkuDtos = productAgentSkuService.queryTenantAgentSku(warehouseDateQueryDTO, loginContextInfoDTO).stream ().filter (e-> ObjectUtil.isNotNull (e.getSkuMapping ()) && ObjectUtil.isNotNull (e.getSkuMapping ().getSku ())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productSkuDtos) ) {
            return PageInfoHelper.createPageInfo(Collections.EMPTY_LIST, warehouseDateQueryDTO.getPageSize());
        }

        List<Long> agentSkuIds = productSkuDtos.stream().map(e-> e.getAgentSkuId()).collect(Collectors.toList());
        summerfarmAgentSkuWarehouseDataInput.setSkuId(agentSkuIds);
        Map<Long, ProductSkuDTO> productSkuDtoMap = productSkuDtos.stream().filter (e-> ObjectUtil.isNotNull (e.getAgentSkuId ())).collect(Collectors.toMap(ProductSkuDTO::getAgentSkuId, item -> item));
        // 查询鲜沐代仓实时库存
        PageInfo<QueryInventoryResp> respPageInfo = saleInventoryServiceFacade.pageQueryAgentSkuWarehouseData(summerfarmAgentSkuWarehouseDataInput, productSkuWarehouseQueryDTO);
        BatchQueryMinShelfLifePurchaseBatchReq batchReq = new BatchQueryMinShelfLifePurchaseBatchReq();
        batchReq.setQueryDTOList(respPageInfo.getList().stream().map(data-> BatchQueryMinShelfLifePurchaseBatchReq.PurchaseBatchQueryDTO.builder().skuId(data.getSkuId()).warehouseNo(data.getWarehouseNo()).build()).collect(Collectors.toList()));
        // 最近保质期
        Map<Long, Map<Integer, MinShelfLifePurchaseBatchDTO>> latestShelfLifeMap = saasInventoryFacade.batchQueryLatestShelfLife(batchReq);

        List<QueryInventoryResp> summerFarmAgentSkuWarehouseDataResps = respPageInfo.getList();

        // 获取符合分页条件的数据查询
        List<Long> skuIds = summerFarmAgentSkuWarehouseDataResps.stream().filter(dto -> Objects.nonNull(productSkuDtoMap.get(dto.getSkuId()))).map(dto -> productSkuDtoMap.get(dto.getSkuId()).getId()).collect(Collectors.toList());
        // 查询代仓品仓库维度当日、近十五天、近三十天销售情况
        Triple<Table<Long, Integer, Integer>, Table<Long, Integer, Integer>, Table<Long, Integer, Integer>> tableTriple = getWarehouseSaleInfo(skuIds, loginContextInfoDTO);
        Table<Long, Integer, Integer> skuSaleAmountMap = tableTriple.getLeft();
        Table<Long, Integer, Integer> skuFifteenSaleAmountMap = tableTriple.getMiddle();
        Table<Long, Integer, Integer> skuThirtySaleAmountMap = tableTriple.getRight();
        // 查询数据的上下架状态
        Map<Long, List<MarketItemOnSaleSimple4StoreResp>> saleSkuIdMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(skuIds)) {
            List<MarketItemOnSaleSimple4StoreResp> marketItems = marketItemBusinessService.selectSaleStatusBySkuIds(loginContextInfoDTO.getTenantId(), skuIds);
            saleSkuIdMap = marketItems.stream().collect(Collectors.groupingBy(MarketItemOnSaleSimple4StoreResp::getSkuId));
        }
        // 按照sku分组
        Map<Long, List<QueryInventoryResp>> map = summerFarmAgentSkuWarehouseDataResps.stream().collect(Collectors.groupingBy(QueryInventoryResp::getSkuId));

        // 查询代仓货品关联鲜沐品SkuId
        Map<Long, String> productAgentSkuMappingMap = productSkuDtos.stream ().collect (Collectors.toMap (ProductSkuDTO::getAgentSkuId, x -> x.getSkuMapping ().getSku ()));
        // 查询sku基于仓维度的覆盖区域
        List<WarehouseSkuFenceResp> warehouseSkuFenceResps = queryWarehouseSkuFence(map, productAgentSkuMappingMap);
        // 转化为map
        Map<String, List<WarehouseSkuFenceStorageResp>> warehouseSkuFenceStorageRespListMap = warehouseSkuFenceResps.stream().collect(Collectors.toMap(WarehouseSkuFenceResp::getSku, WarehouseSkuFenceResp::getWarehouseSkuFenceStorages));
        // 查询品牌方对应adminId
        TenantDTO tenantDTO = tenantService.queryTenantById(loginContextInfoDTO.getTenantId());
        // 组装数据
        Map<Long, List<MarketItemOnSaleSimple4StoreResp>> finalSaleSkuIdMap = saleSkuIdMap;
        List<ProductAgentWarehouseDataVO> productAgentWarehouseDataVos = respPageInfo.getList().stream().map(summerfarmAgentSkuWarehouseDataDTO -> {
            ProductAgentWarehouseDataVO productAgentWarehouseDataVo = new ProductAgentWarehouseDataVO();
            BeanUtils.copyProperties(summerfarmAgentSkuWarehouseDataDTO, productAgentWarehouseDataVo);
            ProductSkuDTO productSkuDto = productSkuDtoMap.get(summerfarmAgentSkuWarehouseDataDTO.getSkuId());
            MinShelfLifePurchaseBatchDTO minShelfLifePurchaseBatchDTO = latestShelfLifeMap.getOrDefault(summerfarmAgentSkuWarehouseDataDTO.getSkuId(), new HashMap<>(0)).getOrDefault(summerfarmAgentSkuWarehouseDataDTO.getWarehouseNo(), new MinShelfLifePurchaseBatchDTO());
            productAgentWarehouseDataVo.setSkuId(productSkuDto.getId());
            productAgentWarehouseDataVo.setSku(productSkuDto.getAgentSkuCode());
            // 上下架状态组装
            Integer onSale = null;
            List<MarketItemOnSaleSimple4StoreResp> marketItems = finalSaleSkuIdMap.get(productSkuDto.getId());
            if (!CollectionUtils.isEmpty(marketItems)) {
                onSale = marketItems.stream().anyMatch(resp -> OnSaleTypeEnum.ON_SALE.getCode().equals(resp.getOnSale())) ? OnSaleTypeEnum.ON_SALE.getCode() : OnSaleTypeEnum.SOLD_OUT.getCode();
            }
            productAgentWarehouseDataVo.setOnSale(onSale);
            productAgentWarehouseDataVo.setOnSaleDesc(OnSaleTypeEnum.getRemark(onSale));

            productAgentWarehouseDataVo.setTitle(productSkuDto.getTitle());
            productAgentWarehouseDataVo.setSpecification(productSkuDto.getSpecification());
            productAgentWarehouseDataVo.setSpecificationUnit(productSkuDto.getSpecificationUnit());
            productAgentWarehouseDataVo.setRoadQuantity(Objects.isNull(summerfarmAgentSkuWarehouseDataDTO.getRoadQuantity()) ? NumberConstant.ZERO : summerfarmAgentSkuWarehouseDataDTO.getRoadQuantity());
            productAgentWarehouseDataVo.setSaleAmount(Optional.ofNullable(skuSaleAmountMap.get(productSkuDto.getId(),summerfarmAgentSkuWarehouseDataDTO.getWarehouseNo())).orElse(NumberConstant.ZERO));
            productAgentWarehouseDataVo.setFifteenDaysSaleAmount(Optional.ofNullable(skuFifteenSaleAmountMap.get(productSkuDto.getId(),summerfarmAgentSkuWarehouseDataDTO.getWarehouseNo())).orElse(NumberConstant.ZERO));
            productAgentWarehouseDataVo.setThirtyDaysSaleAmount(Optional.ofNullable(skuThirtySaleAmountMap.get(productSkuDto.getId(),summerfarmAgentSkuWarehouseDataDTO.getWarehouseNo())).orElse(NumberConstant.ZERO));
            productAgentWarehouseDataVo.setMainPicture(productSkuDto.getMainPicture());
            productAgentWarehouseDataVo.setGoodsValue(summerfarmAgentSkuWarehouseDataDTO.getGoodsValue());
            productAgentWarehouseDataVo.setBatch(minShelfLifePurchaseBatchDTO.getBatch());
            productAgentWarehouseDataVo.setLeftShelfLife(minShelfLifePurchaseBatchDTO.getLeftShelfLife());
            ProductWarehouseDataEnums.SyncStatus syncEnum = ProductWarehouseDataEnums.SyncStatus.getByResp(summerfarmAgentSkuWarehouseDataDTO.getSync());
            Optional.ofNullable(syncEnum).ifPresent(enums -> {
                productAgentWarehouseDataVo.setSync(enums.getStatus());
                productAgentWarehouseDataVo.setSyncDesc(enums.getDesc());
            });
            productAgentWarehouseDataVo.setOnlineQuantity(summerfarmAgentSkuWarehouseDataDTO.getOnlineQuantity());
            productAgentWarehouseDataVo.setWarehouseTenantId(summerfarmAgentSkuWarehouseDataDTO.getWarehouseTenantId());
            // 仓所属租户是当前租户，归属自营仓
            Integer selfWarehouseType = ProductWarehouseDataEnums.SelfWarehouseType.getCode(loginContextInfoDTO.getTenantId().equals(summerfarmAgentSkuWarehouseDataDTO.getWarehouseTenantId()));
            productAgentWarehouseDataVo.setSelfWarehouseType(selfWarehouseType);
            // 获取省市区
            List<WarehouseSkuFenceStorageResp> warehouseSkuFenceStorageResps = warehouseSkuFenceStorageRespListMap.get(productAgentSkuMappingMap.get(summerfarmAgentSkuWarehouseDataDTO.getSkuId()));
            if (!CollectionUtils.isEmpty(warehouseSkuFenceStorageResps)) {
                for (WarehouseSkuFenceStorageResp warehouseSkuFenceStorageResp : warehouseSkuFenceStorageResps) {
                    if (summerfarmAgentSkuWarehouseDataDTO.getWarehouseNo().equals(warehouseSkuFenceStorageResp.getWarehouseNo())) {
                        // 获取仓库服务商
                        if(WarehouseSourceEnum.SAAS_WAREHOUSE.equals(warehouseSkuFenceStorageResp.getSourceEnum())){
                            productAgentWarehouseDataVo.setWarehouseProvider(tenantDTO.getCompanyName());
                        }else {
                            productAgentWarehouseDataVo.setWarehouseProvider("杭州鲜沐科技有限公司");
                        }

                        List<WarehouseSkuFenceAreaResp> warehouseSkuFenceAreas = warehouseSkuFenceStorageResp.getWarehouseSkuFenceAreas();
                        if (!CollectionUtils.isEmpty(warehouseSkuFenceAreas)) {
                            List<WarehouseSkuFenceAreaDTO> warehouseSkuFenceAreaDTOList = warehouseSkuFenceAreas.stream().map(warehouseSkuFenceAreaResp -> {
                                WarehouseSkuFenceAreaDTO warehouseSkuFenceAreaDTO = new WarehouseSkuFenceAreaDTO();
                                warehouseSkuFenceAreaDTO.setProvince(warehouseSkuFenceAreaResp.getProvince());
                                warehouseSkuFenceAreaDTO.setArea(warehouseSkuFenceAreaResp.getArea());
                                warehouseSkuFenceAreaDTO.setCity(warehouseSkuFenceAreaResp.getCity());
                                return warehouseSkuFenceAreaDTO;
                            }).collect(Collectors.toList());
                            productAgentWarehouseDataVo.setWarehouseSkuFenceAreaDTOList(warehouseSkuFenceAreaDTOList);
                            productAgentWarehouseDataVo.setCityNum(warehouseSkuFenceAreaDTOList.size());
                        }
                    }
                }
            }

            return productAgentWarehouseDataVo;
        }).collect(Collectors.toList());
        PageInfo pageInfo = respPageInfo;
        pageInfo.setList(productAgentWarehouseDataVos);
        return pageInfo;
    }

    @Override
    public CommonResult<PageInfo<ProductAgentWarehouseAggregationVO>> warehouseAggregationList(ProductAgentWarehouseDateQueryDTO productAgentWarehouseDateQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        if (grayReleaseConfig.executeProductCenterGray(loginContextInfoDTO.getTenantId ())) {
            PageInfo pageInfo = convertWarehouseAggregation (productAgentWarehouseDateQueryDTO, loginContextInfoDTO);
            return CommonResult.ok (pageInfo);
        }else {
            PageInfo pageInfo = PageInfoHelper.createPageInfo (new ArrayList<> (), productAgentWarehouseDateQueryDTO.getPageSize ());
            // 查询商品名称
            pageInfo = convertWarehouseAggregationOld (productAgentWarehouseDateQueryDTO, loginContextInfoDTO, pageInfo);
            return CommonResult.ok (pageInfo);
        }
    }

    /**
     * 查询代仓品货品维度当日、近十五天、近三十天销售情况
     * @param skuIds sku表的id列表
     * @param loginContextInfoDTO
     * @return
     */
    private Triple<Map<Long, Integer>, Map<Long, Integer>, Map<Long, Integer>> getSaleInfo(List<Long> skuIds, LoginContextInfoDTO loginContextInfoDTO) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Triple.of(Collections.EMPTY_MAP, Collections.EMPTY_MAP, Collections.EMPTY_MAP);
        }
        // 查询代仓品当日、近十五天、近三十天销售情况
        List<OrderSkuSaleDTO> orderSkuSaleDtos = orderBusinessService.querySkuSaleAmount(skuIds, loginContextInfoDTO.getTenantId());
        Map<Long, Integer> skuSaleAmountMap = orderSkuSaleDtos.stream().collect(Collectors.toMap(OrderSkuSaleDTO::getSkuId, OrderSkuSaleDTO::getSaleAmount));
        List<OrderSkuSaleDTO> fifteenOrderSkuSaleDtos = orderBusinessService.querySkuSaleAmountByDays(skuIds, loginContextInfoDTO.getTenantId(), NumberConstant.FIFTEEN);
        Map<Long, Integer> skuFifteenSaleAmountMap = fifteenOrderSkuSaleDtos.stream().collect(Collectors.toMap(OrderSkuSaleDTO::getSkuId, OrderSkuSaleDTO::getSaleAmount));
        List<OrderSkuSaleDTO> thirtyOrderSkuSaleDtos = orderBusinessService.querySkuSaleAmountByDays(skuIds, loginContextInfoDTO.getTenantId(), NumberConstant.THIRTY);
        Map<Long, Integer> skuThirtySaleAmountMap = thirtyOrderSkuSaleDtos.stream().collect(Collectors.toMap(OrderSkuSaleDTO::getSkuId, OrderSkuSaleDTO::getSaleAmount));
        return Triple.of(skuSaleAmountMap, skuFifteenSaleAmountMap, skuThirtySaleAmountMap);
    }

    /**
     * 查询代仓品仓库维度当日、近十五天、近三十天销售情况
     * @param skuIds 商品信息
     * @param loginContextInfoDTO
     * @return Table sku-仓库id-销量
     */
    private Triple<Table<Long, Integer, Integer>, Table<Long, Integer, Integer>, Table<Long, Integer, Integer>> getWarehouseSaleInfo(List<Long> skuIds, LoginContextInfoDTO loginContextInfoDTO) {
        Table<Long, Integer, Integer> orderSkuSaleTable = HashBasedTable.create();
        Table<Long, Integer, Integer> fifteenOrderSkuSaleTable = HashBasedTable.create();
        Table<Long, Integer, Integer> thirtyOrderSkuSaleTable = HashBasedTable.create();
        if (CollectionUtil.isEmpty(skuIds)) {
            return Triple.of(orderSkuSaleTable, fifteenOrderSkuSaleTable, thirtyOrderSkuSaleTable);
        }
        // 查询代仓品仓库维度当日、近十五天、近三十天销售情况
        List<OrderSkuSaleDTO> orderSkuSaleDtos = orderBusinessService.querySkuSaleAmountByDaysWithStoreNo(skuIds, loginContextInfoDTO.getTenantId(), NumberConstant.ONE);
        orderSkuSaleDtos.stream().forEach(orderSkuSaleDTO ->
                orderSkuSaleTable.put(orderSkuSaleDTO.getSkuId(), orderSkuSaleDTO.getWarehouseId(), orderSkuSaleDTO.getSaleAmount()));
        // 统计近十五天的累计销量
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(NumberConstant.FIFTEEN);
        List<ProductSkuOrderSummaryDayDTO> fifteenOrderSkuSaleDtos = productSkuOrderSummaryMapper.querySaleAmountByTenantIdAndSkuId(loginContextInfoDTO.getTenantId(), skuIds, startDate, endDate);
        fifteenOrderSkuSaleDtos.stream().forEach(orderSkuSaleDTO ->
                fifteenOrderSkuSaleTable.put(orderSkuSaleDTO.getSkuId(), orderSkuSaleDTO.getWarehouseNo(), orderSkuSaleDTO.getSaleQuantity()));

        startDate = endDate.minusDays(NumberConstant.THIRTY);
        List<ProductSkuOrderSummaryDayDTO> thirtyOrderSkuSaleDtos = productSkuOrderSummaryMapper.querySaleAmountByTenantIdAndSkuId(loginContextInfoDTO.getTenantId(), skuIds, startDate, endDate);
        thirtyOrderSkuSaleDtos.stream().forEach(orderSkuSaleDTO ->
                thirtyOrderSkuSaleTable.put(orderSkuSaleDTO.getSkuId(), orderSkuSaleDTO.getWarehouseNo(), orderSkuSaleDTO.getSaleQuantity()));
        return Triple.of(orderSkuSaleTable, fifteenOrderSkuSaleTable, thirtyOrderSkuSaleTable);
    }

    /**
     * 查询代仓品仓库维度当日、近十五天、近三十天销售情况
     * @param skuIds 商品信息
     * @param loginContextInfoDTO
     * @return Table sku-仓库id-销量
     */
    private Triple<Table<Long, Integer, Integer>, Table<Long, Integer, Integer>, Table<Long, Integer, Integer>> getWarehouseSaleInfoOld(List<Long> skuIds, LoginContextInfoDTO loginContextInfoDTO) {
        Table<Long, Integer, Integer> orderSkuSaleTable = HashBasedTable.create();
        Table<Long, Integer, Integer> fifteenOrderSkuSaleTable = HashBasedTable.create();
        Table<Long, Integer, Integer> thirtyOrderSkuSaleTable = HashBasedTable.create();
        // 查询代仓品仓库维度当日、近十五天、近三十天销售情况
        List<OrderSkuSaleDTO> orderSkuSaleDtos = orderBusinessService.querySkuSaleAmountByDaysWithStoreNo(skuIds, loginContextInfoDTO.getTenantId(), NumberConstant.ONE);
        List<OrderSkuSaleDTO> fifteenOrderSkuSaleDtos = orderBusinessService.querySkuSaleAmountByDaysWithStoreNo(skuIds, loginContextInfoDTO.getTenantId(), NumberConstant.FIFTEEN);
        List<OrderSkuSaleDTO> thirtyOrderSkuSaleDtos = orderBusinessService.querySkuSaleAmountByDaysWithStoreNo(skuIds, loginContextInfoDTO.getTenantId(), NumberConstant.THIRTY);
        orderSkuSaleDtos.stream().forEach(orderSkuSaleDTO ->
                orderSkuSaleTable.put(orderSkuSaleDTO.getSkuId(), orderSkuSaleDTO.getWarehouseId(), orderSkuSaleDTO.getSaleAmount()));
        fifteenOrderSkuSaleDtos.stream().forEach(orderSkuSaleDTO ->
                fifteenOrderSkuSaleTable.put(orderSkuSaleDTO.getSkuId(), orderSkuSaleDTO.getWarehouseId(), orderSkuSaleDTO.getSaleAmount()));
        thirtyOrderSkuSaleDtos.stream().forEach(orderSkuSaleDTO ->
                thirtyOrderSkuSaleTable.put(orderSkuSaleDTO.getSkuId(), orderSkuSaleDTO.getWarehouseId(), orderSkuSaleDTO.getSaleAmount()));
        return Triple.of(orderSkuSaleTable, fifteenOrderSkuSaleTable, thirtyOrderSkuSaleTable);
    }

    private PageInfo convertWarehouseAggregation(ProductAgentWarehouseDateQueryDTO productAgentWarehouseDateQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        // 查询商品名称
        SummerfarmAgentSkuWarehouseDataInput summerfarmAgentSkuWarehouseDataInput = packagingQueryCondition(productAgentWarehouseDateQueryDTO, loginContextInfoDTO);
        if (CollectionUtils.isEmpty(summerfarmAgentSkuWarehouseDataInput.getWarehouseIds())) {
            return PageInfoHelper.createPageInfo(Collections.EMPTY_LIST, productAgentWarehouseDateQueryDTO.getPageSize());
        }

        List<ProductSkuDTO> productSkuDtos = productAgentSkuService.queryTenantAgentSku(productAgentWarehouseDateQueryDTO, loginContextInfoDTO).stream ().filter (e-> ObjectUtil.isNotNull (e.getSkuMapping ()) && ObjectUtil.isNotNull (e.getSkuMapping ().getSku ())).collect(Collectors.toList());;
        if (CollectionUtils.isEmpty(productSkuDtos)){
            return PageInfoHelper.createPageInfo(Collections.EMPTY_LIST, productAgentWarehouseDateQueryDTO.getPageSize());
        }

        // 查询商品库存聚合接口
        QuerySkuAggregateInventoryRequest querySkuAggregateInventoryRequest = new QuerySkuAggregateInventoryRequest();
        BeanUtils.copyProperties(summerfarmAgentSkuWarehouseDataInput, querySkuAggregateInventoryRequest);
        querySkuAggregateInventoryRequest.setSaleOut(productAgentWarehouseDateQueryDTO.getSaleOut());
        querySkuAggregateInventoryRequest.setSkuIdList(productSkuDtos.stream().map(e-> e.getSkuMapping ().getAgentSkuId ()).collect(Collectors.toList()));
        querySkuAggregateInventoryRequest.setInOutRecord(ProductWarehouseDataEnums.InOutRecord.getReqByCode(productAgentWarehouseDateQueryDTO.getInOutRecord()));
        querySkuAggregateInventoryRequest.setWarehouseNoList(summerfarmAgentSkuWarehouseDataInput.getWarehouseIds().stream().map(Long::intValue).distinct().collect(Collectors.toList()));
        PageInfo<SkuAggregateInventoryDTO> respPageInfo = saleInventoryServiceFacade.querySkuAggregateInventory(querySkuAggregateInventoryRequest);
        List<SkuAggregateInventoryDTO> inventoryDTOList = respPageInfo.getList();

        Map<Long, String> productAgentSkuMappingMap = productSkuDtos.stream ().collect (Collectors.toMap (ProductSkuDTO::getAgentSkuId, x -> x.getSkuMapping ().getSku ()));

        List<WarehouseSkuFenceResp> warehouseSkuFenceResps = queryWarehouseSkuFence(inventoryDTOList, productAgentSkuMappingMap);
        Map<String, List<WarehouseSkuFenceStorageResp>> warehouseSkuFenceStorageRespListMap = new HashMap<>();
        // 转化为map
        if(!CollectionUtils.isEmpty(warehouseSkuFenceResps)) {
            warehouseSkuFenceStorageRespListMap = warehouseSkuFenceResps.stream().collect(Collectors.toMap(WarehouseSkuFenceResp::getSku, WarehouseSkuFenceResp::getWarehouseSkuFenceStorages));
        }

        Map<Long, ProductSkuDTO> productSkuDtoMap = productSkuDtos.stream().collect(Collectors.toMap(ProductSkuDTO::getAgentSkuId, Function.identity()));

//        List<Long> skuIds = inventoryDTOList.stream().filter(dto -> Objects.nonNull(productSkuDtoMap.get(dto.getSkuId()))).map(dto -> productSkuDtoMap.get(dto.getSkuId()).getId()).collect(Collectors.toList());
//        // 查询代仓品当日、近十五天、近三十天销售情况
//        Triple<Map<Long, Integer>, Map<Long, Integer>, Map<Long, Integer>> saleMap = getSaleInfo(skuIds, loginContextInfoDTO);
//        Map<Long, Integer> skuSaleAmountMap = saleMap.getLeft();
//        Map<Long, Integer> skuFifteenSaleAmountMap = saleMap.getMiddle();
//        Map<Long, Integer> skuThirtySaleAmountMap = saleMap.getRight();


        // 获取符合分页条件的数据查询
        List<Long> skuIds = inventoryDTOList.stream().filter(dto -> Objects.nonNull(productSkuDtoMap.get(dto.getSkuId()))).map(dto -> productSkuDtoMap.get(dto.getSkuId()).getId()).collect(Collectors.toList());
        // 查询代仓品仓库维度当日、近十五天、近三十天销售情况
        Triple<Table<Long, Integer, Integer>, Table<Long, Integer, Integer>, Table<Long, Integer, Integer>> tableTriple = getWarehouseSaleInfo(skuIds, loginContextInfoDTO);
        Table<Long, Integer, Integer> skuSaleAmountMap = tableTriple.getLeft();
        Table<Long, Integer, Integer> skuFifteenSaleAmountMap = tableTriple.getMiddle();
        Table<Long, Integer, Integer> skuThirtySaleAmountMap = tableTriple.getRight();


        // 查询数据的上下架状态
        Map<Long, List<MarketItemOnSaleSimple4StoreResp>> saleSkuIdMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(skuIds)) {
            List<MarketItemOnSaleSimple4StoreResp> marketItems = marketItemBusinessService.selectSaleStatusBySkuIds(loginContextInfoDTO.getTenantId(), skuIds);
            saleSkuIdMap = marketItems.stream().collect(Collectors.groupingBy(MarketItemOnSaleSimple4StoreResp::getSkuId));
        }

        // 查询品牌方对应adminId
        TenantDTO tenantDTO = tenantService.queryTenantById(loginContextInfoDTO.getTenantId());
        // 组装数据
        Map<String, List<WarehouseSkuFenceStorageResp>> finalWarehouseSkuFenceStorageRespListMap = warehouseSkuFenceStorageRespListMap;
        Map<Long, List<MarketItemOnSaleSimple4StoreResp>> finalSaleSkuIdMap = saleSkuIdMap;
        List<ProductAgentWarehouseAggregationVO> productAgentWarehouseDataVos = inventoryDTOList.stream().map(skuAggregateInventoryDTO -> {
            ProductAgentWarehouseAggregationVO productAgentWarehouseAggregationVO = SaleInventoryConverter.INSTANCE.dto2vo(skuAggregateInventoryDTO);

            ProductSkuDTO productSkuDto = productSkuDtoMap.get(skuAggregateInventoryDTO.getSkuId());
            productAgentWarehouseAggregationVO.setSkuId(productSkuDto.getId());
            productAgentWarehouseAggregationVO.setSku(productSkuDto.getAgentSkuCode());

            // 上下架状态组装
            Integer onSale = null;
            List<MarketItemOnSaleSimple4StoreResp> marketItems = finalSaleSkuIdMap.get(productSkuDto.getId());
            if (!CollectionUtils.isEmpty(marketItems)) {
                onSale = marketItems.stream().anyMatch(resp -> OnSaleTypeEnum.ON_SALE.getCode().equals(resp.getOnSale())) ? OnSaleTypeEnum.ON_SALE.getCode() : OnSaleTypeEnum.SOLD_OUT.getCode();
            }
            productAgentWarehouseAggregationVO.setOnSale(onSale);
            productAgentWarehouseAggregationVO.setTitle(productSkuDto.getTitle());
            productAgentWarehouseAggregationVO.setSpecification(productSkuDto.getSpecification());
            productAgentWarehouseAggregationVO.setSpecificationUnit(productSkuDto.getSpecificationUnit());
            productAgentWarehouseAggregationVO.setMainPicture(productSkuDto.getMainPicture());

            List<SkuAggregateInventoryDTO.WarehouseCityMappingDTO> cityMappingList = skuAggregateInventoryDTO.getCityMappingList();
            List<WarehouseSkuFenceStorageResp> warehouseSkuFenceStorageResps = finalWarehouseSkuFenceStorageRespListMap.get(productAgentSkuMappingMap.get (skuAggregateInventoryDTO.getSkuId ()));

            List<ProductAgentWarehouseAggregationVO.WarehouseCityMappingVO> cityMappingVOList = cityMappingList.stream().map(warehouseCityMappingDTO -> {
                ProductAgentWarehouseAggregationVO.WarehouseCityMappingVO warehouseCityMappingVO = SaleInventoryConverter.INSTANCE.dto2vo(warehouseCityMappingDTO);

                ProductWarehouseDataEnums.SyncStatus syncStatus = ProductWarehouseDataEnums.SyncStatus.getByResp(warehouseCityMappingDTO.getSync());
                Optional.ofNullable(syncStatus).ifPresent(enums -> warehouseCityMappingVO.setSync(enums.getStatus()));

                if(!CollectionUtils.isEmpty(warehouseSkuFenceStorageResps)) {
                    for (WarehouseSkuFenceStorageResp warehouseSkuFenceStorageResp : warehouseSkuFenceStorageResps) {
                        if (warehouseCityMappingDTO.getWarehouseNo().equals(warehouseSkuFenceStorageResp.getWarehouseNo())) {
                            // 获取仓库服务商
                            if (WarehouseSourceEnum.SAAS_WAREHOUSE.equals(warehouseSkuFenceStorageResp.getSourceEnum())) {
                                warehouseCityMappingVO.setWarehouseProvider(tenantDTO.getCompanyName());
                            } else {
                                warehouseCityMappingVO.setWarehouseProvider("杭州鲜沐科技有限公司");
                            }

                            List<WarehouseSkuFenceAreaResp> warehouseSkuFenceAreas = warehouseSkuFenceStorageResp.getWarehouseSkuFenceAreas();
                            if (!CollectionUtils.isEmpty(warehouseSkuFenceAreas)) {
                                List<WarehouseSkuFenceAreaDTO> warehouseSkuFenceAreaDTOList = warehouseSkuFenceAreas.stream().map(warehouseSkuFenceAreaResp -> {
                                    WarehouseSkuFenceAreaDTO warehouseSkuFenceAreaDTO = new WarehouseSkuFenceAreaDTO();
                                    warehouseSkuFenceAreaDTO.setProvince(warehouseSkuFenceAreaResp.getProvince());
                                    warehouseSkuFenceAreaDTO.setArea(warehouseSkuFenceAreaResp.getArea());
                                    warehouseSkuFenceAreaDTO.setCity(warehouseSkuFenceAreaResp.getCity());
                                    return warehouseSkuFenceAreaDTO;
                                }).collect(Collectors.toList());
                                warehouseCityMappingVO.setWarehouseSkuFenceAreaDTOList(warehouseSkuFenceAreaDTOList);
                            }
                        }
                    }
                }

                warehouseCityMappingVO.setSaleAmount(Optional.ofNullable(skuSaleAmountMap.get(productSkuDto.getId(), warehouseCityMappingDTO.getWarehouseNo())).orElse(NumberConstant.ZERO));
                warehouseCityMappingVO.setFifteenDaysSaleAmount(Optional.ofNullable(skuFifteenSaleAmountMap.get(productSkuDto.getId(), warehouseCityMappingDTO.getWarehouseNo())).orElse(NumberConstant.ZERO));
                warehouseCityMappingVO.setThirtyDaysSaleAmount(Optional.ofNullable(skuThirtySaleAmountMap.get(productSkuDto.getId(), warehouseCityMappingDTO.getWarehouseNo())).orElse(NumberConstant.ZERO));

                return warehouseCityMappingVO;
            }).filter(e -> Objects.nonNull(e)).collect(Collectors.toList());
            productAgentWarehouseAggregationVO.setCityMappingList(cityMappingVOList);

            productAgentWarehouseAggregationVO.setSaleAmount(cityMappingVOList.stream().mapToInt(ProductAgentWarehouseAggregationVO.WarehouseCityMappingVO::getSaleAmount).sum());
            productAgentWarehouseAggregationVO.setFifteenDaysSaleAmount(cityMappingVOList.stream().mapToInt(ProductAgentWarehouseAggregationVO.WarehouseCityMappingVO::getFifteenDaysSaleAmount).sum());
            productAgentWarehouseAggregationVO.setThirtyDaysSaleAmount(cityMappingVOList.stream().mapToInt(ProductAgentWarehouseAggregationVO.WarehouseCityMappingVO::getThirtyDaysSaleAmount).sum());

            return productAgentWarehouseAggregationVO;
        }).collect(Collectors.toList());
        PageInfo pageInfo = respPageInfo;
        pageInfo.setList(productAgentWarehouseDataVos);
        return pageInfo;
    }


    private PageInfo convertWarehouseAggregationOld(ProductAgentWarehouseDateQueryDTO productAgentWarehouseDateQueryDTO, LoginContextInfoDTO loginContextInfoDTO, PageInfo pageInfo) {
        // 查询商品名称
        SummerfarmAgentSkuWarehouseDataInput summerfarmAgentSkuWarehouseDataInput = packagingQueryCondition(productAgentWarehouseDateQueryDTO, loginContextInfoDTO);
        List<ProductSkuDTO> productSkuDtos = productAgentSkuService.queryTenantAgentSku(productAgentWarehouseDateQueryDTO, loginContextInfoDTO);
        if (CollectionUtils.isEmpty(productSkuDtos) || CollectionUtils.isEmpty(summerfarmAgentSkuWarehouseDataInput.getWarehouseIds())) {
            return pageInfo;
        }

        Map<Long, ProductSkuDTO> productSkuDtoMap = productSkuDtos.stream().collect(Collectors.toMap(ProductSkuDTO::getAgentSkuId, Function.identity()));
        // 查询商品库存聚合接口
        QuerySkuAggregateInventoryRequest querySkuAggregateInventoryRequest = new QuerySkuAggregateInventoryRequest();
        BeanUtils.copyProperties(summerfarmAgentSkuWarehouseDataInput, querySkuAggregateInventoryRequest);
        List<Long> agentSkuIds = productSkuDtos.stream().map(ProductSkuDTO::getAgentSkuId).collect(Collectors.toList());
        querySkuAggregateInventoryRequest.setSaleOut(productAgentWarehouseDateQueryDTO.getSaleOut());
        querySkuAggregateInventoryRequest.setSkuIdList(agentSkuIds);
        Boolean inOutRecord = ProductWarehouseDataEnums.InOutRecord.getReqByCode(productAgentWarehouseDateQueryDTO.getInOutRecord());
        querySkuAggregateInventoryRequest.setInOutRecord(inOutRecord);
        querySkuAggregateInventoryRequest.setWarehouseNoList(summerfarmAgentSkuWarehouseDataInput.getWarehouseIds().stream().map(Long::intValue).distinct().collect(Collectors.toList()));
        PageInfo<SkuAggregateInventoryDTO> respPageInfo = saleInventoryServiceFacade.querySkuAggregateInventory(querySkuAggregateInventoryRequest);
        pageInfo.setSize(respPageInfo.getSize());
        pageInfo.setPageSize(respPageInfo.getPageSize());
        pageInfo.setPageNum(respPageInfo.getPageNum());
        pageInfo.setTotal(respPageInfo.getTotal());

        List<SkuAggregateInventoryDTO> list = respPageInfo.getList();
        Set<Long> skuIdSet = productSkuDtos.stream().map(ProductSkuDTO::getId).collect(Collectors.toSet());
        // 查询代仓货品关联鲜沐品SkuId
        List<ProductAgentSkuMapping> productAgentSkuMappings = productAgentSkuMappingDao.selectBySkuIds(new ArrayList<>(skuIdSet), loginContextInfoDTO.getTenantId());
        Map<Long, ProductAgentSkuMapping> productAgentSkuMappingMap = productAgentSkuMappings.stream().collect(Collectors.toMap(ProductAgentSkuMapping::getAgentSkuId, item -> item));
        List<WarehouseSkuFenceResp> warehouseSkuFenceResps = queryWarehouseSkuFenceOld (list, productAgentSkuMappingMap);
        Map<String, List<WarehouseSkuFenceStorageResp>> warehouseSkuFenceStorageRespListMap = new HashMap<>();
        // 转化为map
        if(!CollectionUtils.isEmpty(warehouseSkuFenceResps)) {
            warehouseSkuFenceStorageRespListMap = warehouseSkuFenceResps.stream().collect(Collectors.toMap(WarehouseSkuFenceResp::getSku, WarehouseSkuFenceResp::getWarehouseSkuFenceStorages));
        }

        List<Long> skuIds = list.stream().filter(dto -> Objects.nonNull(productSkuDtoMap.get(dto.getSkuId()))).map(dto -> productSkuDtoMap.get(dto.getSkuId()).getId()).collect(Collectors.toList());
        // 查询代仓品当日、近十五天、近三十天销售情况
        Triple<Map<Long, Integer>, Map<Long, Integer>, Map<Long, Integer>> saleMap = getSaleInfo(skuIds, loginContextInfoDTO);
        Map<Long, Integer> skuSaleAmountMap = saleMap.getLeft();
        Map<Long, Integer> skuFifteenSaleAmountMap = saleMap.getMiddle();
        Map<Long, Integer> skuThirtySaleAmountMap = saleMap.getRight();
        // 查询数据的上下架状态
        Map<Long, List<MarketItemOnSaleSimple4StoreResp>> saleSkuIdMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(skuIds)) {
            List<MarketItemOnSaleSimple4StoreResp> marketItems = marketItemBusinessService.selectSaleStatusBySkuIds(loginContextInfoDTO.getTenantId(), skuIds);
            saleSkuIdMap = marketItems.stream().collect(Collectors.groupingBy(MarketItemOnSaleSimple4StoreResp::getSkuId));
        }

        // 查询品牌方对应adminId
        TenantDTO tenantDTO = tenantService.queryTenantById(loginContextInfoDTO.getTenantId());
        // 组装数据
        Map<String, List<WarehouseSkuFenceStorageResp>> finalWarehouseSkuFenceStorageRespListMap = warehouseSkuFenceStorageRespListMap;
        Map<Long, List<MarketItemOnSaleSimple4StoreResp>> finalSaleSkuIdMap = saleSkuIdMap;
        List<ProductAgentWarehouseAggregationVO> productAgentWarehouseDataVos = respPageInfo.getList().stream().map(productAgentWarehouseDataVo -> {
            ProductAgentWarehouseAggregationVO productAgentWarehouseAggregationVO = new ProductAgentWarehouseAggregationVO();
            BeanUtils.copyProperties(productAgentWarehouseDataVo, productAgentWarehouseAggregationVO);
            ProductSkuDTO productSkuDto = productSkuDtoMap.get(productAgentWarehouseDataVo.getSkuId());
            productAgentWarehouseAggregationVO.setSkuId(productSkuDto.getId());
            productAgentWarehouseAggregationVO.setSku(productSkuDto.getAgentSkuCode());

            // 上下架状态组装
            Integer onSale = null;
            List<MarketItemOnSaleSimple4StoreResp> marketItems = finalSaleSkuIdMap.get(productSkuDto.getId());
            if (!CollectionUtils.isEmpty(marketItems)) {
                onSale = marketItems.stream().anyMatch(resp -> OnSaleTypeEnum.ON_SALE.getCode().equals(resp.getOnSale())) ? OnSaleTypeEnum.ON_SALE.getCode() : OnSaleTypeEnum.SOLD_OUT.getCode();
            }
            productAgentWarehouseAggregationVO.setOnSale(onSale);
            productAgentWarehouseAggregationVO.setTitle(productSkuDto.getTitle());
            productAgentWarehouseAggregationVO.setSpecification(productSkuDto.getSpecification());
            productAgentWarehouseAggregationVO.setSpecificationUnit(productSkuDto.getSpecificationUnit());
            productAgentWarehouseAggregationVO.setRoadQuantity(Objects.isNull(productAgentWarehouseDataVo.getRoadQuantity()) ? NumberConstant.ZERO : productAgentWarehouseDataVo.getRoadQuantity());
            productAgentWarehouseAggregationVO.setSaleAmount(skuSaleAmountMap.containsKey(productSkuDto.getId()) ? skuSaleAmountMap.get(productSkuDto.getId()) : NumberConstant.ZERO);
            productAgentWarehouseAggregationVO.setFifteenDaysSaleAmount(skuFifteenSaleAmountMap.containsKey(productSkuDto.getId()) ? skuFifteenSaleAmountMap.get(productSkuDto.getId()) : NumberConstant.ZERO);
            productAgentWarehouseAggregationVO.setThirtyDaysSaleAmount(skuThirtySaleAmountMap.containsKey(productSkuDto.getId()) ? skuThirtySaleAmountMap.get(productSkuDto.getId()) : NumberConstant.ZERO);
            productAgentWarehouseAggregationVO.setMainPicture(productSkuDto.getMainPicture());
            productAgentWarehouseAggregationVO.setOnlineQuantity(productAgentWarehouseDataVo.getOnlineQuantity());
            List<SkuAggregateInventoryDTO.WarehouseCityMappingDTO> cityMappingList = productAgentWarehouseDataVo.getCityMappingList();
            ProductAgentSkuMapping productAgentSkuMapping = productAgentSkuMappingMap.get(productAgentWarehouseDataVo.getSkuId());
            List<WarehouseSkuFenceStorageResp> warehouseSkuFenceStorageResps = finalWarehouseSkuFenceStorageRespListMap.get(productAgentSkuMapping.getAgentSkuCode());
            List<ProductAgentWarehouseAggregationVO.WarehouseCityMappingVO> cityMappingVOList = cityMappingList.stream().map(warehouseCityMappingDTO -> {
                ProductAgentWarehouseAggregationVO.WarehouseCityMappingVO warehouseCityMappingVO = new ProductAgentWarehouseAggregationVO.WarehouseCityMappingVO();
                warehouseCityMappingVO.setWarehouseName(warehouseCityMappingDTO.getWarehouseName());
                ProductWarehouseDataEnums.SyncStatus syncStatus = ProductWarehouseDataEnums.SyncStatus.getByResp(warehouseCityMappingDTO.getSync());
                Optional.ofNullable(syncStatus).ifPresent(enums -> warehouseCityMappingVO.setSync(enums.getStatus()));

                warehouseCityMappingVO.setWarehouseNo(warehouseCityMappingDTO.getWarehouseNo());
                if(!CollectionUtils.isEmpty(warehouseSkuFenceStorageResps)) {
                    for (WarehouseSkuFenceStorageResp warehouseSkuFenceStorageResp : warehouseSkuFenceStorageResps) {
                        if (warehouseCityMappingDTO.getWarehouseNo().equals(warehouseSkuFenceStorageResp.getWarehouseNo())) {
                            // 获取仓库服务商
                            if (WarehouseSourceEnum.SAAS_WAREHOUSE.equals(warehouseSkuFenceStorageResp.getSourceEnum())) {
                                warehouseCityMappingVO.setWarehouseProvider(tenantDTO.getCompanyName());
                            } else {
                                warehouseCityMappingVO.setWarehouseProvider("杭州鲜沐科技有限公司");
                            }

                            List<WarehouseSkuFenceAreaResp> warehouseSkuFenceAreas = warehouseSkuFenceStorageResp.getWarehouseSkuFenceAreas();
                            if (!CollectionUtils.isEmpty(warehouseSkuFenceAreas)) {
                                List<WarehouseSkuFenceAreaDTO> warehouseSkuFenceAreaDTOList = warehouseSkuFenceAreas.stream().map(warehouseSkuFenceAreaResp -> {
                                    WarehouseSkuFenceAreaDTO warehouseSkuFenceAreaDTO = new WarehouseSkuFenceAreaDTO();
                                    warehouseSkuFenceAreaDTO.setProvince(warehouseSkuFenceAreaResp.getProvince());
                                    warehouseSkuFenceAreaDTO.setArea(warehouseSkuFenceAreaResp.getArea());
                                    warehouseSkuFenceAreaDTO.setCity(warehouseSkuFenceAreaResp.getCity());
                                    return warehouseSkuFenceAreaDTO;
                                }).collect(Collectors.toList());
                                warehouseCityMappingVO.setWarehouseSkuFenceAreaDTOList(warehouseSkuFenceAreaDTOList);
                            }
                        }
                    }
                }
                return warehouseCityMappingVO;
            }).filter(e -> Objects.nonNull(e)).collect(Collectors.toList());
            productAgentWarehouseAggregationVO.setCityMappingList(cityMappingVOList);
            return productAgentWarehouseAggregationVO;
        }).collect(Collectors.toList());
        pageInfo.setList(productAgentWarehouseDataVos);
        return pageInfo;
    }

    /**
     * 查询sku基于仓维度的覆盖区域
     *
     * @param list
     * @param productAgentSkuMappingMap
     * @return
     */
    private List<WarehouseSkuFenceResp> queryWarehouseSkuFenceOld(List<SkuAggregateInventoryDTO> list, Map<Long, ProductAgentSkuMapping> productAgentSkuMappingMap){
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }

        List<WarehouseSkuFenceReq> warehouseSkuFenceReqs = list.stream().map(skuAggregateInventoryDTO -> {
            WarehouseSkuFenceReq warehouseSkuFenceReq = new WarehouseSkuFenceReq();
            ProductAgentSkuMapping productAgentSkuMapping = productAgentSkuMappingMap.get(skuAggregateInventoryDTO.getSkuId());
            warehouseSkuFenceReq.setSku(productAgentSkuMapping.getAgentSkuCode());
            List<SkuAggregateInventoryDTO.WarehouseCityMappingDTO> cityMappingList = skuAggregateInventoryDTO.getCityMappingList();
            List<Integer> warehouseNos = cityMappingList.stream().map(SkuAggregateInventoryDTO.WarehouseCityMappingDTO::getWarehouseNo).collect(Collectors.toList());
            warehouseSkuFenceReq.setWarehouseNos(warehouseNos);
            return warehouseSkuFenceReq;
        }).collect(Collectors.toList());

        List<WarehouseSkuFenceResp> warehouseSkuFenceResps = warehouseStorageFenceQueryFacade.queryWarehouseSkuFence(warehouseSkuFenceReqs);
        return warehouseSkuFenceResps;
    }
    /**
     * 查询sku基于仓维度的覆盖区域
     *
     * @param list
     * @param productAgentSkuMappingMap
     * @return
     */
    private List<WarehouseSkuFenceResp> queryWarehouseSkuFence(List<SkuAggregateInventoryDTO> list, Map<Long, String> productAgentSkuMappingMap){
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }

        List<WarehouseSkuFenceReq> warehouseSkuFenceReqs = list.stream().map(skuAggregateInventoryDTO -> {
            WarehouseSkuFenceReq warehouseSkuFenceReq = new WarehouseSkuFenceReq();
            warehouseSkuFenceReq.setSku(productAgentSkuMappingMap.get(skuAggregateInventoryDTO.getSkuId()));
            List<SkuAggregateInventoryDTO.WarehouseCityMappingDTO> cityMappingList = skuAggregateInventoryDTO.getCityMappingList();
            List<Integer> warehouseNos = cityMappingList.stream().map(SkuAggregateInventoryDTO.WarehouseCityMappingDTO::getWarehouseNo).collect(Collectors.toList());
            warehouseSkuFenceReq.setWarehouseNos(warehouseNos);
            return warehouseSkuFenceReq;
        }).collect(Collectors.toList());

        List<WarehouseSkuFenceResp> warehouseSkuFenceResps = warehouseStorageFenceQueryFacade.queryWarehouseSkuFence(warehouseSkuFenceReqs);
        return warehouseSkuFenceResps;
    }

    @Override
    public CommonResult<ProductAgentWarehouseOverviewVO> warehouseOverview(ProductAgentWarehouseOverviewQueryDTO queryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        // 查询仓库信息
        List<Long> warehouseIds = queryDTO.getWarehouseIds();
        if (CollectionUtils.isEmpty(warehouseIds)) {
            List<ProductAgentWarehouseVO> productAgentWarehouseVos = productAgentWarehouseService.queryByTenantId(loginContextInfoDTO.getTenantId());
            // 查询所有仓库
            warehouseIds = productAgentWarehouseVos.stream().map(ProductAgentWarehouseVO::getWarehouseId).collect(Collectors.toList());
        }

        ProductAgentWarehouseDateQueryDTO productAgentWarehouseDateQueryDTO = new ProductAgentWarehouseDateQueryDTO();
        BeanUtils.copyProperties(queryDTO, productAgentWarehouseDateQueryDTO);
        ProductSkuWarehouseQueryDTO productSkuWarehouseQueryDTO = transferProductSkuWarehouseQueryDTO(queryDTO.getSync(), queryDTO.getInOutRecord());
        // 查询商品名称
        List<ProductSkuDTO> productSkuDtos = productAgentSkuService.queryTenantAgentSku(productAgentWarehouseDateQueryDTO, loginContextInfoDTO);
        if (CollectionUtils.isEmpty(productSkuDtos) || CollectionUtils.isEmpty(warehouseIds)) {
            return CommonResult.ok(ProductAgentWarehouseOverviewVO.getDefaultVO());
        }

        // 发起rpc请求，获取库存统计数据
        List<Long> agentSkuIds = productSkuDtos.stream().map(ProductSkuDTO::getAgentSkuId).collect(Collectors.toList());
        QueryAggregateInventoryRequest queryAggregateInventoryRequest = new QueryAggregateInventoryRequest();
        queryAggregateInventoryRequest.setWarehouseNoList(warehouseIds.stream().map(Long::intValue).collect(Collectors.toList()));
        queryAggregateInventoryRequest.setSkuIdList(agentSkuIds);
        queryAggregateInventoryRequest.setSaleOut(queryDTO.getSaleOut());
        queryAggregateInventoryRequest.setInOutRecord(productSkuWarehouseQueryDTO.getInOutRecord());
        queryAggregateInventoryRequest.setSync(productSkuWarehouseQueryDTO.getSync());
        QueryAggregateInventoryResp.AggregateInventoryDTO aggregateInventoryDTO = saleInventoryServiceFacade.queryAggregateInventory(queryAggregateInventoryRequest);

        ProductAgentWarehouseOverviewVO productAgentWarehouseOverviewVO = new ProductAgentWarehouseOverviewVO();
        productAgentWarehouseOverviewVO.setEnabledQuantity(DecimalFormat.getNumberInstance().format(Optional.ofNullable(aggregateInventoryDTO.getEnabledQuantity()).orElse(NumberConstant.ZERO)));
        BigDecimal goodsValue = Optional.ofNullable(aggregateInventoryDTO).map(QueryAggregateInventoryResp.AggregateInventoryDTO::getGoodsValue).orElse(BigDecimal.ZERO);
        DecimalFormat decimalFormat = new DecimalFormat("##,##0.00");
        productAgentWarehouseOverviewVO.setGoodsValue(decimalFormat.format(goodsValue.setScale(2, BigDecimal.ROUND_HALF_UP)));
        productAgentWarehouseOverviewVO.setLockQuantity(DecimalFormat.getNumberInstance().format(Optional.ofNullable(aggregateInventoryDTO.getLockQuantity()).orElse(NumberConstant.ZERO)));
        productAgentWarehouseOverviewVO.setQuantity(DecimalFormat.getNumberInstance().format(Optional.ofNullable(aggregateInventoryDTO.getQuantity()).orElse(NumberConstant.ZERO)));
        productAgentWarehouseOverviewVO.setRoadQuantity(DecimalFormat.getNumberInstance().format(Optional.ofNullable(aggregateInventoryDTO.getRoadQuantity()).orElse(NumberConstant.ZERO)));
        productAgentWarehouseOverviewVO.setAllocationRoadQuantity(DecimalFormat.getNumberInstance().format(Optional.ofNullable(aggregateInventoryDTO.getAllocationRoadQuantity()).orElse(NumberConstant.ZERO)));
        productAgentWarehouseOverviewVO.setPurchaseRoadQuantity(DecimalFormat.getNumberInstance().format(Optional.ofNullable(aggregateInventoryDTO.getPurchaseRoadQuantity()).orElse(NumberConstant.ZERO)));
        productAgentWarehouseOverviewVO.setSafeQuantity(DecimalFormat.getNumberInstance().format(Optional.ofNullable(aggregateInventoryDTO.getSafeQuantity()).orElse(NumberConstant.ZERO)));
        productAgentWarehouseOverviewVO.setOnlineQuantity(DecimalFormat.getNumberInstance().format(Optional.ofNullable(aggregateInventoryDTO.getOnlineQuantity()).orElse(NumberConstant.ZERO)));

        return CommonResult.ok(productAgentWarehouseOverviewVO);
    }

    @Override
    public CommonResult export(ProductAgentWarehouseDateQueryDTO productAgentWarehouseDateQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        productAgentWarehouseDateQueryDTO.setPageNum(NumberConstant.ONE);
        productAgentWarehouseDateQueryDTO.setPageSize(NumberConstant.TEN_THOUSAND);

        //存储文件下载记录
        Map<String, Object> paramsMap = new HashMap<>();
        if (!Objects.isNull(productAgentWarehouseDateQueryDTO.getSkuId())) {
            paramsMap.put("货品ID", productAgentWarehouseDateQueryDTO.getSkuId());
        }

        if (!CollectionUtils.isEmpty(productAgentWarehouseDateQueryDTO.getWarehouseIds())) {
            List<ProductAgentWarehouseVO> productAgentWarehouseVos = productAgentWarehouseService.queryByTenantId(loginContextInfoDTO.getTenantId());
            Map<Long, String> warehouseMap = productAgentWarehouseVos.stream().collect(Collectors.toMap(ProductAgentWarehouseVO::getWarehouseId, ProductAgentWarehouseVO::getWarehouseName));
            List<String> warehouseList = productAgentWarehouseDateQueryDTO.getWarehouseIds().stream().map(warehouseId -> {
                return warehouseMap.get(warehouseId);
                    }
            ).collect(Collectors.toList());
            paramsMap.put("仓库名称", JSON.toJSONString(warehouseList));
        }

        if (!Objects.isNull(productAgentWarehouseDateQueryDTO.getTitle())) {
            paramsMap.put("货品名称", productAgentWarehouseDateQueryDTO.getTitle());
        }

        if (!Objects.isNull(productAgentWarehouseDateQueryDTO.getSaleOut())) {
            paramsMap.put("是否售罄", productAgentWarehouseDateQueryDTO.getSaleOut() ? "是" : "否");
        }
        if (!Objects.isNull(productAgentWarehouseDateQueryDTO.getOnSale())) {
            paramsMap.put("是否上架", OnSaleTypeEnum.getRemark(productAgentWarehouseDateQueryDTO.getOnSale()));
        }
        if (!Objects.isNull(productAgentWarehouseDateQueryDTO.getSync())) {
            paramsMap.put("库存同步状态", ProductWarehouseDataEnums.SyncStatus.getDescByStatus(productAgentWarehouseDateQueryDTO.getSync()));
        }
        if (!Objects.isNull(productAgentWarehouseDateQueryDTO.getInOutRecord())) {
            paramsMap.put("出入库流水", ProductWarehouseDataEnums.InOutRecord.getDescByCode(productAgentWarehouseDateQueryDTO.getInOutRecord()));
        }

        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
        recordDTO.setBizType(FileDownloadTypeEnum.AGENT_SKU_WAREHOUSE_DATA.getType());
        recordDTO.setTenantId(loginContextInfoDTO.getTenantId());
        recordDTO.setFileName(ExcelTypeEnum.AGENT_SKU_WAREHOUSE_DATA.getFileName());
        recordDTO.setParams(paramsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(paramsMap));
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO).asyncWriteWithOssResp(productAgentWarehouseDateQueryDTO, e -> writeDownloadCenter(e, loginContextInfoDTO, recordDTO.getFileName()));

        return CommonResult.ok();
    }


    public DownloadCenterOssRespDTO writeDownloadCenter(ProductAgentWarehouseDateQueryDTO productAgentWarehouseDateQueryDTO, LoginContextInfoDTO loginContextInfoDTO, String fileName) {
        // 1、表格处理
        String filePath = agentSkuWarehouseDataExportV2(productAgentWarehouseDateQueryDTO, loginContextInfoDTO);

        // 2、文件上传至oss
        OssUploadResult uploadResult = null;
        try {
            uploadResult = OssUploadUtil.upload(fileName, FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
        } catch (IOException e) {
            log.error("filePath={}", filePath, e);
            throw new BizException("读取文件报错");
        } finally {
            commonService.deleteFile(filePath);
        }
        // 3、返回文件地址
        DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
        downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
        downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
        return downloadCenterOssRespDTO;
    }


    @Override
    public CommonResult<PageInfo<ProductAgentWarehouseShelfLifeVO>> pageQueryBatchInventory(ProductAgentShelfLifeQueryDTO queryDTO, Long tenantId) {
        PageInfo<ProductAgentWarehouseShelfLifeVO> pageInfo = new PageInfo<>();
        // to agent skuId
        ProductAgentSkuMapping productAgentSkuMapping = productAgentSkuMappingMapper.selectByTenantIdAndSkuId(tenantId, queryDTO.getSkuId());
        if (productAgentSkuMapping == null) {
            throw new DefaultServiceException("sku 不存在");
        }
        queryDTO.setSkuId(productAgentSkuMapping.getAgentSkuId());
        PageInfo<net.summerfarm.wms.saleinventory.dto.dto.PurchaseBatchInventoryDTO> resp = saasInventoryFacade.pageQueryPurchaseBatchInventory(queryDTO);
        pageInfo.setSize(resp.getSize());
        pageInfo.setPageSize(resp.getPageSize());
        pageInfo.setPageNum(resp.getPageNum());
        pageInfo.setTotal(resp.getTotal());
        if (resp.getList() != null) {
            pageInfo.setList(resp.getList().stream().map(ProductAgentWarehouseShelfLifeMapper.INSTANCE::toVO).collect(Collectors.toList()));
        }
        return CommonResult.ok(pageInfo);
    }
    /**
     * 代仓商品库存数据导出
     *
     * @param productAgentWarehouseDateQueryDTO
     * @param loginContextInfoDTO
     */
    private String agentSkuWarehouseDataExportV2(ProductAgentWarehouseDateQueryDTO productAgentWarehouseDateQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        productAgentWarehouseDateQueryDTO.setPageNum(NumberConstants.ONE);
        productAgentWarehouseDateQueryDTO.setPageSize(NumberConstants.HUNDRED);

        PageInfo pageInfo = new PageInfo();
        if (grayReleaseConfig.executeProductCenterGray(loginContextInfoDTO.getTenantId ())) {
            pageInfo = convertWarehouseData (productAgentWarehouseDateQueryDTO, loginContextInfoDTO);
        }else {
            pageInfo = convertWarehouseDataOld (productAgentWarehouseDateQueryDTO, loginContextInfoDTO, pageInfo);
        }
        int pages = (int) Math.ceil(pageInfo.getTotal() / (double) productAgentWarehouseDateQueryDTO.getPageSize());
        List<ProductAgentWarehouseDataVO> productAgentWarehouseDataVOList = pageInfo.getList();
        // 创建excelWriter
        String filePath = ExcelUtils.tempExcelFilePath();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
        ExcelWriter excelWriter = EasyExcel.write(filePath, ProductAgentWarehouseDataVO.class).registerConverter(new EasyExcelLocalDateConverter()).registerConverter(new LocalDateTimeConverter())
                .withTemplate(ExcelUtils.getExcelFileInputStream(this.getClass(), ExcelTypeEnum.AGENT_SKU_WAREHOUSE_DATA.getName())).build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        excelWriter.fill(Collections.EMPTY_LIST, fillConfig, writeSheet);

        // 分页查询
        for (int pageNum = NumberConstants.ONE; pageNum <= pages; pageNum++) {
            if (pageNum > NumberConstants.ONE) {
                productAgentWarehouseDateQueryDTO.setPageNum(pageNum);
                if (grayReleaseConfig.executeProductCenterGray(loginContextInfoDTO.getTenantId ())) {
                    productAgentWarehouseDataVOList = convertWarehouseData (productAgentWarehouseDateQueryDTO, loginContextInfoDTO).getList();
                }else {
                    productAgentWarehouseDataVOList = convertWarehouseDataOld (productAgentWarehouseDateQueryDTO, loginContextInfoDTO, pageInfo).getList();
                }
            }
            if (CollectionUtil.isEmpty(productAgentWarehouseDataVOList)) {
                break;
            }

            // 实时库存导出数据填充安全库存
            fillDataReplenishmentStock(loginContextInfoDTO.getTenantId(), productAgentWarehouseDataVOList);
            // 分批写
            excelWriter.fill(productAgentWarehouseDataVOList, fillConfig, writeSheet);
        }
        excelWriter.finish();

        return filePath;
    }

    /**
     * 实时库存导出数据填充安全库存
     *
     * @param productAgentWarehouseDataVOList
     */
    private void fillDataReplenishmentStock(Long tenantId, List<ProductAgentWarehouseDataVO> productAgentWarehouseDataVOList) {
        if (CollectionUtils.isEmpty(productAgentWarehouseDataVOList)) {
            return;
        }

        List<ReplenishmentStockConfigBaseQueryInput> queryInputs = productAgentWarehouseDataVOList.stream().map(e -> {
            ReplenishmentStockConfigBaseQueryInput input = new ReplenishmentStockConfigBaseQueryInput();
            input.setSku(e.getSku());
            input.setWarehouseNo(e.getWarehouseNo());
            return input;
        }).collect(Collectors.toList());

        Table<String, Integer, ReplenishmentStockConfigDTO> table = scpFacade.queryReplenishmentStockConfig(tenantId, queryInputs);

        productAgentWarehouseDataVOList.stream().forEach(e -> {
            ReplenishmentStockConfigDTO dto = table.get(e.getSku(), e.getWarehouseNo());
            if(dto != null) {
                e.setStockLevelMinimum(dto.getStockLevelMinimum());
                e.setStockLevelMaximum(dto.getStockLevelMaximum());
            }
        });
    }


    /**
     * 库存数据处理
     *
     * @param warehouseDateQueryDTO
     * @param loginContextInfoDTO
     * @param pageInfo
     * @return
     */
    private PageInfo convertWarehouseDataOld(ProductAgentWarehouseDateQueryDTO warehouseDateQueryDTO, LoginContextInfoDTO loginContextInfoDTO, PageInfo pageInfo) {
        SummerfarmAgentSkuWarehouseDataInput summerfarmAgentSkuWarehouseDataInput = packagingQueryCondition(warehouseDateQueryDTO, loginContextInfoDTO);
        ProductSkuWarehouseQueryDTO productSkuWarehouseQueryDTO = transferProductSkuWarehouseQueryDTO(warehouseDateQueryDTO.getSync(), warehouseDateQueryDTO.getInOutRecord());
        List<ProductAgentWarehouseDataVO> productAgentWarehouseDataVos = new ArrayList<>();
        // 查询商品名称
        List<ProductSkuDTO> productSkuDtos = productAgentSkuService.queryTenantAgentSku(warehouseDateQueryDTO, loginContextInfoDTO);
        if (CollectionUtils.isEmpty(productSkuDtos) || CollectionUtils.isEmpty(summerfarmAgentSkuWarehouseDataInput.getWarehouseIds()) ) {
            return pageInfo;
        }

        List<Long> agentSkuIds = productSkuDtos.stream().map(ProductSkuDTO::getAgentSkuId).collect(Collectors.toList());
        summerfarmAgentSkuWarehouseDataInput.setSkuId(agentSkuIds);
        Map<Long, ProductSkuDTO> productSkuDtoMap = productSkuDtos.stream().collect(Collectors.toMap(ProductSkuDTO::getAgentSkuId, item -> item));
        // 查询鲜沐代仓实时库存
        PageInfo<QueryInventoryResp> respPageInfo = saleInventoryServiceFacade.pageQueryAgentSkuWarehouseData(summerfarmAgentSkuWarehouseDataInput, productSkuWarehouseQueryDTO);
        pageInfo.setSize(respPageInfo.getSize());
        pageInfo.setPageSize(respPageInfo.getPageSize());
        pageInfo.setPageNum(respPageInfo.getPageNum());
        pageInfo.setTotal(respPageInfo.getTotal());
        BatchQueryMinShelfLifePurchaseBatchReq batchReq = new BatchQueryMinShelfLifePurchaseBatchReq();
        batchReq.setQueryDTOList(respPageInfo.getList().stream().map(data-> BatchQueryMinShelfLifePurchaseBatchReq.PurchaseBatchQueryDTO.builder().skuId(data.getSkuId()).warehouseNo(data.getWarehouseNo()).build()).collect(Collectors.toList()));
        // 最近保质期
        Map<Long, Map<Integer, MinShelfLifePurchaseBatchDTO>> latestShelfLifeMap = saasInventoryFacade.batchQueryLatestShelfLife(batchReq);

        List<QueryInventoryResp> summerFarmAgentSkuWarehouseDataResps = respPageInfo.getList();

        // 获取符合分页条件的数据查询
        List<Long> skuIds = summerFarmAgentSkuWarehouseDataResps.stream().filter(dto -> Objects.nonNull(productSkuDtoMap.get(dto.getSkuId()))).map(dto -> productSkuDtoMap.get(dto.getSkuId()).getId()).collect(Collectors.toList());
        // 查询代仓品仓库维度当日、近十五天、近三十天销售情况
        Triple<Table<Long, Integer, Integer>, Table<Long, Integer, Integer>, Table<Long, Integer, Integer>> tableTriple = getWarehouseSaleInfo(skuIds, loginContextInfoDTO);
        Table<Long, Integer, Integer> skuSaleAmountMap = tableTriple.getLeft();
        Table<Long, Integer, Integer> skuFifteenSaleAmountMap = tableTriple.getMiddle();
        Table<Long, Integer, Integer> skuThirtySaleAmountMap = tableTriple.getRight();
        // 查询数据的上下架状态
        Map<Long, List<MarketItemOnSaleSimple4StoreResp>> saleSkuIdMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(skuIds)) {
            List<MarketItemOnSaleSimple4StoreResp> marketItems = marketItemBusinessService.selectSaleStatusBySkuIds(loginContextInfoDTO.getTenantId(), skuIds);
            saleSkuIdMap = marketItems.stream().collect(Collectors.groupingBy(MarketItemOnSaleSimple4StoreResp::getSkuId));
        }
        // 按照sku分组
        Map<Long, List<QueryInventoryResp>> map = summerFarmAgentSkuWarehouseDataResps.stream().collect(Collectors.groupingBy(QueryInventoryResp::getSkuId));
        // 查询sku信息
        Set<Long> skuIdSet = productSkuDtos.stream().map(ProductSkuDTO::getId).collect(Collectors.toSet());
        //
        // 查询代仓货品关联鲜沐品SkuId
        List<ProductAgentSkuMapping> productAgentSkuMappings = productAgentSkuMappingDao.selectBySkuIds(new ArrayList<>(skuIdSet), loginContextInfoDTO.getTenantId());
        Map<Long, ProductAgentSkuMapping> productAgentSkuMappingMap = productAgentSkuMappings.stream().collect(Collectors.toMap(ProductAgentSkuMapping::getAgentSkuId, item -> item));
        // 查询sku基于仓维度的覆盖区域
        List<WarehouseSkuFenceResp> warehouseSkuFenceResps = queryWarehouseSkuFenceOld(map, productAgentSkuMappingMap);
        // 转化为map
        Map<String, List<WarehouseSkuFenceStorageResp>> warehouseSkuFenceStorageRespListMap = warehouseSkuFenceResps.stream().collect(Collectors.toMap(WarehouseSkuFenceResp::getSku, WarehouseSkuFenceResp::getWarehouseSkuFenceStorages));
        // 查询品牌方对应adminId
        TenantDTO tenantDTO = tenantService.queryTenantById(loginContextInfoDTO.getTenantId());
        // 组装数据
        Map<Long, List<MarketItemOnSaleSimple4StoreResp>> finalSaleSkuIdMap = saleSkuIdMap;
        productAgentWarehouseDataVos = respPageInfo.getList().stream().map(summerfarmAgentSkuWarehouseDataDTO -> {
            ProductAgentWarehouseDataVO productAgentWarehouseDataVo = new ProductAgentWarehouseDataVO();
            BeanUtils.copyProperties(summerfarmAgentSkuWarehouseDataDTO, productAgentWarehouseDataVo);
            ProductSkuDTO productSkuDto = productSkuDtoMap.get(summerfarmAgentSkuWarehouseDataDTO.getSkuId());
            MinShelfLifePurchaseBatchDTO minShelfLifePurchaseBatchDTO = latestShelfLifeMap.getOrDefault(summerfarmAgentSkuWarehouseDataDTO.getSkuId(), new HashMap<>(0)).getOrDefault(summerfarmAgentSkuWarehouseDataDTO.getWarehouseNo(), new MinShelfLifePurchaseBatchDTO());
            productAgentWarehouseDataVo.setSkuId(productSkuDto.getId());
            productAgentWarehouseDataVo.setSku(productSkuDto.getAgentSkuCode());
            // 上下架状态组装
            Integer onSale = null;
            List<MarketItemOnSaleSimple4StoreResp> marketItems = finalSaleSkuIdMap.get(productSkuDto.getId());
            if (!CollectionUtils.isEmpty(marketItems)) {
                onSale = marketItems.stream().anyMatch(resp -> OnSaleTypeEnum.ON_SALE.getCode().equals(resp.getOnSale())) ? OnSaleTypeEnum.ON_SALE.getCode() : OnSaleTypeEnum.SOLD_OUT.getCode();
            }
            productAgentWarehouseDataVo.setOnSale(onSale);
            productAgentWarehouseDataVo.setOnSaleDesc(OnSaleTypeEnum.getRemark(onSale));

            productAgentWarehouseDataVo.setTitle(productSkuDto.getTitle());
            productAgentWarehouseDataVo.setSpecification(productSkuDto.getSpecification());
            productAgentWarehouseDataVo.setSpecificationUnit(productSkuDto.getSpecificationUnit());
            productAgentWarehouseDataVo.setRoadQuantity(Objects.isNull(summerfarmAgentSkuWarehouseDataDTO.getRoadQuantity()) ? NumberConstant.ZERO : summerfarmAgentSkuWarehouseDataDTO.getRoadQuantity());
            productAgentWarehouseDataVo.setSaleAmount(Optional.ofNullable(skuSaleAmountMap.get(productSkuDto.getId(),summerfarmAgentSkuWarehouseDataDTO.getWarehouseNo())).orElse(NumberConstant.ZERO));
            productAgentWarehouseDataVo.setFifteenDaysSaleAmount(Optional.ofNullable(skuFifteenSaleAmountMap.get(productSkuDto.getId(),summerfarmAgentSkuWarehouseDataDTO.getWarehouseNo())).orElse(NumberConstant.ZERO));
            productAgentWarehouseDataVo.setThirtyDaysSaleAmount(Optional.ofNullable(skuThirtySaleAmountMap.get(productSkuDto.getId(),summerfarmAgentSkuWarehouseDataDTO.getWarehouseNo())).orElse(NumberConstant.ZERO));
            productAgentWarehouseDataVo.setMainPicture(productSkuDto.getMainPicture());
            productAgentWarehouseDataVo.setGoodsValue(summerfarmAgentSkuWarehouseDataDTO.getGoodsValue());
//            productAgentWarehouseDataVo.setCityNames(summerfarmAgentSkuWarehouseDataDTO.getCityNames());
//            productAgentWarehouseDataVo.setCityNum(summerfarmAgentSkuWarehouseDataDTO.getCityNames().size());
            productAgentWarehouseDataVo.setBatch(minShelfLifePurchaseBatchDTO.getBatch());
            productAgentWarehouseDataVo.setLeftShelfLife(minShelfLifePurchaseBatchDTO.getLeftShelfLife());
            ProductWarehouseDataEnums.SyncStatus syncEnum = ProductWarehouseDataEnums.SyncStatus.getByResp(summerfarmAgentSkuWarehouseDataDTO.getSync());
            Optional.ofNullable(syncEnum).ifPresent(enums -> {
                productAgentWarehouseDataVo.setSync(enums.getStatus());
                productAgentWarehouseDataVo.setSyncDesc(enums.getDesc());
            });
            productAgentWarehouseDataVo.setOnlineQuantity(summerfarmAgentSkuWarehouseDataDTO.getOnlineQuantity());
            productAgentWarehouseDataVo.setWarehouseTenantId(summerfarmAgentSkuWarehouseDataDTO.getWarehouseTenantId());
            // 仓所属租户是当前租户，归属自营仓
            Integer selfWarehouseType = ProductWarehouseDataEnums.SelfWarehouseType.getCode(loginContextInfoDTO.getTenantId().equals(summerfarmAgentSkuWarehouseDataDTO.getWarehouseTenantId()));
            productAgentWarehouseDataVo.setSelfWarehouseType(selfWarehouseType);
            ProductAgentSkuMapping productAgentSkuMapping = productAgentSkuMappingMap.get(summerfarmAgentSkuWarehouseDataDTO.getSkuId());
            // 获取省市区
            List<WarehouseSkuFenceStorageResp> warehouseSkuFenceStorageResps = warehouseSkuFenceStorageRespListMap.get(productAgentSkuMapping.getAgentSkuCode());
            if (!CollectionUtils.isEmpty(warehouseSkuFenceStorageResps)) {
                for (WarehouseSkuFenceStorageResp warehouseSkuFenceStorageResp : warehouseSkuFenceStorageResps) {
                    if (summerfarmAgentSkuWarehouseDataDTO.getWarehouseNo().equals(warehouseSkuFenceStorageResp.getWarehouseNo())) {
                        // 获取仓库服务商
                        if(WarehouseSourceEnum.SAAS_WAREHOUSE.equals(warehouseSkuFenceStorageResp.getSourceEnum())){
                            productAgentWarehouseDataVo.setWarehouseProvider(tenantDTO.getCompanyName());
                        }else {
                            productAgentWarehouseDataVo.setWarehouseProvider("杭州鲜沐科技有限公司");
                        }

                        List<WarehouseSkuFenceAreaResp> warehouseSkuFenceAreas = warehouseSkuFenceStorageResp.getWarehouseSkuFenceAreas();
                        if (!CollectionUtils.isEmpty(warehouseSkuFenceAreas)) {
                            List<WarehouseSkuFenceAreaDTO> warehouseSkuFenceAreaDTOList = warehouseSkuFenceAreas.stream().map(warehouseSkuFenceAreaResp -> {
                                WarehouseSkuFenceAreaDTO warehouseSkuFenceAreaDTO = new WarehouseSkuFenceAreaDTO();
                                warehouseSkuFenceAreaDTO.setProvince(warehouseSkuFenceAreaResp.getProvince());
                                warehouseSkuFenceAreaDTO.setArea(warehouseSkuFenceAreaResp.getArea());
                                warehouseSkuFenceAreaDTO.setCity(warehouseSkuFenceAreaResp.getCity());
                                return warehouseSkuFenceAreaDTO;
                            }).collect(Collectors.toList());
                            productAgentWarehouseDataVo.setWarehouseSkuFenceAreaDTOList(warehouseSkuFenceAreaDTOList);
                            productAgentWarehouseDataVo.setCityNum(warehouseSkuFenceAreaDTOList.size());
                        }
                    }
                }
            }

            return productAgentWarehouseDataVo;
        }).collect(Collectors.toList());
        pageInfo.setList(productAgentWarehouseDataVos);
        return pageInfo;
    }

    /**
     * 转换请求参数
     * @param sync 库存同步状态1:同步;0:不同步
     * @param inOutRecord 是否有出入库流水:0无;1:有
     * @return
     */
    private ProductSkuWarehouseQueryDTO transferProductSkuWarehouseQueryDTO(Integer sync, Integer inOutRecord) {
        return ProductSkuWarehouseQueryDTO.builder()
                .sync(ProductWarehouseDataEnums.SyncStatus.getReqByStatus(sync))
                .inOutRecord(ProductWarehouseDataEnums.InOutRecord.getReqByCode(inOutRecord)).build();
    }

    /**
     * 查询sku基于仓维度的覆盖区域
     *
     * @param map
     * @param productAgentSkuMappingMap
     * @return
     */
    private List<WarehouseSkuFenceResp> queryWarehouseSkuFenceOld(Map<Long, List<QueryInventoryResp>> map, Map<Long, ProductAgentSkuMapping> productAgentSkuMappingMap){
        if(CollectionUtils.isEmpty(map)){
            return new ArrayList<>();
        }

        Set<Long> agentSkuIds = map.keySet();

        List<WarehouseSkuFenceReq> warehouseSkuFenceReqs = agentSkuIds.stream().map(agentSkuId -> {
            ProductAgentSkuMapping productAgentSkuMapping = productAgentSkuMappingMap.get(agentSkuId);
            List<QueryInventoryResp> summerFarmAgentSkuWarehouseDataResps = map.get(agentSkuId);
            List<Integer> warehouseNos = summerFarmAgentSkuWarehouseDataResps.stream().map(QueryInventoryResp::getWarehouseNo).collect(Collectors.toList());
            WarehouseSkuFenceReq warehouseSkuFenceReq = new WarehouseSkuFenceReq();
            warehouseSkuFenceReq.setSku(productAgentSkuMapping.getAgentSkuCode());
            warehouseSkuFenceReq.setWarehouseNos(warehouseNos);
            return warehouseSkuFenceReq;
        }).collect(Collectors.toList());

        List<WarehouseSkuFenceResp> warehouseSkuFenceResps = warehouseStorageFenceQueryFacade.queryWarehouseSkuFence(warehouseSkuFenceReqs);
        return warehouseSkuFenceResps;
    }

    /**
     * 查询sku基于仓维度的覆盖区域
     *
     * @param map
     * @param productAgentSkuMappingMap
     * @return
     */
    private List<WarehouseSkuFenceResp> queryWarehouseSkuFence(Map<Long, List<QueryInventoryResp>> map, Map<Long, String> productAgentSkuMappingMap){
        if(CollectionUtils.isEmpty(map)){
            return new ArrayList<>();
        }

        Set<Long> agentSkuIds = map.keySet();

        List<WarehouseSkuFenceReq> warehouseSkuFenceReqs = agentSkuIds.stream().map(agentSkuId -> {
            List<QueryInventoryResp> summerFarmAgentSkuWarehouseDataResps = map.get(agentSkuId);
            List<Integer> warehouseNos = summerFarmAgentSkuWarehouseDataResps.stream().map(QueryInventoryResp::getWarehouseNo).collect(Collectors.toList());
            WarehouseSkuFenceReq warehouseSkuFenceReq = new WarehouseSkuFenceReq();
            warehouseSkuFenceReq.setSku(productAgentSkuMappingMap.get(agentSkuId));
            warehouseSkuFenceReq.setWarehouseNos(warehouseNos);
            return warehouseSkuFenceReq;
        }).collect(Collectors.toList());

        List<WarehouseSkuFenceResp> warehouseSkuFenceResps = warehouseStorageFenceQueryFacade.queryWarehouseSkuFence(warehouseSkuFenceReqs);
        return warehouseSkuFenceResps;
    }

    /**
     * 封装查询条件
     *
     * @param productAgentWarehouseDateQueryDTO
     * @param loginContextInfoDTO
     * @return
     */
    private SummerfarmAgentSkuWarehouseDataInput packagingQueryCondition(ProductAgentWarehouseDateQueryDTO productAgentWarehouseDateQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        SummerfarmAgentSkuWarehouseDataInput summerfarmAgentSkuWarehouseDataInput = new SummerfarmAgentSkuWarehouseDataInput();
        BeanUtils.copyProperties(productAgentWarehouseDateQueryDTO, summerfarmAgentSkuWarehouseDataInput);
        // 查询租户信息
        TenantDTO tenantDTO = tenantService.queryTenantById(loginContextInfoDTO.getTenantId());
        summerfarmAgentSkuWarehouseDataInput.setAdminId(tenantDTO.getAdminId());
        // 查询仓库信息
        if (CollectionUtils.isEmpty(productAgentWarehouseDateQueryDTO.getWarehouseIds())) {
            List<ProductAgentWarehouseVO> productAgentWarehouseVos = productAgentWarehouseService.queryByTenantId(loginContextInfoDTO.getTenantId());
            // 查询所有仓库
            List<Long> warehouseIds = productAgentWarehouseVos.stream().map(ProductAgentWarehouseVO::getWarehouseId).collect(Collectors.toList());
            summerfarmAgentSkuWarehouseDataInput.setWarehouseIds(warehouseIds);
        }

        return summerfarmAgentSkuWarehouseDataInput;
    }

    @Override
    public CommonResult<PageInfo<ProductStockChangeRecordVO>> pageQueryStockChangeRecordOld(ProductStockChangeRecordQueryDTO productStockChangeRecordQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        //设置默认值
        setDefaultQueryCondition(productStockChangeRecordQueryDTO, loginContextInfoDTO);
        if (Objects.nonNull(productStockChangeRecordQueryDTO.getSkuId())) {
            ProductAgentSkuMapping productAgentSkuMapping = productAgentSkuMappingMapper.selectByTenantIdAndSkuId(loginContextInfoDTO.getTenantId(), productStockChangeRecordQueryDTO.getSkuId());
            if (Objects.isNull(productAgentSkuMapping)) {
                return CommonResult.ok(PageInfoHelper.createPageInfo(new ArrayList<>(), productStockChangeRecordQueryDTO.getPageSize()));
            }
            productStockChangeRecordQueryDTO.setSkuId(productAgentSkuMapping.getAgentSkuId());
        }
        //查询该租户下的代仓商品
        List<ProductAgentSkuMapping> productAgentSkuMappings = productAgentSkuMappingDao.selectByTenantId(loginContextInfoDTO.getTenantId());
        if (CollectionUtils.isEmpty(productAgentSkuMappings)) {
            return CommonResult.ok(PageInfoHelper.createPageInfo(new ArrayList<>(), productStockChangeRecordQueryDTO.getPageSize()));
        }
        List<Long> skuIds = productAgentSkuMappings.stream().map(ProductAgentSkuMapping::getAgentSkuId).collect(Collectors.toList());
        productStockChangeRecordQueryDTO.setPermissionSkuIdList(skuIds);
        ProductStockChangeRecordQueryInput input = Convert.productStockChangeRecord2Input(productStockChangeRecordQueryDTO);
        input.setTenantId(loginContextInfoDTO.getTenantId());
        PageQuerySkuQuantityChangeRecordResp pageQuerySkuQuantityChangeRecordResp = saasInventoryFacade.pageQuerySkuQuantityChangeRecord(input);
        PageInfo<SkuQuantityChangeRecordDTO> respPageInfo = Optional.ofNullable(pageQuerySkuQuantityChangeRecordResp).map(PageQuerySkuQuantityChangeRecordResp::getChangeRecordPageResult).orElse(null);
        if (Objects.isNull(respPageInfo)) {
            return CommonResult.ok(PageInfoHelper.createPageInfo(new ArrayList<>(), productStockChangeRecordQueryDTO.getPageSize()));
        }
        List<SkuQuantityChangeRecordDTO> list = respPageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return CommonResult.ok(PageInfoHelper.createPageInfo(new ArrayList<>(), productStockChangeRecordQueryDTO.getPageSize()));
        }
        List<Long> agentSkuIds = list.stream().map(SkuQuantityChangeRecordDTO::getSkuId).collect(Collectors.toList());
        List<ProductAgentSkuMapping> mappings = productAgentSkuMappingDao.selectByAgentSkuIds(agentSkuIds, loginContextInfoDTO.getTenantId());
        Map<Long, Long> productAgentSkuMappingMap = mappings.stream().collect(Collectors.toMap(ProductAgentSkuMapping::getAgentSkuId, ProductAgentSkuMapping::getSkuId));
        for (SkuQuantityChangeRecordDTO skuQuantityChangeRecordDTO : list) {
            skuQuantityChangeRecordDTO.setSkuId(productAgentSkuMappingMap.get(skuQuantityChangeRecordDTO.getSkuId()));
        }
        return CommonResult.ok(Convert.pageInfoSkuQuantityChangeRecordDTO2VO(respPageInfo));
    }

    @Override
    public CommonResult<PageInfo<ProductStockChangeRecordVO>> pageQueryStockChangeRecord(ProductStockChangeRecordQueryDTO productStockChangeRecordQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        //设置默认值
        setDefaultQueryCondition(productStockChangeRecordQueryDTO, loginContextInfoDTO);
        if (Objects.nonNull(productStockChangeRecordQueryDTO.getSkuId())) {
            ProductsMappingResp productAgentSkuMapping = productFacade.getProductMappingBySkuIdAndTenantId (productStockChangeRecordQueryDTO.getSkuId (), loginContextInfoDTO.getTenantId ());
            if (Objects.isNull(productAgentSkuMapping)) {
                return CommonResult.ok(PageInfoHelper.createPageInfo(new ArrayList<>(), productStockChangeRecordQueryDTO.getPageSize()));
            }
            productStockChangeRecordQueryDTO.setSkuId(productAgentSkuMapping.getAgentSkuId());
        }
        //查询该租户下的代仓商品
        List<ProductsMappingResp> productAgentSkuMappings = productFacade.listProductMappingByTenantId (loginContextInfoDTO.getTenantId ());
        if (CollectionUtils.isEmpty(productAgentSkuMappings)) {
            return CommonResult.ok(PageInfoHelper.createPageInfo(new ArrayList<>(), productStockChangeRecordQueryDTO.getPageSize()));
        }
        List<Long> skuIds = productAgentSkuMappings.stream().map(ProductsMappingResp::getAgentSkuId).collect(Collectors.toList());
        productStockChangeRecordQueryDTO.setPermissionSkuIdList(skuIds);
        ProductStockChangeRecordQueryInput input = Convert.productStockChangeRecord2Input(productStockChangeRecordQueryDTO);
        input.setTenantId(loginContextInfoDTO.getTenantId());

        PageQuerySkuQuantityChangeRecordResp pageQuerySkuQuantityChangeRecordResp = saasInventoryFacade.pageQuerySkuQuantityChangeRecord(input);
        PageInfo<SkuQuantityChangeRecordDTO> respPageInfo = Optional.ofNullable(pageQuerySkuQuantityChangeRecordResp).map(PageQuerySkuQuantityChangeRecordResp::getChangeRecordPageResult).orElse(null);
        if (Objects.isNull(respPageInfo)) {
            return CommonResult.ok(PageInfoHelper.createPageInfo(new ArrayList<>(), productStockChangeRecordQueryDTO.getPageSize()));
        }
        List<SkuQuantityChangeRecordDTO> list = respPageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return CommonResult.ok(PageInfoHelper.createPageInfo(new ArrayList<>(), productStockChangeRecordQueryDTO.getPageSize()));
        }
        List<Long> agentSkuIds = list.stream().map(SkuQuantityChangeRecordDTO::getSkuId).collect(Collectors.toList());

        List<ProductsMappingResp> mappings = productFacade.listProductMappingByAgentSkuIdsAndTenantId (agentSkuIds,loginContextInfoDTO.getTenantId ());
        Map<Long, Long> productAgentSkuMappingMap = mappings.stream().collect(Collectors.toMap(ProductsMappingResp::getAgentSkuId, ProductsMappingResp::getSkuId));
        for (SkuQuantityChangeRecordDTO skuQuantityChangeRecordDTO : list) {
            skuQuantityChangeRecordDTO.setSkuId(productAgentSkuMappingMap.get(skuQuantityChangeRecordDTO.getSkuId()));
        }
        return CommonResult.ok(Convert.pageInfoSkuQuantityChangeRecordDTO2VO(respPageInfo));
    }

    private void setDefaultQueryCondition(ProductStockChangeRecordQueryDTO productStockChangeRecordQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        // 默认仓
        if (Objects.isNull(productStockChangeRecordQueryDTO.getWarehouseNo())) {
            List<ProductAgentWarehouseVO> productAgentWarehouseVos = productAgentWarehouseService.queryByTenantId(loginContextInfoDTO.getTenantId());
            if (CollectionUtils.isEmpty(productAgentWarehouseVos) || Objects.isNull(productAgentWarehouseVos.get(0).getWarehouseId())) {
                throw new BizException(ResultDTOEnum.WAREHOUSE_NO_IS_EMPTY.getMessage());
            }
            productStockChangeRecordQueryDTO.setWarehouseNo(productAgentWarehouseVos.get(0).getWarehouseId().intValue());
        }
        // 默认昨天到今天
        if (Objects.isNull(productStockChangeRecordQueryDTO.getStartTime()) || Objects.isNull(productStockChangeRecordQueryDTO.getEndTime())) {
            String yesterdayString = TimeUtils.getYesterdayString(TimeUtils.FORMAT);
            String todayString = TimeUtils.getTodayString(TimeUtils.FORMAT);
            productStockChangeRecordQueryDTO.setStartTime(TimeUtils.convertStringToLocalDateTime(yesterdayString, TimeUtils.FORMAT));
            productStockChangeRecordQueryDTO.setEndTime(TimeUtils.convertStringToLocalDateTime(todayString, TimeUtils.FORMAT));
            return;
        }
        // 判断是否时间间隔一个月内
        LocalDateTime startTime = productStockChangeRecordQueryDTO.getStartTime();
        LocalDateTime endTime = productStockChangeRecordQueryDTO.getEndTime();
        LocalDateTime endTimeOneMonthAgo = endTime.minusMonths(NumberConstant.ONE).minusDays(NumberConstant.ONE);
        if (startTime.isBefore(endTimeOneMonthAgo)) {
            throw new ParamsException(ResultDTOEnum.QUERY_TIME_ERROR.getMessage());
        }
    }
}
