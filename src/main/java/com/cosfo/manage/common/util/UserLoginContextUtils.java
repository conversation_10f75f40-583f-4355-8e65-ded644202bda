package com.cosfo.manage.common.util;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.facade.usercenter.UserCenterTenantAccountFacade;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.ShiroUser;
import net.xianmu.authentication.common.utils.AuthUserUtils;
import net.xianmu.authentication.model.DTO.ShiroUserExtendDto;
import net.xianmu.usercenter.client.tenant.resp.TenantAccountResultResp;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AccountException;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
public class UserLoginContextUtils {

    public static LoginContextInfoDTO getMerchantInfoDTO() {
        Object principal = SecurityUtils.getSubject().getPrincipal();
        if (Objects.isNull(principal)) {
           return null;
        }
        ShiroUser shiroUser = JSON.parseObject(JSON.toJSONString(principal), ShiroUser.class);
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(shiroUser.getTenantId());
        loginContextInfoDTO.setAuthUserId(shiroUser.getId());
        loginContextInfoDTO.setPhone(shiroUser.getPhone());
        loginContextInfoDTO.setNickName(getNickName(shiroUser.getId()));
        loginContextInfoDTO.setUsername(shiroUser.getUsername());
        loginContextInfoDTO.setEmail(shiroUser.getEmail());
        if (Constants.CROSS_LOGIN_TYPE.equals(shiroUser.getLoginType())) {
            loginContextInfoDTO.setIsSuperAccount(Boolean.TRUE);
        }
        handleSuperAccountInfo(loginContextInfoDTO);
        return loginContextInfoDTO;
    }

    private static String getNickName(Long authUserId){
        UserCenterTenantAccountFacade userCenterTenantAccountFacade = SpringContextUtil.getBean("userCenterTenantAccountFacade", UserCenterTenantAccountFacade.class);
        TenantAccountResultResp tenantAccountResultResp = userCenterTenantAccountFacade.getTenantAccountInfoFromCache(authUserId, true);
        return Optional.ofNullable(tenantAccountResultResp).map(TenantAccountResultResp::getNickname).orElse(null);
    }

    private static void handleSuperAccountInfo(LoginContextInfoDTO loginContextInfoDTO) {
        if (loginContextInfoDTO.getIsSuperAccount() == null || !loginContextInfoDTO.getIsSuperAccount()) {
            return;
        }
        ShiroUserExtendDto extendDto = AuthUserUtils.getShiroUserExtendDto(SecurityUtils.getSubject().getSession().getId().toString());
        if (extendDto == null) {
            log.warn("超级账号补充信息已过期, loginInfo={}", loginContextInfoDTO);
            throw new AccountException("请重新登录");
        }
        loginContextInfoDTO.setMockAuthUserId(extendDto.getToAuthId());
    }

    public static Long getAuthUserId() {
        LoginContextInfoDTO merchantInfoDTO = getMerchantInfoDTO();
        if(merchantInfoDTO != null) {
            return merchantInfoDTO.getAuthUserId();
        }
        return ThreadTokenHolder.getAuthUserId();
    }

    public static Long getTenantId() {
        LoginContextInfoDTO merchantInfoDTO = getMerchantInfoDTO();
        if(merchantInfoDTO != null) {
            return merchantInfoDTO.getTenantId();
        }
        return ThreadTokenHolder.getTenantId();
    }
}
