package com.cosfo.manage.common.util.email;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.util.Properties;
import java.util.Random;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @descripton
 * @date 2025/5/6 14:21
 */
@Component("emailSessionPool")
public class SessionPool implements InitializingBean {

    private final EmailConfig emailConfig;
    private final AtomicReference<Session> sessionRef = new AtomicReference<>();

    public SessionPool(EmailConfig emailConfig) {
        this.emailConfig = emailConfig;
    }

    @Override
    public void afterPropertiesSet() {
        refreshSession();
    }

    public synchronized void refreshSession() {
        Properties props = new Properties();
        props.put("mail.smtp.host", emailConfig.getHost());
        props.put("mail.smtp.port", emailConfig.getPort());
        props.put("mail.smtp.ssl.enable", "true");
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.connectiontimeout", "5000");
        props.put("mail.smtp.timeout", "10000");

        Session newSession = Session.getInstance(props, new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(
                        emailConfig.getUser(),
                        emailConfig.getPassword()
                );
            }
        });

        sessionRef.set(newSession);
        System.out.println("SMTP Session refreshed with new configuration");
    }

    public Session getSession() {
        return sessionRef.get();
    }
}
