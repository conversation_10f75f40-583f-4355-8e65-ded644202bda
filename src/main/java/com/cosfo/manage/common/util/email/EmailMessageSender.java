package com.cosfo.manage.common.util.email;

import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.mail.AuthenticationFailedException;
import javax.mail.Authenticator;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.util.Properties;
import java.util.Random;

@Component
public class EmailMessageSender {

    @Resource
    private EmailConfig emailConfig;
    @Resource
    private SessionPool sessionPool;

    public boolean sendVerificationCode(String toEmail, String verificationCode ) {
        try {
            // 构建邮件内容
            MimeMessage message = new MimeMessage(sessionPool.getSession());
            message.setFrom(new InternetAddress(emailConfig.getUser(), emailConfig.getDisplayName()));
            message.addRecipient(Message.RecipientType.TO, new InternetAddress(toEmail));
            message.setSubject("【重要】您的验证码（请勿泄露）");

            String htmlContent = String.format(
                    "<div style=\"font-family: 'Microsoft YaHei', sans-serif; max-width: 600px; margin: 20px auto; padding: 30px; border: 1px solid #eee;\">" +
                            "    <h2 style=\"color: #165dff; margin-bottom: 30px;\">验证码通知</h2>" +
                            "    <p style=\"font-size: 16px;\">您的验证码为：<strong style=\"color: #ff5722; font-size: 24px;\">%s</strong></p>" +
                            "    <p style=\"color: #999; font-size: 14px; margin-top: 20px;\">⚠️ 有效期15分钟，请勿转发他人</p>" +
                            "    <hr style=\"border-color: #eee; margin: 30px 0;\">" +
                            "    <p style=\"color: #666; font-size: 12px;\">本邮件由系统自动发送，请勿直接回复</p>" +
                            "</div>",
                    verificationCode
            );

            message.setContent(htmlContent, "text/html;charset=UTF-8");

            // 发送邮件
            Transport.send(message);
            System.out.println("验证码发送成功至：" + toEmail);

            // 实际应用应存储验证码到Redis等缓存系统
            // redisTemplate.opsForValue().set("code:"+toEmail, code, 15, TimeUnit.MINUTES);

            return true;
        } catch (AuthenticationFailedException e) {
            System.err.println("认证失败，请检查：");
            System.err.println("1. SMTP授权码是否正确（不是邮箱密码）");
            System.err.println("2. 是否已开启SMTP服务");
            return false;
        } catch (MessagingException e) {
            System.err.println("邮件发送失败：" + e.getMessage());
            return false;
        } catch (Exception e) {
            System.err.println("系统异常：" + e.getMessage());
            return false;
        }
    }


    public boolean sendUserPassword(String toEmail, String username, String password) {
        try {
            MimeMessage message = new MimeMessage(sessionPool.getSession());
            message.setFrom(new InternetAddress(emailConfig.getUser(), emailConfig.getDisplayName()));
            message.addRecipient(Message.RecipientType.TO, new InternetAddress(toEmail));
            message.setSubject("【重要】您的账户密码通知");

            String htmlContent = String.format(
                    "<div style=\"font-family: 'Microsoft YaHei', sans-serif; max-width: 600px; margin: 20px auto; padding: 30px; border: 1px solid #eee;\">" +
                            "    <h2 style=\"color: #165dff; margin-bottom: 30px;\">账户密码通知</h2>" +
                            "    <p style=\"font-size: 16px;\">尊敬的<strong>%s</strong>，您的初始密码为：</p>" +
                            "    <div style=\"background: #f5f5f5; padding: 15px; margin: 20px 0; border-radius: 4px;\">" +
                            "        <code style=\"font-size: 20px; letter-spacing: 2px;\">%s</code>" +
                            "    </div>" +
                            "    <p style=\"color: #ff0000; font-size: 14px;\">⚠️ 安全提示：</p>" +
                            "    <ul style=\"color: #666; font-size: 14px; padding-left: 20px;\">" +
                            "        <li>请首次登录后立即修改密码</li>" +
                            "        <li>不要将密码告知他人</li>" +
                            "        <li>建议定期更换密码</li>" +
                            "    </ul>" +
                            "    <hr style=\"border-color: #eee; margin: 30px 0;\">" +
                            "    <p style=\"color: #999; font-size: 12px;\">本邮件由系统自动发送，请勿直接回复</p>" +
                            "</div>",
                    username, password
            );

            message.setContent(htmlContent, "text/html;charset=UTF-8");
            Transport.send(message);
            System.out.println("密码邮件发送成功至：" + toEmail);
            return true;
        } catch (Exception e) {
            System.err.println("密码发送失败：" + e.getMessage());
            return false;
        }
    }

    private static String generateRandomCode(int length) {
        String numbers = "0123456789";
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(numbers.charAt(random.nextInt(numbers.length())));
        }
        return sb.toString();
    }
}