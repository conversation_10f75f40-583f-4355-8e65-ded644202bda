package com.cosfo.manage.common.executor;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.download.support.util.BizAssert;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * @author: monna.chen
 * @Date: 2024/2/29 17:20
 * @Description:
 */
@Slf4j
public class ThreadPoolHelper {
    private static ExecutorService executor;


    private ThreadPoolHelper() {
    }

    private ThreadPoolHelper(ExecutorService executor) {
        BizAssert.notNull(executor, "请指定线程池");
        ThreadPoolHelper.executor = executor;
    }

    public static ThreadPoolHelper build(ExecutorService executor) {
        return new ThreadPoolHelper(executor);
    }


    /**
     * 执行任务
     *
     * @param tasks
     */
    public void batchTask(final List<Runnable> tasks) {
        for (final Runnable task : tasks) {
            executor.submit(task);
        }
    }

    /**
     * 执行任务后等待返回
     *
     * @param tasks
     * @throws Exception
     */
    public void batchTaskWithCDL(final List<Runnable> tasks) throws Exception {
        final CountDownLatch cdl = new CountDownLatch(tasks.size());
        for (final Runnable task : tasks) {
            executor.submit(() -> {
                try {
                    task.run();
                } catch (final Exception e) {
                    log.error("ThreadPoolHelper - batchTaskWithCDL error : {}", e.getMessage(), e);
                } finally {
                    cdl.countDown();
                }
            });
        }
        cdl.await();
    }

    /**
     * 执行任务后等待返回，有等待超时
     *
     * @param tasks
     * @param timeout
     * @param timeUnit
     * @throws Exception
     */
    public void batchTaskWithTimeout(final List<Runnable> tasks, final long timeout, final TimeUnit timeUnit) throws Exception {
        batchTaskWithTimeout(tasks, timeout, timeUnit, null);
    }

    public void batchTaskWithTimeout(final List<Runnable> tasks, final long timeout, final TimeUnit timeUnit, final ThreadCDLTimeout fallback)
        throws Exception {
        final CountDownLatch cdl = new CountDownLatch(tasks.size());
        for (final Runnable task : tasks) {
            executor.submit(() -> {
                try {
                    task.run();
                } catch (final Exception e) {
                    log.error("TraceThreadPoolUtil - batchTaskWithTimeout error : {}", e.getMessage(), e);
                } finally {
                    cdl.countDown();
                }
            });
        }
        if (Objects.nonNull(fallback)) {
            if (!cdl.await(timeout, timeUnit)) {
                fallback.fallback();
            }
        } else {
            cdl.await(timeout, timeUnit);
        }
    }
}
