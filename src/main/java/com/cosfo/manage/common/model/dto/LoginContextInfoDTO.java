package com.cosfo.manage.common.model.dto;

import lombok.Data;

@Data
public class LoginContextInfoDTO {
    /**
     * 商户Id
     */
    private Long mId;
    /**
     * 租户Id
     */
    private Long tenantId;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 租户类型
     */
    private Integer type;
    /**
     * 密码强度
     */
    private Integer level;
    /**
     * 租户名称
     */
    private String tenantName;
    /**
     * jwtToken
     */
    private String jwtToken;

    /**
     * authUserId
     */
    private Long authUserId;

    /**
     * 当前登录用户系统来源
     */
    private String systemOrigin;

    /**
     * admin账号使用,模拟登录的租户的超管userId
     */
    private Long mockAuthUserId;

    /**
     * 标识是否超级账号
     */
    private Boolean isSuperAccount;

    /**
     * 当前用户名称
     */
    private String nickName;

    /**
     * 当前用户登录的用户名
     */
    private String username;


    /**
     * email
     */
    private String email;

}
