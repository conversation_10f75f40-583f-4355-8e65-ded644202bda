package com.cosfo.manage.supplier.service;

import com.cosfo.manage.supplier.model.dto.SupplierDeliveryInfoDTO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/9
 */
public interface SupplierDeliveryInfoService {

    /**
     * 查询订单运费信息
     *
     * @param orderNos
     * @return
     */
    List<SupplierDeliveryInfoDTO> querySupplierDeliveryInfo(List<String> orderNos);

    /**
     * 查询订单运费信息
     *
     * @param orderNo
     * @return
     */
    BigDecimal querySupplierDeliveryInfo(String orderNo);
}
