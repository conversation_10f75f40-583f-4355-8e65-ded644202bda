package com.cosfo.manage.supplier.service.impl;

import com.cosfo.manage.supplier.mapper.SupplierDeliveryInfoMapper;
import com.cosfo.manage.supplier.model.dto.SupplierDeliveryInfoDTO;
import com.cosfo.manage.supplier.service.SupplierDeliveryInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/9
 */
@Service
@Slf4j
public class SupplierDeliveryInfoServiceImpl implements SupplierDeliveryInfoService {
    @Resource
    private SupplierDeliveryInfoMapper supplierDeliveryInfoMapper;

    @Override
    public List<SupplierDeliveryInfoDTO> querySupplierDeliveryInfo(List<String> orderNos) {
        List<SupplierDeliveryInfoDTO> supplierDeliveryInfoDTOS = supplierDeliveryInfoMapper.batchQuery(orderNos);
        return supplierDeliveryInfoDTOS;
    }

    @Override
    public BigDecimal querySupplierDeliveryInfo(String orderNo) {
        List<SupplierDeliveryInfoDTO> supplierDeliveryInfoDTOS = supplierDeliveryInfoMapper.batchQuery(Arrays.asList(orderNo));
        return CollectionUtils.isEmpty(supplierDeliveryInfoDTOS) ? BigDecimal.ZERO : supplierDeliveryInfoDTOS.get(0).getSupplierDeliveryFee();
    }
}
