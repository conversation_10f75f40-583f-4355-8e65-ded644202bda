package com.cosfo.manage.downloadcenter.item;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.cosfo.manage.common.context.*;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.facade.MarketFacade;
import com.cosfo.manage.facade.SupplierFacade;
import com.cosfo.manage.facade.category.CategoryServiceFacade;
import com.cosfo.manage.market.converter.MarketItemConverter;
import com.cosfo.manage.market.model.dto.*;
import com.cosfo.manage.market.service.MarketClassificationService;
import com.cosfo.manage.merchant.model.dto.MerchantStoreGroupQueryDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreQueryDTO;
import com.cosfo.manage.merchant.model.po.MerchantStore;
import com.cosfo.manage.merchant.model.po.MerchantStoreGroup;
import com.cosfo.manage.merchant.service.MerchantStoreGroupService;
import com.cosfo.manage.merchant.service.MerchantStoreService;
import com.cosfo.manage.product.model.dto.ProductCategoryTreeDTO;
import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import com.cosfo.manage.product.model.dto.ProductSkuQueryConditionDTO;
import com.cosfo.manage.product.service.ProductSkuService;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.download.support.dto.DownloadCenterDataMsg;
import net.xianmu.download.support.handler.DownloadCenterImportDefaultHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 导入商品
 */
@Component
@Slf4j
public class ItemImportHandler extends DownloadCenterImportDefaultHandler<MarketExcelDataDTO> {

    @Resource
    private MerchantStoreService merchantStoreService;
    @Resource
    private ProductSkuService productSkuService;
    @Resource
    private MerchantStoreGroupService merchantStoreGroupService;
    @Resource
    private MarketClassificationService marketClassificationService;
    @Resource
    private SupplierFacade supplierFacade;
    @Resource
    private MarketFacade marketFacade;
    @Resource
    private CategoryServiceFacade categoryServiceFacade;
    @Override
    public DownloadCenterEnum.RequestSource getSource() {
        return DownloadCenterEnum.RequestSource.SAAS;
    }

    @Override
    public Integer getBizType() {
        return FileDownloadTypeEnum.ITEM_IMPORT.getType();
    }

    @Override
    public void dealExcelData(List<MarketExcelDataDTO> list, DownloadCenterDataMsg downloadCenterDataMsg) {
        Long tenantId = downloadCenterDataMsg.getAuthUser ().getTenantId ();

        //前台分类map《2级分类name/1级分类name，2级别分类id》
        Map<String, Long> classificationMap = marketClassificationService.getClassificationMap (tenantId);
        //供应商
        Map<String, Long> supplierMap = supplierFacade.getNameIdMapByTenantId(tenantId);
        //查询所有货品
        Map<Long, ProductSkuDTO> skuMap = getSkuMap(tenantId,list.stream ().filter (e-> StringUtils.isNotEmpty (e.getSkuId ())).map (MarketExcelDataDTO::getSkuId).collect(Collectors.toList()));
        //查询门店
        Set<String> storeCodeList = list.stream().filter (e-> Objects.equals (PriceTargetExcelEnum.STORE.getDesc (),e.getPriceTargetType ()) && StringUtils.isNotEmpty ( e.getPriceTargets())).collect(Collectors.toList()).stream().flatMap(e -> Arrays.stream(e.getPriceTargets().split("，"))).filter (StringUtils::isNotEmpty).collect(Collectors.toSet());
        Map<String, Long> storeMap = getStoreMap (storeCodeList, tenantId);
        //查询门店分组
        Map<String, Long> storeGroupMap = getStoreGroupMap(tenantId);
        //后台类目
        Map<String,Long> caregroyMap = getCategoryTreeMap();

        log.info ("ItemImportHandler - list={}",list);
        for (MarketExcelDataDTO excelDataDTO : list) {
            try {
                validExcelPropertyEmpty (excelDataDTO);
                marketFacade.addMarket (buildMarketSpuInput (tenantId,caregroyMap,excelDataDTO, classificationMap,supplierMap,skuMap,storeMap, storeGroupMap));
            } catch (Exception e){
                log.error ("ItemImportHandler - error",e);
                excelDataDTO.setErrorMsg (e.getMessage ());
            }
        }
    }

    private Map<String, Long> getCategoryTreeMap() {
        Map<String, Long> map = new HashMap<> ();
        List<ProductCategoryTreeDTO> list = categoryServiceFacade.listCategoryTreeNew ();
        list.forEach (e->{
            List<ProductCategoryTreeDTO> childList = e.getChildList ();
            buildValue(map,e.getName (),e.getId (),childList);
        });
        return map;
    }

    private void buildValue(Map<String, Long> map,String Key,Long value, List<ProductCategoryTreeDTO> children) {
        if(CollectionUtil.isEmpty (children)){
            map.put (Key,value);
        }else{
            children.forEach (e-> buildValue(map,Key+"/"+e.getName (),e.getId (),e.getChildList ()));
        }
    }
    private Map<String, Long> getStoreGroupMap(Long tenantId) {
        MerchantStoreGroupQueryDTO query = new MerchantStoreGroupQueryDTO();
        query.setTenantId(tenantId);
        return merchantStoreGroupService.list(query).stream ().collect(Collectors.toMap(MerchantStoreGroup::getName,MerchantStoreGroup::getId,(existing, replacement) -> existing));
    }

    private Map<String,Long> getStoreMap(Set<String> storeCodeList, Long tenantId) {
        MerchantStoreQueryDTO queryReq = new MerchantStoreQueryDTO();
        queryReq.setStoreNos (new ArrayList<> (storeCodeList));
        queryReq.setTenantId (tenantId);
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO ();
        loginContextInfoDTO.setTenantId (tenantId);
        return merchantStoreService.listStore (queryReq,loginContextInfoDTO).stream().collect(Collectors.toMap(MerchantStore::getStoreNo, MerchantStore::getId,(existing, replacement) -> existing));
    }

    private Map<Long, ProductSkuDTO> getSkuMap(Long tenantId, List<Long> skuIds) {
        if(CollectionUtil.isEmpty (skuIds)){
            return Collections.emptyMap ();
        }

        List<ProductSkuDTO> reslut = new ArrayList<> (skuIds.size ());

        ProductSkuQueryConditionDTO productSkuQueryConditionDTO = new ProductSkuQueryConditionDTO();
        productSkuQueryConditionDTO.setSkuIds(skuIds);
        productSkuQueryConditionDTO.setTenantId(tenantId);
        // 查询货品信息
        List<ProductSkuDTO> productSkuDTOS = productSkuService.queryByCondition(productSkuQueryConditionDTO);
        productSkuQueryConditionDTO.setTenantId(1L);
        List<ProductSkuDTO> productSkuDTOSXM = productSkuService.queryByCondition(productSkuQueryConditionDTO);
        if(CollectionUtil.isNotEmpty (productSkuDTOS)) {
            reslut.addAll (productSkuDTOS);
        }
        if(CollectionUtil.isNotEmpty (productSkuDTOSXM)){
            reslut.addAll (productSkuDTOSXM);
        }

        if(CollectionUtil.isNotEmpty (reslut)){
            return reslut.stream ().collect(Collectors.toMap(ProductSkuDTO::getId,x -> x));
        }
        return Collections.emptyMap ();
    }

    private MarketSpuInput buildMarketSpuInput(Long tenantId,Map<String,Long> caregroyMap,MarketExcelDataDTO excelDataDTO, Map<String, Long> classificationMap,Map<String, Long> supplierMap,Map<Long, ProductSkuDTO> skuMap,Map<String, Long> storeMap, Map<String, Long> storeGroupMap) {
        MarketSpuInput marketSpuInput = new MarketSpuInput();
        marketSpuInput.setSubTitle(excelDataDTO.getSubTitle ());
        marketSpuInput.setTenantId (tenantId);
        marketSpuInput.setTitle (excelDataDTO.getTitle ());
        if(StringUtils.isNotEmpty (excelDataDTO.getFirstCategoryName ()) &&StringUtils.isNotEmpty (excelDataDTO.getSecondCategoryName ()) &&StringUtils.isNotEmpty (excelDataDTO.getThirdCategoryName ())){
            marketSpuInput.setCategoryId (caregroyMap.get (excelDataDTO.getFirstCategoryName ()+"/"+excelDataDTO.getSecondCategoryName ()+"/"+excelDataDTO.getThirdCategoryName ()));
        }
        MarketItemInput marketItemInput = MarketItemConverter.convertToMarketItemInput4ExcelDataDTO (excelDataDTO,tenantId);
        String classification = excelDataDTO.getSecondClassificationName () + "/" + excelDataDTO.getFirstClassificationName ();
        Long classifcationId = classificationMap.get (classification);
        if(classifcationId == null){
            throw new BizException ("商品分类不存在");
        }else{
            marketSpuInput.setClassificationId (classifcationId);
        }

        Long supplierId = null;
        if(MarketOperationModeExcelEnum.NON_HEADQUARTER_INVENTOR.getDesc ().equals (excelDataDTO.getOperationMode())){
            String supplierName = excelDataDTO.getSupplierName ();
            if(supplierName == null){
                throw new BizException ("非总部备货，供应商不能为空");
            }else {
                supplierId = supplierMap.get (supplierName);
                if(supplierId == null){
                    throw new BizException ("非总部备货，供应商不存在");
                }else if(supplierId.equals (1L)){
                    if(excelDataDTO.getSkuId () == null){
                        throw new BizException ("货品id不能为空");
                    }
                    marketItemInput.setGoodsType (GoodsTypeEnum.QUOTATION_TYPE.getCode ());
                } else{
                    fillSpecification(excelDataDTO,marketItemInput);

                    if (excelDataDTO.getNoGoodsSupplyPrice() == null) {
                        throw new BizException ("供应价 不能为空");
                    }
                    marketItemInput.setGoodsType (GoodsTypeEnum.NO_GOOD_TYPE.getCode ());
                }
            }
            marketItemInput.setSupplierId (String.valueOf (supplierId));
        }else{
            fillSpecification(excelDataDTO,marketItemInput);

            if(excelDataDTO.getSkuId () == null){
                throw new BizException ("货品id不能为空");
            }
            marketItemInput.setGoodsType (GoodsTypeEnum.SELF_GOOD_TYPE.getCode ());
        }

        if(excelDataDTO.getSkuId () != null){
            ProductSkuDTO skuDTO = skuMap.get (excelDataDTO.getSkuId ());
            if(skuDTO == null){
                throw new BizException ("货品不存在");
            }
            if (!SkuUseFlagEnum.IN_USER.getCode().equals(skuDTO.getUseFlag())) {
                throw new BizException("sku停用不可生成商品");
            }
            marketItemInput.setSpecification (skuDTO.getSpecification ());
            marketItemInput.setSpecificationUnit (skuDTO.getSpecificationUnit ());
        }


        OnsaleTypeExcelEnum onsale = OnsaleTypeExcelEnum.getByDesc (excelDataDTO.getOnSale ());
        if(onsale == null){
            throw new BizException ("上下架错误");
        }
        marketItemInput.setOnSale(onsale.getCode ());


        ItemSaleLimitRuleExcelEnum limitRuleExcelEnum = ItemSaleLimitRuleExcelEnum.getByDesc (excelDataDTO.getSaleLimitRuleName ());
        if(limitRuleExcelEnum == null){
            throw new BizException ("下单上限方式");
        }
        if(!limitRuleExcelEnum.getCode ().equals (ItemSaleLimitRuleExcelEnum.NO_LIMIT.getCode ()) && excelDataDTO.getSaleLimitQuantity () == null){
            throw new BizException ("限购数量不可为空");
        }
        marketItemInput.setSaleLimitRule(limitRuleExcelEnum.getCode ());

        SwitchFlagEnum buyMultipleSwitch = SwitchFlagEnum.getByDesc (excelDataDTO.getBuyMultipleSwitch ());
        if(buyMultipleSwitch == null){
            throw new BizException ("倍数订货 是否开启错误");
        }
        marketItemInput.setBuyMultipleSwitch(buyMultipleSwitch.getFlag().equals (SwitchFlagEnum.OPEN.getFlag ()));
        if(marketItemInput.getBuyMultipleSwitch () && excelDataDTO.getBuyMultiple () == null){
            throw new BizException ("倍数订货数值不可为空");
        }
        marketItemInput.setBuyMultiple(excelDataDTO.getBuyMultiple ());

        checkLimitQuantityAndBuyMultiple(marketSpuInput.getMarketItemInput());

        SwitchFlagEnum presaleSwitch = SwitchFlagEnum.getByDesc (excelDataDTO.getPresaleSwitch ());
        if(presaleSwitch == null){
            throw new BizException ("商品预售开关错误");
        }
        marketItemInput.setPresaleSwitch(presaleSwitch.getFlag ());

        ItemSaleModeExcelEnum itemSaleModeEnum =  ItemSaleModeExcelEnum.getByDesc(excelDataDTO.getItemSaleModeName ());
        if(itemSaleModeEnum == null){
            throw new BizException ("销售方式错误");
        }
        marketItemInput.setItemSaleMode(itemSaleModeEnum.getType());

        SwitchFlagEnum storeInventoryControlFlag = SwitchFlagEnum.getByDesc (excelDataDTO.getStoreInventoryControlFlag ());
        if(storeInventoryControlFlag == null){
            throw new BizException ("管控门店库存错误");
        }
        marketItemInput.setStoreInventoryControlFlag(storeInventoryControlFlag.getFlag().equals (SwitchFlagEnum.OPEN.getFlag ()));

        // 价格相关
        PriceStrategyTypeExcelEnum strategyTypeExcelEnum = PriceStrategyTypeExcelEnum.getByDesc (excelDataDTO.getPriceStrategy ());
        if(strategyTypeExcelEnum == null){
            throw new BizException ("定价方式错误");
        }

        PriceTargetExcelEnum priceTargetExcelEnum = PriceTargetExcelEnum.getByDesc (excelDataDTO.getPriceTargetType ());
        if(priceTargetExcelEnum == null){
            throw new BizException ("价格策略错误");
        }
        MarketAreaItemMappingInput price  = new MarketAreaItemMappingInput();
        price.setPriceType (priceTargetExcelEnum.getCode ());
        price.setType (strategyTypeExcelEnum.getCode ());
        price.setMappingNumber (excelDataDTO.getPrice ());
        switch (priceTargetExcelEnum) {
            case ALL:
                marketItemInput.setDefaultPrice (price);
                break;
            case STORE:
                if (StringUtils.isBlank (excelDataDTO.getPriceTargets ())) {
                    throw new BizException ("门店 不能为空");
                }
                Set<String> storeCodeList =  Arrays.stream(excelDataDTO.getPriceTargets().split("，")).filter (StringUtils::isNotEmpty).collect(Collectors.toSet());
                Set<Long> storeIds = storeCodeList.stream ()
                        .map (storeMap::get).filter (StringUtils::isNotEmpty)
                        .collect (Collectors.toSet ());
                if(CollectionUtil.isEmpty (storeIds) || storeIds.size () != storeCodeList.size ()){
                    throw new BizException ("门店 不存在");
                }
                price.setStoreIds (new ArrayList<> (storeIds));
                marketItemInput.setStoreGroupPrice (Collections.singletonList (price));
                break;
            case GROUP:
                if (StringUtils.isBlank (excelDataDTO.getPriceTargets ())) {
                    throw new BizException ("门店 不能为空");
                }

                Set<String> storeGroupNameList =  Arrays.stream(excelDataDTO.getPriceTargets().split("，")).filter (StringUtils::isNotEmpty).collect(Collectors.toSet());
                Set<Long> storeGroupIds = storeGroupNameList.stream ()
                        .map (storeGroupMap::get).filter (StringUtils::isNotEmpty)
                        .collect (Collectors.toSet ());
                if(CollectionUtil.isEmpty (storeGroupIds) || storeGroupNameList.size () != storeGroupIds.size ()){
                    throw new BizException ("门店 不存在");
                }
                price.setStoreGroupIds (new ArrayList<> (storeGroupIds));
                marketItemInput.setStorePrice (Collections.singletonList (price));
                break;
        }

        UnfairPriceStrategyDefaultFlagEnum byDesc = UnfairPriceStrategyDefaultFlagEnum.getByDesc (excelDataDTO.getUnfairPriceStrategyDefaultFlag ());
        if(byDesc == null){
            throw new BizException ("防倒挂规则错误");
        }
        MarketItemUnfairPriceStrategyDTO marketItemUnfairPriceStrategyDTO = new MarketItemUnfairPriceStrategyDTO ();
        marketItemUnfairPriceStrategyDTO.setDefaultFlag(byDesc.getCode ());
        if(UnfairPriceStrategyDefaultFlagEnum.SELF.getCode ().equals (byDesc.getCode ())) {
            if (StringUtils.isBlank (excelDataDTO.getUnfairPriceStrategy())) {
                throw new BizException("防倒挂价格策略 不能为空") ;
            }
            StrategyValueEnum strategyValueEnum = StrategyValueEnum.getByDesc (excelDataDTO.getUnfairPriceStrategy ());
            if(strategyValueEnum == null){
                throw new BizException ("防倒挂策略错误");
            }
            marketItemUnfairPriceStrategyDTO.setStrategyType (strategyValueEnum.getCode ());
        }
//        marketItemInput.setPriceType();
        marketItemInput.setMarketItemUnfairPriceStrategyDTO(marketItemUnfairPriceStrategyDTO);

        marketSpuInput.setMarketItemInput (marketItemInput);
        return marketSpuInput;
    }

    private void fillSpecification(MarketExcelDataDTO excelDataDTO,MarketItemInput marketItemInput) {
        String specificationType = excelDataDTO.getSpecificationType ();
        if(StringUtils.isEmpty (specificationType )){
            throw new BizException ("规格类型不能为空");
        }
        SpecificationTypeExcelEnum specificationTypeExcelEnum = SpecificationTypeExcelEnum.getByDesc (specificationType);
        if(specificationTypeExcelEnum == null){
            throw new BizException ("规格类型错误");
        }
        switch (specificationTypeExcelEnum) {
            case QJ:
                marketItemInput.setSpecification ("1_" + marketItemInput.getSpecification ());
                break;
            case  RL:
                marketItemInput.setSpecification ("0_" + marketItemInput.getSpecification ());
                break;
        }
    }

    private void checkLimitQuantityAndBuyMultiple(MarketItemInput marketItemInput) {
        if(ObjectUtil.isEmpty (marketItemInput)){
            return;
        }
        if(Objects.equals (Boolean.TRUE,marketItemInput.getBuyMultipleSwitch ()) && Objects.equals (ItemSaleLimitRuleEnum.EVERY_TIME.getCode (),marketItemInput.getSaleLimitRule ())){
            Integer saleLimitQuantity = marketItemInput.getSaleLimitQuantity ();
            if(saleLimitQuantity < marketItemInput.getBuyMultiple ()){
                throw new BizException("订货倍数不可大于每次下单上限");
            }
        }
    }
    private void validExcelPropertyEmpty(MarketExcelDataDTO excelDataDTO) {
        // 验证每个字段
        if (StringUtils.isBlank (excelDataDTO.getTitle())) {
            throw new BizException("商品名称 不能为空");
        }

        if (StringUtils.isBlank (excelDataDTO.getFirstClassificationName ())) {
            throw new BizException("前台一级分组 不能为空");
        }

        if (StringUtils.isBlank (excelDataDTO.getSecondClassificationName())) {
            throw new BizException("前台二级分组 不能为空");
        }

        if (StringUtils.isBlank (excelDataDTO.getOperationMode())) {
            throw new BizException("货源模式 不能为空");
        }else if(Arrays.stream (MarketOperationModeExcelEnum.values ()).noneMatch (e->e.getDesc().equals (excelDataDTO.getOperationMode()))){
            throw new BizException("货源模式 值非法");
        }

        if (excelDataDTO.getOnSale() == null) {
            throw new BizException("上架状态 不能为空");
        }

        if (excelDataDTO.getMiniOrderQuantity() == null) {
            throw new BizException("起订量 不能为空");
        }

        if (StringUtils.isBlank (excelDataDTO.getBuyMultipleSwitch())) {
            throw new BizException("订货倍数开关 不能为空");
        }

        if (StringUtils.isBlank (excelDataDTO.getSaleLimitRuleName())) {
            throw new BizException("下单上限方式 不能为空");
        }

        if (StringUtils.isBlank (excelDataDTO.getItemSaleModeName())) {
            throw new BizException("销售方式 不能为空");
        }

        if (StringUtils.isBlank (excelDataDTO.getPresaleSwitch())) {
            throw new BizException("是否开启预售 不能为空");
        }

        if (StringUtils.isBlank (excelDataDTO.getStoreInventoryControlFlag())) {
            throw new BizException("管控门店库存 不能为空");
        }

        if (StringUtils.isBlank (excelDataDTO.getStoreCostUnit())) {
            throw new BizException("成本单位 不能为空");
        }

        if (StringUtils.isBlank (excelDataDTO.getStoreInventoryUnit())) {
            throw new BizException("库存单位 不能为空");
        }

        if (StringUtils.isBlank (excelDataDTO.getStoreOrderingUnit())) {
            throw new BizException("订货单位 不能为空");
        }

        if (excelDataDTO.getStoreInventoryCostUnitMultiple() == null) {
            throw new BizException("库存单位转换成成本单位比值 不能为空");
        }

        if (excelDataDTO.getStoreOrderingInventoryUnitMultiple() == null) {
            throw new BizException("订货单位转换成库存单位比值 不能为空");
        }

        if (excelDataDTO.getStandardUnitPrice() == null) {
            throw new BizException("标准单价 不能为空");
        }

        if (StringUtils.isBlank (excelDataDTO.getAfterSaleUnit())) {
            throw new BizException("售后单位 不能为空");
        }

        if (excelDataDTO.getMaxAfterSaleAmount() == null) {
            throw new BizException("最大售后数量 不能为空");
        }
        if (StringUtils.isBlank (excelDataDTO.getPriceStrategy())) {
            throw new BizException("定价方式 不能为空");
        }

        if (StringUtils.isBlank (excelDataDTO.getPriceTargetType ())) {
            throw new BizException("价格策略 不能为空");
        }

        if (excelDataDTO.getPrice() == null) {
            throw new BizException("价格 不能为空");
        }

        if (StringUtils.isBlank (excelDataDTO.getUnfairPriceStrategyDefaultFlag())) {
            throw new BizException("防倒挂规则 不能为空");
        }
    }


    @Override
    protected File generateErrDataExcelFile(List<MarketExcelDataDTO> errorDataList) {
        log.info("生成错误数据文件, {}", errorDataList);
        File file = new File(UUID.randomUUID().toString() + ".xlsx");
        EasyExcel.write(file, MarketExcelDataDTO.class).sheet().registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).doWrite(errorDataList);
        return file;
    }
}
