package com.cosfo.manage.downloadcenter.item;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import lombok.Data;
import net.xianmu.download.support.dto.ImportExcelBaseDTO;

import java.math.BigDecimal;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/23
 */
@Data
public class MarketExcelDataDTO extends ImportExcelBaseDTO {
    /**
     * 租户id
     */
    @ExcelIgnore
    private Long tenantId;

    @ExcelProperty("商品名称\n" +
            "1.必填\n" +
            "2.请输入20个字以内的商品名称；")
    private String title;

    @ExcelProperty("商品副标题\n" +
            "1.非必填；\n" +
            "2.可输入20个字以内的副标题")
    private String subTitle;

    @ExcelProperty("前台一级分组\n" +
            "1.必填，分组名称可在后台\"商品-商品管理-商品分组\"页面查看；\n" +
            "2.分组名称需是已存在的；")
    private String firstClassificationName;

    @ExcelProperty("前台二级分组\n" +
            "1.必填，分组名称可在后台\"商品-商品管理-商品分组\"页面查看；\n" +
            "2.分组名称需是已存在的；")
    private String secondClassificationName;

    @ExcelProperty("后台一级类目\n" +
            "1.非必填；\n" +
            "2.如填写，类目名称需在后台类目表(右表-后台类目)")
    private String firstCategoryName;

    @ExcelProperty("后台二级类目\n" +
            "1.非必填；\n" +
            "2.如填写，类目名称需在后台类目表(右表-后台类目)")
    private String secondCategoryName;

    @ExcelProperty("后台三级类目\n" +
            "1.非必填；\n" +
            "2.如填写，类目名称需在后台类目表(右表-后台类目)")
    private String thirdCategoryName;

    @ExcelProperty("货源模式\n" +
            "1.必选；")
    private String operationMode;

    @ExcelProperty("货品ID\n" +
            "1.供货模式为总部备货或者非总部供货的供应商为鲜沐时则必填；非总部备货并且供应商不为鲜沐时则可不填；\n" +
            "2.如填写，需填写已经存在的货品ID；")
    private Long skuId;

    @ExcelProperty("供应商编号\n" +
            "1.供货模式为非总部供货，则必填；供应商编号可在后台-采购-供应商管理页面查看；")
    private Long supplierId;

    @ExcelProperty("供应商名称\n" +
            "1.供货模式为非总部供货，则必填；供应商编号可在后台-采购-供应商管理页面查看；")
    private String supplierName;

    @ExcelProperty("供应价\n" +
            "1.供货模式为非总部备货并且供应商不为鲜沐时，则供应价必填")
    @NumberFormat("#.##")
    private BigDecimal noGoodsSupplyPrice;

    @ExcelProperty("商品自有编码\n" +
            "1.非必填；")
    private String itemCode;

    @ExcelProperty("规格单位\n" +
            "1.供货模式为非总部备货并且供应商不为鲜沐时，则必选；\n" +
            "2.如1箱=24袋，每袋=1KG，则规格单位为箱")
    private String specificationUnit;

    @ExcelProperty("规格类型\n" +
            "1.非总部备货并且供应商不为鲜沐时，则必填；")
    private String specificationType;

    @ExcelProperty("具体规格\n" +
            "1.供货模式为非总部备货并且供应商不为鲜沐时，则必选；\n" +
            "2.如1箱=24袋，每袋=1KG，则具体规格为 1KG*24袋；")
    private String specification;

    @ExcelProperty("上架状态\n" +
            "1.必选；")
    private String onSale;

    @ExcelProperty("起订量\n" +
            "1.必填")
    private Integer miniOrderQuantity;

    @ExcelProperty("库存\n" +
            "1.非总部备货并且供应商不为鲜沐可填写，非必填")
    private Integer stock;

    @ExcelProperty("订货倍数开启\n" +
            "1.必选")
    private String buyMultipleSwitch;

    @ExcelProperty("订货倍数\n" +
            "1.当开启订货倍数时，此倍数必填；")
    private Integer buyMultiple;

    @ExcelProperty("下单上限方式\n" +
            "1.必选；")
    private String saleLimitRuleName;

    @ExcelProperty("上限数量\n" +
            "1.如下单上限为无限制，则无需填写；如不为无限制，则必须填写；")
    private Integer saleLimitQuantity;

    @ExcelProperty("销售方式\n" +
            "1.必选；")
    private String itemSaleModeName;

    @ExcelProperty("是否开启预售\n" +
            "1.必选，可选是和否")
    private String presaleSwitch;

    @ExcelProperty("价格策略\n" +
            "1.必选，有所有门店，指定分组或指定门店；\n" +
            "2.默认价格即是选择所有门店；\n" +
            "3.如多种价格策略并存，则先导入商品后再编辑；")
    private String priceTargetType;

    @ExcelProperty("门店信息\n" +
            "1.如价格策略为默认价格(所有门店)，则无需填写；\n" +
            "2.如价格策略为指定分组，请填写分组名称，多个分组用中文逗号隔开；\n" +
            "3.如价格策略为指定门店，请填写门店编码，多个编码用中文逗号隔开；")
    private String priceTargets;

    @ExcelProperty("定价方式\n" +
            "1.必选；")
    private String priceStrategy;

    @ExcelProperty("价格\n" +
            "1.必填；\n" +
            "2.如定价方式为供应价实时上浮，请填写上浮的百分比；\n" +
            "3.如定价方式为供应价实时加价，请填写加价金额(元)；\n" +
            "4.如定价方式为自定义固定价，请填写固定价；")
    @NumberFormat("#.##")
    private BigDecimal price;

    @ExcelProperty("防倒挂规则\n" +
            "1.必选；")
    private String unfairPriceStrategyDefaultFlag;

    @ExcelProperty("防倒挂价格策略\n" +
            "1.如防倒挂规则为遵从全局，则无需填写；\n" +
            "2.如防倒挂规则为独立规则，则必填，可选择以供应价售卖、以自定义价格售卖、自动下架；\n")
    private String unfairPriceStrategy;

    @ExcelProperty("管控门店库存\n" +
            "1.必选；\n" +
            "2.如开启管控，则商品会进入到门店进销稽核表中；")
    private String storeInventoryControlFlag;

    @ExcelProperty("成本单位\n" +
            "1.必选；\n" +
            "最小单位；\n" +
            "2.制作在产品、产成品对应的原料用量单位，例如制作一杯咖啡对应的咖啡豆用量单位")
    private String storeCostUnit;

    @ExcelProperty("库存单位\n" +
            "1.必选；\n" +
            "2.商品在门店的存货计量单位；")
    private String storeInventoryUnit;

    @ExcelProperty("订货单位\n" +
            "1.必选，用于门店订货的单位，一般是最大包装单位")
    private String storeOrderingUnit;

    @ExcelProperty("库存单位转换成成本单位比值\n" +
            "1.必填；\n" +
            "2.比如：成本单位：ml，库存单位：瓶，\n" +
            "500ml=1瓶，该字段填写500；")
    @NumberFormat("#.##")
    private BigDecimal storeInventoryCostUnitMultiple;

    @ExcelProperty("订货单位转换成库存单位比值\n" +
            "1.必填；\n" +
            "2.比如：订货单位：箱，库存单位：瓶，\n" +
            "12瓶=1箱，该字段填写12")
    @NumberFormat("#.##")
    private BigDecimal storeOrderingInventoryUnitMultiple;

    @ExcelProperty("标准单价\n" +
            "1.必填；\n" +
            "2.以库存单位计算的单价，比如1箱=100元，1箱=10瓶，库存单位为瓶，则每瓶为10元；")
    @NumberFormat("#.##")
    private BigDecimal standardUnitPrice;

    @ExcelProperty("售后单位\n" +
            "1.必填；")
    private String afterSaleUnit;

    @ExcelProperty("最大售后数量\n" +
            "1.必填")
    private Integer maxAfterSaleAmount;
}
