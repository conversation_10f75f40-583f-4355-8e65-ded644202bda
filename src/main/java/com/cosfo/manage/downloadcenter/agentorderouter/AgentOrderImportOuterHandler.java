package com.cosfo.manage.downloadcenter.agentorderouter;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.cofso.item.client.resp.MarketItemInfoResp;
import com.cosfo.common.excel.easyexcel.converter.EasyExcelLocalDateConverter;
import com.cosfo.common.excel.easyexcel.converter.LocalDateTimeConverter;
import com.cosfo.manage.agentorder.model.input.command.AgentOrderAddInput;
import com.cosfo.manage.agentorder.model.input.command.AgentOrderItemInput;
import com.cosfo.manage.agentorder.model.input.command.AgentOrderStoreInput;
import com.cosfo.manage.agentorder.model.vo.AgentOrderCheckVO;
import com.cosfo.manage.agentorder.service.AgentOrderService;
import com.cosfo.manage.common.context.AgentOrderEnum;
import com.cosfo.manage.common.context.ExcelTypeEnum;
import com.cosfo.manage.common.context.FileDownloadTypeEnum;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.util.ExcelUtils;
import com.cosfo.manage.common.util.ThreadTokenHolder;
import com.cosfo.manage.facade.MarketFacade;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreFacade;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.download.support.dto.DownloadCenterDataMsg;
import net.xianmu.download.support.handler.DownloadCenterImportDefaultHandler;
import net.xianmu.usercenter.client.merchant.resp.MerchantStorePageResultResp;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 批量导入代下单-外部导单
 *
 * @author: xiaowk
 * @date: 2025/3/20 下午3:13
 */
@Component
@Slf4j
public class AgentOrderImportOuterHandler extends DownloadCenterImportDefaultHandler<AgentOrderOuterExcelDTO> {

    @Resource
    private AgentOrderService agentOrderService;
    @Resource
    private UserCenterMerchantStoreFacade userCenterMerchantStoreFacade;
    @Resource
    private MarketFacade marketFacade;

    @Override
    public DownloadCenterEnum.RequestSource getSource() {
        return DownloadCenterEnum.RequestSource.SAAS;
    }

    @Override
    public Integer getBizType() {
        return FileDownloadTypeEnum.AGENT_ORDER_IMPORT_OUTER.getType();
    }

    @Override
    protected void dealExcelData(List<AgentOrderOuterExcelDTO> list, DownloadCenterDataMsg downloadCenterDataMsg) {

        Long tenantId = downloadCenterDataMsg.getAuthUser().getTenantId();
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(tenantId);
        loginContextInfoDTO.setAuthUserId(downloadCenterDataMsg.getAuthUser().getId());
        ThreadTokenHolder.setToken(loginContextInfoDTO);

        // 有相同门店、相同商品的记录
        Set<String> duplicateStoreNos = list.stream()
                .collect(Collectors.groupingBy(
                        order -> order.getStoreNo() + "_##_" + order.getItemCode(),
                        Collectors.counting()
                ))
                .entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .map(entry -> entry.getKey().split("_##_")[0])
                .collect(Collectors.toSet());

        Map<String, List<AgentOrderOuterExcelDTO>> agentOrderMap = list.stream()
                .collect(Collectors.groupingBy(
                        AgentOrderOuterExcelDTO::getStoreNo, // 按 storeNo 分组
                        LinkedHashMap::new, // 保持顺序
                        Collectors.toList() // 收集为 List
                ));

        for (Map.Entry<String, List<AgentOrderOuterExcelDTO>> entry : agentOrderMap.entrySet()) {
            String storeNo = entry.getKey();
            List<AgentOrderOuterExcelDTO> itemList = entry.getValue();

            if (duplicateStoreNos.contains(storeNo)) {
                itemList.forEach(item -> item.setErrorMsg("相同门店、相同商品请合并数量填写"));
                continue;
            }

            try {
                // 组装请求参数
                AgentOrderAddInput agentOrderAddInput = buildAgentOrderAddInput(storeNo, itemList, tenantId);
                // 检查代下单信息
                boolean checkPassFlag = checkPlanOrder(itemList, tenantId, agentOrderAddInput);
                if (!checkPassFlag) {
                    continue;
                }

                // 校验通过，代下单
                agentOrderService.addPlanOrder(agentOrderAddInput, tenantId);

            } catch (Exception e) {
                log.warn("storeNo={} 代下单异常，", storeNo, e);
                itemList.forEach(item -> item.setErrorMsg(Optional.ofNullable(e.getMessage()).orElse("代下单异常")));
            }
        }
    }

    private AgentOrderAddInput buildAgentOrderAddInput(String storeNo, List<AgentOrderOuterExcelDTO> itemList, Long tenantId) {
        MerchantStorePageResultResp merchantStore = userCenterMerchantStoreFacade.getMerchantStoreByCode(tenantId, storeNo);
        if (merchantStore == null
                || merchantStore.getTenantId() == null
                || !merchantStore.getTenantId().equals(tenantId)) {
            throw new BizException("门店不存在");
        }

        List<String> itemcodes = itemList.stream().map(AgentOrderOuterExcelDTO::getItemCode).distinct().collect(Collectors.toList());
        Map<String, List<MarketItemInfoResp>> itemcode2itemMap = marketFacade.listMarketItemInfoByItemCodes(tenantId, itemcodes);

        // 商品校验错误
        itemList.forEach(e -> {
            if (itemcode2itemMap.containsKey(e.getItemCode())) {
                List<MarketItemInfoResp> tmpList = itemcode2itemMap.get(e.getItemCode());
                if(tmpList.size() != 1){
                    e.setErrorMsg(String.format("三方商品编码:%s存在多条商品信息，需要对应商品唯一", e.getItemCode()));
                }else{
                    e.setItemId(tmpList.get(0).getItemId());
                }
            } else {
                e.setErrorMsg(String.format("三方商品编码:%s不存在商品", e.getItemCode()));
            }
        });

        Long storeId = merchantStore.getId();

        AgentOrderAddInput agentOrderAddInput = new AgentOrderAddInput();
        List<AgentOrderItemInput> itemInputs = itemList.stream().map(e -> {
            AgentOrderItemInput itemInput = new AgentOrderItemInput();
            itemInput.setItemId(e.getItemId());
            itemInput.setItemAmount(e.getItemAmount());
            return itemInput;
        }).collect(Collectors.toList());
        agentOrderAddInput.setItemInputs(itemInputs);
        AgentOrderStoreInput storeInput = new AgentOrderStoreInput();
        storeInput.setStoreId(storeId);
        storeInput.setStoreNo(merchantStore.getStoreNo());
        storeInput.setPlanType(AgentOrderEnum.PlanTypeEnum.CREATE_ORDER.name());
        agentOrderAddInput.setStoreInputs(Lists.newArrayList(storeInput));
        return agentOrderAddInput;
    }


    /**
     * 检查代下单信息 true-成功 false-失败
     *
     * @param itemList
     * @param tenantId
     * @return
     */
    private boolean checkPlanOrder(List<AgentOrderOuterExcelDTO> itemList, Long tenantId, AgentOrderAddInput agentOrderAddInput) {
        boolean existErrorMsg = itemList.stream().anyMatch(e -> StringUtils.isNotBlank(e.getErrorMsg()));

        if(existErrorMsg) {
            // 商品校验错误
            itemList.forEach(e -> {
                if (StringUtils.isBlank(e.getErrorMsg())) {
                    e.setErrorMsg(String.format("三方门店编号:%s存在其他商品校验错误", e.getStoreNo()));
                }
            });
            return false;
        }

        // 代下单提交前校验商品
        List<AgentOrderCheckVO> agentOrderCheckVOS = agentOrderService.checkPlanOrder(agentOrderAddInput, tenantId);
        if (!CollectionUtils.isEmpty(agentOrderCheckVOS)) {
            if (StringUtils.isNotBlank(agentOrderCheckVOS.get(0).getStoreCheckFailMsg())) {
                // 门店校验错误
                itemList.forEach(e -> e.setErrorMsg(agentOrderCheckVOS.get(0).getStoreCheckFailMsg()));

            } else if (!CollectionUtils.isEmpty(agentOrderCheckVOS.get(0).getItemCheckResultList())) {
                Map<Long, String> itemFailMsgMap = agentOrderCheckVOS.get(0).getItemCheckResultList().stream().collect(Collectors.toMap(AgentOrderCheckVO.ItemCheckResult::getItemId, AgentOrderCheckVO.ItemCheckResult::getFailMsg, (v1, v2) -> v1));
                // 商品校验错误
                itemList.forEach(e -> {
                    if (itemFailMsgMap.containsKey(e.getItemId())) {
                        e.setErrorMsg(String.format("三方门店编号:%s, %s", e.getStoreNo(), itemFailMsgMap.get(e.getItemId())));
                    } else {
                        e.setErrorMsg(String.format("三方门店编号:%s存在其他商品校验错误", e.getStoreNo()));
                    }
                });

            } else {
                itemList.forEach(e -> e.setErrorMsg("校验异常"));
            }

            return false;
        }

        return true;
    }

    @Override
    protected File generateErrDataExcelFile(List<AgentOrderOuterExcelDTO> errorDataList) {
        InputStream templateFileInputStream = ExcelUtils.getExcelFileInputStream(this.getClass(), ExcelTypeEnum.IMPORT_AGENT_ORDER_OUTER_ERROR.getName());
        String filePath = ExcelUtils.tempExcelFilePath();
        ExcelWriter excelWriter = EasyExcel.write(filePath).registerConverter(new EasyExcelLocalDateConverter()).registerConverter(new LocalDateTimeConverter()).withTemplate(templateFileInputStream).build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();

        // 先写入storeId为空的记录
        List<AgentOrderOuterExcelDTO> storeIdNullList = errorDataList.stream().filter(e -> StringUtils.isBlank(e.getStoreNo())).collect(Collectors.toList());
        excelWriter.fill(storeIdNullList, fillConfig, writeSheet);

        Map<String, List<AgentOrderOuterExcelDTO>> agentOrderMap = errorDataList.stream()
                .filter(e -> StringUtils.isNotBlank(e.getStoreNo())) // 过滤storeId不为空
                .collect(Collectors.groupingBy(
                        AgentOrderOuterExcelDTO::getStoreNo, // 按 storeId 分组
                        LinkedHashMap::new, // 保持顺序
                        Collectors.toList() // 收集为 List
                ));

        for (Map.Entry<String, List<AgentOrderOuterExcelDTO>> entry : agentOrderMap.entrySet()) {
            excelWriter.fill(entry.getValue(), fillConfig, writeSheet);
        }

        excelWriter.finish();
        File file = new File(filePath);
        return file;
    }
}
