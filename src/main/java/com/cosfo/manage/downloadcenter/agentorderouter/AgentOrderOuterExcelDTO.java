package com.cosfo.manage.downloadcenter.agentorderouter;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import net.xianmu.download.support.dto.ImportExcelBaseDTO;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: xiaowk
 * @date: 2025/3/20 下午3:14
 */
@Data
public class AgentOrderOuterExcelDTO extends ImportExcelBaseDTO {

    /**
     * 三方门店编号
     */
    @ExcelProperty("三方门店编号\n" +
            "1.下单后根据商品编码和下单数量等自动计算订单的商品总价和运费；\n" +
            "2.仅适用于有账期的门店，下单后自动扣减门店账期，请填写门店-门店管理列表中的门店编号；")
    @NotBlank(message = "三方门店编号不能为空")
    private String storeNo;

    /**
     * 商品自有编码
     */
    @ExcelProperty("三方商品编码(即帆台商品自有编码)\n" +
            "请填写商品-商品列表中的自有编码")
    @NotBlank(message = "三方商品编码不能为空")
    private String itemCode;

    /**
     * 商品id
     */
    private Long itemId;

    /**
     * 商品数量
     */
    @ExcelProperty("下单数量\n" +
            "需符合下单规则(起订量，倍数订货，下单上限)")
    @NotNull(message = "下单数量不能为空")
    private Integer itemAmount;


}
