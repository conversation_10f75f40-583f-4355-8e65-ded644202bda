package com.cosfo.manage.downloadcenter.agentorder;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.cosfo.common.excel.easyexcel.converter.EasyExcelLocalDateConverter;
import com.cosfo.common.excel.easyexcel.converter.LocalDateTimeConverter;
import com.cosfo.manage.agentorder.model.input.command.AgentOrderAddInput;
import com.cosfo.manage.agentorder.model.input.command.AgentOrderItemInput;
import com.cosfo.manage.agentorder.model.input.command.AgentOrderStoreInput;
import com.cosfo.manage.agentorder.model.vo.AgentOrderCheckVO;
import com.cosfo.manage.agentorder.service.AgentOrderService;
import com.cosfo.manage.common.context.AgentOrderEnum;
import com.cosfo.manage.common.context.ExcelTypeEnum;
import com.cosfo.manage.common.context.FileDownloadTypeEnum;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.util.ExcelUtils;
import com.cosfo.manage.common.util.ThreadTokenHolder;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreFacade;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.download.support.dto.DownloadCenterDataMsg;
import net.xianmu.download.support.handler.DownloadCenterImportDefaultHandler;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 批量导入代下单
 *
 * @author: xiaowk
 * @time: 2024/7/9 下午6:37
 */
@Component
@Slf4j
public class AgentOrderImportHandler extends DownloadCenterImportDefaultHandler<AgentOrderExcelDTO> {

    @Resource
    private AgentOrderService agentOrderService;
    @Resource
    private UserCenterMerchantStoreFacade userCenterMerchantStoreFacade;


    @Override
    public DownloadCenterEnum.RequestSource getSource() {
        return DownloadCenterEnum.RequestSource.SAAS;
    }

    @Override
    public Integer getBizType() {
        return FileDownloadTypeEnum.AGENT_ORDER_IMPORT.getType();
    }

    @Override
    protected void dealExcelData(List<AgentOrderExcelDTO> list, DownloadCenterDataMsg downloadCenterDataMsg) {

        Long tenantId = downloadCenterDataMsg.getAuthUser().getTenantId();
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(tenantId);
        loginContextInfoDTO.setAuthUserId(downloadCenterDataMsg.getAuthUser().getId());
        ThreadTokenHolder.setToken(loginContextInfoDTO);

        // 有相同门店、相同商品的记录
        Set<Long> duplicateStoreIds = list.stream()
                .collect(Collectors.groupingBy(
                        order -> order.getStoreId() + "-" + order.getItemId(),
                        Collectors.counting()
                ))
                .entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .map(entry -> Long.valueOf(entry.getKey().split("-")[0]))
                .collect(Collectors.toSet());

        Map<Long, List<AgentOrderExcelDTO>> agentOrderMap = list.stream()
                .collect(Collectors.groupingBy(
                        AgentOrderExcelDTO::getStoreId, // 按 storeId 分组
                        LinkedHashMap::new, // 保持顺序
                        Collectors.toList() // 收集为 List
                ));

        for (Map.Entry<Long, List<AgentOrderExcelDTO>> entry : agentOrderMap.entrySet()) {
            Long storeId = entry.getKey();
            List<AgentOrderExcelDTO> itemList = entry.getValue();

            if (duplicateStoreIds.contains(storeId)) {
                itemList.forEach(item -> item.setErrorMsg("相同门店、相同商品请合并数量填写"));
                continue;
            }

            try {
                // 组装请求参数
                AgentOrderAddInput agentOrderAddInput = buildAgentOrderAddInput(storeId, itemList, tenantId);
                // 检查代下单信息
                boolean checkPassFlag = checkPlanOrder(storeId, itemList, tenantId, agentOrderAddInput);
                if (!checkPassFlag) {
                    continue;
                }

                // 校验通过，代下单
                agentOrderService.addPlanOrder(agentOrderAddInput, tenantId);

            } catch (Exception e) {
                log.warn("storeId={} 代下单异常，", storeId, e);
                itemList.forEach(item -> item.setErrorMsg(Optional.ofNullable(e.getMessage()).orElse("代下单异常")));
            }
        }
    }

    private AgentOrderAddInput buildAgentOrderAddInput(Long storeId, List<AgentOrderExcelDTO> itemList, Long tenantId) {
        MerchantStoreResultResp merchantStore = userCenterMerchantStoreFacade.getMerchantStoreById(storeId);
        if (merchantStore == null
                || merchantStore.getTenantId() == null
                || !merchantStore.getTenantId().equals(tenantId)) {
            throw new BizException("门店不存在");
        }


        AgentOrderAddInput agentOrderAddInput = new AgentOrderAddInput();
        List<AgentOrderItemInput> itemInputs = itemList.stream().map(e -> {
            AgentOrderItemInput itemInput = new AgentOrderItemInput();
            itemInput.setItemId(e.getItemId());
            itemInput.setItemAmount(e.getItemAmount());
            return itemInput;
        }).collect(Collectors.toList());
        agentOrderAddInput.setItemInputs(itemInputs);
        AgentOrderStoreInput storeInput = new AgentOrderStoreInput();
        storeInput.setStoreId(storeId);
        storeInput.setStoreNo(merchantStore.getStoreNo());
        storeInput.setPlanType(AgentOrderEnum.PlanTypeEnum.CREATE_ORDER.name());
        agentOrderAddInput.setStoreInputs(Lists.newArrayList(storeInput));
        return agentOrderAddInput;
    }

    /**
     * 检查代下单信息 true-成功 false-失败
     *
     * @param storeId
     * @param itemList
     * @param tenantId
     * @return
     */
    private boolean checkPlanOrder(Long storeId, List<AgentOrderExcelDTO> itemList, Long tenantId, AgentOrderAddInput agentOrderAddInput) {
        // 代下单提交前校验商品
        List<AgentOrderCheckVO> agentOrderCheckVOS = agentOrderService.checkPlanOrder(agentOrderAddInput, tenantId);
        if (!CollectionUtils.isEmpty(agentOrderCheckVOS)) {
            if (StringUtils.isNotBlank(agentOrderCheckVOS.get(0).getStoreCheckFailMsg())) {
                // 门店校验错误
                itemList.forEach(e -> e.setErrorMsg(agentOrderCheckVOS.get(0).getStoreCheckFailMsg()));

            } else if (!CollectionUtils.isEmpty(agentOrderCheckVOS.get(0).getItemCheckResultList())) {
                Map<Long, String> itemFailMsgMap = agentOrderCheckVOS.get(0).getItemCheckResultList().stream().collect(Collectors.toMap(AgentOrderCheckVO.ItemCheckResult::getItemId, AgentOrderCheckVO.ItemCheckResult::getFailMsg, (v1, v2) -> v1));
                // 商品校验错误
                itemList.forEach(e -> {
                    if (itemFailMsgMap.containsKey(e.getItemId())) {
                        e.setErrorMsg(itemFailMsgMap.get(e.getItemId()));
                    } else {
                        e.setErrorMsg(String.format("门店ID:%s存在其他商品校验错误", e.getStoreId()));
                    }
                });

            } else {
                itemList.forEach(e -> e.setErrorMsg("校验异常"));
            }

            return false;
        }

        return true;
    }

    @Override
    protected File generateErrDataExcelFile(List<AgentOrderExcelDTO> errorDataList) {
        InputStream templateFileInputStream = ExcelUtils.getExcelFileInputStream(this.getClass(), ExcelTypeEnum.IMPORT_AGENT_ORDER_ERROR.getName());
        String filePath = ExcelUtils.tempExcelFilePath();
        ExcelWriter excelWriter = EasyExcel.write(filePath).registerConverter(new EasyExcelLocalDateConverter()).registerConverter(new LocalDateTimeConverter()).withTemplate(templateFileInputStream).build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();

        // 先写入storeId为空的记录
        List<AgentOrderExcelDTO> storeIdNullList = errorDataList.stream().filter(e -> e.getStoreId() == null).collect(Collectors.toList());
        excelWriter.fill(storeIdNullList, fillConfig, writeSheet);

        Map<Long, List<AgentOrderExcelDTO>> agentOrderMap = errorDataList.stream()
                .filter(e -> e.getStoreId() != null) // 过滤storeId不为空
                .collect(Collectors.groupingBy(
                        AgentOrderExcelDTO::getStoreId, // 按 storeId 分组
                        LinkedHashMap::new, // 保持顺序
                        Collectors.toList() // 收集为 List
                ));

        for (Map.Entry<Long, List<AgentOrderExcelDTO>> entry : agentOrderMap.entrySet()) {
            excelWriter.fill(entry.getValue(), fillConfig, writeSheet);
        }

        excelWriter.finish();
        File file = new File(filePath);
        return file;
    }
}
