package com.cosfo.manage.downloadcenter.agentorder;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import net.xianmu.download.support.dto.ImportExcelBaseDTO;

import javax.validation.constraints.NotNull;

/**
 * @author: xiaowk
 * @time: 2025/1/20 下午5:53
 */
@Data
public class AgentOrderExcelDTO extends ImportExcelBaseDTO {

    /**
     * 门店id
     */
    @ExcelProperty("门店ID\n" +
            "1.下单后根据商品编码和下单数量等自动计算订单的商品总价和运费；\n" +
            "2.仅适用于有账期的门店，下单后自动扣减门店账期，请填写门店-门店管理列表中的门店ID；")
    @NotNull(message = "门店ID不能为空")
    private Long storeId;
    /**
     * 商品id
     */
    @ExcelProperty("商品编码\n" +
            "请填写商品-商品列表中的商品编码")
    @NotNull(message = "商品编码不能为空")
    private Long itemId;
    /**
     * 商品数量
     */
    @ExcelProperty("下单数量\n" +
            "需符合下单规则(起订量，倍数订货，下单上限)")
    @NotNull(message = "下单数量不能为空")
    private Integer itemAmount;



}
