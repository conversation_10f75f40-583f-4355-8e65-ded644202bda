package com.cosfo.manage.downloadcenter.presaleorder;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @author: xiaowk
 * @time: 2024/7/9 下午6:34
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PresaleOrderSetDeliveryDateImportDTO extends PresaleOrderSetDeliveryDateExportDTO {

    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @NotEmpty(message = "配送日期不能为空")
    private String deliveryDate;
}
