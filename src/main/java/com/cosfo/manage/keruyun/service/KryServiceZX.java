package com.cosfo.manage.keruyun.service;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.keruyun.dto.*;
import com.cosfo.manage.keruyun.facade.AuthType;
import com.cosfo.manage.keruyun.facade.KryAPI;
import com.cosfo.manage.keruyun.facade.KryCallFacade;
import com.cosfo.manage.keruyun.facade.KryCallZXFacade;
import com.cosfo.manage.keruyun.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class KryServiceZX {

    @Autowired
    private KryCallZXFacade kryCallService;
    public KryZXOrderDetailDataVO orderDetail(Long shopId, String orderId) {
        if(StringUtils.isBlank (orderId)){
            return null;
        }
        String token = kryCallService.getToken(shopId);
        if(StringUtils.isBlank (token)){
            return null;
        }
        OrderDetailZXQueryDTO queryDTO = new OrderDetailZXQueryDTO ();
        queryDTO.setOrderId(orderId);
        log.info ("请求pos订单详情，shopid={},orderId={}", shopId, orderId);
        String response = kryCallService.postCall (KryAPI.ORDER_DETAIL_ZX, AuthType.SHOP, shopId, token, queryDTO);
        log.info ("请求pos订单详情，shopid={},orderIds={},reponse={}", shopId, orderId,response);
        if(StringUtils.isBlank (response)){
            return null;
        }
        KryResponse kryResponse = JSON.parseObject (response, KryResponse.class);
        if(kryResponse.getCode () == 0){
            KryZXResponseVO responseVO = JSON.parseObject (JSON.toJSONString (kryResponse.getResult ()), KryZXResponseVO.class);
            if(responseVO.isSuccess ()){
                KryZXOrderDetailDataVO dataVO = JSON.parseObject (JSON.toJSONString (responseVO.getData ()), KryZXOrderDetailDataVO.class);
                dataVO.setDetailInfo (JSON.toJSONString (responseVO.getData ()));
                return dataVO;
            }else {
                return null;
            }
        }else{
            log.error ("请求pos订单详情失败，shopid={},orderIds={},reponse={}", shopId, orderId,response);
            return null;
        }
    }

    public KryZXPageDataVO orderList(Long shopId, String startTime, String endTime, Integer pageNo,Integer type) {
        String token = kryCallService.getToken(shopId);
        if(StringUtils.isBlank (token)){
            return null;
        }
        OrderListZXQueryDTO queryDTO;
        if(type==1){
            queryDTO = new OrderListZXForHereQueryDTO ();
        }else{
            queryDTO = new OrderListZXOtherQueryDTO ();
        }

        queryDTO.setStartDate(startTime);
        queryDTO.setEndDate(endTime);
        PageBean pageBean = new PageBean ();
        pageBean.setPageNum (String.valueOf (pageNo));
        queryDTO.setPageBean (pageBean);

        log.info ("请求第{}页pos订单，shopid={},startTime={},endTime={}", pageNo, shopId, startTime, endTime);
        String response = kryCallService.postCall(KryAPI.ORDER_LIST_ZX, AuthType.SHOP, shopId, token, queryDTO);
        if(StringUtils.isBlank (response)){
            return null;
        }
        log.info ("请求第{}页pos订单，shopid={},startTime={},endTime={},response={}", pageNo, shopId, startTime, endTime,response);
        KryResponse kryResponse = JSON.parseObject (response, KryResponse.class);
        if(kryResponse.getCode () == 0){
            KryZXResponseVO responseVO = JSON.parseObject (JSON.toJSONString (kryResponse.getResult ()), KryZXResponseVO.class);
            if(responseVO.isSuccess ()){
                return JSON.parseObject (JSON.toJSONString (responseVO.getData ()), KryZXPageDataVO.class);
            }else {
                return null;
            }
        }else{
            log.error ("请求第{}页pos订单失败，shopid={},startTime={},endTime={},response={}", pageNo, shopId, startTime, endTime,response);
            return null;
        }
    }
}
