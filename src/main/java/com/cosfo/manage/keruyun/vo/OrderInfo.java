/**
  * Copyright 2023 bejson.com 
  */
package com.cosfo.manage.keruyun.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * Auto-generated: 2023-11-29 18:57:11
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
@Data
public class OrderInfo {

    //商品总额
    private BigDecimal dishTotalAmount;
    private BigDecimal extrachargeTotal;
    private BigDecimal privilegesTotal;
    private BigDecimal overFlowAmt;
    private BigDecimal molinAmount;
    private BigDecimal tradeOverFlow;
    private BigDecimal custShouldPay;
    private BigDecimal custActualPay;
    private BigDecimal originAmount;

}