/**
  * Copyright 2023 bejson.com 
  */
package com.cosfo.manage.keruyun.vo;

/**
 * Auto-generated: 2023-11-29 18:57:11
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
public class PayInfos {

    private long paymentItemId;
    private String payTranNo;
    private int payModelId;
    private String payModeName;
    private int custActualPay;
    private int shopActualAmount;
    private int payType;
    private int paySource;
    private int payStatus;
    private long payStartTime;
    private int payChannel;
    private String cashierBusinessNo;
    public void setPaymentItemId(long paymentItemId) {
         this.paymentItemId = paymentItemId;
     }
     public long getPaymentItemId() {
         return paymentItemId;
     }

    public void setPayTranNo(String payTranNo) {
         this.payTranNo = payTranNo;
     }
     public String getPayTranNo() {
         return payTranNo;
     }

    public void setPayModelId(int payModelId) {
         this.payModelId = payModelId;
     }
     public int getPayModelId() {
         return payModelId;
     }

    public void setPayModeName(String payModeName) {
         this.payModeName = payModeName;
     }
     public String getPayModeName() {
         return payModeName;
     }

    public void setCustActualPay(int custActualPay) {
         this.custActualPay = custActualPay;
     }
     public int getCustActualPay() {
         return custActualPay;
     }

    public void setShopActualAmount(int shopActualAmount) {
         this.shopActualAmount = shopActualAmount;
     }
     public int getShopActualAmount() {
         return shopActualAmount;
     }

    public void setPayType(int payType) {
         this.payType = payType;
     }
     public int getPayType() {
         return payType;
     }

    public void setPaySource(int paySource) {
         this.paySource = paySource;
     }
     public int getPaySource() {
         return paySource;
     }

    public void setPayStatus(int payStatus) {
         this.payStatus = payStatus;
     }
     public int getPayStatus() {
         return payStatus;
     }

    public void setPayStartTime(long payStartTime) {
         this.payStartTime = payStartTime;
     }
     public long getPayStartTime() {
         return payStartTime;
     }

    public void setPayChannel(int payChannel) {
         this.payChannel = payChannel;
     }
     public int getPayChannel() {
         return payChannel;
     }

    public void setCashierBusinessNo(String cashierBusinessNo) {
         this.cashierBusinessNo = cashierBusinessNo;
     }
     public String getCashierBusinessNo() {
         return cashierBusinessNo;
     }

}