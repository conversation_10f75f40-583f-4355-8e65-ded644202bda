package com.cosfo.manage.keruyun.facade;

import lombok.Getter;

/**
 * <AUTHOR>
 */

public enum KryAPI {
    GET_SHOP_TOKEN( "获取门店token", "/open/v1/token/get"),
    ORDER_DETAIL_ONPOS("订单详情", "/open/v1/data/order/exportDetail"),
    ORDER_LIST_ONPOS ("订单列表", "/open/v1/data/order/export2"),


    ORDER_DETAIL_ZX ("订单详情", "/open/standard/order/queryDetail"),
    ORDER_LIST_ZX ("订单列表", "/open/standard/order/queryList"),
    ;
    private String description;
    @Getter
    private String uri;

    KryAPI(String description, String uri) {
        this.description = description;
        this.uri = uri;
    }
}
