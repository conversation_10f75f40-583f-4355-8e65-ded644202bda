package com.cosfo.manage.keruyun.convert;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.cofso.item.client.req.MarketItemPriceInput;
import com.cofso.item.client.resp.MarketItemInfoResp;
import com.cosfo.manage.keruyun.vo.*;
import com.cosfo.manage.market.model.vo.MarketAreaItemMappingVO;
import com.cosfo.manage.pos.model.po.PosOrder;
import com.cosfo.manage.pos.model.po.PosOrderItem;
import com.cosfo.manage.pos.model.po.PosOrderPrivilege;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Mapper
public interface KryOrderConvert {

    KryOrderConvert INSTANCE = Mappers.getMapper(KryOrderConvert.class);


//    List<PosOrderItem> kryOrderDetailVO2PosOrderItems(KryOrderDetailVO e);
    @Mapping(target = "outStoreCode", expression = "java(String.valueOf(baseInfo.getShopId()))")
    @Mapping(target = "outStoreName", source = "baseInfo.shopName")
    @Mapping(target = "outMenuCode", source = "dishInfo.dishCode")
    @Mapping(target = "outMenuName", source = "dishInfo.dishName")
    @Mapping(target = "quantity", source = "dishInfo.quantity")
    @Mapping(target = "totalPrice", expression = "java(com.cosfo.manage.keruyun.convert.KryOrderConvert.fen2Yuan(dishInfo.getAmount()))")
    @Mapping(target = "channelType",source = "baseInfo.channelType", defaultValue = "2")
    @Mapping(target = "merchantStoreCode",source = "ftStoreCode")
    @Mapping(target = "tenantId",source = "tenantId")
    @Mapping(target = "orderNo",source = "baseInfo.tradeNo")
    @Mapping(target = "remarks",source = "baseInfo.tradeMemo")
    @Mapping(target = "availableDate",expression = "java(com.cosfo.manage.keruyun.convert.KryOrderConvert.convertTimestampToDate(baseInfo.getCreateTime()))")
    @Mapping(target = "createTime",expression = "java(java.time.LocalDateTime.now())")
    PosOrderItem kryOrderDetailVO2PosOrderItem(BaseOrderInfo baseInfo,DishInfo dishInfo,String ftStoreCode, Long tenantId);

    List<PosOrderPrivilege> kryPrivilegeInfos2PosOrderPrivileges(List<PrivilegeInfo> privilegeInfos);

    @Mapping(target = "privilegeAmount", expression = "java(com.cosfo.manage.keruyun.convert.KryOrderConvert.fen2Yuan2Privilege(privilegeInfo.getPrivilegeAmount()))")
    PosOrderPrivilege kryPrivilegeInfos2PosOrderPrivilege(PrivilegeInfo privilegeInfo);
    static LocalDate convertTimestampToDate(long timestamp) {
        return LocalDateTimeUtil.of (Instant.ofEpochMilli(timestamp * 1000), ZoneId.systemDefault ()).toLocalDate ();
    }
    static BigDecimal fen2Yuan(BigDecimal fen) {
        return fen.divide (new BigDecimal (100));
    }
    static BigDecimal fen2Yuan2Privilege(BigDecimal fen) {
        return fen.divide (new BigDecimal (100)).multiply (new BigDecimal (-1));
    }

    @Mapping(target = "outStoreName", source = "baseInfo.shopName")
    @Mapping(target = "outStoreCode", expression = "java(String.valueOf(baseInfo.getShopId()))")
    @Mapping(target = "merchantStoreCode",source = "ftStoreCode")
    @Mapping(target = "tenantId",source = "tenantId")
    @Mapping(target = "channelType",source = "baseInfo.channelType", defaultValue = "2")
    @Mapping(target = "orderNo",source = "baseInfo.orderId")
//    @Mapping(target = "totalPrice",source = "baseInfo.orderReceivedAmt")
//    @Mapping(target = "discountPrice",source = "baseInfo.promoAmt")
    @Mapping(target = "remarks",source = "baseInfo.subject")
    @Mapping(target = "createTime",expression = "java(java.time.LocalDateTime.now())")
    @Mapping(target = "availableDate",expression = "java(com.cosfo.manage.common.util.LocalDateTimeUtil.stringToDateSimple1(baseInfo.getOpenTime()))")
    @Mapping(target = "id", ignore = true)
    PosOrder kryOrderBaseVO2PosOrder(OrderBaseVO baseInfo, String ftStoreCode, Long tenantId);



    @Mapping(target = "outStoreName", source = "baseInfo.shopName")
    @Mapping(target = "outStoreCode", expression = "java(String.valueOf(baseInfo.getShopId()))")
    @Mapping(target = "merchantStoreCode",source = "ftStoreCode")
    @Mapping(target = "tenantId",source = "tenantId")
    @Mapping(target = "channelType",source = "baseInfo.channelType", defaultValue = "2")
    @Mapping(target = "orderNo",source = "baseInfo.orderId")
    @Mapping(target = "outMenuCode", source = "dishInfo.itemSkuId")
    @Mapping(target = "outMenuName", source = "dishInfo.itemName")
    @Mapping(target = "outMenuSpecification", source = "dishInfo.specName")
    @Mapping(target = "quantity",expression = "java(new java.math.BigDecimal (dishInfo.getQuantity()).intValue ())")
    @Mapping(target = "remarks",source = "dishInfo.itemSubject")
    @Mapping(target = "createTime",expression = "java(java.time.LocalDateTime.now())")
    @Mapping(target = "availableDate",expression = "java(com.cosfo.manage.common.util.LocalDateTimeUtil.stringToDateSimple1(baseInfo.getOpenTime()))")
    @Mapping(target = "id", ignore = true)
    PosOrderItem kryOrderItemVoList2PosOrderItem(OrderBaseVO baseInfo, OrderItemVoList dishInfo, String ftStoreCode, Long tenantId);
}