package com.cosfo.manage.marketing.service;

import com.cosfo.manage.marketing.model.dto.ItemSaleLimitConfigDTO;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ItemSaleLimitConfigService {

    /**
     * 查询租户下商品的限购配置
     * @param tenantId
     * @param itemIdList
     * @return
     */
    Map<Long, ItemSaleLimitConfigDTO> queryItemSaleLimitConfigMap(Long tenantId, Collection<Long> itemIdList);

    /**
     * 查询租户下商品的限购配置
     * @param tenantId
     * @param itemId
     * @return
     */
    ItemSaleLimitConfigDTO queryItemSaleLimitConfig(Long tenantId, Long itemId);

    /**
     * 保存商品限购配置
     * @param itemSaleLimitConfigDTO
     * @return
     */
    boolean saveItemSaleLimitConfig(ItemSaleLimitConfigDTO itemSaleLimitConfigDTO);
}
