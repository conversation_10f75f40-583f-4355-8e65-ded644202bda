package com.cosfo.manage.marketing.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 商品限购配置
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-25
 */
@Getter
@Setter
@TableName("item_sale_limit_config")
public class ItemSaleLimitConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * market_item主键
     */
    @TableField("market_item_id")
    private Long marketItemId;

    /**
     * 限制数量
     */
    @TableField("sale_limit_quantity")
    private Integer saleLimitQuantity;

    /**
     * 0不限制,1每次,2每自然日，3每自然周，4每自然月
     */
    @TableField("sale_limit_rule")
    private Integer saleLimitRule;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}
