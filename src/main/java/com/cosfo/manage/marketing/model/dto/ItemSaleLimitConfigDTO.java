package com.cosfo.manage.marketing.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ItemSaleLimitConfigDTO implements Serializable {

    private Long id;

    private Long tenantId;

    private Long marketItemId;

    /**
     * 限制数量
     */
    private Integer saleLimitQuantity;

    /**
     * 0不限制,1每次,2每自然日，3每自然周，4每自然月
     */
    private Integer saleLimitRule;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    public static ItemSaleLimitConfigDTO DEFAULT() {
        ItemSaleLimitConfigDTO itemSaleLimitConfigDTO = new ItemSaleLimitConfigDTO();
        itemSaleLimitConfigDTO.setSaleLimitQuantity(0);
        itemSaleLimitConfigDTO.setSaleLimitRule(0);
        return itemSaleLimitConfigDTO;
    }
}
