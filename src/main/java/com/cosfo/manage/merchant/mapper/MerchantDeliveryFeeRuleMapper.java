package com.cosfo.manage.merchant.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.cosfo.manage.merchant.model.po.MerchantDeliveryFeeRule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 门店运费规则 mapper
 * <AUTHOR>
 */
@Mapper
public interface MerchantDeliveryFeeRuleMapper {

    /**
     * 删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(MerchantDeliveryFeeRule record);

    /**
     *
     * 插入
     * @param record
     * @return
     */
    int insertSelective(MerchantDeliveryFeeRule record);

    /**
     * 根据主键查询
     * @param id
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    MerchantDeliveryFeeRule selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(MerchantDeliveryFeeRule record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(MerchantDeliveryFeeRule record);

    /**
     * 查询运费规则
     * @param tenantId
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<MerchantDeliveryFeeRule> listAll(Long tenantId);


    /**
     * 按类型查询规则,并按优先级升序排序
     * @param tenantId
     * @param type
     * @return
     */
    List<MerchantDeliveryFeeRule> listSortRuleByType(@Param("tenantId") Long tenantId, @Param("type") Integer type);

    /**
     * 批量删除
     * @param ids
     * @param tenantId
     * @return
     */
    int deleteByIds(@Param("ids") List<Long> ids, @Param("tenantId") Long tenantId);

    /**
     * 批量插入
     * @param ruleDTOList
     * @return
     */
    int batchInsert(List<MerchantDeliveryFeeRule> ruleDTOList);

    /**
     * 按租户查询规则，租户为空查询所有规则
     * @param tenantIds
     * @return
     */
    List<MerchantDeliveryFeeRule> listRuleByTenant(@Param("tenantIds") List<Long> tenantIds);


    int deleteSpecialRule(@Param("tenantId") Long tenantId, @Param("type") Integer type, @Param("notDelIds") List<Long> notDelIds);

    /**
     * 查询例外运费规则
     * @param tenantId
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<MerchantDeliveryFeeRule> listSpecialWithAddMarket(Long tenantId);

    /**
     * 批量更新
     * @param updateRules 需要更新的对象
     * @return
     */
    int updateWithItemAdd(@Param("updateRules") List<MerchantDeliveryFeeRule> updateRules);

}
