package com.cosfo.manage.merchant.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.cosfo.manage.merchant.model.po.Merchant;
import com.cosfo.manage.merchant.model.vo.TenantDetailVO;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Deprecated
@Repository
public interface MerchantMapper {

//    /**
//     * 删除
//     * @param id
//     * @return
//     */
//    int deleteByPrimaryKey(Long id);
//
//    /**
//     * 插入
//     * @param record
//     * @return
//     */
//    int insert(Merchant record);
//
//    /**
//     * 插入
//     * @param record
//     * @return
//     */
//    int insertSelective(Merchant record);
//
//    /**
//     * 查询
//     * @param id
//     * @return
//     */
//    @InterceptorIgnore(tenantLine = "on")
//    Merchant selectByPrimaryKey(Long id);
//
//    /**
//     * 更新
//     * @param record
//     * @return
//     */
//    int updateByPrimaryKeySelective(Merchant record);
//
//    /**
//     * 更新
//     * @param record
//     * @return
//     */
//    int updateByPrimaryKey(Merchant record);
//
//    /**
//     * 查询账号信息
//     *
//     * @param mId
//     * @param tenantId
//     * @return
//     */
//    @InterceptorIgnore(tenantLine = "on")
//    TenantDetailVO selectByMId(@Param("tenantId") Long tenantId);
//
//    /**
//     * 根据租户编号查询商户
//     *
//     * @param tenantId
//     * @return
//     */
//    @InterceptorIgnore(tenantLine = "on")
//    Merchant selectByTenantId(@Param("tenantId") Long tenantId);
}
