package com.cosfo.manage.merchant.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.cosfo.manage.merchant.model.dto.MerchantStoreAccountDTO;
import com.cosfo.manage.merchant.model.po.MerchantStoreAccount;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Deprecated
@Repository
public interface MerchantStoreAccountMapper {

//    /**
//     * 删除
//     * @param id
//     * @return
//     */
//    int deleteByPrimaryKey(Long id);
//
//    /**
//     * 插入
//     * @param record
//     * @return
//     */
//    int insert(MerchantStoreAccount record);
//
//    /**
//     * 插入
//     * @param record
//     * @return
//     */
//    int insertSelective(MerchantStoreAccount record);
//
//    /**
//     * 查询
//     * @param id
//     * @return
//     */
//    @InterceptorIgnore(tenantLine = "on")
//    MerchantStoreAccount selectByPrimaryKey(Long id);
//    List<MerchantStoreAccount> listByIds(List<Long> uids);
//
//    /**
//     * 更新
//     * @param record
//     * @return
//     */
//    int updateByPrimaryKeySelective(MerchantStoreAccount record);
//
//    /**
//     * 更新
//     * @param record
//     * @return
//     */
//    int updateByPrimaryKey(MerchantStoreAccount record);
//
//    /**
//     * 根据门店id查询
//     * @param tenantId
//     * @param storeId
//     * @param deleteFlag
//     * @return
//     */
//    @InterceptorIgnore(tenantLine = "on")
//    List<MerchantStoreAccountDTO> selectByStoreId(@Param("tenantId") Long tenantId, @Param("storeId") Long storeId, @Param("deleteFlag") Integer deleteFlag);
//
//    /**
//     * 模糊匹配手机号查询商户账号信息
//     *
//     * @param phone
//     * @return
//     */
//    @InterceptorIgnore(tenantLine = "on")
//    List<MerchantStoreAccount> queryMerchantStoreAccount(@Param("phone")String phone,@Param("tenantId") Long tenantId);
//
//    /**
//     * 查询
//     * @param query
//     * @return
//     */
//    @InterceptorIgnore(tenantLine = "on")
//    MerchantStoreAccount queryOne(MerchantStoreAccount query);
//
//    /**
//     * 查询店长信息
//     * @param storeId
//     * @return
//     */
//    MerchantStoreAccount queryManagerByStoreId(Long storeId);
//
//    /**
//     * 查询一个
//     * @param query
//     * @return
//     */
//    MerchantStoreAccount selectOne(MerchantStoreAccount query);
//
//    /**
//     * 批量更新状态
//     * @param accountIds
//     * @param operateStatus
//     */
//    void updateStatusBatchByIds(@Param("list") List<Long> accountIds, @Param("status") Integer operateStatus);

}
