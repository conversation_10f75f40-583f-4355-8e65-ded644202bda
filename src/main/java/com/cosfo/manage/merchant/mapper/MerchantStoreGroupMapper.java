package com.cosfo.manage.merchant.mapper;

import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 门店分组 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-14
 */
@Deprecated
@Mapper
public interface MerchantStoreGroupMapper {

//    /**
//     * 分页查询
//     *
//     * @param merchantStoreGroupQueryDto
//     * @return
//     */
//    List<MerchantStoreGroupVO> list(MerchantStoreGroupQueryDTO merchantStoreGroupQueryDto);
//
//    /**
//     * 根据门店id批量查询
//     * @param tenantId
//     * @param storeIds
//     * @return
//     */
//    List<MerchantStoreGroupInfoDTO> queryBatchByStoreIds(@Param("tenantId") Long tenantId, @Param("storeIds") List<Long> storeIds);

}
