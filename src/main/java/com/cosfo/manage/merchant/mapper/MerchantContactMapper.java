package com.cosfo.manage.merchant.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.cosfo.manage.merchant.model.dto.MerchantContactDTO;
import com.cosfo.manage.merchant.model.po.MerchantContact;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Deprecated
@Repository
public interface MerchantContactMapper {

//    /**
//     * 删除
//     * @param id
//     * @return
//     */
//    int deleteByPrimaryKey(Long id);
//
//    /**
//     * 插入
//     * @param record
//     * @return
//     */
//    int insert(MerchantContact record);
//
//    /**
//     * 插入
//     * @param record
//     * @return
//     */
//    int insertSelective(MerchantContact record);
//
//    /**
//     * 查询
//     * @param id
//     * @return
//     */
//    @InterceptorIgnore(tenantLine = "on")
//    MerchantContact selectByPrimaryKey(Long id);
//
//    /**
//     * 更新
//     * @param record
//     * @return
//     */
//    int updateByPrimaryKeySelective(MerchantContact record);
//
//    /**
//     * 更新
//     * @param record
//     * @return
//     */
//    int updateByPrimaryKey(MerchantContact record);
//
//    /**
//     * 根据租户id和门店id查询联系人
//     *
//     * @param tenantId
//     * @param storeId
//     * @return
//     */
//    @InterceptorIgnore(tenantLine = "on")
//    List<MerchantContactDTO> selectByStoreId(@Param("tenantId") Long tenantId, @Param("storeId") Long storeId);
}
