package com.cosfo.manage.merchant.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.cosfo.manage.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreQueryDTO;
import com.cosfo.manage.merchant.model.po.MerchantStore;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Deprecated
@Repository
public interface MerchantStoreMapper {

//    /**
//     * 删除
//     * @param id
//     * @return
//     */
//    int deleteByPrimaryKey(Long id);
//
//    /**
//     * todo 待确认
//     * 插入
//     * @param record
//     * @return
//     */
//    int insert(MerchantStore record);
//
//    /**
//     * 插入
//     * @param record
//     * @return
//     */
//    int insertSelective(MerchantStore record);
//
//    /**
//     * 查询
//     * @param id
//     * @return
//     */
//    @InterceptorIgnore(tenantLine = "on")
//    MerchantStore selectByPrimaryKey(Long id);
//
//    /**
//     * 更新
//     * @param record
//     * @return
//     */
//    int updateByPrimaryKeySelective(MerchantStore record);
//
//    /**
//     * 更新
//     * @param record
//     * @return
//     */
//    int updateByPrimaryKey(MerchantStore record);
//
//    /**
//     * 查询列表
//     * @param storeQueryDTO
//     * @return
//     */
//    List<MerchantStoreDTO> listAll(MerchantStoreQueryDTO storeQueryDTO);
//
//    /**
//     * 查询门店信息
//     *
//     * @param storeType
//     * @param storeName
//     * @return
//     */
//    @InterceptorIgnore(tenantLine = "on")
//    List<MerchantStore> queryMerchantStore(@Param("storeType") Integer storeType,
//                                           @Param("storeName")String storeName,
//                                           @Param("phone") String phone,
//                                           @Param("tenantId") Long tenantId);
//
//    /**
//     * 查询待审核门店个数
//     *
//     * @param tenantId
//     * @return
//     */
//    @InterceptorIgnore(tenantLine = "on")
//    Integer queryWaitAuditStoreNum(@Param("tenantId") Long tenantId);
//
//    /**
//     * 批量查询
//     *
//     * @param tenantId
//     * @param storeId
//     * @return
//     */
//    @InterceptorIgnore(tenantLine = "on")
//    List<MerchantStoreDTO> batchQuery(@Param("tenantId") Long tenantId, @Param("storeIds") List<Long> storeIds);
//
//    /**
//     * 批量查询
//     *
//     * @param tenantId
//     * @param storeIds
//     * @return
//     */
//    @InterceptorIgnore(tenantLine = "on")
//    List<MerchantStoreDTO> batchQueryByStoreIds(@Param("tenantId") Long tenantId, @Param("storeIds") List<Long> storeIds);
//
//    /**
//     * 查看租户维度下门店个数
//     * @param tenantId
//     * @return
//     */
//    @InterceptorIgnore(tenantLine = "on")
//    Long countStoreNum(@Param("tenantId") Long tenantId);
//
//
//    /**
//     * 查询所有门店
//     * @return
//     */
//    List<MerchantStore> selectAllByTenantId(@Param("tenantId") Long tenantId);
//
//    /**
//     * 根据门店名查询
//     * @param tenantId
//     * @param storeName
//     * @return
//     */
//    @InterceptorIgnore(tenantLine = "on")
//    MerchantStore selectByStoreName(@Param("tenantId") Long tenantId, @Param("storeName") String storeName);
//
//    /**
//     * 根据门店名查询
//     * @param tenantId
//     * @param storeName
//     * @return
//     */
//    @InterceptorIgnore(tenantLine = "on")
//    List<Long> selectIdListByParam(@Param("tenantId") Long tenantId, @Param("storeName") String storeName, @Param("storeNo") String storeNo, @Param("type") Integer type, @Param("groupId") Long groupId);
//
//    @InterceptorIgnore(tenantLine = "on")
//    List<MerchantStore> listStore(MerchantStoreQueryDTO storeQueryDTO);
//
//    /**
//     * 根据租户id查询出最大的门店编号
//     * @param tenantId
//     * @return
//     */
//    @InterceptorIgnore(tenantLine = "on")
//    Long selectMaxStoreNo(Long tenantId);
//
//    /**
//     * 根据条件查询
//     * @param merchantStoreQueryDTO
//     * @return
//     */
//    List<MerchantStoreDTO> selectList(MerchantStoreQueryDTO merchantStoreQueryDTO);
}
