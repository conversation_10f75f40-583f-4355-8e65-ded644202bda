package com.cosfo.manage.merchant.controller;

import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreAccountDTO;
import com.cosfo.manage.merchant.service.MerchantStoreAccountService;
import net.xianmu.common.result.CommonResult;
import net.xianmu.log.annation.BizLogRecord;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 账号管理
 *
 * <AUTHOR>
 * @date 2022/11/29 11:27
 */
@RestController
@RequestMapping("/merchant-store-account")
public class MerchantStoreAccountController extends BaseController {

    @Resource
    private MerchantStoreAccountService merchantStoreAccountService;

    /**
     * 删除账号
     *
     * @param id
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/delete-account", method = RequestMethod.POST)
    @BizLogRecord(operationName = "变更门店信息", bizKey = "#storeId", bizKeyTenantId = "#tenantId", content = "T(com.alibaba.fastjson.JSONObject).toJSONString(#content)")
    public CommonResult deleteAccount(@RequestParam Long id) {
        return merchantStoreAccountService.deleteAccount(id);
    }

    /**
     * 保存账号
     *
     * @param merchantStoreAccountDto
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/insert-account", method = RequestMethod.POST)
    public CommonResult insertAccount(@RequestBody MerchantStoreAccountDTO merchantStoreAccountDto) {
        LoginContextInfoDTO loginContextInfoDto = getMerchantInfoDTO();
        return merchantStoreAccountService.insertAccount(merchantStoreAccountDto, loginContextInfoDto);
    }
}
