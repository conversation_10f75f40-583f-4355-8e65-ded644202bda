package com.cosfo.manage.merchant.dao;

import com.cosfo.manage.merchant.model.po.MerchantOrderQuantityRule;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 起订量规则表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-20
 */
public interface MerchantOrderQuantityRuleDao extends IService<MerchantOrderQuantityRule> {

    /**
     * 删除其他规则
     * @param tenantId
     */
    void delOtherRule(Long tenantId);

    /**
     * 获取起订量规则
     * @param tenantId
     * @return
     */
    List<MerchantOrderQuantityRule> listOrderQuantityRule(Long tenantId);

    /**
     * 获取租户例外规则
     * @param tenantId
     * @return
     */
    List<MerchantOrderQuantityRule> listOrderQuantitySpecialRule(Long tenantId);

}
