package com.cosfo.manage.merchant.service.deliveryfeerule;

import com.cosfo.manage.common.context.MerchantDeliveryFeeRuleEnum;
import com.cosfo.manage.common.context.WarehouseTypeEnum;
import com.cosfo.manage.merchant.convert.MerchantDeliveryFeeRuleConvert;
import com.cosfo.manage.merchant.mapper.MerchantDeliveryFeeRuleMapper;
import com.cosfo.manage.merchant.model.dto.MerchantDeliveryFeeRuleEditDTO;
import com.cosfo.manage.merchant.model.po.MerchantDeliveryFeeRule;
import com.cosfo.manage.merchant.model.vo.MerchantDeliveryFeeRuleVO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ThreePartiesDeliveryFeeRuleStrategy implements DeliveryFeeRuleStrategy {
    @Resource
    private MerchantDeliveryFeeRuleMapper merchantDeliveryFeeRuleMapper;
    @Resource
    private CommonFeeRuleHandle commonFeeRuleHandle;


    @Override
    public Integer type() {
        return WarehouseTypeEnum.THREE_PARTIES.getCode();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveDeliveryFeeRule(MerchantDeliveryFeeRuleEditDTO feeRule) {
        List<MerchantDeliveryFeeRuleEditDTO> roleList;
        List<Long> allItemIds = null;
        // 处理跟随仓报价
        if (MerchantDeliveryFeeRuleEnum.MerchantDeliveryFeeRuleTypeEnum.FOLLOW_WAREHOUSE.getCode().equals(feeRule.getRuleType())) {
            roleList = Lists.newArrayList(feeRule);
        } else {
            // 每日运费
            roleList = commonFeeRuleHandle.unFoldRule(feeRule);
            allItemIds = commonFeeRuleHandle.queryAllItemIds(feeRule.getTenantId(), roleList);
        }
        commonFeeRuleHandle.removeSpecialRule(feeRule.getTenantId(), type(), roleList.stream().map(MerchantDeliveryFeeRuleEditDTO::getId).filter(Objects::nonNull).collect(Collectors.toList()));

        for (int i = 0; i < roleList.size(); i++) {
            commonFeeRuleHandle.handleCommonRule(roleList.get(i), allItemIds, i);
        }
        return true;
    }

    @Override
    public MerchantDeliveryFeeRuleVO getRule(Long tenantId) {
        MerchantDeliveryFeeRuleVO result = commonFeeRuleHandle.handleCommonRule(tenantId, type());
        return result;
    }
}
