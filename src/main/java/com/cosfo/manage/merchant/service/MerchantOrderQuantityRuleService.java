package com.cosfo.manage.merchant.service;

import com.cosfo.manage.merchant.model.dto.quantityrule.OrderQuantityRuleManageDTO;
import com.cosfo.manage.merchant.model.vo.OrderQuantityRuleManageVO;

/**
 * <AUTHOR>
 */
public interface MerchantOrderQuantityRuleService {

    /**
     * 查询品牌方商城起订量配置规则
     *
     * @param tenantId 品牌方
     * @return 规则列表
     */
    OrderQuantityRuleManageVO queryOrderQuantityRule(Long tenantId);

    /**
     * 修改起订量配置规则
     * @param manageDTO
     * @return
     */
    Boolean modifyOrderQuantityRule(OrderQuantityRuleManageDTO manageDTO, Long tenantId);

    /**
     * 更新规则商品变更
     * @return
     */
    boolean updateRuleWithMarketItemUpdate(Long tenantId, Long marketItem, Integer event);
}
