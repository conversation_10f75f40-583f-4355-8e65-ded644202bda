package com.cosfo.manage.merchant.service.impl;

import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.merchant.service.MerchantBizLogService;
import net.xianmu.log.annation.BizLogRecord;
import net.xianmu.log.config.BizLogRecordContext;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: monna.chen
 * @Date: 2024/1/11 16:52
 * @Description:
 */
@Service
public class MerchantBizLogServiceImpl implements MerchantBizLogService {


    @Override
    @BizLogRecord(operationName = "新增门店", bizKey = "#merchantStoreId", bizKeyTenantId = "#tenantId", content = "T(com.alibaba.fastjson.JSONObject).toJSONString(#content)")
    public void saveStoreLog(Long storeId, String storeName) {
        BizLogRecordContext.put("merchantStoreId", storeId);
        BizLogRecordContext.put("tenantId", UserLoginContextUtils.getTenantId());
        Map<String, Object> content = new HashMap<>();
        content.put("merchantStoreName", storeName);
        BizLogRecordContext.put("content", content);
    }
}
