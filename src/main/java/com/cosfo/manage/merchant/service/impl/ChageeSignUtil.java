package com.cosfo.manage.merchant.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.cosfo.manage.facade.openapi.dto.ChageeRequestDTO;
import com.cosfo.manage.merchant.model.dto.openapi.MerchantStoreDeliveryRuleDTO;
import net.xianmu.common.exception.BizException;
import org.apache.commons.codec.digest.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.TreeMap;

public class ChageeSignUtil {

    public static void main(String[] args) {
        String biz = "{\n" +
                "            \"rules\": [\n" +
                "                {\n" +
                "                    \"logisticsMethod\": 1,\n" +
                "                    \"expressDeliveryBeforeCloseTimeBuyDispatchDate\": \"2025-04-19 00:00:00\",\n" +
                "                    \"closeTime\": \"2025-04-18 16:00:00\",\n" +
                "                    \"expressDeliveryAfterCloseTimeBuyDispatchDate\": \"2025-04-20 00:00:00\",\n" +
                "                    \"storeName\": \"南京新接口旗舰店\",\n" +
                "                    \"storeCode\": \"zachtest1\"\n" +
                "                }\n" +
                "            ]\n" +
                "        }";
        MerchantStoreDeliveryRuleDTO merchantStoreDeliveryRuleDTO = JSONObject.parseObject(biz, MerchantStoreDeliveryRuleDTO.class);

        Object bizContent = JSONObject.parseObject("{\n" +
                "            \"rules\": [\n" +
                "                {\n" +
                "                    \"logisticsMethod\": 1,\n" +
                "                    \"expressDeliveryBeforeCloseTimeBuyDispatchDate\": \"2025-04-19 00:00:00\",\n" +
                "                    \"closeTime\": \"2025-04-18 16:00:00\",\n" +
                "                    \"expressDeliveryAfterCloseTimeBuyDispatchDate\": \"2025-04-20 00:00:00\",\n" +
                "                    \"storeName\": \"南京新接口旗舰店\",\n" +
                "                    \"storeCode\": \"zachtest1\"\n" +
                "                }\n" +
                "            ]\n" +
                "        }");

        String timestamp = "1744884487910";
        String appsecret = "qLibAeV11Z3KZYzoiqLgCyv5o7XH5mOXeo8dAho6GDE";
        String appKey = "05d1b467ece1ccfb821639ebfb480a6a";

        String signValue1 = ChageeSignUtil.sign(merchantStoreDeliveryRuleDTO, timestamp, appsecret);
        String signValue11 = ChageeSignUtil.sign(JSONObject.parseObject(JSON.toJSONString(merchantStoreDeliveryRuleDTO)), timestamp, appsecret);
        String signValue2 = ChageeSignUtil.sign(bizContent, timestamp, appsecret);

        System.out.println("signValue1 >>> " + signValue1);
        System.out.println("signValue11 >>> " + signValue11);
        System.out.println("signValue2 >>> " + signValue2);

        ChageeRequestDTO chageeRequestDTO = new ChageeRequestDTO(
                timestamp,
                appKey,
                ChageeSignUtil.sign(merchantStoreDeliveryRuleDTO, timestamp, appsecret),
                merchantStoreDeliveryRuleDTO);
        System.out.println(JSON.toJSONString(chageeRequestDTO));
    }

    public static String sign(Object bizContent, String timestamp, String appSecret) {
        JSONObject dataJsonObject = null;
        try {
            String dataJsonStr = JSON.toJSONString(bizContent);
            dataJsonObject = JSON.parseObject(dataJsonStr);
        } catch (Exception e) {
            throw new BizException("签名异常 bizContent >>> " + JSON.toJSONString(bizContent));
        }
        dataJsonObject.put("timestamp", timestamp);
        //验签
        return doSign(dataJsonObject, appSecret);
    }


    public static String doSign(Map map, String secret) {
        StringBuilder stringBuilder = new StringBuilder();
        // 将map放入 treeMap 进行升序排序
        TreeMap treeMap = sortMap(map);
        String treeMapStr = JSONObject.toJSONString(treeMap);
        stringBuilder.append(treeMapStr);
        // 将私钥加在排好序的 map 字符串后
        String orgStr = stringBuilder.append(secret).toString();
        String genSign = DigestUtils.md5Hex(orgStr.getBytes(StandardCharsets.UTF_8));
        // 打印日志
        return genSign;
    }

    /**
     * map排序
     *
     * @param map
     * @return
     */
    private static TreeMap sortMap(Map<String, Object> map) {
        TreeMap treeMap = new TreeMap();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (entry.getValue() instanceof Map) {
                TreeMap subTreeMap = sortMap((Map) entry.getValue());
                treeMap.put(entry.getKey(), subTreeMap);
            } else {
                treeMap.put(entry.getKey(), entry.getValue());
            }
        }
        return treeMap;
    }


}
