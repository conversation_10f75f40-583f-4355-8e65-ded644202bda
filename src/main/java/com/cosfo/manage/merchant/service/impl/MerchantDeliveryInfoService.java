package com.cosfo.manage.merchant.service.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.config.ChageeNacosConfig;
import com.cosfo.manage.facade.ofc.OfcDeliveryQueryFacade;
import com.cosfo.manage.facade.openapi.OpenPlatformFacade;
import com.cosfo.manage.facade.openapi.constants.ChageeOpenPlatformConstants;
import com.cosfo.manage.facade.openapi.dto.ChageeRequestDTO;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantAddressFacade;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreFacade;
import com.cosfo.manage.merchant.convert.MerchantStoreDeliveryInfoConverter;
import com.cosfo.manage.merchant.model.dto.openapi.MerchantStoreDeliveryRuleDTO;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.resp.DeliveryDateQueryResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStorePageQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantAddressResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStorePageResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/16 16:12
 * @Version 1.0
 */
@Slf4j
@Component
public class MerchantDeliveryInfoService {

    @Autowired
    private OfcDeliveryQueryFacade ofcDeliveryQueryFacade;

    @Autowired
    private UserCenterMerchantAddressFacade userCenterMerchantAddressFacade;

    @Autowired
    private UserCenterMerchantStoreFacade userCenterMerchantStoreFacade;

    @Autowired
    private OpenPlatformFacade openPlatformFacade;

    @Resource
    private ChageeNacosConfig chageeNacosConfig;


    /**
     * 【霸王茶姬专用】发送所有门店配送信息
     */
    public void sendChageeMerchantDeliveryInfoAll() {
        List<Long> chageeTenantIdList = chageeNacosConfig.getChageeTenantIdList();
        if (CollectionUtils.isEmpty(chageeTenantIdList)) {
            return;
        }
        for (Long tenantId : chageeTenantIdList) {
            this.sendMerchantDeliveryInfoAll(tenantId);
        }
    }


    /**
     * 发送所有门店配送信息
     *
     * @param tenantId 租户id
     */
    public void sendMerchantDeliveryInfoAll(Long tenantId) {
        MerchantStorePageQueryReq queryReq = new MerchantStorePageQueryReq();
        queryReq.setTenantId(tenantId);
        PageQueryReq pageQueryReq = new PageQueryReq();
        pageQueryReq.setPageIndex(1);
        pageQueryReq.setPageSize(1);
        PageInfo<MerchantStorePageResultResp> merchantStoreAndAddressPage =
                userCenterMerchantStoreFacade.getMerchantStorePage(queryReq, pageQueryReq);
        long total = merchantStoreAndAddressPage.getTotal();
        if (total <= 0) {
            return;
        }
        int pageSize = 100;
        long cycleTimes = total % pageSize == 0 ? total / pageSize : total / pageSize + 1;
        for (int i = 1; i <= cycleTimes; i++) {
            pageQueryReq.setPageIndex(i);
            pageQueryReq.setPageSize(pageSize);
            PageInfo<MerchantStorePageResultResp> pageResult =
                    userCenterMerchantStoreFacade.getMerchantStorePage(queryReq, pageQueryReq);
            List<MerchantStorePageResultResp> data = pageResult.getList();
            if (CollectionUtils.isEmpty(data)) {
                break;
            }
            for (MerchantStorePageResultResp merchantStorePageResultResp : data) {
                this.sendMerchantDeliveryInfo(merchantStorePageResultResp.getId());
            }
        }
    }


    /**
     * 发送门店配送规则信息
     *
     * @param storeId 门店id
     */
    public void sendMerchantDeliveryInfo(Long storeId) {
        try {
            // 1. 查询门店基础信息
            MerchantStoreResultResp merchantStore = userCenterMerchantStoreFacade.getMerchantStoreById(storeId);
            if (!chageeNacosConfig.judgeIsTenantOfChagee(merchantStore.getTenantId())) {
                return;
            }
            // 2. 查询门店地址信息
            MerchantAddressResultResp merchantAddress = this.getMerchantAddress(storeId);
            // 3. 查询配送周期信息
            DeliveryDateQueryResp deliveryDateQueryResp = null;
            try {
                deliveryDateQueryResp = ofcDeliveryQueryFacade.queryMerchantDeliveryDate(merchantAddress, LocalDateTime.now());
            } catch (Exception e) {
                log.warn("查询配送周期信息异常，deliveryDateQueryResp 已按照null处理");
            }
            // 4. 组装参数
            MerchantStoreDeliveryRuleDTO merchantStoreDeliveryRuleDTO = MerchantStoreDeliveryInfoConverter.convert(merchantStore, deliveryDateQueryResp);
            // 5. 调用open platform 接口
            String timestamp = System.currentTimeMillis() + "";
            String appKey = chageeNacosConfig.getAppKey();
            String appSecret = chageeNacosConfig.getAppSecret();
            ChageeRequestDTO chageeRequestDTO = new ChageeRequestDTO(
                    timestamp,
                    appKey,
                    ChageeSignUtil.sign(merchantStoreDeliveryRuleDTO, timestamp, appSecret),
                    merchantStoreDeliveryRuleDTO);
            openPlatformFacade.syncInvokeEventThrowBizEx(
                    storeId.toString(),
                    ChageeOpenPlatformConstants.CHAGEE_EVENT_MERCHANT_DELIVERY_INFO,
                    ChageeOpenPlatformConstants.CHAGEE_OPEN_PLATFORM_SPI_KEY,
                    JSON.toJSONString(chageeRequestDTO),
                    null,
                    null
            );
        } catch (Exception e) {
            log.error("发送门店配送规则失败 >>> {}", e.getMessage(), e);
        }
    }

    private MerchantAddressResultResp getMerchantAddress(Long storeId) {
        MerchantAddressQueryReq merchantAddressQueryReq = new MerchantAddressQueryReq();
        merchantAddressQueryReq.setStoreId(storeId);
        List<MerchantAddressResultResp> merchantAddressList = userCenterMerchantAddressFacade.getMerchantAddressList(merchantAddressQueryReq);
        if (CollectionUtils.isEmpty(merchantAddressList)) {
            throw new BizException("门店的地址信息为空");
        }
        return merchantAddressList.get(0);
    }
}
