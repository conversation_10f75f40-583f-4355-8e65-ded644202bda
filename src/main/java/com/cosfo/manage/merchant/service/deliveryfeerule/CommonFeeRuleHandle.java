package com.cosfo.manage.merchant.service.deliveryfeerule;

import com.cofso.item.client.provider.MarketItemProvider;
import com.cofso.item.client.req.MarketItemCommonQueryReq;
import com.cosfo.manage.common.context.DeleteFlagEnum;
import com.cosfo.manage.facade.MarketItemFacade;
import com.cosfo.manage.market.model.vo.MarketItemInfoPageVO;
import com.cosfo.manage.merchant.convert.MerchantDeliveryFeeRuleConvert;
import com.cosfo.manage.merchant.convert.MerchantDeliveryStepFeeConvert;
import com.cosfo.manage.merchant.mapper.MerchantDeliveryFeeRuleMapper;
import com.cosfo.manage.merchant.model.dto.MerchantDeliveryFeeRuleEditDTO;
import com.cosfo.manage.merchant.model.po.MerchantDeliveryFeeRule;
import com.cosfo.manage.merchant.model.po.MerchantDeliveryStepFee;
import com.cosfo.manage.merchant.model.vo.MerchantDeliveryFeeRuleVO;
import com.cosfo.manage.merchant.model.vo.MerchantDeliveryStepFeeVO;
import com.cosfo.manage.merchant.repository.MerchantDeliveryStepFeeRepository;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import net.xianmu.common.exception.BizException;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class CommonFeeRuleHandle {

    @DubboReference
    private MarketItemProvider marketItemProvider;
    @Resource
    private MerchantDeliveryFeeRuleMapper merchantDeliveryFeeRuleMapper;
    @Resource
    private MerchantDeliveryStepFeeRepository merchantDeliveryStepFeeRepository;
    @Resource
    private MarketItemFacade marketItemFacade;

    public List<MerchantDeliveryFeeRuleEditDTO> unFoldRule(MerchantDeliveryFeeRuleEditDTO feeRule) {
        List<MerchantDeliveryFeeRuleEditDTO> roleList = new ArrayList<>();
        List<MerchantDeliveryFeeRuleEditDTO> specialDeliveryFeeRule = feeRule.getSpecialDeliveryFeeRule();
        if (CollectionUtils.isEmpty(specialDeliveryFeeRule)) {
            roleList.add(feeRule);
            return roleList;
        }
        specialDeliveryFeeRule.forEach(rule -> {
            rule.setDefaultType(0);
            rule.setTenantId(feeRule.getTenantId());
            rule.setType(feeRule.getType());
            rule.setRuleType(feeRule.getRuleType());
            rule.setIncludeNewFlag(rule.getIncludeNewFlag());
            if (rule.getHitAreas() == null) {
                rule.setHitAreas(Lists.newArrayList());
            }
            if (rule.getHitItemIds() == null) {
                rule.setHitItemIds(Lists.newArrayList());
            }
            if(rule.getHitStoreIds() == null) {
                rule.setHitStoreIds(Lists.newArrayList());
            }
        });
        roleList.addAll(specialDeliveryFeeRule);
        roleList.add(feeRule);
        return roleList;
    }

    public List<Long> queryAllItemIds(Long tenantId, List<MerchantDeliveryFeeRuleEditDTO> ruleList) {
        List<Long> allItemIds = new ArrayList<>();
        Optional<MerchantDeliveryFeeRuleEditDTO> allItemSelected = ruleList.stream()
            // 选中所有商品，且不包含后续新增商品时，需要查询所有商品列表
            .filter(rule -> rule.getAllItemHit() != null && rule.getAllItemHit() && (null == rule.getIncludeNewFlag() || !rule.getIncludeNewFlag()))
            .findAny();
        if (allItemSelected.isPresent()) {
            // 获取所有item
            MarketItemCommonQueryReq req = new MarketItemCommonQueryReq ();
            req.setTenantId (tenantId);
            req.setPageNum (1);
            req.setPageSize(100);
            req.setCombineFlag(Boolean.FALSE);
            req.setDeleteFlag (DeleteFlagEnum.NORMAL.getFlag ());
            while (true) {
                PageInfo<MarketItemInfoPageVO> page = marketItemFacade.pageMarketItem (req);
                if (CollectionUtils.isEmpty(page.getList())) {
                    break;
                }
                allItemIds.addAll(page.getList().stream().map(MarketItemInfoPageVO::getId).collect(Collectors.toList()));
                req.setPageNum(page.getPageNum() + 1);
            }
            return allItemIds;

        }
        return null;
    }

    public MerchantDeliveryFeeRule handleCommonRule(MerchantDeliveryFeeRuleEditDTO ruleEditDTO, List<Long> allItemIds, int priority) {
        MerchantDeliveryFeeRule deliveryFeeRule = MerchantDeliveryFeeRuleConvert.INSTANCE.dtoToEntity(ruleEditDTO);
        deliveryFeeRule.setPriority(priority);

        if (ruleEditDTO.getAllItemHit() != null && ruleEditDTO.getAllItemHit() && (null == ruleEditDTO.getIncludeNewFlag() || !ruleEditDTO.getIncludeNewFlag())) {
            // 选中所有商品，且不包含后续新增商品时，命中当前已存在商品
            deliveryFeeRule.setHitItemIds(allItemIds);
        } else if (ruleEditDTO.getAllItemHit() != null && ruleEditDTO.getAllItemHit() && ruleEditDTO.getIncludeNewFlag() && ruleEditDTO.getIncludeNewFlag()){
            // 选中所有商品，且包含后续新增商品时，清空命中商品列表
            deliveryFeeRule.setHitItemIds(Collections.emptyList());
        }
        if (deliveryFeeRule.getId() != null) {
            merchantDeliveryFeeRuleMapper.updateByPrimaryKeySelective(deliveryFeeRule);
        } else {
            merchantDeliveryFeeRuleMapper.insertSelective(deliveryFeeRule);
        }
        // 更新阶梯价
        List<MerchantDeliveryStepFeeVO> stepFeeList = ruleEditDTO.getStepFeeList();
        // 阶梯价为空则初始化0
        if (CollectionUtils.isEmpty(stepFeeList)) {
            MerchantDeliveryStepFeeVO stepFeeVO = new MerchantDeliveryStepFeeVO();
            stepFeeVO.setDeliveryFee(BigDecimal.ZERO);
            stepFeeVO.setStepThreshold(BigDecimal.ZERO);
            stepFeeVO.setFeeRule(deliveryFeeRule.getFreeDeliveryType());
            stepFeeList = Lists.newArrayList(stepFeeVO);
        }
        List<MerchantDeliveryStepFee> stepFees = MerchantDeliveryStepFeeConvert.INSTANCE.voListToList(stepFeeList, deliveryFeeRule);
        handleStepFee(deliveryFeeRule.getTenantId(), deliveryFeeRule.getId(), stepFees);
        return deliveryFeeRule;
    }

    public boolean handleStepFee(Long tenantId, Long ruleId, List<MerchantDeliveryStepFee> stepFees) {
        merchantDeliveryStepFeeRepository.deleteStepFee(tenantId, ruleId);
        return merchantDeliveryStepFeeRepository.batchInsert(stepFees) > 0;
    }

    public MerchantDeliveryFeeRuleVO handleCommonRule(Long tenantId, Integer type) {
        List<MerchantDeliveryFeeRule> merchantDeliveryFeeRules = merchantDeliveryFeeRuleMapper.listSortRuleByType(tenantId, type);
        Map<Integer, List<MerchantDeliveryFeeRule>> typeFeeMap = merchantDeliveryFeeRules.stream().collect(Collectors.groupingBy(MerchantDeliveryFeeRule::getDefaultType));
        // 默认规则 默认规则枚举
        MerchantDeliveryFeeRule defaultFeeRule = new MerchantDeliveryFeeRule();
        if(!CollectionUtils.isEmpty(typeFeeMap.get(1))){
            defaultFeeRule = typeFeeMap.get(1).get(0);
        }else{
            // 没有默认规则，初始化一个
            defaultFeeRule.setTenantId(tenantId);
            defaultFeeRule.setType(type);
            defaultFeeRule.setRuleType(0);
            defaultFeeRule.setRelateNumber(new BigDecimal("0"));
            defaultFeeRule.setDefaultType(1);
            defaultFeeRule.setFreeDeliveryType(0);
            defaultFeeRule.setPriority(0);
            defaultFeeRule.setHitItemIds(Lists.newArrayList());
            defaultFeeRule.setHitAreas(Lists.newArrayList());
            defaultFeeRule.setIncludeNewFlag(false);
            defaultFeeRule.setMatchRegionType(0);
            defaultFeeRule.setHitStoreIds(Lists.newArrayList());
            defaultFeeRule.setIncludeAllStoreFlag(false);
            defaultFeeRule.setMatchItemType(0);
            defaultFeeRule.setFulfillmentType(0);
        }
        MerchantDeliveryFeeRuleVO defaultResult = convertRuleVo(defaultFeeRule);
        // 特例规则
        List<MerchantDeliveryFeeRule> specialFeeRuleList = typeFeeMap.get(0);
        if (CollectionUtils.isEmpty(specialFeeRuleList)) {
            return defaultResult;
        }
        List<MerchantDeliveryFeeRuleVO> specialRuleResult = specialFeeRuleList.stream().map(this::convertRuleVo).collect(Collectors.toList());
        defaultResult.setSpecialDeliveryFeeRule(specialRuleResult);
        return defaultResult;
    }


    private MerchantDeliveryFeeRuleVO convertRuleVo(MerchantDeliveryFeeRule feeRule) {
        MerchantDeliveryFeeRuleVO defaultResult = MerchantDeliveryFeeRuleConvert.INSTANCE.from(feeRule);
        // 增加是否包含所有商品
        if (defaultResult.getIncludeNewFlag()){
            defaultResult.setAllItemHit(true);
        }

        if(feeRule.getId() == null){
            return defaultResult;
        }

        // 获取阶梯价
        List<MerchantDeliveryStepFee> stepFees = merchantDeliveryStepFeeRepository.queryStepFee(feeRule.getTenantId(), feeRule.getId());
        if (CollectionUtils.isEmpty(stepFees)) {
            throw new BizException("运费模版功能升级中，请稍后再试！");
        }
        List<MerchantDeliveryStepFeeVO> stepFee = MerchantDeliveryStepFeeConvert.INSTANCE.from(stepFees);
        List<MerchantDeliveryStepFeeVO> sortedList = stepFee.stream().sorted(Comparator.comparing(MerchantDeliveryStepFeeVO::getStepThreshold)).collect(Collectors.toList());
        defaultResult.setStepFeeList(sortedList);
        return defaultResult;
    }

    public int removeSpecialRule(Long tenantId, Integer type, List<Long> notDelIds) {
        return merchantDeliveryFeeRuleMapper.deleteSpecialRule(tenantId, type, notDelIds);
    }
}
