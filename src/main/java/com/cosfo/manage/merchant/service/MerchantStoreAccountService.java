package com.cosfo.manage.merchant.service;

import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreAccountDTO;
import com.cosfo.manage.merchant.model.po.MerchantStoreAccount;
import com.cosfo.manage.merchant.model.vo.MerchantStoreAccountVO;
import net.xianmu.common.result.CommonResult;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/19
 */
public interface MerchantStoreAccountService {

    /**
     * 模糊匹配手机号查询商户账号信息
     *
     * @param phone
     * @return
     */
    List<MerchantStoreAccount> queryMerchantStoreAccount(String phone, Long tenantId);

    /**
     * 查询账户信息
     *
     * @param accountId
     * @return
     */
    MerchantStoreAccountVO queryAccountInfo(Long accountId);

    /**
     * 删除账号
     *
     * @param id
     * @return
     */
    CommonResult deleteAccount(Long id);

    /**
     * 创建账号
     *
     * @param merchantStoreAccountDto
     * @param loginContextInfoDto
     * @return
     */
    CommonResult insertAccount(MerchantStoreAccountDTO merchantStoreAccountDto, LoginContextInfoDTO loginContextInfoDto);

    /**
     * 清空账号用户缓存
     *
     * @param ids
     */
    void removeUserCache(List<Long> ids);

    /**
     * 根据门店id查询
     *
     * @param tenantId
     * @param storeId
     * @param deleteFlag
     * @return
     */
    List<MerchantStoreAccountDTO> selectByStoreId(Long tenantId, Long storeId, Integer deleteFlag);

    /**
     * 查询一个
     *
     * @param query
     * @return
     */
    MerchantStoreAccount selectOne(MerchantStoreAccount query);

    /**
     * 查询店长
     */
    MerchantStoreAccountDTO selectManager(Long tenantId, Long storeId);

    /**
     * 批量查询店长
     */
    Map<Long, MerchantStoreAccountDTO> selectManagerMap(Long tenantId, Collection<Long> storeIds);
}
