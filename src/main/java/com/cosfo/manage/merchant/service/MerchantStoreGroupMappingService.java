package com.cosfo.manage.merchant.service;

import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreLinkQueryDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreQueryDTO;
import com.cosfo.manage.merchant.model.po.MerchantStoreGroupMapping;
import com.cosfo.manage.merchant.model.vo.MerchantStoreLinkedVO;
import com.cosfo.manage.merchant.model.vo.MerchantStoreVO;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupResultResp;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/2/20 21:46
 */
public interface MerchantStoreGroupMappingService {

    /**
     * 根据分组Id查询
     *
     * @param groupIds
     * @param tenantId
     * @return
     */
    List<MerchantStoreGroupResultResp> selectByGroupId(List<Long> groupIds, Long tenantId);

    /** 分页查询所有默认分组关联门店信息
     *
     * @param merchantStoreQueryDTO
     * @param contextInfoDTO
     * @return
     */
    CommonResult<PageInfo<MerchantStoreDTO>> listAll(MerchantStoreQueryDTO merchantStoreQueryDTO, LoginContextInfoDTO contextInfoDTO);

    /**
     * 根据分组id查询该分组已关联门店信息
     * @param groupId
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult<List<MerchantStoreDTO>> listByTenantId(Long groupId, LoginContextInfoDTO loginContextInfoDTO);
}
