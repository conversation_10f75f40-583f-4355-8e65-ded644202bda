package com.cosfo.manage.merchant.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.manage.merchant.model.po.MerchantDeliveryRuleWarehouseRelation;
import com.cosfo.manage.merchant.repository.MerchantDeliveryRuleWarehouseRelationRepository;
import com.cosfo.manage.merchant.service.MerchantDeliveryRuleWarehouseRelationService;
import com.cosfo.manage.product.model.po.ProductAgentSkuMapping;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-04-14
 * @Description:
 */
@Service
public class MerchantDeliveryRuleWarehouseRelationServiceImpl implements MerchantDeliveryRuleWarehouseRelationService {

    @Resource
    private MerchantDeliveryRuleWarehouseRelationRepository merchantDeliveryRuleWarehouseRelationRepository;

    @Override
    public List<MerchantDeliveryRuleWarehouseRelation> queryByRuleIds(Long tenantId, List<Long> ruleIdList) {
        if (CollectionUtils.isEmpty(ruleIdList)) {
            return Lists.newArrayList();
        }

        LambdaQueryWrapper<MerchantDeliveryRuleWarehouseRelation> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(MerchantDeliveryRuleWarehouseRelation::getTenantId, tenantId);
        queryWrapper.in(MerchantDeliveryRuleWarehouseRelation::getRuleId, ruleIdList);
        return merchantDeliveryRuleWarehouseRelationRepository.list(queryWrapper);
    }

    @Override
    public boolean deleteByRuleIds(List<Long> deleteRuleIds, Long tenantId) {
        if (CollectionUtils.isEmpty(deleteRuleIds)) {
            return false;
        }
        return merchantDeliveryRuleWarehouseRelationRepository.deleteByRuleIds(deleteRuleIds, tenantId);
    }

    @Override
    public boolean batchInsert(Long ruleId, List<Integer> warehouseNoList, Long tenantId) {
        if (CollectionUtils.isEmpty(warehouseNoList)) {
            return false;
        }

        List<MerchantDeliveryRuleWarehouseRelation> lists = Lists.newArrayList();
        for (Integer warehouseNo : warehouseNoList) {
            MerchantDeliveryRuleWarehouseRelation merchantDeliveryRuleWarehouseRelation = new MerchantDeliveryRuleWarehouseRelation();
            merchantDeliveryRuleWarehouseRelation.setTenantId(tenantId);
            merchantDeliveryRuleWarehouseRelation.setWarehouseNo(warehouseNo);
            merchantDeliveryRuleWarehouseRelation.setRuleId(ruleId);
            lists.add(merchantDeliveryRuleWarehouseRelation);
        }
        return merchantDeliveryRuleWarehouseRelationRepository.saveBatch(lists);
    }

    @Override
    public List<MerchantDeliveryRuleWarehouseRelation> queryByTenantId(List<Long> tenantIds) {
        LambdaQueryWrapper<MerchantDeliveryRuleWarehouseRelation> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.in(MerchantDeliveryRuleWarehouseRelation::getTenantId, tenantIds);
        return merchantDeliveryRuleWarehouseRelationRepository.list(queryWrapper);
    }

    @Override
    public boolean deleteByParam(Long ruleId, List<Integer> dbNoList, Long tenantId) {
        if (CollectionUtils.isEmpty(dbNoList)) {
            return false;
        }
        return merchantDeliveryRuleWarehouseRelationRepository.deleteByParam(ruleId, dbNoList, tenantId);
    }
}
