package com.cosfo.manage.merchant.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.common.context.ExcelTypeEnum;
import com.cosfo.manage.common.context.FileDownloadTypeEnum;
import com.cosfo.manage.common.context.MerchantStoreGroupTypeEnum;
import com.cosfo.manage.common.exception.DefaultServiceException;
import com.cosfo.manage.common.executor.ExecutorFactory;
import com.cosfo.manage.common.model.dto.ExcelImportResDTO;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTOEnum;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.common.util.AssertCheckParams;
import com.cosfo.manage.common.util.ExcelUtils;
import com.cosfo.manage.common.util.PageInfoConverter;
import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreGroupFacade;
import com.cosfo.manage.file.service.FileDownloadRecordService;
import com.cosfo.manage.merchant.convert.MerchantConvertUtil;
import com.cosfo.manage.merchant.convert.MerchantStoreGroupMapperConvert;
import com.cosfo.manage.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreGroupDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreGroupInfoDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreGroupInputDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreGroupQueryDTO;
import com.cosfo.manage.merchant.model.po.MerchantStore;
import com.cosfo.manage.merchant.model.po.MerchantStoreGroup;
import com.cosfo.manage.merchant.model.vo.MerchantStoreGroupExportVO;
import com.cosfo.manage.merchant.model.vo.MerchantStoreGroupVO;
import com.cosfo.manage.merchant.service.MerchantStoreGroupMappingService;
import com.cosfo.manage.merchant.service.MerchantStoreGroupService;
import com.cosfo.manage.merchant.service.MerchantStoreService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreGroupBatchImportReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreGroupCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreGroupQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupBatchImportResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupPageResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupResultResp;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/11/14
 */
@Slf4j
@Service
public class MerchantStoreGroupServiceImpl implements MerchantStoreGroupService {

//    @Resource
//    private MerchantStoreGroupRepository merchantStoreGroupRepository;
//    @Resource
//    private MerchantStoreGroupMappingRepository merchantStoreGroupMappingRepository;
//    @Resource
//    private MerchantStoreMapper merchantStoreMapper;
//    @Resource
//    private MerchantAddressMapper merchantAddressMapper;
//    @Resource
//    private MerchantStoreGroupMapper merchantStoreGroupMapper;
//    @Resource
//    private MerchantStoreGroupMappingMapper merchantStoreGroupMappingMapper;
    
    @Resource
    private MerchantStoreGroupMappingService merchantStoreGroupMappingService;
    @Resource
    private CommonService commonService;
    @Resource
    private FileDownloadRecordService fileDownloadRecordService;
    @Resource
    private MerchantStoreService merchantStoreService;
    @Resource
    private UserCenterMerchantStoreGroupFacade userCenterMerchantStoreGroupFacade;



    @Override
    public CommonResult<PageInfo<MerchantStoreGroupVO>> list(MerchantStoreGroupQueryDTO merchantStoreGroupQueryDto, LoginContextInfoDTO loginContextInfoDTO) {
        merchantStoreGroupQueryDto.setTenantId(loginContextInfoDTO.getTenantId());
        PageHelper.startPage(merchantStoreGroupQueryDto.getPageIndex(), merchantStoreGroupQueryDto.getPageSize());
//        List<MerchantStoreGroupVO> list = merchantStoreGroupMapper.list(merchantStoreGroupQueryDto);
        PageInfo<MerchantStoreGroupVO> pageInfo = pageList(merchantStoreGroupQueryDto);

//        list.forEach(merchantStoreGroupVo -> {
//            merchantStoreGroupVo.setStoreNum(merchantStoreGroupMappingMapper.countStoreNumByGroupId(loginContextInfoDTO.getTenantId(), merchantStoreGroupVo.getId()));
//        });
        return CommonResult.ok(pageInfo);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public CommonResult add(MerchantStoreGroupDTO merchantStoreGroupDTO, LoginContextInfoDTO loginContextInfoDTO) {
        Long tenantId = loginContextInfoDTO.getTenantId();
//        MerchantStoreGroupResultResp merchantStoreGroupResultResp = getByName(tenantId, merchantStoreGroupDTO.getName());
//        AssertCheckBiz.isNull(merchantStoreGroupResultResp, ResultDTOEnum.MERCHANT_STORE_GROUP_NAME_EXISTING.getCode(), ResultDTOEnum.MERCHANT_STORE_GROUP_NAME_EXISTING.getMessage());
//        MerchantStoreGroup merchantStoreGroup = new MerchantStoreGroup();
//        merchantStoreGroup.setTenantId(tenantId);
//        merchantStoreGroup.setName(merchantStoreGroupDTO.getName());
//        merchantStoreGroupRepository.save(merchantStoreGroup);
//        List<MerchantStoreDTO> storeDTOList = new LinkedList<>();
//        if (!CollectionUtils.isEmpty(merchantStoreGroupDTO.getStoreId())){
////            storeDTOList = merchantStoreMapper.batchQueryByStoreIds(tenantId, merchantStoreGroupDTO.getStoreId());
//            storeDTOList = merchantStoreService.batchQuery(merchantStoreGroupDTO.getStoreId(), tenantId);
//        }
//        if (!CollectionUtils.isEmpty(storeDTOList)){
//            MerchantStoreGroup finalMerchantStoreGroup = merchantStoreGroup;
//            List<MerchantStoreGroupMapping> mappingList = storeDTOList.stream().map(item -> {
//                MerchantStoreGroupMapping merchantStoreGroupMapping = new MerchantStoreGroupMapping();
//                merchantStoreGroupMapping.setStoreId(item.getId());
//                merchantStoreGroupMapping.setGroupId(finalMerchantStoreGroup.getId());
//                merchantStoreGroupMapping.setTenantId(tenantId);
//                return merchantStoreGroupMapping;
//            }).collect(Collectors.toList());
//            // 从默认分组里删除门店
//            merchantStoreGroupMappingRepository.deleteByStoreIds(merchantStoreGroupDTO.getStoreId(), tenantId);
//            // 分到新门店
//            merchantStoreGroupMappingRepository.saveBatch(mappingList);
//        }

        MerchantStoreGroupCommandReq merchantStoreGroupCommandReq = new MerchantStoreGroupCommandReq();
        merchantStoreGroupCommandReq.setTenantId(tenantId);
        merchantStoreGroupCommandReq.setStoreIdList(merchantStoreGroupDTO.getStoreId());
        merchantStoreGroupCommandReq.setName(merchantStoreGroupDTO.getName());
        Long id = userCenterMerchantStoreGroupFacade.create(merchantStoreGroupCommandReq);
        if (NumberConstant.ZERO > id) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR);
        }
        return CommonResult.ok();
    }

    @Override
    public CommonResult update(MerchantStoreGroupDTO merchantStoreGroupDTO, LoginContextInfoDTO loginContextInfoDTO) {
//        MerchantStoreGroupResultResp merchantStoreGroupResultResp = getByName(loginContextInfoDTO.getTenantId(), merchantStoreGroupDTO.getName());
//        if (!Objects.isNull(merchantStoreGroupResultResp)) {
//            AssertCheckParams.isTrue(merchantStoreGroupResultResp.getMerchantStoreGroupId().equals(merchantStoreGroupDTO.getId()), ResultDTOEnum.MERCHANT_STORE_GROUP_NAME_EXISTING.getCode(), ResultDTOEnum.MERCHANT_STORE_GROUP_NAME_EXISTING.getMessage());
//        }
//
//        MerchantStoreGroup merchantStoreGroup = new MerchantStoreGroup();
//        merchantStoreGroup.setId(merchantStoreGroupDTO.getId());
//        merchantStoreGroup.setName(merchantStoreGroupDTO.getName());
//        merchantStoreGroupRepository.updateById(merchantStoreGroup);
//        return CommonResult.ok();
        return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,"网络拥堵,请稍后再试");
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public CommonResult bindStore(MerchantStoreGroupDTO merchantStoreGroupDTO, LoginContextInfoDTO loginContextInfoDTO) {
//        List<Long> storeIds = merchantStoreGroupDTO.getStoreId();
//        List<MerchantStoreGroupMapping> mappings = merchantStoreGroupMappingRepository.selectByGroupId(Arrays.asList(merchantStoreGroupDTO.getId()), loginContextInfoDTO.getTenantId());
//        if (CollectionUtils.isEmpty(mappings)) {
//            return CommonResult.ok();
//        }
//        MerchantStoreGroupResultResp defaultGroup = queryDefaultGroup(loginContextInfoDTO.getTenantId());
//        List<MerchantStoreGroupMapping> needUpdateMappings = mappings.stream().filter(item -> {
//            return !storeIds.contains(item.getStoreId());
//        }).map(el -> {
//            el.setGroupId(defaultGroup.getMerchantStoreGroupId());
//            return el;
//        }).collect(Collectors.toList());
//        // 更新
//        merchantStoreGroupMappingRepository.updateBatchById(needUpdateMappings);
//        return CommonResult.ok();
        return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,"网络拥堵,请稍后再试");
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public CommonResult input(MultipartFile file, LoginContextInfoDTO loginContextInfoDto) throws IOException {
        List<MerchantStoreGroupInputDTO> list = new ArrayList<> ();
        try{
              list = ExcelUtils.read(file.getInputStream(), MerchantStoreGroupInputDTO.class);
        }catch (Exception e){
            log.error ("importstoreggroup,读取失败，tenantid={}",loginContextInfoDto.getTenantId (),e);
            throw new DefaultServiceException ("表格读取失败，请检查数据格式后重新试试，或联系客服处理一下~");
        }
        if (list.size() > NumberConstant.FIVE_HUNDRED) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "单次导入的门店分组不能超过500条");
        }
        List<MerchantStoreGroupBatchImportReq> reqList = list.stream().map(dto -> {
            MerchantStoreGroupBatchImportReq merchantStoreGroupBatchImportReq = new MerchantStoreGroupBatchImportReq();
            merchantStoreGroupBatchImportReq.setName(dto.getName());
            merchantStoreGroupBatchImportReq.setStoreName(dto.getStoreName());
            return merchantStoreGroupBatchImportReq;
        }).collect(Collectors.toList());
        List<MerchantStoreGroupBatchImportResp> errorList = userCenterMerchantStoreGroupFacade.batchCreate(reqList, loginContextInfoDto.getTenantId());
//        userCenterMerchantStoreGroupFacade.XXXX(list);
//        List<MerchantStoreGroupInputDTO> errorList = new ArrayList<>(NumberConstant.TEN);
//        for (MerchantStoreGroupInputDTO merchantStoreGroupInputDto : list) {
//            if (StringUtils.isEmpty(merchantStoreGroupInputDto.getName())) {
//                merchantStoreGroupInputDto.setError(ResultDTOEnum.MERCHANT_STORE_GROUP_NAME_NOT＿NULL.getMessage());
//                errorList.add(merchantStoreGroupInputDto);
//                continue;
//            }
//
//            if (merchantStoreGroupInputDto.getName().length() > NumberConstant.TEN) {
//                merchantStoreGroupInputDto.setError(ResultDTOEnum.GROUP_NAME_TOO_LONG.getMessage());
//                errorList.add(merchantStoreGroupInputDto);
//                continue;
//            }
//
//            // 根据分组名称查询分组信息
//            MerchantStoreGroupResultResp merchantStoreGroupResultResp = getByName(loginContextInfoDto.getTenantId(), merchantStoreGroupInputDto.getName());
//            if (Objects.isNull(merchantStoreGroupResultResp)) {
//                MerchantStoreGroup merchantStoreGroup = new MerchantStoreGroup();
//                merchantStoreGroup.setTenantId(loginContextInfoDto.getTenantId());
//                merchantStoreGroup.setName(merchantStoreGroupInputDto.getName());
//                merchantStoreGroupRepository.save(merchantStoreGroup);
//            }
//
//            if (!StringUtils.isEmpty(merchantStoreGroupInputDto.getStoreName())) {
//                // 查询门店是否存在
//                MerchantStore merchantStore = merchantStoreService.selectByStoreName(loginContextInfoDto.getTenantId(), merchantStoreGroupInputDto.getStoreName());
//                if (Objects.isNull(merchantStore)) {
//                    merchantStoreGroupInputDto.setError(ResultDTOEnum.STORE_NOT_EXISTING.getMessage());
//                    errorList.add(merchantStoreGroupInputDto);
//                    continue;
//                }
//
//                // 查询门店是否已经绑定分组
//                MerchantStoreGroupMapping merchantStoreGroupMapping = merchantStoreGroupMappingRepository.selectByStoreId(merchantStore.getId(), loginContextInfoDto.getTenantId());
//                if (!Objects.isNull(merchantStoreGroupMapping)) {
//                    MerchantStoreGroupResultResp group = queryById(merchantStoreGroupMapping.getGroupId());
//                    // 如果是默认分组的门店
//                    if (MerchantStoreGroupTypeEnum.DEFAULT.getType().equals(group.getType())){
//                        merchantStoreGroupMapping.setGroupId(merchantStoreGroupResultResp.getMerchantStoreGroupId());
//                        merchantStoreGroupMappingRepository.updateById(merchantStoreGroupMapping);
//                    }else {
//                        String errorInfo = merchantStoreGroupMapping.getGroupId().equals(merchantStoreGroupResultResp.getMerchantStoreGroupId()) ? ResultDTOEnum.STORE_HAVING_EXISTING_IN_GROUP.getMessage() : ResultDTOEnum.STORE_HAVING_EXISTING_IN_OTHER_GROUP.getMessage();
//                        merchantStoreGroupInputDto.setError(errorInfo);
//                        errorList.add(merchantStoreGroupInputDto);
//                        continue;
//                    }
//                }
//            }
//        }
//
        ExcelImportResDTO excelImportResDTO = new ExcelImportResDTO();
        excelImportResDTO.setSuccessRow(list.size() - errorList.size());
        excelImportResDTO.setFailRow(errorList.size());
        // 上传错误文件到七牛云
        if (!CollectionUtils.isEmpty(errorList) && errorList.size() > 0) {
            // 导出数据
            String filePath = commonService.exportExcel(errorList, ExcelTypeEnum.MERCHANT_STORE_GROUP_INPUT_ERROR.getName());
            // 上传数据到七牛云
            File errorFile = new File(filePath.trim());
            commonService.uploadExcelFile(errorFile, filePath);
            excelImportResDTO.setErrorFilePath(Constants.FILE_DIR + errorFile.getName());
        }

        return CommonResult.ok(excelImportResDTO);
    }

    @Override
    public CommonResult export(MerchantStoreGroupQueryDTO merchantStoreGroupQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        Map<String, Object> paramsMap = new HashMap<>(NumberConstant.SIXTEEN);
        if (!Objects.isNull(merchantStoreGroupQueryDTO.getId())) {
            paramsMap.put("分组Id", merchantStoreGroupQueryDTO.getId());
        }

        if (!StringUtils.isEmpty(merchantStoreGroupQueryDTO.getName())) {
            paramsMap.put("分组名称", merchantStoreGroupQueryDTO.getName());
        }

        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
        recordDTO.setBizType(FileDownloadTypeEnum.MERCHANT_STORE_GROUP.getType());
        recordDTO.setTenantId(loginContextInfoDTO.getTenantId());
        recordDTO.setFileName(ExcelTypeEnum.MERCHANT_STORE_GROUP_EXPORT.getFileName());
        recordDTO.setParams(paramsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(paramsMap));
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO).asyncWriteWithOssResp(merchantStoreGroupQueryDTO, e -> writeDownloadCenter(e, loginContextInfoDTO.getTenantId(), recordDTO.getFileName()));

//        FileDownloadRecord fileDownloadRecord = new FileDownloadRecord();
//        fileDownloadRecord.setParams(JSON.toJSONString(paramsMap));
//        fileDownloadRecord.setStatus(FileDownloadStatusEnum.PROCESSING.getStatus());
//        fileDownloadRecord.setType(FileDownloadTypeEnum.MERCHANT_STORE_GROUP.getType());
//        fileDownloadRecord.setTenantId(loginContextInfoDTO.getTenantId());
//        fileDownloadRecordService.generateFileDownloadRecord(fileDownloadRecord);
//
//        ExecutorFactory.generateExcelExecutor.execute(() -> {
//            try {
//                exportMerchantStoreGroup(merchantStoreGroupQueryDTO, loginContextInfoDTO.getTenantId(), fileDownloadRecord.getId());
//            } catch (Exception e) {
//                fileDownloadRecordService.updateFailStatus(fileDownloadRecord.getId());
//                log.error("分组导出失败", e);
//            }
//        });
        return CommonResult.ok();
    }


    public DownloadCenterOssRespDTO writeDownloadCenter(MerchantStoreGroupQueryDTO merchantStoreGroupQueryDTO, Long tenantId, String fileName) {
        // 1、表格处理
        String filePath = exportMerchantStoreGroup(merchantStoreGroupQueryDTO, tenantId);

        // 2、文件上传至oss
        OssUploadResult uploadResult = null;
        try {
            uploadResult = OssUploadUtil.upload(fileName, FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
        } catch (IOException e) {
            log.error("filePath={}", filePath, e);
            throw new BizException("读取文件报错");
        } finally {
            commonService.deleteFile(filePath);
        }
        // 3、返回文件地址
        DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
        downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
        downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
        return downloadCenterOssRespDTO;
    }

    /**
     * 导出门店分组
     *
     * @param merchantStoreGroupQueryDTO
     * @param tenantId
     */
    private String exportMerchantStoreGroup(MerchantStoreGroupQueryDTO merchantStoreGroupQueryDTO, Long tenantId) {
        // 查询所有分组列表
//        List<MerchantStoreGroup> merchantStoreGroups = merchantStoreGroupRepository.list(merchantStoreGroupQueryDTO, tenantId);
        merchantStoreGroupQueryDTO.setTenantId(tenantId);
        List<MerchantStoreGroup> merchantStoreGroups = list(merchantStoreGroupQueryDTO);
        List<MerchantStoreGroupExportVO> list = new ArrayList<>(NumberConstant.TEN);
        if (!CollectionUtils.isEmpty(merchantStoreGroups)) {
            List<Long> groupIds = merchantStoreGroups.stream().map(MerchantStoreGroup::getId).collect(Collectors.toList());
            // 查询所有分组关联门店
            List<MerchantStoreGroupResultResp> merchantStoreGroupMappings = merchantStoreGroupMappingService.selectByGroupId(groupIds, tenantId);
            Map<Long, List<MerchantStoreGroupResultResp>> merchantStoreGroupMappingMap = new HashMap<>(NumberConstant.SIXTEEN);
            Map<Long, MerchantStoreDTO> merchantStoreDtoMap = new HashMap<>(NumberConstant.SIXTEEN);
            if (!CollectionUtils.isEmpty(merchantStoreGroupMappings)) {
                List<Long> storeIds = merchantStoreGroupMappings.stream().map(MerchantStoreGroupResultResp::getStoreId).collect(Collectors.toList());
                merchantStoreGroupMappingMap = merchantStoreGroupMappings.stream().collect(Collectors.groupingBy(MerchantStoreGroupResultResp::getMerchantStoreGroupId));
                List<MerchantStoreDTO> merchantStoreDtos = merchantStoreService.batchQuery(storeIds, tenantId);
                merchantStoreDtoMap = merchantStoreDtos.stream().collect(Collectors.toMap(MerchantStoreDTO::getId, item -> item));
            }

            Map<Long, List<MerchantStoreGroupResultResp>> finalMerchantStoreGroupMappingMap = merchantStoreGroupMappingMap;
            Map<Long, MerchantStoreDTO> finalMerchantStoreDtoMap = merchantStoreDtoMap;
            merchantStoreGroups.forEach(merchantStoreGroup -> {
                if (finalMerchantStoreGroupMappingMap.containsKey(merchantStoreGroup.getId())) {
                    List<MerchantStoreGroupResultResp> merchantStoreGroupMappingList = finalMerchantStoreGroupMappingMap.get(merchantStoreGroup.getId());
                    merchantStoreGroupMappingList.forEach(merchantStoreGroupMapping -> {
                        MerchantStoreGroupExportVO merchantStoreGroupExportVO = new MerchantStoreGroupExportVO();
                        BeanUtils.copyProperties(merchantStoreGroup, merchantStoreGroupExportVO);
                        MerchantStoreDTO merchantStoreDto = finalMerchantStoreDtoMap.get(merchantStoreGroupMapping.getStoreId());
                        merchantStoreGroupExportVO.setStoreId(merchantStoreGroupMapping.getStoreId());
                        if (Objects.nonNull(merchantStoreDto)) {
                            merchantStoreGroupExportVO.setStoreName(merchantStoreDto.getStoreName());
                        }
                        list.add(merchantStoreGroupExportVO);
                    });
                } else {
                    MerchantStoreGroupExportVO merchantStoreGroupExportVO = new MerchantStoreGroupExportVO();
                    BeanUtils.copyProperties(merchantStoreGroup, merchantStoreGroupExportVO);
                    list.add(merchantStoreGroupExportVO);
                }
            });
        }

        String filePath = commonService.exportExcel(list, ExcelTypeEnum.MERCHANT_STORE_GROUP_EXPORT.getName());
        return filePath;
        // 导出数据
//        commonService.generateAndUploadExcel(list, ExcelTypeEnum.MERCHANT_STORE_GROUP_EXPORT, fileDownloadRecordId);
    }

    @Override
    public CommonResult delete(MerchantStoreGroupDTO merchantStoreGroupDTO, LoginContextInfoDTO loginContextInfoDTO) {
        AssertCheckParams.notNull(merchantStoreGroupDTO.getId(), ResultDTOEnum.PARAMETER_MISSING.getCode(), ResultDTOEnum.PARAMETER_MISSING.getMessage());
        AssertCheckParams.notNull(merchantStoreGroupDTO.getId(), ResultDTOEnum.PARAMETER_MISSING.getCode(), ResultDTOEnum.PARAMETER_MISSING.getMessage());
        Long id = merchantStoreGroupDTO.getId();
        Long tenantId = loginContextInfoDTO.getTenantId();
        MerchantStoreGroupPageResultResp group = queryById(id, tenantId);
        if (Objects.isNull(group)) {
            return CommonResult.ok();
        }
        if (Objects.equals(group.getType(), MerchantStoreGroupTypeEnum.DEFAULT.getType())) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "默认的分组不能删除");
        }
        MerchantStoreGroupCommandReq merchantStoreGroupCommandReq = new MerchantStoreGroupCommandReq();
        merchantStoreGroupCommandReq.setTenantId(loginContextInfoDTO.getTenantId());
        merchantStoreGroupCommandReq.setId(merchantStoreGroupDTO.getId());
        Boolean remove = userCenterMerchantStoreGroupFacade.remove(merchantStoreGroupCommandReq);
        if (!Boolean.TRUE.equals(remove)) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR);
        }

//        AssertCheckParams.notNull(merchantStoreGroupDTO.getId(), ResultDTOEnum.PARAMETER_MISSING.getCode(), ResultDTOEnum.PARAMETER_MISSING.getMessage());
//        Long id = merchantStoreGroupDTO.getId();
//        MerchantStoreGroup group = merchantStoreGroupRepository.getById(id);
//        if (Objects.isNull(group)) {
//            return CommonResult.ok();
//        }
//        if (Objects.equals(group.getType(), MerchantStoreGroupTypeEnum.DEFAULT.getType())) {
//            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "默认的分组不能删除");
//        }
//        // 更新为默认分组
//        MerchantStoreGroupResultResp defaultGroup = queryDefaultGroup(loginContextInfoDTO.getTenantId());
//        List<MerchantStoreGroupMapping> merchantStoreGroupMappings = merchantStoreGroupMappingRepository.selectByGroupId(Arrays.asList(id), loginContextInfoDTO.getTenantId());
//        if (CollectionUtils.isEmpty(merchantStoreGroupMappings)) {
//            merchantStoreGroupRepository.removeById(merchantStoreGroupDTO.getId());
//            return CommonResult.ok();
//        }
//        Long groupId = defaultGroup.getMerchantStoreGroupId();
//        merchantStoreGroupMappings.forEach(el -> el.setGroupId(groupId));
//        merchantStoreGroupMappingRepository.updateBatchById(merchantStoreGroupMappings);
//
//        // 删除分组
//        merchantStoreGroupRepository.removeById(merchantStoreGroupDTO.getId());
        return CommonResult.ok();
    }

    @Override
    public CommonResult storeStatistical(LoginContextInfoDTO loginContextInfoDTO) {
        MerchantStoreGroupVO allMerchantStoreGroupVO = new MerchantStoreGroupVO();
        allMerchantStoreGroupVO.setName("全部门店");
        allMerchantStoreGroupVO.setId(0L);
        List<MerchantStore> merchantStores = merchantStoreService.selectByTenantId(loginContextInfoDTO.getTenantId());
//        List<MerchantStore> merchantStores = merchantStoreMapper.selectAllByTenantId(loginContextInfoDTO.getTenantId());
        allMerchantStoreGroupVO.setStoreNum(merchantStores.size());
        List<MerchantStoreGroupVO> list = Arrays.asList(allMerchantStoreGroupVO);
        return CommonResult.ok(list);
    }

    @Override
    public Map<Long, String> queryBatchByStoreIds(Long tenantId, List<Long> storeIds) {
        List<MerchantStoreGroupInfoDTO> merchantStoreGroupInfoDTOS = batchQueryByStoreIds(tenantId, storeIds);
        return merchantStoreGroupInfoDTOS.stream().collect(Collectors.toMap(MerchantStoreGroupInfoDTO::getStoreId, MerchantStoreGroupInfoDTO::getMerchantStoreGroupName));
    }

    @Override
    public List<MerchantStoreGroup> queryBatchByIds(List<Long> groupIds, Long tenantId) {
        MerchantStoreGroupQueryDTO query = new MerchantStoreGroupQueryDTO();
        query.setGroupIds(groupIds);
        query.setTenantId(tenantId);
//        return merchantStoreGroupRepository.list(query, tenantId);
        return list(query);
    }

    @Override
    public CommonResult<List<MerchantStoreGroupVO>> listAll(LoginContextInfoDTO loginContextInfoDTO) {
        MerchantStoreGroupQueryDTO query = new MerchantStoreGroupQueryDTO();
        query.setTenantId(loginContextInfoDTO.getTenantId());
//        List<MerchantStoreGroup> list = merchantStoreGroupRepository.list(query, loginContextInfoDTO.getTenantId());
        List<MerchantStoreGroup> list = list(query);
        if (CollectionUtils.isEmpty(list)) {
            return CommonResult.ok();
        }
        return CommonResult.ok(list.stream().map(MerchantConvertUtil::convertMerchantStoreGroup2VO).collect(Collectors.toList()));
    }

    @Override
    public String queryGroupNameByIds(Long tenantId, List<Long> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return StringConstants.EMPTY;
        }
        List<MerchantStoreGroup> groups = queryBatchByIds(groupIds, tenantId);
        if (CollectionUtils.isEmpty(groups)) {
            return StringConstants.EMPTY;
        }
        return groups.stream().map(MerchantStoreGroup::getName).collect(Collectors.joining(StringConstants.CHINESE_SEPARATING_SYMBOL));
    }


    /**
     * 按条件更新已关联门店
     * @param merchantStoreGroupUpdateDTO
     * @param loginContextInfoDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public CommonResult updateMerchant(MerchantStoreGroupDTO merchantStoreGroupUpdateDTO, LoginContextInfoDTO loginContextInfoDTO) {
        MerchantStoreGroupCommandReq merchantStoreGroupCommandReq = new MerchantStoreGroupCommandReq();
        merchantStoreGroupCommandReq.setName(merchantStoreGroupUpdateDTO.getName());
        merchantStoreGroupCommandReq.setId(merchantStoreGroupUpdateDTO.getId());
        merchantStoreGroupCommandReq.setTenantId(loginContextInfoDTO.getTenantId());
        merchantStoreGroupCommandReq.setStoreIdList(merchantStoreGroupUpdateDTO.getStoreId());
        Boolean update = userCenterMerchantStoreGroupFacade.updateMerchantGroupWithRemove(merchantStoreGroupCommandReq);
        if (!Boolean.TRUE.equals(update)) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR);
        }

//        // 相同分组名称校验
//        MerchantStoreGroupResultResp merchantStoreGroupExist = getByName(loginContextInfoDTO.getTenantId(), merchantStoreGroupUpdateDTO.getName());
//        if (!Objects.isNull(merchantStoreGroupExist)) {
//            AssertCheckParams.isTrue(merchantStoreGroupExist.getMerchantStoreGroupId().equals(merchantStoreGroupUpdateDTO.getId()), ResultDTOEnum.MERCHANT_STORE_GROUP_NAME_EXISTING.getCode(), ResultDTOEnum.MERCHANT_STORE_GROUP_NAME_EXISTING.getMessage());
//        }
//        Long tenantId = loginContextInfoDTO.getTenantId();
//        MerchantStoreGroup merchantStoreGroup = merchantStoreGroupRepository.getById(merchantStoreGroupUpdateDTO.getId());
//        AssertCheckBiz.notNull(merchantStoreGroup, ResultDTOEnum.GROUP_STORE_NON_FOUND.getCode(), ResultDTOEnum.GROUP_STORE_NON_FOUND.getMessage());
//        merchantStoreGroup.setName(merchantStoreGroupUpdateDTO.getName());
//        merchantStoreGroupRepository.updateById(merchantStoreGroup);
//        // 查出默认分组
//        MerchantStoreGroupResultResp storeGroup = queryDefaultGroup(tenantId);
//        MerchantStoreLinkQueryDTO merchantStoreLinkQueryDTO = new MerchantStoreLinkQueryDTO();
//        // 分组id
//        merchantStoreLinkQueryDTO.setId(merchantStoreGroupUpdateDTO.getId());
//        merchantStoreLinkQueryDTO.setTenantId(tenantId);
//        // 当前分组的关联关系
//        List<MerchantStoreGroupMapping> merchantStoreGroupMappings = merchantStoreGroupMappingRepository.selectByCondition(merchantStoreLinkQueryDTO);
//        if (CollectionUtils.isEmpty(merchantStoreGroupUpdateDTO.getStoreId())){
//            // 如果上传门店id为空而当前分组id下的门店不为空
//            if (!CollectionUtils.isEmpty(merchantStoreGroupMappings)){
//                List<MerchantStoreGroupMapping> updateMappingList = merchantStoreGroupMappings.stream().map(item -> {
//                    item.setGroupId(storeGroup.getMerchantStoreGroupId());
//                    return item;
//                }).collect(Collectors.toList());
//                // 把门店批量更新回默认分组
//                merchantStoreGroupMappingRepository.updateBatchById(updateMappingList);
//            }
//        }else {
//            // 要更新的门店id
//            Set<Long> storeIdList = merchantStoreGroupUpdateDTO.getStoreId().stream().collect(Collectors.toSet());
//
//            // 更新门店ids不包含的,分组下已存在的门店id，需要变更为默认分组
//            List<MerchantStoreGroupMapping> updateDefaultGroupList = merchantStoreGroupMappings.stream().filter(o -> !storeIdList.contains(o.getStoreId())).collect(Collectors.toList());
//            updateDefaultGroupList.stream().map(item -> {
//                item.setGroupId(storeGroup.getMerchantStoreGroupId());
//                return item;
//            }).collect(Collectors.toList());
//            if(!CollectionUtils.isEmpty(updateDefaultGroupList)) {
//                merchantStoreGroupMappingRepository.updateBatchById(updateDefaultGroupList);
//            }
//
//            // 如果上传门店id不为空而当前分组id下的门店为空
//            List<MerchantStoreGroupMapping> merchantStoreGroupMappingsOrigin = merchantStoreGroupMappingRepository.selectByStoreIds(storeIdList.stream().collect(Collectors.toList()), tenantId);
//            // 门店id查出的不正确的mapping记录
//            List<MerchantStoreGroupMapping> unTrueMappings = new LinkedList<>();
//            List<MerchantStoreGroupMapping> unTrueMappingsBySelf = new LinkedList<>();
//            // 筛出不属于默认分组的
//            unTrueMappings = merchantStoreGroupMappingsOrigin.stream().filter(item -> !storeGroup.getMerchantStoreGroupId().equals(item.getGroupId())).collect(Collectors.toList());
//            if (!CollectionUtils.isEmpty(unTrueMappings)){
//                // 筛出既不属于默认分组又不属于当前分组的
//                unTrueMappingsBySelf = unTrueMappings.stream().filter(item -> !merchantStoreGroupUpdateDTO.getId().equals(item.getGroupId())).collect(Collectors.toList());
//                AssertCheckParams.isEmpty(unTrueMappingsBySelf, ResultDTOEnum.STORE_ID_NOT_TRUE.getCode(), ResultDTOEnum.STORE_ID_NOT_TRUE.getMessage());
//            }
//            // 更新所有门店的id为当前分组id
//            List<MerchantStoreGroupMapping> updateMappingList = merchantStoreGroupMappingsOrigin.stream().map(item -> {
//                item.setGroupId(merchantStoreGroupUpdateDTO.getId());
//                return item;
//            }).collect(Collectors.toList());
//            merchantStoreGroupMappingRepository.updateBatchById(updateMappingList);
//        }
        return CommonResult.ok();
    }

    @Override
    public PageInfo<MerchantStoreGroupVO> pageList(MerchantStoreGroupQueryDTO merchantStoreGroupQueryDto) {
        MerchantStoreGroupQueryReq merchantStoreGroupQueryReq = new MerchantStoreGroupQueryReq();
        merchantStoreGroupQueryReq.setTenantId(merchantStoreGroupQueryDto.getTenantId());
        merchantStoreGroupQueryReq.setId(merchantStoreGroupQueryDto.getId());
        merchantStoreGroupQueryReq.setMerchantStoreGroupName(merchantStoreGroupQueryDto.getName());
        merchantStoreGroupQueryReq.setCreateTimeSort(merchantStoreGroupQueryDto.getCreateTimeSort());
        merchantStoreGroupQueryReq.setUpdateTimeSort(merchantStoreGroupQueryDto.getUpdateTimeSort());

        PageQueryReq pageQueryReq = new PageQueryReq();
        pageQueryReq.setPageIndex(merchantStoreGroupQueryDto.getPageIndex());
        pageQueryReq.setPageSize(merchantStoreGroupQueryDto.getPageSize());
        PageInfo<MerchantStoreGroupPageResultResp> merchantStoreGroupPage = userCenterMerchantStoreGroupFacade.getMerchantStoreGroupPage(merchantStoreGroupQueryReq, pageQueryReq);
        List<MerchantStoreGroupPageResultResp> list = merchantStoreGroupPage.getList();
        List<MerchantStoreGroupVO> merchantStoreGroupVOS = MerchantStoreGroupMapperConvert.INSTANCE.respListToVoList(list);
        return PageInfoConverter.toPageInfoTransfer(merchantStoreGroupPage, merchantStoreGroupVOS);
    }

    @Override
    public List<MerchantStoreGroup> list(MerchantStoreGroupQueryDTO merchantStoreGroupQueryDto) {
        MerchantStoreGroupQueryReq merchantStoreGroupQueryReq = new MerchantStoreGroupQueryReq();
        merchantStoreGroupQueryReq.setTenantId(merchantStoreGroupQueryDto.getTenantId());
        merchantStoreGroupQueryReq.setId(merchantStoreGroupQueryDto.getId());
        merchantStoreGroupQueryReq.setMerchantStoreGroupName(merchantStoreGroupQueryDto.getName());
        merchantStoreGroupQueryReq.setIdList(merchantStoreGroupQueryDto.getGroupIds());
        merchantStoreGroupQueryReq.setCreateTimeSort(merchantStoreGroupQueryDto.getCreateTimeSort());
        merchantStoreGroupQueryReq.setUpdateTimeSort(merchantStoreGroupQueryDto.getUpdateTimeSort());

        List<MerchantStoreGroupPageResultResp> merchantStoreGroupResultResps = userCenterMerchantStoreGroupFacade.getMerchantStoreGroups(merchantStoreGroupQueryReq);
        return MerchantStoreGroupMapperConvert.INSTANCE.respListToGroupList(merchantStoreGroupResultResps);
    }

    @Override
    public List<MerchantStoreGroupInfoDTO> batchQueryByStoreIds(Long tenantId, List<Long> storeIds) {
        List<MerchantStoreGroupResultResp> merchantStoreGroupResultRespList = userCenterMerchantStoreGroupFacade.getGroupByStoreIds(tenantId, storeIds);
        return MerchantStoreGroupMapperConvert.INSTANCE.respListToDtoList(merchantStoreGroupResultRespList);
    }

    @Override
    public MerchantStoreGroupPageResultResp queryDefaultGroup(Long tenantId) {
        MerchantStoreGroupQueryReq merchantStoreGroupQueryReq = new MerchantStoreGroupQueryReq();
        merchantStoreGroupQueryReq.setTenantId(tenantId);
        merchantStoreGroupQueryReq.setType(MerchantStoreGroupTypeEnum.DEFAULT.getType());
        List<MerchantStoreGroupPageResultResp> merchantStoreGroups = userCenterMerchantStoreGroupFacade.getMerchantStoreGroups(merchantStoreGroupQueryReq);
        if (CollectionUtils.isEmpty(merchantStoreGroups)) {
            return null;
        }
        return merchantStoreGroups.get(NumberConstant.ZERO);
    }

    @Override
    public MerchantStoreGroupPageResultResp queryById(Long groupId, Long tenantId) {
        MerchantStoreGroupQueryReq merchantStoreGroupQueryReq = new MerchantStoreGroupQueryReq();
        merchantStoreGroupQueryReq.setId(groupId);
        merchantStoreGroupQueryReq.setTenantId(tenantId);

        List<MerchantStoreGroupPageResultResp> merchantStoreGroupResultResps = userCenterMerchantStoreGroupFacade.getMerchantStoreGroups(merchantStoreGroupQueryReq);
        if (CollectionUtils.isEmpty(merchantStoreGroupResultResps)) {
            return null;
        }
        return merchantStoreGroupResultResps.get(NumberConstant.ZERO);
    }

    @Override
    public MerchantStoreGroupResultResp selectByStoreId(Long storeId, Long tenantId) {
        List<MerchantStoreGroupResultResp> merchantStoreGroups = selectByStoreIds(Collections.singletonList(storeId), tenantId);
        if (CollectionUtils.isEmpty(merchantStoreGroups)) {
            return null;
        }
        return merchantStoreGroups.get(NumberConstant.ZERO);
    }

    @Override
    public List<MerchantStoreGroupResultResp> selectByStoreIds(List<Long> storeIds, Long tenantId) {
        if (CollectionUtils.isEmpty(storeIds)) {
            return Collections.emptyList();
        }
        List<MerchantStoreGroupResultResp> merchantStoreGroups = userCenterMerchantStoreGroupFacade.getGroupByStoreIds(tenantId, storeIds);
        return merchantStoreGroups;
    }


    @Override
    public List<MerchantStoreGroupVO> listGroupByParam(MerchantStoreGroupQueryDTO merchantStoreGroupQueryDto) {
        MerchantStoreGroupQueryReq merchantStoreGroupQueryReq = new MerchantStoreGroupQueryReq();
        merchantStoreGroupQueryReq.setTenantId(merchantStoreGroupQueryDto.getTenantId());
        merchantStoreGroupQueryReq.setId(merchantStoreGroupQueryDto.getId());
        merchantStoreGroupQueryReq.setMerchantStoreGroupName(merchantStoreGroupQueryDto.getName());
        merchantStoreGroupQueryReq.setIdList(merchantStoreGroupQueryDto.getGroupIds());
        merchantStoreGroupQueryReq.setCreateTimeSort(merchantStoreGroupQueryDto.getCreateTimeSort());
        merchantStoreGroupQueryReq.setUpdateTimeSort(merchantStoreGroupQueryDto.getUpdateTimeSort());

        List<MerchantStoreGroupPageResultResp> merchantStoreGroupResultResps = userCenterMerchantStoreGroupFacade.listGroupByParam(merchantStoreGroupQueryReq);
        return MerchantStoreGroupMapperConvert.INSTANCE.respListToVoList(merchantStoreGroupResultResps);
    }

}
