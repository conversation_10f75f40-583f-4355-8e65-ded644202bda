package com.cosfo.manage.merchant.service;

import com.cosfo.manage.merchant.model.po.MerchantDeliveryRuleWarehouseRelation;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-04-14
 * @Description:
 */
public interface MerchantDeliveryRuleWarehouseRelationService {

    List<MerchantDeliveryRuleWarehouseRelation> queryByRuleIds(Long tenantId, List<Long> ruleIdList);

    boolean deleteByRuleIds(List<Long> ruleIds, Long tenantId);

    boolean batchInsert(Long ruleId, List<Integer> warehouseNoList, Long tenantId);

    List<MerchantDeliveryRuleWarehouseRelation> queryByTenantId(List<Long> tenantIds);

    boolean deleteByParam(Long ruleId, List<Integer> dbNoList, Long tenantId);
}
