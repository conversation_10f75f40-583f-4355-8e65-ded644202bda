package com.cosfo.manage.merchant.service;

import com.cosfo.manage.merchant.model.dto.MerchantStoreTradeSummaryInitDTO;

/**
 * @description:
 * @author: George
 * @date: 2023-10-31
 **/
public interface MerchantStoreTradeSummaryService {

    /**
     * 生成门店交易汇总
     * @param paymentId
     */
    void generateStoreDimensionPaymentSummary(Long paymentId);

    /**
     * 初始化某个时间范围的门店交易汇总
     */
    void initStoreDimensionTradeSummary(MerchantStoreTradeSummaryInitDTO merchantStoreTradeSummaryInitDTO);

    /**
     * 稽核门店交易汇总
     */
    void auditStoreDimensionTradeSummary(Long tenantId, String startTime, String endTime);

    /**
     * 生成退款汇总
     * @param businessId
     */
    void generateStoreDimensionRefundSummary(Long businessId);
}
