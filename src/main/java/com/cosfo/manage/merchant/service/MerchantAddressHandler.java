package com.cosfo.manage.merchant.service;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.client.merchant.req.MerchantStoreOpenReq;
import com.cosfo.manage.common.config.MerchantAddressMappingNacosConfig;
import com.cosfo.manage.merchant.model.dto.address.AddressNameMappingDTO;
import com.cosfo.manage.merchant.model.dto.address.NameMapping;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 门店地址处理器
 *
 * <AUTHOR>
 * @Date 2025/4/18 14:36
 * @Version 1.0
 */
@Slf4j
@Component
public class MerchantAddressHandler {

    @Resource
    private MerchantAddressMappingNacosConfig merchantAddressMappingNacosConfig;

    /**
     * 处理非标准的门店地址 省市区
     * 把merchantStoreOpenReq里的 contactList 里面的 addressInfo，按照addressNameMapping里的省市区映射替换
     *
     * @param merchantStoreOpenReq 门店请求参数
     */
    public void handleNonstandardAddressName(MerchantStoreOpenReq merchantStoreOpenReq) {
        if (CollectionUtils.isEmpty(merchantStoreOpenReq.getContactList())) {
            return;
        }
        AddressNameMappingDTO addressNameMapping = merchantAddressMappingNacosConfig.querySaasMerchantAddressMapping();
        if (null == addressNameMapping) {
            return;
        }
        Map<String, String> provinceMap = createMapping(addressNameMapping.getProvinceMapping());
        Map<String, String> cityMap = createMapping(addressNameMapping.getCityMapping());
        Map<String, String> areaMap = createMapping(addressNameMapping.getAreaMapping());

        log.info("更新门店地址映射,provinceMap >>> {}, cityMap >>> {}, areaMap >>> {}", provinceMap, cityMap, areaMap);
        log.info("更新门店地址映射开始, 原地址 >>> {}", JSON.toJSONString(merchantStoreOpenReq.getContactList()));
        for (MerchantStoreOpenReq.ContactReq contact : merchantStoreOpenReq.getContactList()) {
            MerchantStoreOpenReq.AddressReq addressInfo = contact.getAddressInfo();
            if (addressInfo == null) {
                continue;
            }
            String province = addressInfo.getProvince();
            String city = addressInfo.getCity();
            String area = addressInfo.getArea();

            if (!StringUtils.isEmpty(province)) {
                addressInfo.setProvince(provinceMap.getOrDefault(province, province));
            }
            if (!StringUtils.isEmpty(city)) {
                addressInfo.setCity(cityMap.getOrDefault(city, city));
            }
            if (!StringUtils.isEmpty(area)) {
                addressInfo.setArea(areaMap.getOrDefault(area, area));
            }
            contact.setAddressInfo(addressInfo);
        }
        log.info("更新门店地址映射结束, 新地址 >>> {}", JSON.toJSONString(merchantStoreOpenReq.getContactList()));
    }

    private Map<String, String> createMapping(List<NameMapping> nameMappings) {
        Map<String, String> mapping = new HashMap<>();
        if (!CollectionUtils.isEmpty(nameMappings)) {
            for (NameMapping nameMapping : nameMappings) {
                mapping.put(nameMapping.getSourceName(), nameMapping.getTargetName());
            }
        }
        return mapping;
    }
}