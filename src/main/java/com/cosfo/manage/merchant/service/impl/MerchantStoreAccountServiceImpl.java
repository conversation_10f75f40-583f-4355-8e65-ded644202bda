package com.cosfo.manage.merchant.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.constant.RedisConstants;
import com.cosfo.manage.common.context.MerchantAccountTypeEnum;
import com.cosfo.manage.common.context.MerchantStoreAccountDeleteFlagEnum;
import com.cosfo.manage.common.context.MerchantStoreAccountStatusEnum;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTOEnum;
import com.cosfo.manage.common.util.AssertCheckParams;
import com.cosfo.manage.common.util.RedisUtils;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreAccountFacade;
import com.cosfo.manage.merchant.convert.MerchantStoreAccountMapperConvert;
import com.cosfo.manage.merchant.model.dto.MerchantStoreAccountDTO;
import com.cosfo.manage.merchant.model.po.MerchantStoreAccount;
import com.cosfo.manage.merchant.model.vo.MerchantStoreAccountVO;
import com.cosfo.manage.merchant.service.MerchantStoreAccountService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.log.config.BizLogRecordContext;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/19
 */
@Slf4j
@Service
public class MerchantStoreAccountServiceImpl implements MerchantStoreAccountService {
    //    @Resource
//    private MerchantStoreAccountMapper merchantStoreAccountMapper;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private UserCenterMerchantStoreAccountFacade userCenterMerchantStoreAccountFacade;

    @Override
    public List<MerchantStoreAccount> queryMerchantStoreAccount(String phone, Long tenantId) {
        MerchantStoreAccountQueryReq merchantStoreAccountQueryReq = new MerchantStoreAccountQueryReq();
        merchantStoreAccountQueryReq.setTenantId(tenantId);
        merchantStoreAccountQueryReq.setPhone(phone);
        List<MerchantStoreAccountResultResp> respList = userCenterMerchantStoreAccountFacade.getMerchantStoreList(merchantStoreAccountQueryReq);
        return MerchantStoreAccountMapperConvert.INSTANCE.respListToAccountList(respList);
//        return merchantStoreAccountMapper.queryMerchantStoreAccount(phone, tenantId);
    }


    @Override
    public MerchantStoreAccountVO queryAccountInfo(Long accountId) {
        AssertCheckParams.notNull(accountId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "账户Id不能为空");
        MerchantStoreAccount merchantStoreAccount = MerchantStoreAccountMapperConvert.INSTANCE.respToMerchantStoreAccount(userCenterMerchantStoreAccountFacade.getMerchantStoreAccountInfo(accountId));
//        MerchantStoreAccount merchantStoreAccount = merchantStoreAccountMapper.selectByPrimaryKey(accountId);
        MerchantStoreAccountVO merchantStoreAccountVO = new MerchantStoreAccountVO();
        BeanUtils.copyProperties(merchantStoreAccount, merchantStoreAccountVO);
        return merchantStoreAccountVO;
    }

    @Override
    public CommonResult deleteAccount(Long id) {
//        MerchantStoreAccount merchantStoreAccount = merchantStoreAccountMapper.selectByPrimaryKey(id);
        MerchantStoreAccount merchantStoreAccount = MerchantStoreAccountMapperConvert.INSTANCE.respToMerchantStoreAccount(userCenterMerchantStoreAccountFacade.getMerchantStoreAccountInfo(id));
        AssertCheckParams.notNull(merchantStoreAccount, ResultStatusEnum.SERVER_ERROR.getStatus(), "未查询到账号");

        if (Objects.equals(merchantStoreAccount.getType(), MerchantAccountTypeEnum.MANAGER.getType())) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "店长被不能删除");
        }

//        // 清空openId
//        merchantStoreAccount.setOpenId(null);
//        merchantStoreAccount.setDeleteFlag(MerchantStoreAccountDeleteFlagEnum.DELETED.getStatus());
//        merchantStoreAccountMapper.updateByPrimaryKey(merchantStoreAccount);
        MerchantStoreAccountCommandReq merchantStoreAccountCommandReq = new MerchantStoreAccountCommandReq();
        merchantStoreAccountCommandReq.setId(id);
        Boolean remove = userCenterMerchantStoreAccountFacade.removeMerchantStoreAccount(merchantStoreAccountCommandReq);
        if (!Boolean.TRUE.equals(remove)) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR);
        }

        // T出登录
        removeUserCache(Arrays.asList(id));

        log.info("账号：{}已被管理员删除");
        // 设置操作日志上下文
        buildDeleteAccountContext(merchantStoreAccount);
        return CommonResult.ok();
    }

    @Override
    public void removeUserCache(List<Long> ids) {
        List<String> userKeys = ids.stream().map(id -> {
            return RedisConstants.MALL_TOKEN_PREFIX + id;
        }).collect(Collectors.toList());
        redisUtils.delete(userKeys);
    }

    private void buildDeleteAccountContext(MerchantStoreAccount account) {
        try {
            BizLogRecordContext.put("storeId", account.getStoreId());
            BizLogRecordContext.put("tenantId", UserLoginContextUtils.getTenantId());
            Map<String, Object> content = new HashMap<>();
            content.put("deleteAccount", account);
            BizLogRecordContext.put("content", content);
        } catch (Exception e) {
            log.error("获取日志上下文失败！", e);
        }
    }

//    @Override
//    public void insert(MerchantStoreAccount record) {
//        MerchantStoreAccountCommandReq merchantStoreAccountCommandReq = MerchantStoreAccountMapperConvert.INSTANCE.recordToReq(record);
//        userCenterMerchantStoreAccountFacade.createMerchantStoreAccount(merchantStoreAccountCommandReq);
//    }
//
//    @Override
//    public void updateByPrimaryKeySelective(MerchantStoreAccount record) {
//        MerchantStoreAccountCommandReq merchantStoreAccountCommandReq = MerchantStoreAccountMapperConvert.INSTANCE.recordToReq(record);
//        userCenterMerchantStoreAccountFacade.updateMerchantStoreAccount(merchantStoreAccountCommandReq);
//    }

    @Override
    public List<MerchantStoreAccountDTO> selectByStoreId(Long tenantId, Long storeId, Integer deleteFlag) {
        MerchantStoreAccountQueryReq merchantStoreAccountQueryReq = new MerchantStoreAccountQueryReq();
        merchantStoreAccountQueryReq.setTenantId(tenantId);
        merchantStoreAccountQueryReq.setStoreId(storeId);
        merchantStoreAccountQueryReq.setDeleteFlag(deleteFlag);
        List<MerchantStoreAccountResultResp> respList = userCenterMerchantStoreAccountFacade.getMerchantStoreList(merchantStoreAccountQueryReq);
        return MerchantStoreAccountMapperConvert.INSTANCE.respListToDtoList(respList);
    }

    @Override
    public MerchantStoreAccount selectOne(MerchantStoreAccount query) {
        MerchantStoreAccountQueryReq merchantStoreAccountQueryReq = new MerchantStoreAccountQueryReq();
        merchantStoreAccountQueryReq.setStoreId(query.getStoreId());
        merchantStoreAccountQueryReq.setType(query.getType());
        merchantStoreAccountQueryReq.setTenantId(query.getTenantId());
        merchantStoreAccountQueryReq.setPhone(query.getPhone());
        List<MerchantStoreAccountResultResp> respList = userCenterMerchantStoreAccountFacade.getMerchantStoreList(merchantStoreAccountQueryReq);
        List<MerchantStoreAccount> merchantStoreAccounts = MerchantStoreAccountMapperConvert.INSTANCE.respListToAccountList(respList);
        if (CollectionUtil.isNotEmpty(merchantStoreAccounts) && merchantStoreAccounts.size() == NumberConstant.ONE) {
            return merchantStoreAccounts.get(NumberConstant.ZERO);
        }

        throw AssertCheckParams.buildParamsException(ResultStatusEnum.SERVER_ERROR.getStatus(), "数据异常");
    }

    @Override
    public MerchantStoreAccountDTO selectManager(Long tenantId, Long storeId) {
        return selectByStoreId(tenantId, storeId, MerchantStoreAccountDeleteFlagEnum.NORMAL.getStatus()).stream()
            .filter(el -> Objects.equals(el.getType(), MerchantAccountTypeEnum.MANAGER.getType()))
            .findFirst().orElse(null);
    }

    @Override
    public Map<Long, MerchantStoreAccountDTO> selectManagerMap(Long tenantId, Collection<Long> storeIds) {
        MerchantStoreAccountQueryReq merchantStoreAccountQueryReq = new MerchantStoreAccountQueryReq();
        merchantStoreAccountQueryReq.setTenantId(tenantId);
        merchantStoreAccountQueryReq.setStoreIdList(new ArrayList<>(storeIds));
        merchantStoreAccountQueryReq.setType(MerchantAccountTypeEnum.MANAGER.getType());
        merchantStoreAccountQueryReq.setDeleteFlag(MerchantStoreAccountDeleteFlagEnum.NORMAL.getStatus());
        List<MerchantStoreAccountResultResp> respList = userCenterMerchantStoreAccountFacade.getMerchantStoreList(merchantStoreAccountQueryReq);
        if (CollectionUtils.isEmpty(respList)) {
            return Collections.emptyMap();
        }
        List<MerchantStoreAccountDTO> merchantStoreAccountDTOS = MerchantStoreAccountMapperConvert.INSTANCE.respListToDtoList(respList);
        return merchantStoreAccountDTOS.stream().collect(Collectors.toMap(MerchantStoreAccountDTO::getStoreId, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    public CommonResult insertAccount(MerchantStoreAccountDTO merchantStoreAccountDto, LoginContextInfoDTO loginContextInfoDto) {
        String accountName = merchantStoreAccountDto.getAccountName();
        String phone = merchantStoreAccountDto.getPhone();
        Integer type = merchantStoreAccountDto.getType();
        Long storeId = merchantStoreAccountDto.getStoreId();
        AssertCheckParams.notNull(phone, ResultStatusEnum.SERVER_ERROR.getStatus(), "手机号码不能为空");
        AssertCheckParams.notNull(accountName, ResultStatusEnum.SERVER_ERROR.getStatus(), "账号名称不能为空");
        AssertCheckParams.notNull(type, ResultStatusEnum.SERVER_ERROR.getStatus(), "角色不能为空");
        AssertCheckParams.notNull(storeId, ResultStatusEnum.SERVER_ERROR.getStatus(), "门店id不能为空");

//        List<MerchantStoreAccountDTO> merchantStoreAccounts = selectByStoreId(loginContextInfoDto.getTenantId(), merchantStoreAccountDto.getStoreId(), MerchantStoreAccountDeleteFlagEnum.NORMAL.getStatus());
//        long accountNum = merchantStoreAccounts.stream().filter(el -> Objects.equals(el.getDeleteFlag(), MerchantStoreAccountDeleteFlagEnum.DELETED.getStatus())).count();
//        if (accountNum > NumberConstant.TEN) {
//            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "最多有一个店长十个店员");
//        }
//
//        MerchantStoreAccount query = new MerchantStoreAccount();
//        query.setTenantId(loginContextInfoDto.getTenantId());
//        query.setStoreId(storeId);
//        query.setPhone(phone);
//        MerchantStoreAccount account = selectOne(query);
//        if (Objects.nonNull(account)) {
//            if (Objects.equals(account.getDeleteFlag(), MerchantStoreAccountDeleteFlagEnum.NORMAL.getStatus())) {
//                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "该手机号已经是当前门店的员工，请勿重复提交");
//            }
//            // 更新该账号为正常使用
//            MerchantStoreAccount update = new MerchantStoreAccount();
//            update.setId(account.getId());
//            update.setDeleteFlag(MerchantStoreAccountDeleteFlagEnum.NORMAL.getStatus());
//            update.setAccountName(merchantStoreAccountDto.getAccountName());
////            merchantStoreAccountMapper.updateByPrimaryKeySelective(update);
//            updateByPrimaryKeySelective(update);
//            return CommonResult.ok();
//        }

        MerchantStoreAccountCommandReq insertAccount = new MerchantStoreAccountCommandReq();
        insertAccount.setTenantId(loginContextInfoDto.getTenantId());
        insertAccount.setStoreId(storeId);
        insertAccount.setAccountName(accountName);
        insertAccount.setPhone(phone);
        insertAccount.setType(MerchantAccountTypeEnum.CLERK.getType());
        insertAccount.setRegisterTime(LocalDateTime.now());
        insertAccount.setAuditTime(LocalDateTime.now());
        insertAccount.setStatus((MerchantStoreAccountStatusEnum.AUDIT_SUCCESS.getStatus()));
        Long id = userCenterMerchantStoreAccountFacade.createMerchantStoreAccount(insertAccount);
        if (NumberConstant.ZERO > id) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR);
        }
//        merchantStoreAccountMapper.insert(insertAccount);
        return CommonResult.ok();
    }
}
