package com.cosfo.manage.merchant.service.impl;

import cn.hutool.core.util.IdUtil;
import com.cosfo.manage.common.context.WarehouseTypeEnum;
import com.cosfo.manage.common.context.warehouse.WarehouseQueryEnum;
import com.cosfo.manage.common.converter.merchant.OrderQuantityRuleMapper;
import com.cosfo.manage.facade.WarehouseStorageQueryFacade;
import com.cosfo.manage.market.service.MarketItemBusinessService;
import com.cosfo.manage.merchant.dao.MerchantOrderQuantityRuleDao;
import com.cosfo.manage.merchant.model.dto.quantityrule.OrderQuantityRuleDTO;
import com.cosfo.manage.merchant.model.dto.quantityrule.OrderQuantityRuleManageDTO;
import com.cosfo.manage.merchant.model.po.MerchantOrderQuantityRule;
import com.cosfo.manage.merchant.model.vo.OrderQuantityRuleManageVO;
import com.cosfo.manage.merchant.model.vo.OrderQuantityRuleVO;
import com.cosfo.manage.merchant.service.MerchantOrderQuantityRuleService;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.client.enums.WarehouseSourceEnum;
import net.summerfarm.wnc.client.resp.WarehouseStorageResp;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MerchantOrderQuantityRuleServiceImpl implements MerchantOrderQuantityRuleService {

    @Resource
    private MerchantOrderQuantityRuleDao merchantOrderQuantityRuleDao;

    @Resource
    private WarehouseStorageQueryFacade warehouseStorageQueryFacade;
    @Resource
    private MarketItemBusinessService marketItemBusinessService;

    @Override
    public OrderQuantityRuleManageVO queryOrderQuantityRule(Long tenantId) {
        List<MerchantOrderQuantityRule> list = merchantOrderQuantityRuleDao.listOrderQuantityRule(tenantId);
        OrderQuantityRuleManageVO manageVO = new OrderQuantityRuleManageVO();
        if (CollectionUtils.isEmpty(list)) {
            return manageVO;
        }
        Predicate<MerchantOrderQuantityRule> allMallPredicate = (rule) -> rule.getRuleTarget().equals(0L) && WarehouseTypeEnum.NO_WAREHOUSE.getCode().equals(rule.getWarehouseType());
        Optional<MerchantOrderQuantityRule> allMallRuleOption = list.stream().filter(allMallPredicate).findAny();
        allMallRuleOption.ifPresent(rule -> manageVO.setAllMallRule(OrderQuantityRuleMapper.INSTANCE.ruleToVo(rule)));
        List<MerchantOrderQuantityRule> otherRuleList = list.stream().filter(allMallPredicate.negate()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(otherRuleList)) {
            return manageVO;
        }

        List<OrderQuantityRuleVO> otherRuleVoToShow = new ArrayList<>();

        List<OrderQuantityRuleVO> otherRuleVoList = OrderQuantityRuleMapper.INSTANCE.toRuleVOList(otherRuleList);
        Map<Long, List<OrderQuantityRuleVO>> ruleListMap = otherRuleVoList.stream().collect(Collectors.groupingBy(OrderQuantityRuleVO::getRuleGroup));

        // 处理同组只展示一条
        for (Map.Entry<Long, List<OrderQuantityRuleVO>> entry : ruleListMap.entrySet()) {
            OrderQuantityRuleVO ruleVO = entry.getValue().get(0);
            List<Long> warehouseIdList = entry.getValue().stream().map(OrderQuantityRuleVO::getRuleTarget).collect(Collectors.toList());
            ruleVO.setRuleTargetList(warehouseIdList);
            //处理仓库名称
            List<String> warehouseNameList = getWarehouseNameList(tenantId, warehouseIdList);
            ruleVO.setRuleTargetNameList(warehouseNameList);
            otherRuleVoToShow.add(ruleVO);
        }
        List<OrderQuantityRuleVO> otherRuleSort = otherRuleVoToShow.stream().sorted(Comparator.comparing(OrderQuantityRuleVO::getRuleSort)).collect(Collectors.toList());
        manageVO.setOtherRule(otherRuleSort);
        return manageVO;
    }

    private List<String> getWarehouseNameList(Long tenantId, List<Long> warehouseIdList) {
        List<Integer> proprietaryIds = warehouseIdList.stream().filter(warehouse -> warehouse > 0).map(Long::intValue).collect(Collectors.toList());
        Map<Long, String> warehouseNameMap = new HashMap<>();
        warehouseNameMap.put(Long.valueOf(WarehouseQueryEnum.NO_WAREHOUSE.getId()), WarehouseQueryEnum.NO_WAREHOUSE.getName());
        warehouseNameMap.put(Long.valueOf(WarehouseQueryEnum.THIRD_WAREHOUSE.getId()), WarehouseQueryEnum.THIRD_WAREHOUSE.getName());
        if (!CollectionUtils.isEmpty(proprietaryIds)) {
            List<WarehouseStorageResp> warehouseStorageResps = warehouseStorageQueryFacade.queryWarehouseStorageList(tenantId, WarehouseSourceEnum.SAAS_WAREHOUSE, proprietaryIds);
            if (!CollectionUtils.isEmpty(warehouseStorageResps)) {
                for (WarehouseStorageResp storageResp : warehouseStorageResps) {
                    warehouseNameMap.put(Long.valueOf(storageResp.getWarehouseNo()), storageResp.getWarehouseName());
                }
            }
        }
        List<String> warehouseNameList = warehouseIdList.stream().map(warehouseNameMap::get).collect(Collectors.toList());
        return warehouseNameList;
    }

    @Override
    public Boolean modifyOrderQuantityRule(OrderQuantityRuleManageDTO manageDTO, Long tenantId) {
        List<MerchantOrderQuantityRule> ruleList = new ArrayList<>();
        MerchantOrderQuantityRule allMallRule = OrderQuantityRuleMapper.INSTANCE.dtoToRule(manageDTO.getAllMallRule(), tenantId);
        ruleList.add(allMallRule);

        List<OrderQuantityRuleDTO> otherRule = manageDTO.getOtherRule();
        if (!CollectionUtils.isEmpty(otherRule)) {
            merchantOrderQuantityRuleDao.delOtherRule(tenantId);
            // 判断是否需要查询所有商品ids
            Optional<OrderQuantityRuleDTO> any = otherRule.stream().filter(rule -> Boolean.TRUE.equals(rule.getAllItemHit()) && Boolean.FALSE.equals(rule.getIncludeNewFlag())).findAny();
            Set<Long> itemIds = new HashSet<>();
            if (any.isPresent()) {
                itemIds.addAll(marketItemBusinessService.queryAllItemIds(tenantId));
            }
            for (int i = 0; i < otherRule.size(); i++) {
                OrderQuantityRuleDTO orderQuantityRuleDTO = otherRule.get(i);
                List<OrderQuantityRuleDTO> groupRuleList = new ArrayList<>();
                List<Long> ruleTargetList = orderQuantityRuleDTO.getRuleTargetList();
                if (Boolean.TRUE.equals(orderQuantityRuleDTO.getAllItemHit()) && Boolean.FALSE.equals(orderQuantityRuleDTO.getIncludeNewFlag())) {
                    orderQuantityRuleDTO.setHitItemIds(itemIds);
                }
                if (Boolean.TRUE.equals(orderQuantityRuleDTO.getAllItemHit()) && Boolean.TRUE.equals(orderQuantityRuleDTO.getIncludeNewFlag())) {
                    orderQuantityRuleDTO.setHitItemIds(Collections.emptySet());
                }

                long groupId = IdUtil.getSnowflakeNextId();
                for (Long ruleTarget : ruleTargetList) {
                    OrderQuantityRuleDTO singleRule = new OrderQuantityRuleDTO();
                    BeanUtils.copyProperties(orderQuantityRuleDTO, singleRule);
                    singleRule.setId(null);
                    if (Long.valueOf(WarehouseQueryEnum.NO_WAREHOUSE.getId()).equals(ruleTarget)) {
                        singleRule.setWarehouseType(WarehouseTypeEnum.NO_WAREHOUSE.getCode());
                    } else if (Long.valueOf(WarehouseQueryEnum.THIRD_WAREHOUSE.getId()).equals(ruleTarget)) {
                        singleRule.setWarehouseType(WarehouseTypeEnum.THREE_PARTIES.getCode());
                    } else {
                        singleRule.setWarehouseType(WarehouseTypeEnum.PROPRIETARY.getCode());
                    }
                    singleRule.setRuleTarget(ruleTarget);
                    singleRule.setRuleGroup(groupId);
                    singleRule.setRuleSort(i);
                    groupRuleList.add(singleRule);
                }
                ruleList.addAll(OrderQuantityRuleMapper.INSTANCE.dtoToRuleList(groupRuleList, tenantId));
            }

        } else {
            //删除规则
            merchantOrderQuantityRuleDao.delOtherRule(tenantId);
        }
        return merchantOrderQuantityRuleDao.saveOrUpdateBatch(ruleList);
    }

    @Override
    public boolean updateRuleWithMarketItemUpdate(Long tenantId, Long marketItem, Integer event) {
        // 获取租户的特殊规则
        List<MerchantOrderQuantityRule> merchantOrderQuantityRules = merchantOrderQuantityRuleDao.listOrderQuantitySpecialRule(tenantId);
        if (CollectionUtils.isEmpty(merchantOrderQuantityRules)) {
            return true;
        }
        List<MerchantOrderQuantityRule> needUpdateRuleList = merchantOrderQuantityRules.stream().filter(rule -> !CollectionUtils.isEmpty(rule.getHitItemIds())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(needUpdateRuleList)) {
            return true;
        }
        for (MerchantOrderQuantityRule merchantOrderQuantityRule : needUpdateRuleList) {
           merchantOrderQuantityRule.getHitItemIds().remove(marketItem);
        }
        return merchantOrderQuantityRuleDao.saveOrUpdateBatch(merchantOrderQuantityRules);
    }
}
