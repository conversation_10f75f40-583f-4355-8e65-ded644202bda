package com.cosfo.manage.merchant.service;

import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.merchant.model.dto.balance.BalanceRecordDTO;
import com.cosfo.manage.merchant.model.dto.balance.MerchantStoreBalanceDTO;
import com.cosfo.manage.merchant.model.po.MerchantStoreBalanceChangeRecord;
import com.cosfo.manage.merchant.model.vo.balance.BalanceChangeRecordVO;
import com.cosfo.manage.merchant.model.vo.balance.BalanceOverviewVO;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;

/**
 * <AUTHOR>
 */
public interface MerchantStoreBalanceService {


    /**
     * 添加余额变动明细
     * @param merchantStoreBalanceChangeRecord 记录信息
     */
    MerchantStoreBalanceChangeRecord addBalanceChangeRecord(MerchantStoreBalanceChangeRecord merchantStoreBalanceChangeRecord);

    /**
     * 调整门店余额
     * @param merchantStoreBalanceDTO
     * @return
     */
    CommonResult<Object> adjustBalance(MerchantStoreBalanceDTO merchantStoreBalanceDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 余额概览:查询租户下的余额信息
     * @param merchantInfoDTO
     * @return
     */
    CommonResult<BalanceOverviewVO> balanceOverview(LoginContextInfoDTO merchantInfoDTO);

    /**
     * 余额明细记录
     *
     * @param merchantInfoDTO
     * @param balanceRecordDTO
     * @param queryOrder 是否需要查询具体单号
     * @return
     */
    CommonResult<PageInfo<BalanceChangeRecordVO>> balanceChangeRecord(LoginContextInfoDTO merchantInfoDTO, BalanceRecordDTO balanceRecordDTO, Boolean queryOrder);

    /**
     * 余额明细记录导出
     * @param merchantInfoDTO
     * @param balanceRecordDTO
     * @return
     */
    CommonResult<Object> exportBalanceChangeRecord(LoginContextInfoDTO merchantInfoDTO, BalanceRecordDTO balanceRecordDTO);
}
