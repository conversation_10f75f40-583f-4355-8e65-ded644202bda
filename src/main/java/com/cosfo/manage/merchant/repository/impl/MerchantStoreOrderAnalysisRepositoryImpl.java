package com.cosfo.manage.merchant.repository.impl;

import com.cosfo.manage.merchant.model.dto.MerchantStoreOrderAnalysisQueryDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreOrderOverviewDTO;
import com.cosfo.manage.merchant.model.po.MerchantStoreOrderAnalysis;
import com.cosfo.manage.report.mapper.MerchantStoreOrderAnalysisMapper;
import com.cosfo.manage.merchant.repository.MerchantStoreOrderAnalysisRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 门店订货分析 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-05
 */
@Service
public class MerchantStoreOrderAnalysisRepositoryImpl extends ServiceImpl<MerchantStoreOrderAnalysisMapper, MerchantStoreOrderAnalysis> implements MerchantStoreOrderAnalysisRepository {

    @Resource
    private MerchantStoreOrderAnalysisMapper merchantStoreOrderAnalysisMapper;

    @Override
    public MerchantStoreOrderOverviewDTO queryStoreOrderSum(MerchantStoreOrderAnalysisQueryDTO merchantStoreOrderAnalysisQueryDTO) {
        return merchantStoreOrderAnalysisMapper.queryStoreOrderSum(merchantStoreOrderAnalysisQueryDTO);
    }

    @Override
    public List<MerchantStoreOrderAnalysis> listByCondition(MerchantStoreOrderAnalysisQueryDTO merchantStoreOrderAnalysisQueryDTO) {
        return merchantStoreOrderAnalysisMapper.listByCondition(merchantStoreOrderAnalysisQueryDTO);
    }
}
