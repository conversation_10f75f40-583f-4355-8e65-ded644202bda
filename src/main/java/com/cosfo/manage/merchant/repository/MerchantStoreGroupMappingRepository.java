package com.cosfo.manage.merchant.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.merchant.model.po.MerchantStoreGroupMapping;

import java.util.List;

/**
 * <p>
 * 门店分组映射表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-14
 */
@Deprecated
public interface MerchantStoreGroupMappingRepository {
//
//    /**
//     * 删除门店绑定分组
//     *
//     * @param storeId
//     * @param tenantId
//     */
//    void deleteByStoreId(Long storeId, Long tenantId);
//
//
//    /**
//     * 根据分组Id查询
//     *
//     * @param groupIds
//     * @param tenantId
//     * @return
//     */
//    List<MerchantStoreGroupMapping> selectByGroupId(List<Long> groupIds, Long tenantId);
//
//    /**
//     * 根据门店Id查询
//     *
//     * @param storeId
//     * @param tenantId
//     * @return
//     */
//    MerchantStoreGroupMapping selectByStoreId(Long storeId, Long tenantId);
//
//    /**
//     * 删除门店分组关联信息
//     *
//     * @param groupId
//     * @param tenantId
//     */
//    void deleteByGroupId(Long groupId, Long tenantId);
//
//    /**
//     * 批量查询
//     *
//     * @param storeIds
//     * @param tenantId
//     * @return
//     */
//    List<MerchantStoreGroupMapping> selectByStoreIds(List<Long> storeIds, Long tenantId);
//
//    /**
//     * 按条件查询
//     */
//    List<MerchantStoreGroupMapping> selectByCondition(MerchantStoreLinkQueryDTO merchantStoreLinkQueryDTO);
//
//    /**
//     * 批量删除门店绑定分组
//     *
//     * @param storeIds
//     * @param tenantId
//     */
//    void deleteByStoreIds(List<Long> storeIds, Long tenantId);
}
