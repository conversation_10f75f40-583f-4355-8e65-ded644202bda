package com.cosfo.manage.merchant.repository;

import com.cosfo.manage.merchant.model.dto.MerchantStoreGroupDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreGroupInfoDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreGroupQueryDTO;
import com.cosfo.manage.merchant.model.po.MerchantStoreGroup;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.merchant.model.vo.MerchantStoreGroupVO;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;

import java.util.List;

/**
 * <p>
 * 门店分组 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-14
 */
@Deprecated
public interface MerchantStoreGroupRepository {

//    /**
//     * 根据name查询分组
//     *
//     * @param tenantId
//     * @param name
//     * @return
//     */
//    MerchantStoreGroup getByName(Long tenantId, String name);
//
//    /**
//     * 批量查询
//     *
//     * @param merchantStoreGroupQueryDTO
//     * @param tenantId
//     * @return
//     */
//    List<MerchantStoreGroup> list(MerchantStoreGroupQueryDTO merchantStoreGroupQueryDTO, Long tenantId);
//
//    /**
//     * 根据门店id批量查询
//     * @param tenantId
//     * @param storeIds
//     * @return
//     */
//    List<MerchantStoreGroupInfoDTO> queryBatchByStoreIds(Long tenantId, List<Long> storeIds);
//
//    /**
//     * 查询默认分组
//     * @param tenantId
//     * @return
//     */
//    MerchantStoreGroup queryDefaultGroup(Long tenantId);
}
