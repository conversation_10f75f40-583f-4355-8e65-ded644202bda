package com.cosfo.manage.merchant.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.merchant.mapper.MerchantStoreTradeSummaryMapper;
import com.cosfo.manage.merchant.model.po.MerchantStoreTradeSummary;
import com.cosfo.manage.merchant.repository.MerchantStoreTradeSummaryRepository;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 门店交易汇总 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-25
 */
@Service
public class MerchantStoreTradeSummaryRepositoryImpl extends ServiceImpl<MerchantStoreTradeSummaryMapper, MerchantStoreTradeSummary> implements MerchantStoreTradeSummaryRepository {

    @Override
    public List<MerchantStoreTradeSummary> queryAll(Long tenantId, String startTime, String endTime, Integer type) {
        LambdaQueryWrapper<MerchantStoreTradeSummary> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(tenantId), MerchantStoreTradeSummary::getTenantId, tenantId);
        wrapper.eq(MerchantStoreTradeSummary::getTradeType, type);
        wrapper.ge(MerchantStoreTradeSummary::getTradeTime, startTime); // 大于等于 startTime
        wrapper.lt(MerchantStoreTradeSummary::getTradeTime, endTime);   // 小于 endTime
        return this.list(wrapper);
    }

    @Override
    public BigDecimal selectSuccessPaymentPrice(Long tenantId, String startTime, String endTime, Integer type) {
        List<MerchantStoreTradeSummary> merchantStoreTradeSummaries = queryAll(tenantId, startTime, endTime, type);
        return merchantStoreTradeSummaries.stream().map(MerchantStoreTradeSummary::getTradeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public MerchantStoreTradeSummary selectByUniqueKey(String businessNo) {
        LambdaQueryWrapper<MerchantStoreTradeSummary> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MerchantStoreTradeSummary::getBusinessNo, businessNo);
        return this.getOne(wrapper);
    }
}
