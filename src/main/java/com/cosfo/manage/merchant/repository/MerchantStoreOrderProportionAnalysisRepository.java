package com.cosfo.manage.merchant.repository;

import com.cosfo.manage.merchant.model.dto.MerchantStoreOrderProportion;
import com.cosfo.manage.merchant.model.dto.MerchantStoreOrderProportionQueryDTO;
import com.cosfo.manage.merchant.model.po.MerchantStoreOrderProportionAnalysis;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 门店订货占比分析 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-05
 */
public interface MerchantStoreOrderProportionAnalysisRepository extends IService<MerchantStoreOrderProportionAnalysis> {

    /**
     * 查询占比
     * @param merchantStoreOrderProportionQueryDTO 查询条件
     * @return 占比数据
     */
    List<MerchantStoreOrderProportion> querySumByCondition(MerchantStoreOrderProportionQueryDTO merchantStoreOrderProportionQueryDTO);

    /**
     * 查询
     * @param merchantStoreOrderProportionQueryDTO
     */
    List<MerchantStoreOrderProportionAnalysis> listByCondition(MerchantStoreOrderProportionQueryDTO merchantStoreOrderProportionQueryDTO);
}
