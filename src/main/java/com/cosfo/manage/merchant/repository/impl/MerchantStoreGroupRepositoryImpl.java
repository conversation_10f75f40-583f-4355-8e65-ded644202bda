package com.cosfo.manage.merchant.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.manage.common.context.MerchantStoreGroupTypeEnum;
import com.cosfo.manage.common.util.BeanCopyUtils;
import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.merchant.model.dto.MerchantStoreGroupInfoDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreGroupQueryDTO;
import com.cosfo.manage.merchant.model.po.MerchantStoreGroup;
import com.cosfo.manage.merchant.mapper.MerchantStoreGroupMapper;
import com.cosfo.manage.merchant.model.vo.MerchantStoreGroupVO;
import com.cosfo.manage.merchant.repository.MerchantStoreGroupRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 门店分组 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-14
 */
@Deprecated
@Service
public class MerchantStoreGroupRepositoryImpl implements MerchantStoreGroupRepository {
//    @Resource
//    private MerchantStoreGroupMapper merchantStoreGroupMapper;

//    @Override
//    public MerchantStoreGroup getByName(Long tenantId, String name) {
//        LambdaQueryWrapper<MerchantStoreGroup> queryWrapper = new LambdaQueryWrapper();
//        queryWrapper.eq(MerchantStoreGroup::getTenantId, tenantId);
//        queryWrapper.eq(MerchantStoreGroup::getName, name);
//        MerchantStoreGroup merchantStoreGroup = getOne(queryWrapper);
//        return merchantStoreGroup;
//    }
//
//    @Override
//    public List<MerchantStoreGroup> list(MerchantStoreGroupQueryDTO merchantStoreGroupQueryDTO, Long tenantId) {
//        LambdaQueryWrapper<MerchantStoreGroup> queryWrapper = new LambdaQueryWrapper();
//        queryWrapper.eq(MerchantStoreGroup::getTenantId, tenantId);
//        // 分组Id
//        queryWrapper.eq(!Objects.isNull(merchantStoreGroupQueryDTO.getId()), MerchantStoreGroup::getId, merchantStoreGroupQueryDTO.getId());
//        // 分组名称
//        queryWrapper.like(!StringUtils.isEmpty(merchantStoreGroupQueryDTO.getName()), MerchantStoreGroup::getName, merchantStoreGroupQueryDTO.getName());
//        // 创建时间根据id排序
//        queryWrapper.orderByDesc(MerchantStoreGroup::getType);
//        Boolean sort = StringUtils.isEmpty(merchantStoreGroupQueryDTO.getCreateTimeSort()) ? false : merchantStoreGroupQueryDTO.getCreateTimeSort().equals("asc");
//        queryWrapper.orderBy(true, sort, MerchantStoreGroup::getId);
//        if (!StringUtils.isEmpty(merchantStoreGroupQueryDTO.getUpdateTimeSort())) {
//            queryWrapper.orderBy(true, merchantStoreGroupQueryDTO.getUpdateTimeSort().equals("asc"), MerchantStoreGroup::getUpdateTime);
//        }
//        // 分组ids
//        queryWrapper.in(!CollectionUtils.isEmpty(merchantStoreGroupQueryDTO.getGroupIds()), MerchantStoreGroup::getId, merchantStoreGroupQueryDTO.getGroupIds());
//
//        return list(queryWrapper);
//    }
//
//    @Override
//    public List<MerchantStoreGroupInfoDTO> queryBatchByStoreIds(Long tenantId, List<Long> storeIds) {
//        return merchantStoreGroupMapper.queryBatchByStoreIds(tenantId, storeIds);
//    }
//
//    @Override
//    public MerchantStoreGroup queryDefaultGroup(Long tenantId) {
//        LambdaQueryWrapper<MerchantStoreGroup> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.eq(MerchantStoreGroup::getTenantId, tenantId);
//        lambdaQueryWrapper.eq(MerchantStoreGroup::getType, MerchantStoreGroupTypeEnum.DEFAULT.getType());
//        return merchantStoreGroupMapper.selectOne(lambdaQueryWrapper);
//    }
}
