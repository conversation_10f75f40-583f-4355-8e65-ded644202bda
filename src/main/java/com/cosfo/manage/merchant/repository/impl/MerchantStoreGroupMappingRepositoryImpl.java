package com.cosfo.manage.merchant.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.manage.merchant.model.dto.MerchantStoreLinkQueryDTO;
import com.cosfo.manage.merchant.model.po.MerchantStoreGroup;
import com.cosfo.manage.merchant.model.po.MerchantStoreGroupMapping;
import com.cosfo.manage.merchant.mapper.MerchantStoreGroupMappingMapper;
import com.cosfo.manage.merchant.repository.MerchantStoreGroupMappingRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.merchant.repository.MerchantStoreGroupRepository;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 门店分组映射表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-14
 */
@Deprecated
@Service
public class MerchantStoreGroupMappingRepositoryImpl implements MerchantStoreGroupMappingRepository {

//    @Resource
//    private MerchantStoreGroupRepository merchantStoreGroupRepository;
//
//    @Override
//    public void deleteByStoreId(Long storeId, Long tenantId) {
//        LambdaQueryWrapper<MerchantStoreGroupMapping> queryWrapper = new LambdaQueryWrapper();
//        queryWrapper.eq(MerchantStoreGroupMapping::getStoreId, storeId);
//        queryWrapper.eq(MerchantStoreGroupMapping::getTenantId, tenantId);
//        remove(queryWrapper);
//    }
//    @Override
//    public List<MerchantStoreGroupMapping> selectByGroupId(List<Long> groupIds, Long tenantId) {
//        LambdaQueryWrapper<MerchantStoreGroupMapping> queryWrapper = new LambdaQueryWrapper();
//        queryWrapper.in(MerchantStoreGroupMapping::getGroupId, groupIds);
//        queryWrapper.eq(MerchantStoreGroupMapping::getTenantId, tenantId);
//        return list(queryWrapper);
//    }
//
//    @Override
//    public MerchantStoreGroupMapping selectByStoreId(Long storeId, Long tenantId) {
//        LambdaQueryWrapper<MerchantStoreGroupMapping> queryWrapper = new LambdaQueryWrapper();
//        queryWrapper.eq(MerchantStoreGroupMapping::getStoreId, storeId);
//        queryWrapper.eq(MerchantStoreGroupMapping::getTenantId, tenantId);
//        return getOne(queryWrapper);
//    }
//
//    @Override
//    public void deleteByGroupId(Long groupId, Long tenantId) {
//        LambdaQueryWrapper<MerchantStoreGroupMapping> queryWrapper = new LambdaQueryWrapper();
//        queryWrapper.eq(MerchantStoreGroupMapping::getGroupId, groupId);
//        queryWrapper.eq(MerchantStoreGroupMapping::getTenantId, tenantId);
//        remove(queryWrapper);
//    }
//
//    @Override
//    public List<MerchantStoreGroupMapping> selectByStoreIds(List<Long> storeIds, Long tenantId) {
//        LambdaQueryWrapper<MerchantStoreGroupMapping> queryWrapper = new LambdaQueryWrapper();
//        queryWrapper.in(MerchantStoreGroupMapping::getStoreId, storeIds);
//        queryWrapper.eq(MerchantStoreGroupMapping::getTenantId, tenantId);
//        return list(queryWrapper);
//    }
//
//    @Override
//    public List<MerchantStoreGroupMapping> selectByCondition(MerchantStoreLinkQueryDTO merchantStoreLinkQueryDTO) {
//        LambdaQueryWrapper<MerchantStoreGroupMapping> queryWrapper = new LambdaQueryWrapper();
//        queryWrapper.eq(ObjectUtils.isNotEmpty(merchantStoreLinkQueryDTO.getId()), MerchantStoreGroupMapping::getGroupId, merchantStoreLinkQueryDTO.getId());
//        queryWrapper.eq(ObjectUtils.isNotEmpty(merchantStoreLinkQueryDTO.getTenantId()), MerchantStoreGroupMapping::getTenantId, merchantStoreLinkQueryDTO.getTenantId());
//        queryWrapper.eq(ObjectUtils.isNotEmpty(merchantStoreLinkQueryDTO.getStoreId()), MerchantStoreGroupMapping::getStoreId, merchantStoreLinkQueryDTO.getStoreId());
//        queryWrapper.in(!CollectionUtils.isEmpty(merchantStoreLinkQueryDTO.getStoreIdList()), MerchantStoreGroupMapping::getStoreId, merchantStoreLinkQueryDTO.getStoreIdList());
//        return list(queryWrapper);
//    }
//
//    @Override
//    public void deleteByStoreIds(List<Long> storeIds, Long tenantId) {
//        LambdaQueryWrapper<MerchantStoreGroupMapping> queryWrapper = new LambdaQueryWrapper();
//        queryWrapper.in(MerchantStoreGroupMapping::getStoreId, storeIds);
//        queryWrapper.eq(MerchantStoreGroupMapping::getTenantId, tenantId);
//        remove(queryWrapper);
//    }
}
