package com.cosfo.manage.merchant.repository;


import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.merchant.model.po.MerchantDeliveryRuleWarehouseRelation;
import com.cosfo.manage.merchant.model.po.MerchantStoreBalance;

import java.util.List;

/**
 * <p>
 * 门店余额表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
public interface MerchantDeliveryRuleWarehouseRelationRepository extends IService<MerchantDeliveryRuleWarehouseRelation> {

    boolean deleteByRuleIds(List<Long> ruleIds, Long tenantId);

    boolean deleteByParam(Long ruleId, List<Integer> warehouseNoList, Long tenantId);
}
