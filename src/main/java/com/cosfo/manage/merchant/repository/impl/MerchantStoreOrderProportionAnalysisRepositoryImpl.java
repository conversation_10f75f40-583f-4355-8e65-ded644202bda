package com.cosfo.manage.merchant.repository.impl;

import com.cosfo.manage.merchant.model.dto.MerchantStoreOrderProportion;
import com.cosfo.manage.merchant.model.dto.MerchantStoreOrderProportionQueryDTO;
import com.cosfo.manage.merchant.model.po.MerchantStoreOrderProportionAnalysis;
import com.cosfo.manage.report.mapper.MerchantStoreOrderProportionAnalysisMapper;
import com.cosfo.manage.merchant.repository.MerchantStoreOrderProportionAnalysisRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 门店订货占比分析 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-05
 */
@Service
public class MerchantStoreOrderProportionAnalysisRepositoryImpl extends ServiceImpl<MerchantStoreOrderProportionAnalysisMapper, MerchantStoreOrderProportionAnalysis> implements MerchantStoreOrderProportionAnalysisRepository {

    @Resource
    private MerchantStoreOrderProportionAnalysisMapper merchantStoreOrderProportionAnalysisMapper;

    @Override
    public List<MerchantStoreOrderProportion> querySumByCondition(MerchantStoreOrderProportionQueryDTO merchantStoreOrderProportionQueryDTO) {
        return merchantStoreOrderProportionAnalysisMapper.querySumByCondition(merchantStoreOrderProportionQueryDTO);
    }

    @Override
    public List<MerchantStoreOrderProportionAnalysis> listByCondition(MerchantStoreOrderProportionQueryDTO merchantStoreOrderProportionQueryDTO) {
        return merchantStoreOrderProportionAnalysisMapper.listByCondition(merchantStoreOrderProportionQueryDTO);
    }
}
