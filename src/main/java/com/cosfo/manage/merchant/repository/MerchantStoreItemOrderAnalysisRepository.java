package com.cosfo.manage.merchant.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.merchant.model.dto.MerchantStoreItemOrderAnalysisQueryDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreItemOrderOverviewDTO;
import com.cosfo.manage.merchant.model.po.MerchantStoreItemOrderAnalysis;

import java.util.List;

/**
 * <p>
 * 门店商品订货分析 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-05
 */
public interface MerchantStoreItemOrderAnalysisRepository extends IService<MerchantStoreItemOrderAnalysis> {

    /**
     * 查询稽核-门店商品维度概况
     * @param merchantStoreItemOrderAnalysisQueryDTO 查询条件
     * @return 概况数据
     */
    MerchantStoreItemOrderOverviewDTO queryStoreItemOrderSum(MerchantStoreItemOrderAnalysisQueryDTO merchantStoreItemOrderAnalysisQueryDTO);

    /**
     * 查询稽核-门店商品维度列表
     * @param merchantStoreItemOrderAnalysisQueryDTO 查询条件
     * @return 列表数据
     */
    List<MerchantStoreItemOrderAnalysis> listByCondition(MerchantStoreItemOrderAnalysisQueryDTO merchantStoreItemOrderAnalysisQueryDTO);
}
