package com.cosfo.manage.merchant.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.merchant.mapper.MerchantDeliveryRuleWarehouseRelationMapper;
import com.cosfo.manage.merchant.model.po.MerchantDeliveryRuleWarehouseRelation;
import com.cosfo.manage.merchant.repository.MerchantDeliveryRuleWarehouseRelationRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 门店分组映射表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-14
 */
@Service
public class MerchantDeliveryRuleWarehouseRelationRepositoryImpl extends ServiceImpl<MerchantDeliveryRuleWarehouseRelationMapper, MerchantDeliveryRuleWarehouseRelation> implements MerchantDeliveryRuleWarehouseRelationRepository {

    @Resource
    private MerchantDeliveryRuleWarehouseRelationMapper merchantDeliveryRuleWarehouseRelationMapper;

    @Override
    public boolean deleteByRuleIds(List<Long> ruleIds, Long tenantId) {
        return merchantDeliveryRuleWarehouseRelationMapper.deleteByRuleIds(ruleIds, tenantId) > NumberConstant.ZERO;
    }

    @Override
    public boolean deleteByParam(Long ruleId, List<Integer> warehouseNoList, Long tenantId) {
        return merchantDeliveryRuleWarehouseRelationMapper.deleteByWarehouseNoList(ruleId, tenantId, warehouseNoList) > NumberConstant.ZERO;
    }
}
