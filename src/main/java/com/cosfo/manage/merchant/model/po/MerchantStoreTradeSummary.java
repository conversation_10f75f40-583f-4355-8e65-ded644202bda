package com.cosfo.manage.merchant.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 门店交易汇总
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-25
 */
@Getter
@Setter
@TableName("merchant_store_trade_summary")
public class MerchantStoreTradeSummary implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 门店id
     */
    @TableField("store_id")
    private Long storeId;

    /**
     * 门店类型 0、直营店 1、加盟店 2、托管店 3、个人店 4、连锁店
     */
    @TableField("store_type")
    private Integer storeType;

    /**
     * 业务id（支付单id/退款单id）
     */
    @TableField("business_id")
    private Long businessId;

    /**
     * 业务单号（支付/退款单号）
     */
    @TableField("business_no")
    private String businessNo;

    /**
     * 交易金额
     */
    @TableField("trade_amount")
    private BigDecimal tradeAmount;

    /**
     * 交易类型 1、支付 2、退款
     */
    @TableField("trade_type")
    private Integer tradeType;

    /**
     * 交易时间
     */
    @TableField("trade_time")
    private LocalDateTime tradeTime;

    /**
     * 付款件数
     */
    @TableField("trade_quantity")
    private Integer tradeQuantity;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}
