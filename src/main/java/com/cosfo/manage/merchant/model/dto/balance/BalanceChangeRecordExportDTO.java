package com.cosfo.manage.merchant.model.dto.balance;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.string.StringImageConverter;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author: fansongsong
 * @date: 2023-03-16  14:34
 * @Description: 变动余额导出操作类
 */
@Data
public class BalanceChangeRecordExportDTO {

    /**
     * 记录变动时间
     */
    @ExcelProperty("变动时间")
    private LocalDateTime createTime;

    /**
     * 门店编号
     */
    @ExcelProperty("门店ID")
    private String storeNo;

    /**
     * 门店名称
     */
    @ExcelProperty("门店名称")
    private String storeName;

    /**
     * 余额变动类型描述 0、预付 1、消费 2、消费退款
     */
    @ExcelProperty("变动类型")
    private String typeDesc;

    /**
     * 变动额度
     */
    @ExcelProperty("变动额度")
    private BigDecimal changeBalance;

    /**
     * 变动后余额
     */
    @ExcelProperty("变动后余额")
    private BigDecimal afterChangeBalance;

    /**
     * 操作人信息(名称+手机号)
     */
    @ExcelProperty("操作人")
    private String operator;

    /**
     * 相关单号
     */
    @ExcelProperty("相关单号")
    private String associatedOrderNo;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remark;

    /**
     * 附件1
     */
    @ExcelProperty(value = "附件1")
    private String attachmentOne;

    /**
     * 附件2
     */
    @ExcelProperty(value = "附件2")
    private String attachmentTwo;

    /**
     * 附件3
     */
    @ExcelProperty(value = "附件3")
    private String attachmentThree;

    /**
     * 附件4
     */
    @ExcelProperty(value = "附件4")
    private String attachmentFour;

    /**
     * 附件5
     */
    @ExcelProperty(value = "附件5")
    private String attachmentFive;
}