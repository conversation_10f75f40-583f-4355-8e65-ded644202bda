package com.cosfo.manage.merchant.model.dto;

import lombok.Data;

import java.util.Date;

/**
 * @Author: fansongsong
 * @Date: 2023-10-18
 * @Description:
 */
@Data
public class TenantAccountMsgConfigDTO {

    private Long id;

    /**
     * 1=付款后消息提醒;2=退款后消息提醒;3=待发货消息汇总提醒
     */
    private Integer bussinessType;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 租户accoountId
     */
    private Long tenantAccountId;

    /**
     * 是否可接收0=可;1=不可
     */
    private Integer availableStatus;

    /**
     * 发送时间
     */
    private Integer pushHour;

    /**
     * 供应商ID
     */
    private Long supplierId;
}
