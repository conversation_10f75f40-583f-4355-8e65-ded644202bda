package com.cosfo.manage.merchant.model.dto.balance;

import com.cosfo.manage.common.model.dto.PageQueryDTO;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class BalanceRecordDTO extends PageQueryDTO {

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 门店编码
     */
    private String storeNo;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 符合条件的门店ID列表，根据门店编码、名称查询所得
     */
    private List<Long> storeIdList;
    /**
     * 租户编号
     */
    private Long tenantId;

    /**
     * 余额变动类型 0、预付 1、消费 2、消费退款
     */
    private Integer type;
}
