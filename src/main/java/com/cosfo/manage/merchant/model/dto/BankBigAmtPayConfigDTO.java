package com.cosfo.manage.merchant.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date : 2022/12/20 18:02
 */
@Data
public class BankBigAmtPayConfigDTO implements Serializable {

    /**
     *开关状态
     */
    private String switch_state;
    /**
     *大额转账标识申请类型
     */
    private String biz_type;
    /**
     *费率（百分比/%）
     */
    private String fee_rate;
    /**
     *交易手续费（固定/元）
     */
    private String fee_fix_amt;
    /**
     *手续费外扣标记
     */
    private String out_fee_flag;
    /**
     *手续费外扣时的汇付ID
     */
    private String out_fee_huifuid;
    /**
     *外扣手续费费承担账户号
     */
    private String out_fee_acct_id;
}
