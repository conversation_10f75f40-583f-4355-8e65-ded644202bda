package com.cosfo.manage.merchant.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @description: 门店订货占比
 * @author: <PERSON>
 * @date: 2023-06-09
 **/
@Data
public class MerchantStoreOrderProportion {

    /**
     * 门店类型
     */
    private Integer storeType;

    /**
     * 门店分组名称
     */
    private String storeGroupName;

    /**
     * 订货数量
     */
    private BigDecimal orderAmount;

    /**
     * 订单金额
     */
    private BigDecimal orderPrice;
}
