package com.cosfo.manage.merchant.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 门店订货分析
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-05
 */
@Getter
@Setter
@TableName("merchant_store_order_analysis")
public class MerchantStoreOrderAnalysis implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 时间标签 yyyyMMdd
     */
    @TableField("time_tag")
    private String timeTag;

    /**
     * 1、周 2、月 3、季度
     */
    @TableField("type")
    private Integer type;

    /**
     * 门店id
     */
    @TableField("store_id")
    private Long storeId;

    /**
     * 平均订货周期
     */
    @TableField("average_order_period")
    private BigDecimal averageOrderPeriod;

    /**
     * 上周期平均订货周期
     */
    @TableField("average_order_period_last_period")
    private BigDecimal averageOrderPeriodLastPeriod;

    /**
     * 平均订货周期较上周期
     */
    @TableField("average_order_period_upper_period")
    private BigDecimal averageOrderPeriodUpperPeriod;

    /**
     * 订货数量
     */
    @TableField("order_amount")
    private Integer orderAmount;

    /**
     * 上周期订货数量
     */
    @TableField("order_amount_last_period")
    private Integer orderAmountLastPeriod;

    /**
     * 订货数量较上周期
     */
    @TableField("order_amount_upper_period")
    private BigDecimal orderAmountUpperPeriod;

    /**
     * 订货金额
     */
    @TableField("order_price")
    private BigDecimal orderPrice;

    /**
     * 上周期订货金额
     */
    @TableField("order_price_last_period")
    private BigDecimal orderPriceLastPeriod;

    /**
     * 订货金额较上周期
     */
    @TableField("order_price_upper_period")
    private BigDecimal orderPriceUpperPeriod;

    /**
     * 最后订货日期 yyyy-MM-dd
     */
    @TableField("last_order_time")
    private String lastOrderTime;

    /**
     * 最后订货数量
     */
    @TableField("last_order_amount")
    private Integer lastOrderAmount;

    /**
     * 最后订货金额
     */
    @TableField("last_order_price")
    private BigDecimal lastOrderPrice;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}
