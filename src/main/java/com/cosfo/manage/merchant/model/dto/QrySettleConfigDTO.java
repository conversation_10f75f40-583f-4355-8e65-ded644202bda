package com.cosfo.manage.merchant.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date : 2022/12/20 17:27
 */
@Data
public class QrySettleConfigDTO implements Serializable {

    /**
     *结算类型
     */
    private String settle_type;
    /**
     *结算周期
     */
    private String settle_cycle;
    /**
     *起结金额
     */
    private String min_amt;
    /**
     *留存金额
     */
    private String remained_amt;
    /**
     *D1结算手续费率(%)
     */
    private String fee_rate;
    /**
     *结算摘要
     */
    private String settle_abstract;
    /**
     *手续费外扣标记
     */
    private String out_settle_flag;
    /**
     *结算手续费外扣商户号
     */
    private String out_settle_huifuid;
    /**
     *结算手续费外扣账户类型
     */
    private String out_settle_acct_type;
    /**
     *结算开关
     */
    private String settle_status;
    /**
     *结算方式
     */
    private String settle_pattern;
    /**
     *是否优先到账
     */
    private String is_priority_receipt;
    /**
     *自定义结算处理时间
     */
    private String settle_time;

}
