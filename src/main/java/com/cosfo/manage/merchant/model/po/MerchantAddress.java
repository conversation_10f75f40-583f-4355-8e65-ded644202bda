package com.cosfo.manage.merchant.model.po;

import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * 描述:mch_address表的实体类
 * @version
 * @author:  Song
 * @创建时间: 2022-05-06
 */
@Data
public class MerchantAddress {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 们拍好
     */
    private String houseNumber;

    /**
     * poi地址
     */
    private String poiNote;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}
