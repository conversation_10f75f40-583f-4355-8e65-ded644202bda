package com.cosfo.manage.merchant.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: fansongsong
 * @Date: 2023-10-26
 * @Description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class MerchantStoreOpenDTO {
    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 门店编号
     */
    private String storeNo;

    /**
     * 成功状态
     */
    private Boolean success;

    /**
     * 失败原因
     */
    private String failMessage;
}
