package com.cosfo.manage.merchant.model.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 门店订货占比分析
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-05
 */
@Getter
@Setter
public class MerchantStoreOrderProportionAnalysisExcelDTO implements Serializable {

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 商品id
     */
    private String title;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 订货数量
     */
    private Integer orderAmount;

    /**
     * 订货数量占比
     */
    private String orderAmountProportion;

    /**
     * 上周期订货数量占比
     */
    private String orderAmountProportionUpperPeriod;

    /**
     * 订货金额
     */
    private BigDecimal orderPrice;

    /**
     * 订货金额占比
     */
    private String orderPriceProportion;

    /**
     * 上周期订货金额占比
     */
    private String orderPriceProportionUpperPeriod;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店类型
     * @see com.cosfo.manage.common.context.MerchantStoreEnum.Type
     */
    private String storeType;

    /**
     * 门店分组
     */
    private String storeGroup;

    /**
     * 当前周期的总订货数量
     */
    private Integer totalOrderAmount;

    /**
     * 上周期的订货数量
     */
    private Integer orderAmountUpperPeriod;

    /**
     * 上周期的总订货数量
     */
    private Integer totalOrderAmountUpperPeriod;

    /**
     * 当前周期的总订货金额
     */
    private BigDecimal totalOrderPrice;

    /**
     * 上周期的订货金额
     */
    private BigDecimal orderPriceUpperPeriod;

    /**
     * 上周期的总订货金额
     */
    private BigDecimal totalOrderPriceUpperPeriod;
}
