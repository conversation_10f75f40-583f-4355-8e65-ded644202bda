package com.cosfo.manage.merchant.model.dto.quantityrule;

import com.cosfo.manage.merchant.model.validator.ValidOrderQuantityRuleDetail;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
public class OrderQuantityRuleDTO implements Serializable {
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 0,全商城,其他值表示具体仓     -1 表示无仓，-2表示三方
     */
    private Long ruleTarget;

    /**
     * -1 表示无仓，-2表示三方
     */
    private List<Long> ruleTargetList;

    /**
     * 0,无仓1三方仓 2自营仓
     */
    private Integer warehouseType;

    /**
     * 规则json
     */
    private String rule;

    /**
     * 规则详情
     */
    @ValidOrderQuantityRuleDetail
    private List<OrderQuantityRuleDetailDTO> ruleDetailList;

    /**
     * 操作符 and or
     */
    private String operator;

    private Long ruleGroup;
    /**
     * 规则命中商品ids
     */
    private Set<Long> hitItemIds;

    /**
     * 货源类型
     */
    private Integer hitGoodsSource;

    /**
     * 是否包含后续新增
     */
    private Boolean includeNewFlag;

    /**
     * 选中所有商品
     */
    private Boolean allItemHit;

    /**
     * 规则排序，越小越先命中
     */
    private Integer ruleSort;
}
