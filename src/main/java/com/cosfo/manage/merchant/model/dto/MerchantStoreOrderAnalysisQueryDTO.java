package com.cosfo.manage.merchant.model.dto;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.util.List;

/**
 * @description: 门店稽核查询VO
 * @author: <PERSON>
 * @date: 2023-06-05
 **/
@Data
public class MerchantStoreOrderAnalysisQueryDTO extends BasePageInput {

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 时间标签
     */
    private String timeTag;

    /**
     * 类型 1、周 2、月 3、季度
     */
    private Integer type;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店类型
     * @see com.cosfo.manage.common.context.MerchantStoreEnum.Type
     */
    private Integer storeType;

    /**
     * 分组id
     */
    private Long storeGroupId;

    /**
     * 平均订货周期排序
     */
    private String averageOrderPeriodSort;

    /**
     * 订货数量排序
     */
    private String orderAmountSort;

    /**
     * 订货金额排序
     */
    private String orderPriceSort;

    /**
     * 最后订货日期排序
     */
    private String lastOrderTimeSort;

    /**
     * 最后订货数量排序
     */
    private String lastOrderAmountSort;

    /**
     * 最后订货金额排序
     */
    private String lastOrderPriceSort;

    /**
     * 门店ids
     */
    private List<Long> storeIds;

}
