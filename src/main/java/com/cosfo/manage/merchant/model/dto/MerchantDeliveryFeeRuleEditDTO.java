package com.cosfo.manage.merchant.model.dto;

import com.cosfo.manage.merchant.model.vo.MerchantDeliveryStepFeeVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MerchantDeliveryFeeRuleEditDTO implements Serializable {

    /**
     * primary key
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 0、无仓 1、三方仓 2、自营仓 10、全局规则
     */
    private Integer type;

    /**
     * 规则类型 0,每单1,每日2,基于仓运费报价
     */
    private Integer ruleType;

    /**
     * 定价方式：0,固定 1实时加价 2实时上浮
     */
    private Integer priceType;


    /**
     * 基于仓运费报价：实时加价运费，实时上浮百分比
     */
    private BigDecimal relateNumber;

    /**
     * 例外规则
     */
    private List<MerchantDeliveryFeeRuleEditDTO> specialDeliveryFeeRule;

    /**
     * 是否是仓库的默认数据0:非默认类型;1:默认类型
     */
    private Integer defaultType;

    /**
     * 免运费规则，0金额，1数量，2-重量
     */
    private Integer freeDeliveryType;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 命中商品id列表
     */
    private List<Long> hitItemIds;

    /**
     * 选中所有商品
     */
    private Boolean allItemHit;

    /**
     * 仓库编号,0:无实义等价于空
     */
    private List<Integer> warehouseNoList;

    /**
     * 仓库名称列表
     */
    private List<String> warehouseNameList;

    /**
     * 命中配送区域
     */
    private List<List<String>> hitAreas;

    /**
     * 包含后续新增商品 0 - 不包含 1 - 包含
     */
    private Boolean includeNewFlag;

    /**
     * 阶梯价
     */
    private List<MerchantDeliveryStepFeeVO> stepFeeList;


    /**
     * 匹配区域类型 0 - 按照配送区域 1 - 按照选中门店
     */
    private Integer matchRegionType;

    /**
     * 选中所有门店
     */
    private Boolean allStoreHit;

    /**
     * 命中门店id列表
     */
    private List<Long> hitStoreIds;

    /**
     * 匹配商品类型 0-根据商品设置 1-根据货源设置
     */
    private Integer matchItemType;

    /**
     * 货源类型,和goodsType不同, 0:全部，1:自营货品，2自营货品-代仓，3供应商直发货品
     */
    private Integer hitGoodsSource;

    /**
     * 履约类型：0-城配履约；1-快递履约
     */
    private Integer fulfillmentType;

}
