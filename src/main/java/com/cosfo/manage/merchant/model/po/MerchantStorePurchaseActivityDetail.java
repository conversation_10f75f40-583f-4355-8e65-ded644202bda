package com.cosfo.manage.merchant.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 门店采购活跃明细
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
@Getter
@Setter
@TableName("merchant_store_purchase_activity_detail")
public class MerchantStorePurchaseActivityDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 时间标签（yyyyMMdd）
     */
    @TableField("time_tag")
    private String timeTag;

    /**
     * 门店id
     */
    @TableField("store_id")
    private Long storeId;

    /**
     * 前7日采购数
     */
    @TableField("purchased_amount_7d")
    private Integer purchasedAmount7d;

    /**
     * 前30日采购数
     */
    @TableField("purchased_amount_30d")
    private Integer purchasedAmount30d;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}
