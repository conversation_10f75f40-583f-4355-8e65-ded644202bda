package com.cosfo.manage.merchant.model.vo;

import lombok.Data;
/**
 * <AUTHOR>
 * @date : 2023/3/10 15:44
 */
@Data
public class MerchantStoreLinkedVO {
    /**
     * 分组id，兼容查询非默认分组的方法
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;


    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店类型：0、直营店 1、加盟店 2、托管店
     */
    private Integer type;


    /**
     * 门店状态：0、审核中 1、审核通过 2、审核拒绝 3、关店
     */
    private Integer status;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 详情地址
     */
    private String address;

    /**
     * 门店编号
     */
    private String storeNo;

    /**
     * 分组Id
     */
    private Long groupId;

    /**
     * 页面大小
     */
    private Integer pageSize;

    /**
     * 页码
     */
    private Integer pageIndex;

}
