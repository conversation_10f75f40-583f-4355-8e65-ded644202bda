package com.cosfo.manage.merchant.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 阶梯运费
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-14
 */
@Getter
@Setter
@TableName(value = "merchant_delivery_step_fee", autoResultMap = true)
public class MerchantDeliveryStepFee implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 运费规则ID
     */
    @TableField("rule_id")
    private Long ruleId;

    /**
     * 门槛类型  0-金额 1-数量 2-重量
     */
    @TableField("fee_rule")
    private Integer feeRule;

    /**
     * 阶梯门槛。即满xx元/件/kg
     */
    @TableField("step_threshold")
    private BigDecimal stepThreshold;

    /**
     * 运费
     */
    @TableField("delivery_fee")
    private BigDecimal deliveryFee;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


    /**
     * 运费计算方式 0-固定运费为delivery_fee 1-动态计算
     */
    @TableField("cal_type")
    private Integer calType;

    /**
     * 运费动态计算规则 json
     */
    @TableField(value = "deliveryfee_cal_rule", typeHandler = JacksonTypeHandler.class)
    private DeliveryFeeCalRule deliveryfeeCalRule;

}
