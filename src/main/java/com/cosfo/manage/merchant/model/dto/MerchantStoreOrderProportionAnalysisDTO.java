package com.cosfo.manage.merchant.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @description: 门店订货占比分析VO
 * @author: George
 * @date: 2023-06-06
 **/
@Data
public class MerchantStoreOrderProportionAnalysisDTO {

    /**
     * 门店名称
     */
    private String storeName;


    /**
     * 门店类型 0、直营店 1、加盟 2、托管
     * @see com.cosfo.manage.common.context.MerchantStoreEnum.Type
     */
    private Integer storeType;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 订货数量
     */
    private Integer orderAmount;

    /**
     * 订货数量占比
     */
    private BigDecimal orderAmountProportion;

    /**
     * 上周期订货数量占比
     */
    private BigDecimal orderAmountProportionUpperPeriod;

    /**
     * 订货金额
     */
    private BigDecimal orderPrice;

    /**
     * 订货金额占比
     */
    private BigDecimal orderPriceProportion;

    /**
     * 上周期订货金额占比
     */
    private BigDecimal orderPriceProportionUpperPeriod;

    /**
     * 门店id
     */
    private Long storeId;
}
