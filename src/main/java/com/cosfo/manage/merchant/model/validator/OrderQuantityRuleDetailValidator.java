package com.cosfo.manage.merchant.model.validator;

import com.cosfo.manage.merchant.model.dto.quantityrule.OrderQuantityRuleDetailDTO;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
public class OrderQuantityRuleDetailValidator implements ConstraintValidator<ValidOrderQuantityRuleDetail, List<OrderQuantityRuleDetailDTO>> {
    @Override
    public boolean isValid(List<OrderQuantityRuleDetailDTO> value, ConstraintValidatorContext context) {
        if (value == null || value.isEmpty()) {
            return false;
        }
        boolean result = true;
        for (OrderQuantityRuleDetailDTO orderQuantityRuleDetailDTO : value) {
            if (orderQuantityRuleDetailDTO.getRuleDetailType() == 0) {
                result = result && orderQuantityRuleDetailDTO.getAmount() != null && BigDecimal.ZERO.compareTo(orderQuantityRuleDetailDTO.getAmount()) < 1;
            }
            if (orderQuantityRuleDetailDTO.getRuleDetailType() == 1) {
                result = result && orderQuantityRuleDetailDTO.getQuantity() != null && orderQuantityRuleDetailDTO.getQuantity() > 0;
            }
        }
        return result;
    }
}
