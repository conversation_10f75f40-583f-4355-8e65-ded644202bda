package com.cosfo.manage.merchant.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 门店配送规则表
 * <AUTHOR>
 */
@Data
@TableName("merchant_delivery_fee_rule")
public class MerchantDeliveryFeeRule implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 0、无仓 1、三方仓 2、自营仓 10、全局规则
     */
    private Integer type;

    /**
     * 规则类型 0,每单1,每日2,基于仓运费报价
     */
    private Integer ruleType;

    /**
     * 定价方式：0,固定 1实时加价 2实时上浮
     */
    @Deprecated
    private Integer priceType;

    /**
     * 运费
     * 查看 merchant_delivery_step_fee
     */
    @Deprecated
    private BigDecimal deliveryFee;

    /**
     * 免运费金额
     */
    @Deprecated
    private BigDecimal freeDeliveryPrice;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 基于仓运费报价：实时加价运费，实时上浮百分比
     */
    private BigDecimal relateNumber;

    /**
     * 是否是仓库的默认数据0:非默认类型;1:默认类型
     */
    private Integer defaultType;

    /**
     * 免运费数量
     */
    @Deprecated
    private Integer freeDeliveryQuantity;

    /**
     * 免运费规则，0金额，1数量，2重量
     */
    private Integer freeDeliveryType;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 选中商品列表
     */
    private List<Long> hitItemIds;

    /**
     * 选中配送区域
     */
    private List<List<String>> hitAreas;

    /**
     * 包含后续新增商品 0 - 不包含 1 - 包含
     */
    private Boolean includeNewFlag;

    /**
     * 匹配区域类型 0 - 按照配送区域 1 - 按照选中门店
     */
    private Integer matchRegionType;

    /**
     * 选中门店ID列表
     */
    private List<Long> hitStoreIds;

    /**
     * 包含所有门店及后续新增门店 0 - 不包含 1 - 包含
     */
    private Boolean includeAllStoreFlag;

    /**
     * 匹配商品类型 0-根据商品设置 1-根据货源设置
     */
    private Integer matchItemType;

    /**
     * 货源类型,和goodsType不同, 0:全部，1:自营货品，2自营货品-代仓，3供应商直发货品
     */
    private Integer hitGoodsSource;

    /**
     * 履约类型：0-城配履约；1-快递履约
     */
    private Integer fulfillmentType;



    private static final long serialVersionUID = 1L;
}
