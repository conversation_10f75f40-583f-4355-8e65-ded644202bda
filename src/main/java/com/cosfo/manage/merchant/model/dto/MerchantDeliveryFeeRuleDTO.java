package com.cosfo.manage.merchant.model.dto;

import com.cosfo.manage.merchant.model.dto.balance.MerchantDeliveryFeeSubRuleDTO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/27 15:35
 */
@Data
public class MerchantDeliveryFeeRuleDTO {

    /**
     * primary key
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 0、无仓 1、三方仓 2、自营仓
     */
    private Integer type;

    /**
     * 规则类型 0,每单1,每日2,基于仓运费报价
     */
    private Integer ruleType;

    /**
     * 定价方式：0,固定 1实时加价 2实时上浮
     */
    private Integer priceType;

    /**
     * 运费
     */
    private BigDecimal deliveryFee;

    /**
     * 免运费金额
     */
    private BigDecimal freeDeliveryPrice;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * 基于仓运费报价：实时加价运费，实时上浮百分比
     */
    private BigDecimal relateNumber;

    /**
     * 子集
     */
    private List<MerchantDeliveryFeeSubRuleDTO> merchantDeliveryFeeSubRuleDTOList;

    /**
     * 是否是仓库的默认数据0:非默认类型;1:默认类型
     */
    private Integer defaultType;

    /**
     * 免运费数量
     */
    private Integer freeDeliveryQuantity;

    /**
     * 免运费规则，0金额，1数量
     */
    private Integer freeDeliveryType;
}
