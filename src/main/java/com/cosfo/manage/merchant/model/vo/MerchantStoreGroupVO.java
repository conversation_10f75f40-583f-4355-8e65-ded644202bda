package com.cosfo.manage.merchant.model.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/11/11
 */
@Data
public class MerchantStoreGroupVO {
    /**
     * 分组Id
     */
    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * 门店数
     */
    private Integer storeNum;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 分组列表
     */
    private List<MerchantStoreVO> merchantStoreVos;

    /**
     * 分组类型
     */
    private Integer type;
}
