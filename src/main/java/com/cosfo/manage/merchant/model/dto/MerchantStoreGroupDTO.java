package com.cosfo.manage.merchant.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/11/10
 */
@Data
public class MerchantStoreGroupDTO {
    /**
     * 主键Id
     */
    private Long id;
    /**
     * 分组名称
     */
    @NotNull(message = "分组名称不能为空")
    private String name;
    /**
     * 绑定门店Id
     */
    private List<Long> storeId;
}
