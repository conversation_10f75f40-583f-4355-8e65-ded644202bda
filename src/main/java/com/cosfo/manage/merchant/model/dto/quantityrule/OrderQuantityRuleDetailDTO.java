package com.cosfo.manage.merchant.model.dto.quantityrule;

import com.cosfo.manage.merchant.model.validator.ValidOrderQuantityRuleDetail;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class OrderQuantityRuleDetailDTO implements Serializable {

    /**
     * 0 表示金额，1表示数量
     */
    private Integer ruleDetailType;

    /**
     * 满足金额
     */
    private BigDecimal amount;

    /**
     * 满足数量
     */
    private Integer quantity;
}
