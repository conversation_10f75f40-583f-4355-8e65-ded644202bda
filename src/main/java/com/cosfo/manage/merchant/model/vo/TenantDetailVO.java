package com.cosfo.manage.merchant.model.vo;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class TenantDetailVO {
    /**
     * 品牌方租户Id
     */
    private Long tenantId;
    /**
     * 品牌名称
     */
    private String merchantName;
    /**
     * logo
     */
    private String logoImage;
    /**
     * 背景图
     */
    private String backgroundImage;
    /**
     * 企业工商名称
     */
    private String companyName;
    /**
     * 统一社会信用代码
     */
    private String creditCode;
    /**
     * 营业执照
     */
    private String businessLicense;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区
     */
    private String area;
    /**
     * 街道地址
     */
    private String address;
    /**
     * 联系电话
     */
    private String companyPhone;
    /**
     * 手机号
     */
    private String phone;

    /**
     * 帆台商城网址
     */
    private String mallUrl;
}
