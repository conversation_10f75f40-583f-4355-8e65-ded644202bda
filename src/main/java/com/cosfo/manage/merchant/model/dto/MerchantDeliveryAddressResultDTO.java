package com.cosfo.manage.merchant.model.dto;


import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-05-26 10:50:24
 */
@Data
public class MerchantDeliveryAddressResultDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 地址id
     */
    private Long id;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 门店id
     */
    private Long storeId;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区
     */
    private String area;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 门牌号
     */
    private String houseNumber;
    /**
     * 商家腾讯地图坐标
     */
    private String poiNote;

    /**
     * 联系人名称
     */
    private String contactName;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 是否是默认地址：0、否 1、是
     */

    private Integer defaultFlag;
    /**
     * 状态(1-正常或审核通过、2-删除、3-待审核、4-审核不通过)
     */
    private Integer status;

    /**
     * 完整收货地址
     */
    private String deliveryAddress;

}