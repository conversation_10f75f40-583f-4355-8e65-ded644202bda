package com.cosfo.manage.merchant.model.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 *
 *
 * @author: xiaowk
 * @date: 2024/11/6 下午1:37
 */
@Data
public class MerchantDeliveryStartStepFeeRule {

    /**
     * 起步计算门槛
     */
    private BigDecimal startThreshold;

    /**
     * 起步计算运费，满足起步门槛
     */
    private BigDecimal startDeliveryFee;

    /**
     * 阶梯计算因子，默认为1，件数每加1，运费加step_fee
     */
    private BigDecimal stepFactor;

    /**
     * 阶梯费
     */
    private BigDecimal stepFee;

}
