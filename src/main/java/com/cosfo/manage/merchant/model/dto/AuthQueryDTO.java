package com.cosfo.manage.merchant.model.dto;

import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.util.StringUtils;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: fansongsong
 * @Date: 2023-06-13
 * @Description:
 */
@Builder
@Data
public class AuthQueryDTO implements Serializable {

    private Long tenantId;

    private Long authUserId;

    public String builderKey() {
        return tenantId + Constants.UNDERLINE + authUserId;
    }

    public static AuthQueryDTO getAuthQueryDTOByKey(String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }
        if (!key.contains(Constants.UNDERLINE)) {
            return null;
        }
        String[] split = key.split(Constants.UNDERLINE);
        try {

            return AuthQueryDTO.builder().tenantId(Long.valueOf(split[0])).authUserId(Long.valueOf(split[1])).build();
        } catch (Exception e) {
            return null;
        }
    }
}
