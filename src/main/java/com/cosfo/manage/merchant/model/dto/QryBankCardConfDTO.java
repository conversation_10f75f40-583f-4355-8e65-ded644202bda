package com.cosfo.manage.merchant.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date : 2022/12/20 17:42
 * 用户银行卡支付信息
 */
@Data
public class QryBankCardConfDTO implements Serializable {

    /**
     *	银联商户号
     */
    private String bank_mer_code;
    /**
     *	银行业务手续费类型
     */
    private String charge_cate_code;
    /**
     *借记卡手续费（%）
     */
    private String debit_fee_rate;
    /**
     *借记卡封顶值
     */
    private String debit_fee_limit;
    /**
     *贷记卡手续费（%）
     */
    private String credit_fee_rate;
    /**
     *是否开通云闪付
     */
    private String is_open_cloud_flag;
    /**
     *云闪付贷记卡手续费1000以下（%）
     */
    private String cloud_credit_fee_rate_down;
    /**
     *云闪付贷记卡手续费1000以上（%）
     */
    private String cloud_credit_fee_rate_up;
    /**
     *云闪付借记卡封顶1000以下（元）
     */
    private String cloud_debit_fee_limit_down;
    /**
     *云闪付借记卡封顶1000以上（元）
     */
    private String cloud_debit_fee_limit_up;
    /**
     *云闪付借记卡手续费1000以上（%）
     */
    private String cloud_debit_fee_rate_up;
    /**
     *云闪付借记卡手续费1000以下（%）
     */
    private String cloud_debit_fee_rate_down;
}
