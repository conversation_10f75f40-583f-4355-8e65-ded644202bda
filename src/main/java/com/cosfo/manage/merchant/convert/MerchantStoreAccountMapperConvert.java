package com.cosfo.manage.merchant.convert;

import com.cosfo.manage.merchant.model.dto.MerchantStoreAccountDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.manage.merchant.model.po.MerchantStoreAccount;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreDomainCommandReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-05-26
 * @Description:
 */
@Mapper
public interface MerchantStoreAccountMapperConvert {

    MerchantStoreAccountMapperConvert INSTANCE = Mappers.getMapper(MerchantStoreAccountMapperConvert.class);

    /**
     * record转为创建req
     * @param record
     * @return
     */
    MerchantStoreAccountCommandReq recordToReq(MerchantStoreAccount record);

    /**
     * resp转为MerchantStoreAccount
     * @param resp
     * @return
     */
    MerchantStoreAccount respToMerchantStoreAccount(MerchantStoreAccountResultResp resp);

    /**
     * respList转为List<MerchantStoreAccountDTO>
     * @param respList
     * @return
     */
    List<MerchantStoreAccountDTO> respListToDtoList(List<MerchantStoreAccountResultResp> respList);

    /**
     * respList转为List<MerchantStoreAccount>
     * @param respList
     * @return
     */
    List<MerchantStoreAccount> respListToAccountList(List<MerchantStoreAccountResultResp> respList);

}
