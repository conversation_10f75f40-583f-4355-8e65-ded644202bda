package com.cosfo.manage.merchant.convert;

import com.cosfo.manage.merchant.model.dto.*;
import com.cosfo.manage.merchant.model.po.MerchantStoreItemOrderAnalysis;
import com.cosfo.manage.merchant.model.po.MerchantStoreOrderAnalysis;
import com.cosfo.manage.merchant.model.po.MerchantStoreOrderProportionAnalysis;

import java.util.Objects;

/**
 * @description: 门店稽核转换器
 * @author: George
 * @date: 2023-06-06
 **/
public class MerchantStoreAuditConvertUtil {

    public static MerchantStoreOrderAnalysisDTO toDTO(MerchantStoreOrderAnalysis merchantStoreOrderAnalysis) {

        if (merchantStoreOrderAnalysis == null) {
            return null;
        }
        MerchantStoreOrderAnalysisDTO merchantStoreOrderAnalysisDTO = new MerchantStoreOrderAnalysisDTO();
        merchantStoreOrderAnalysisDTO.setAverageOrderPeriod(merchantStoreOrderAnalysis.getAverageOrderPeriod());
        merchantStoreOrderAnalysisDTO.setAverageOrderPeriodUpperPeriod(merchantStoreOrderAnalysis.getAverageOrderPeriodUpperPeriod());
        merchantStoreOrderAnalysisDTO.setOrderAmount(merchantStoreOrderAnalysis.getOrderAmount());
        merchantStoreOrderAnalysisDTO.setOrderAmountUpperPeriod(merchantStoreOrderAnalysis.getOrderAmountUpperPeriod());
        merchantStoreOrderAnalysisDTO.setOrderPriceUpperPeriod(merchantStoreOrderAnalysis.getOrderPriceUpperPeriod());
        merchantStoreOrderAnalysisDTO.setOrderPrice(merchantStoreOrderAnalysis.getOrderPrice());
        merchantStoreOrderAnalysisDTO.setLastOrderTime(merchantStoreOrderAnalysis.getLastOrderTime());
        merchantStoreOrderAnalysisDTO.setLastOrderAmount(merchantStoreOrderAnalysis.getLastOrderAmount());
        merchantStoreOrderAnalysisDTO.setLastOrderPrice(merchantStoreOrderAnalysis.getLastOrderPrice());
        merchantStoreOrderAnalysisDTO.setStoreId(merchantStoreOrderAnalysis.getStoreId());
        return merchantStoreOrderAnalysisDTO;
    }

    public static MerchantStoreItemOrderAnalysisDTO toDTO(MerchantStoreItemOrderAnalysis item) {

        if (item == null) {
            return null;
        }
        MerchantStoreItemOrderAnalysisDTO merchantStoreItemOrderAnalysisDTO = new MerchantStoreItemOrderAnalysisDTO();
        merchantStoreItemOrderAnalysisDTO.setAverageOrderPeriod(item.getAverageOrderPeriod());
        merchantStoreItemOrderAnalysisDTO.setAverageOrderPeriodUpperPeriod(item.getAverageOrderPeriodUpperPeriod());
        merchantStoreItemOrderAnalysisDTO.setOrderAmount(item.getOrderAmount());
        merchantStoreItemOrderAnalysisDTO.setOrderAmountUpperPeriod(item.getOrderAmountUpperPeriod());
        merchantStoreItemOrderAnalysisDTO.setOrderPrice(item.getOrderPrice());
        merchantStoreItemOrderAnalysisDTO.setOrderPriceUpperPeriod(item.getOrderPriceUpperPeriod());
        merchantStoreItemOrderAnalysisDTO.setLastOrderTime(item.getLastOrderTime());
        merchantStoreItemOrderAnalysisDTO.setLastOrderAmount(item.getLastOrderAmount());
        merchantStoreItemOrderAnalysisDTO.setLastOrderPrice(item.getLastOrderPrice());
        merchantStoreItemOrderAnalysisDTO.setStoreId(item.getStoreId());
        merchantStoreItemOrderAnalysisDTO.setTitle(item.getTitle());
        return merchantStoreItemOrderAnalysisDTO;
    }

    public static MerchantStoreItemOrderAnalysisExcelDTO toExcelDTO(MerchantStoreItemOrderAnalysis data) {

        if (data == null) {
            return null;
        }
        MerchantStoreItemOrderAnalysisExcelDTO merchantStoreItemOrderAnalysisExcelDTO = new MerchantStoreItemOrderAnalysisExcelDTO();
        merchantStoreItemOrderAnalysisExcelDTO.setId(data.getId());
        merchantStoreItemOrderAnalysisExcelDTO.setTenantId(data.getTenantId());
        merchantStoreItemOrderAnalysisExcelDTO.setTimeTag(data.getTimeTag());
        merchantStoreItemOrderAnalysisExcelDTO.setType(data.getType());
        merchantStoreItemOrderAnalysisExcelDTO.setTitle(data.getTitle());
        merchantStoreItemOrderAnalysisExcelDTO.setStoreId(data.getStoreId());
        merchantStoreItemOrderAnalysisExcelDTO.setAverageOrderPeriod(data.getAverageOrderPeriod());
        merchantStoreItemOrderAnalysisExcelDTO.setAverageOrderPeriodLastPeriod(data.getAverageOrderPeriodLastPeriod());
        merchantStoreItemOrderAnalysisExcelDTO.setAverageOrderPeriodUpperPeriod(Objects.isNull(data.getAverageOrderPeriodUpperPeriod()) ? "-" : data.getAverageOrderPeriodUpperPeriod().toPlainString() + "%");
        merchantStoreItemOrderAnalysisExcelDTO.setOrderAmount(data.getOrderAmount());
        merchantStoreItemOrderAnalysisExcelDTO.setOrderAmountLastPeriod(data.getOrderAmountLastPeriod());
        merchantStoreItemOrderAnalysisExcelDTO.setOrderAmountUpperPeriod(Objects.isNull(data.getOrderAmountUpperPeriod()) ? "-" : data.getOrderAmountUpperPeriod().toPlainString() + "%");
        merchantStoreItemOrderAnalysisExcelDTO.setOrderPriceLastPeriod(data.getOrderPriceLastPeriod());
        merchantStoreItemOrderAnalysisExcelDTO.setOrderPrice(data.getOrderPrice());
        merchantStoreItemOrderAnalysisExcelDTO.setOrderPriceUpperPeriod(Objects.isNull(data.getOrderPriceUpperPeriod()) ? "-" : data.getOrderPriceUpperPeriod().toPlainString() + "%");
        merchantStoreItemOrderAnalysisExcelDTO.setLastOrderTime(data.getLastOrderTime());
        merchantStoreItemOrderAnalysisExcelDTO.setLastOrderAmount(data.getLastOrderAmount());
        merchantStoreItemOrderAnalysisExcelDTO.setLastOrderPrice(data.getLastOrderPrice());
        return merchantStoreItemOrderAnalysisExcelDTO;
    }

    public static MerchantStoreOrderAnalysisExcelDTO toExcelDTO(MerchantStoreOrderAnalysis data) {

        if (data == null) {
            return null;
        }
        MerchantStoreOrderAnalysisExcelDTO merchantStoreOrderAnalysisExcelDTO = new MerchantStoreOrderAnalysisExcelDTO();
        merchantStoreOrderAnalysisExcelDTO.setId(data.getId());
        merchantStoreOrderAnalysisExcelDTO.setTenantId(data.getTenantId());
        merchantStoreOrderAnalysisExcelDTO.setTimeTag(data.getTimeTag());
        merchantStoreOrderAnalysisExcelDTO.setType(data.getType());
        merchantStoreOrderAnalysisExcelDTO.setStoreId(data.getStoreId());
        merchantStoreOrderAnalysisExcelDTO.setAverageOrderPeriod(data.getAverageOrderPeriod());
        merchantStoreOrderAnalysisExcelDTO.setAverageOrderPeriodLastPeriod(data.getAverageOrderPeriodLastPeriod());
        merchantStoreOrderAnalysisExcelDTO.setAverageOrderPeriodUpperPeriod(Objects.isNull(data.getAverageOrderPeriodUpperPeriod()) ? "-" : data.getAverageOrderPeriodUpperPeriod().toPlainString() + "%");
        merchantStoreOrderAnalysisExcelDTO.setOrderAmountLastPeriod(data.getOrderAmountLastPeriod());
        merchantStoreOrderAnalysisExcelDTO.setOrderAmount(data.getOrderAmount());
        merchantStoreOrderAnalysisExcelDTO.setOrderAmountUpperPeriod(Objects.isNull(data.getOrderAmountUpperPeriod()) ? "-" : data.getOrderAmountUpperPeriod().toPlainString() + "%");
        merchantStoreOrderAnalysisExcelDTO.setOrderPrice(data.getOrderPrice());
        merchantStoreOrderAnalysisExcelDTO.setOrderPriceLastPeriod(data.getOrderPriceLastPeriod());
        merchantStoreOrderAnalysisExcelDTO.setOrderPriceUpperPeriod(Objects.isNull(data.getOrderPriceUpperPeriod()) ? "-" : data.getOrderPriceUpperPeriod().toPlainString() + "%");
        merchantStoreOrderAnalysisExcelDTO.setLastOrderTime(data.getLastOrderTime());
        merchantStoreOrderAnalysisExcelDTO.setLastOrderAmount(data.getLastOrderAmount());
        merchantStoreOrderAnalysisExcelDTO.setLastOrderPrice(data.getLastOrderPrice());
        return merchantStoreOrderAnalysisExcelDTO;
    }

    public static MerchantStoreOrderProportionAnalysisDTO toDTO(MerchantStoreOrderProportionAnalysis data) {

        if (data == null) {
            return null;
        }
        MerchantStoreOrderProportionAnalysisDTO merchantStoreOrderProportionAnalysisDTO = new MerchantStoreOrderProportionAnalysisDTO();
        merchantStoreOrderProportionAnalysisDTO.setOrderAmount(data.getOrderAmount());
        merchantStoreOrderProportionAnalysisDTO.setOrderAmountProportion(data.getOrderAmountProportion());
        merchantStoreOrderProportionAnalysisDTO.setOrderAmountProportionUpperPeriod(data.getOrderAmountProportionUpperPeriod());
        merchantStoreOrderProportionAnalysisDTO.setOrderPrice(data.getOrderPrice());
        merchantStoreOrderProportionAnalysisDTO.setOrderPriceProportion(data.getOrderPriceProportion());
        merchantStoreOrderProportionAnalysisDTO.setOrderPriceProportionUpperPeriod(data.getOrderPriceProportionUpperPeriod());
        merchantStoreOrderProportionAnalysisDTO.setStoreId(data.getStoreId());
        merchantStoreOrderProportionAnalysisDTO.setStoreType(data.getStoreType());
        merchantStoreOrderProportionAnalysisDTO.setGroupName(data.getStoreGroupName());
        return merchantStoreOrderProportionAnalysisDTO;
    }

    public static MerchantStoreOrderProportionAnalysisExcelDTO toExcelDTO(MerchantStoreOrderProportionAnalysis data) {

        if (data == null) {
            return null;
        }
        MerchantStoreOrderProportionAnalysisExcelDTO merchantStoreOrderProportionAnalysisExcelDTO = new MerchantStoreOrderProportionAnalysisExcelDTO();
        merchantStoreOrderProportionAnalysisExcelDTO.setTenantId(data.getTenantId());
        merchantStoreOrderProportionAnalysisExcelDTO.setTitle(data.getTitle());
        merchantStoreOrderProportionAnalysisExcelDTO.setStoreId(data.getStoreId());
        merchantStoreOrderProportionAnalysisExcelDTO.setOrderAmount(data.getOrderAmount());
        merchantStoreOrderProportionAnalysisExcelDTO.setOrderAmountProportion(Objects.isNull(data.getOrderAmountProportion()) ? "-" : data.getOrderAmountProportion().toPlainString() + "%");
        merchantStoreOrderProportionAnalysisExcelDTO.setOrderAmountProportionUpperPeriod(Objects.isNull(data.getOrderAmountProportionUpperPeriod()) ? "-" : data.getOrderAmountProportionUpperPeriod().toPlainString() + "%");
        merchantStoreOrderProportionAnalysisExcelDTO.setOrderPrice(data.getOrderPrice());
        merchantStoreOrderProportionAnalysisExcelDTO.setOrderPriceProportion(Objects.isNull(data.getOrderPriceProportion()) ? "-" : data.getOrderPriceProportion().toPlainString() + "%");
        merchantStoreOrderProportionAnalysisExcelDTO.setOrderPriceProportionUpperPeriod(Objects.isNull(data.getOrderPriceProportionUpperPeriod()) ? "-" : data.getOrderPriceProportionUpperPeriod().toPlainString() + "%");
        return merchantStoreOrderProportionAnalysisExcelDTO;
    }
}
