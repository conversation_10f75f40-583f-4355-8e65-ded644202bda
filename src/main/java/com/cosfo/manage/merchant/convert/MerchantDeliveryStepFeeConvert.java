package com.cosfo.manage.merchant.convert;

import com.cosfo.manage.merchant.model.po.DeliveryFeeCalRule;
import com.cosfo.manage.merchant.model.po.MerchantDeliveryFeeRule;
import com.cosfo.manage.merchant.model.po.MerchantDeliveryStepFee;
import com.cosfo.manage.merchant.model.vo.MerchantDeliveryStartStepFeeRule;
import com.cosfo.manage.merchant.model.vo.MerchantDeliveryStepFeeVO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface MerchantDeliveryStepFeeConvert {
    MerchantDeliveryStepFeeConvert INSTANCE = Mappers.getMapper(MerchantDeliveryStepFeeConvert.class);

    @Mapping(target = "startStepFeeRule", source = "deliveryfeeCalRule")
    MerchantDeliveryStepFeeVO from(MerchantDeliveryStepFee stepFee);
    List<MerchantDeliveryStepFeeVO> from(List<MerchantDeliveryStepFee> stepFeeList);

    MerchantDeliveryStartStepFeeRule fromEntity(DeliveryFeeCalRule deliveryFeeCalRule);

    @Mapping(target = "stepFactor", source = "stepFactor", defaultValue = "1")
    DeliveryFeeCalRule voToEntity(MerchantDeliveryStartStepFeeRule merchantDeliveryStartStepFeeRule);

    @Mapping(target = "deliveryfeeCalRule", source = "startStepFeeRule")
    MerchantDeliveryStepFee voToEntity(MerchantDeliveryStepFeeVO vo, @Context MerchantDeliveryFeeRule context);

    List<MerchantDeliveryStepFee> voListToList(List<MerchantDeliveryStepFeeVO> voList, @Context MerchantDeliveryFeeRule context);

    @AfterMapping
    default void handleRoleId(@MappingTarget MerchantDeliveryStepFee stepFee, @Context MerchantDeliveryFeeRule context) {
        stepFee.setRuleId(context.getId());
        stepFee.setTenantId(context.getTenantId());
        stepFee.setFeeRule(context.getFreeDeliveryType());
    }

}
