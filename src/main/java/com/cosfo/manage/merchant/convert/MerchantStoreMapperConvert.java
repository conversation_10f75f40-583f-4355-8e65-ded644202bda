package com.cosfo.manage.merchant.convert;

import com.cosfo.manage.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreQueryDTO;
import com.cosfo.manage.merchant.model.po.MerchantStore;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreDomainCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStorePageQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndAddressResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStorePageResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-05-26
 * @Description:
 */
@Mapper
public interface MerchantStoreMapperConvert {

    MerchantStoreMapperConvert INSTANCE = Mappers.getMapper(MerchantStoreMapperConvert.class);

    /**
     * result转实体
     * @param merchantStoreResultResp
     * @return
     */
    MerchantStore respToMerchantStore(MerchantStoreResultResp merchantStoreResultResp);

    /**
     * result列表转dto列表
     * @param resultResps
     * @return
     */
    List<MerchantStoreDTO> respListToMerchantStoreDtoList(List<MerchantStoreResultResp> resultResps);

    /**
     * result列表转实体列表
     * @param merchantStoreList
     * @return
     */
    List<MerchantStore> respListToMerchantStoreList(List<MerchantStoreResultResp> merchantStoreList);

    /**
     * dto转queryReq
     * @param storeQueryDTO
     * @return
     */
    MerchantStorePageQueryReq queryDtoToQueryReq(MerchantStoreQueryDTO storeQueryDTO);

    /**
     * storeAddressResp列表转dto列表
     * @param list
     * @return
     */
    List<MerchantStoreDTO> storeAddressRespListToDtoList(List<MerchantStoreAndAddressResultResp> list);

    /**
     * dto转commandReq
     * @param storeDTO
     * @return
     */
    MerchantStoreCommandReq dtoToStoreCommandReq(MerchantStoreDTO storeDTO);

    /**
     * pageResult转dto
     * @param list
     * @return
     */
    List<MerchantStoreDTO> pageRespListToMerchantStoreDtoList(List<MerchantStorePageResultResp> list);

    /**
     * result转DTO
     * @param storeResp
     * @return
     */
    MerchantStoreDTO convert2StoreDto(MerchantStoreResultResp storeResp);


    /**
     * 实体转DTO
     * @param store
     * @return
     */
    MerchantStoreDTO convert2StoreDto(MerchantStore store);
}
