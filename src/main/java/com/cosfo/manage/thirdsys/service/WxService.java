package com.cosfo.manage.thirdsys.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.manage.common.constant.FanTaiTenant;
import com.cosfo.manage.facade.AuthWechatFacade;
import com.cosfo.manage.facade.MessageServiceFacade;
import com.cosfo.manage.thirdsys.model.WxNotifyMessageVO;
import com.cosfo.message.client.enums.JumpUrlTypeEnum;
import com.cosfo.message.client.enums.MessageContentTypeEnum;
import com.cosfo.message.client.enums.MsgBodyTypeEnum;
import com.cosfo.message.client.enums.TemplateWechatEnum;
import com.cosfo.message.client.req.MessageBodyReq;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.cosfo.manage.thirdsys.enums.WxEeventType;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;

@Component
@Slf4j
public class WxService {

    @Autowired
    private AuthWechatFacade authWechatFacade;
    @Resource
    private MessageServiceFacade messageServiceFacade;

    public String dealNotify(String xml) {
        WxNotifyMessageVO wxServiceMsgDto = analysisXmlToMsgDTO(xml);
        if (wxServiceMsgDto == null || StrUtil.isEmpty(wxServiceMsgDto.getMsgType()) || StrUtil.isEmpty(wxServiceMsgDto.getEvent())) {
            return xml;
        }
        WxEeventType eventType = WxEeventType.wrap(wxServiceMsgDto.getEvent());
        log.info("接收微信请求：{}", JSONUtil.toJsonStr(wxServiceMsgDto));
        if (eventType == null){
            return null;
        }
        String openId = wxServiceMsgDto.getFromUserName ();
        if (eventType.code.equals(WxEeventType.SCAN.code) &&
                !StringUtils.isEmpty(wxServiceMsgDto.getEventKey())){
            String phone = wxServiceMsgDto.getEventKey ();
            bindWechatCare4FTGYL (openId,phone);
        }else if (eventType.code.equals(WxEeventType.SUBSCRIBE.code) &&
                !StringUtils.isEmpty(wxServiceMsgDto.getEventKey())
                && wxServiceMsgDto.getEventKey().startsWith("qrscene_")){
            String phone = wxServiceMsgDto.getEventKey ().split ("_")[1];
            bindWechatCare4FTGYL (openId,phone);
        }else if(eventType.code.equals(WxEeventType.UNSUBSCRIBE.code)){
            authWechatFacade.closeWechatCare4FTGYLByOpenId (openId, FanTaiTenant.TENANT_ID);
        }
        return null;
    }

    private void bindWechatCare4FTGYL(String openId, String phone) {
        // 查询该手机号是否已经绑定过
        Map<String, String> map = authWechatFacade.queryAuthMapRespByPhones4FTGYL(Collections.singletonList(phone), FanTaiTenant.TENANT_ID);
        if (CollectionUtil.isNotEmpty(map) && ObjectUtil.isNotEmpty(map.get(phone))) {
            // 关注码已失效，请生成新的关注码后，再扫描一次试试
            // 调用消息中心发送客服消息
            sendCustomMessage("关注码已失效，请生成新的关注码后，再扫描一次试试", openId);
        } else {
            try {
                authWechatFacade.bindWechatCare4FTGYL(openId, phone, FanTaiTenant.TENANT_ID);
                // 发送成功消息 文案=关注成功，后续可以接收订单通知消息
                sendCustomMessage("关注成功，后续可以接收订单通知消息", openId);
            } catch (Exception e) {
                // 关注码已失效，请生成新的关注码后，再扫描一次试试
                sendCustomMessage("关注码已失效，请生成新的关注码后，再扫描一次试试", openId);
            }
        }
    }

    /**
     * 发送客服消息
     *
     * @param content
     * @return
     */
    private void sendCustomMessage(String content, String openId) {
        MessageBodyReq messageBodyReq = new MessageBodyReq();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("content", content);
        messageBodyReq.setData(jsonObject.toJSONString());
        messageBodyReq.setMsgBodyType(MsgBodyTypeEnum.TEXT.getType());
        messageBodyReq.setContentType(MessageContentTypeEnum.CUSTOM_MESSAGE.getType());
        messageServiceFacade.batchSendFTWechatOaMessage(FanTaiTenant.TENANT_ID, Collections.singletonList(openId), messageBodyReq);
    }


    /**
     * 解析xml 返回dto
     *
     * @param xml io=>xml
     * @return dto
     */
    private WxNotifyMessageVO analysisXmlToMsgDTO(String xml) {
        if (StrUtil.isEmpty(xml)) {
            return null;
        }
        String toUserName = xmlSubBetween(xml, "<ToUserName>", "</ToUserName>");
        String fromUserName = xmlSubBetween(xml, "<FromUserName>", "</FromUserName>");
        String createTime = xmlSubBetween(xml, "<CreateTime>", "</CreateTime>");
        String msgType = xmlSubBetween(xml, "<MsgType>", "</MsgType>");
        String event = xmlSubBetween(xml, "<Event>", "</Event>");
        String content = xmlSubBetween(xml, "<Content>", "</Content>");
        String eventKey = xmlSubBetween(xml, "<EventKey>", "</EventKey>");
        WxNotifyMessageVO wxNotifyMessageVO = new WxNotifyMessageVO ();
        wxNotifyMessageVO.setToUserName(toUserName);
        wxNotifyMessageVO.setFromUserName(fromUserName);
        wxNotifyMessageVO.setCreateTime(StrUtil.isEmpty(createTime) ? 0L : Long.parseLong(createTime));
        wxNotifyMessageVO.setMsgType(msgType);
        wxNotifyMessageVO.setEvent(event);
        wxNotifyMessageVO.setEventKey(eventKey);
        return wxNotifyMessageVO;
    }


    /**
     * 截取xml标签，注意：标签内容可能包含CDATA转义府，需要替换
     *
     * @param xml    xml
     * @param before 前符号
     * @param after  后符号
     * @return xml标签内容
     */
    private String xmlSubBetween(String xml, String before, String after) {
        String CDATA_CHARACTER = "<![CDATA[";
        String LEFT_CHARACTER = "]]>";

        String xmlContent = StrUtil.subBetween(xml, before, after);
        if (xmlContent == null || !xmlContent.contains(LEFT_CHARACTER)) {
            return xmlContent;
        }
        return StrUtil.subBetween(xmlContent, CDATA_CHARACTER, LEFT_CHARACTER);
    }
}
