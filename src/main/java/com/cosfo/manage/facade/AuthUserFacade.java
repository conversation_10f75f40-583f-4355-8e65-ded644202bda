package com.cosfo.manage.facade;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.converter.AuthMenuPurviewMapper;
import com.cosfo.manage.common.converter.AuthRoleMapper;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.facade.convert.Convert;
import com.cosfo.manage.facade.convert.UserBaseConvert;
import com.cosfo.manage.facade.dto.AuthUseLoginDTO;
import com.cosfo.manage.tenant.convert.RoleConvert;
import com.cosfo.manage.tenant.model.dto.MenuPurviewDTO;
import com.cosfo.manage.tenant.model.dto.RoleDTO;
import com.cosfo.manage.tenant.model.dto.RoleQueryDTO;
import com.cosfo.manage.tenant.model.dto.TenantAccountDTO;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.*;
import net.xianmu.authentication.client.dto.user.AuthUserLastUpdatePwdTimeResp;
import net.xianmu.authentication.client.enums.LoginLockEnum;
import net.xianmu.authentication.client.input.AuthRoleQueryVO;
import net.xianmu.authentication.client.input.AuthRoleUpdateVO;
import net.xianmu.authentication.client.input.PurviewWeighVO;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.provider.AuthPurviewProvider;
import net.xianmu.authentication.client.input.user.AuthUserQueryInput;
import net.xianmu.authentication.client.provider.AuthBaseUserProvider;
import net.xianmu.authentication.client.provider.AuthUserProvider;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.input.PageSortInput;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.common.user.UserBase;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AuthUserFacade {

    @DubboReference
    private AuthUserProvider authUserProvider;
    @DubboReference
    private AuthBaseUserProvider authBaseUserProvider;



    /**
     * 获取角色列表
     *
     * @return
     */
    public PageInfo<RoleDTO> queryRoleByPage(RoleQueryDTO roleQueryDTO) {
        Long tenantId = UserLoginContextUtils.getTenantId();
        Long authUserId = UserLoginContextUtils.getAuthUserId();
        AuthRoleQueryVO authRoleQueryVO = AuthRoleMapper.INSTANCE.ToAuthRoleQueryVO(roleQueryDTO, tenantId);
        if (StringUtils.isNotBlank(roleQueryDTO.getUpdateTimeSort())) {
            PageSortInput pageSortInput = new PageSortInput();
            pageSortInput.setOrderBy(roleQueryDTO.getUpdateTimeSort());
            pageSortInput.setSortBy("update_time");
            authRoleQueryVO.setSortList(Lists.newArrayList(pageSortInput));
        }
        log.info("rpc-req authUserProvider.roleList, req = {}, userId={}, tenantId={}", JSON.toJSONString(authRoleQueryVO), authUserId, tenantId);
        DubboResponse<PageInfo<AuthRoleDTO>> response = authUserProvider.roleList(SystemOriginEnum.COSFO_MANAGE, authUserId, tenantId, authRoleQueryVO);
        log.info("rpc-res authUserProvider.roleList, res={}", JSON.toJSONString(response));
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        PageInfo<AuthRoleDTO> pageData = response.getData();
        return AuthRoleMapper.INSTANCE.authRoleToRoleDTOPage(pageData);
    }

    /**
     * 新增角色
     *
     * @param roleDTO
     * @return
     */
    public Long addRole(RoleDTO roleDTO, Long tenantId) {
        tenantId = Objects.nonNull(tenantId) ? tenantId : UserLoginContextUtils.getTenantId();
        Long authUserId = UserLoginContextUtils.getAuthUserId();
        AuthRoleUpdateVO authRoleUpdateVO = AuthRoleMapper.INSTANCE.roleDTOToRoleUpdateVO(roleDTO, tenantId);
        log.info("rpc-req authUserProvider.addRole, req = {}, userId={}, tenantId={}", JSON.toJSONString(authRoleUpdateVO), authUserId, tenantId);
        DubboResponse<AuthRole> response = authUserProvider.addRole(SystemOriginEnum.COSFO_MANAGE, authUserId, tenantId, authRoleUpdateVO);
        log.info("rpc-res authUserProvider.addRole, res={}", JSON.toJSONString(response));
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        AuthRole data = response.getData();
        return data.getId();
    }

    /**
     * 新增角色
     *
     * @param roleDTO
     * @return
     */
    public Long addHistoryRole(RoleDTO roleDTO, Long tenantId) {
        AuthRoleUpdateVO authRoleUpdateVO = RoleConvert.convertToAuthRoleUpdateVO(roleDTO);
        DubboResponse<AuthRole> response = authUserProvider.addRole(SystemOriginEnum.COSFO_MANAGE, null, tenantId, authRoleUpdateVO);
        log.info("rpc-res authUserProvider.addRole, res={}", JSON.toJSONString(response));
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        AuthRole data = response.getData();
        return data.getId();
    }

    /**
     * 修改角色
     *
     * @param roleDTO
     * @return
     */
    public Boolean updateRole(RoleDTO roleDTO) {
        Long tenantId = UserLoginContextUtils.getTenantId();
        Long authUserId = UserLoginContextUtils.getAuthUserId();
        AuthRoleUpdateVO authRoleUpdateVO = AuthRoleMapper.INSTANCE.roleDTOToRoleUpdateVO(roleDTO, tenantId);
        log.info("rpc-req authUserProvider.updateRole, req = {}, userId={}, tenantId={}", JSON.toJSONString(authRoleUpdateVO), authUserId, tenantId);
        DubboResponse<Boolean> response = authUserProvider.updateRole(SystemOriginEnum.COSFO_MANAGE, authUserId, tenantId, authRoleUpdateVO);
        log.info("rpc-res authUserProvider.updateRole, res={}", JSON.toJSONString(response));
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        return response.getData();
    }

    /**
     * 删除角色
     *
     * @param roleId
     * @return
     */
    public Boolean delRole(Long roleId) {
        Long tenantId = UserLoginContextUtils.getTenantId();
        Long authUserId = UserLoginContextUtils.getAuthUserId();
        log.info("rpc-req authUserProvider.delRole, roleId = {}, userId={}, tenantId={}", roleId, authUserId, tenantId);
        DubboResponse<Boolean> response = authUserProvider.deleteRole(SystemOriginEnum.COSFO_MANAGE, authUserId, tenantId, roleId);
        log.info("rpc-res authUserProvider.deleteRole, res={}", JSON.toJSONString(response));
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        return response.getData();
    }

    /**
     * 获取角色详情
     *
     * @param roleId
     * @return
     */
    public RoleDTO getRole(Long roleId) {
        Long tenantId = UserLoginContextUtils.getTenantId();
        Long authUserId = UserLoginContextUtils.getAuthUserId();
        log.info("rpc-req authUserProvider.roleDetail, roleId = {}, userId={}, tenantId={}", roleId, authUserId, tenantId);
        DubboResponse<AuthRoleDetailsDTO> response = authUserProvider.roleDetail(SystemOriginEnum.COSFO_MANAGE, authUserId, tenantId, roleId);
        log.info("rpc-res authUserProvider.roleDetail, res={}", JSON.toJSONString(response));
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        AuthRoleDetailsDTO detailDTO = response.getData();
        return AuthRoleMapper.INSTANCE.roleDetailDTOToRoleDTO(detailDTO);
    }

    /**
     * 获取菜单
     *
     * @return
     */
//    public List<MenuPurviewDTO> listMenuPurviewTree() {
//        log.info("rpc-req authUserProvider.getAuthMenuPurviews");
//        DubboResponse<List<AuthMenuPurviewDto>> response = authUserProvider.getAuthMenuPurviewDto(SystemOriginEnum.COSFO_MANAGE);
//        log.info("rpc-res authUserProvider.getAuthMenuPurviews, res={}", JSON.toJSONString(response));
//        if (!response.isSuccess()) {
//            throw new ProviderException(response.getMsg());
//        }
//        List<AuthMenuPurviewDto> data = response.getData();
//        return AuthMenuPurviewMapper.INSTANCE.menuPurviewDtoTomenuPurviewDTOList(data);
//    }
    /**
     * 获取菜单
     *
     * @return
     */
//    public List<MenuPurviewDTO> listMenuPurview() {
//        log.info("rpc-req authUserProvider.getAuthMenuPurviews");
//        DubboResponse<List<AuthMenuPurview>> response = authUserProvider.getAuthMenuPurviews(SystemOriginEnum.COSFO_MANAGE);
//        log.info("rpc-res authUserProvider.getAuthMenuPurviews, res={}", JSON.toJSONString(response));
//        if (!response.isSuccess()) {
//            throw new ProviderException(response.getMsg());
//        }
//        List<AuthMenuPurview> data = response.getData();
//        return AuthMenuPurviewMapper.INSTANCE.menuPurviewTomenuPurviewDTOList(data);
//    }

    /**
     * 获取当前用户菜单
     * @return
     */
    public List<MenuPurviewDTO> listUserMenuPurview() {
        Long tenantId = UserLoginContextUtils.getTenantId();
        Long authUserId = UserLoginContextUtils.getAuthUserId();
        List<AuthMenuPurview> data = listUserMenuPurviewById(tenantId, authUserId);
        return AuthMenuPurviewMapper.INSTANCE.menuPurviewTomenuPurviewDTOList(data);
    }

    public List<MenuPurviewDTO> listUserMenuPurview(Long tenantId, Long authUserId) {
        List<AuthMenuPurview> data = listUserMenuPurviewById(tenantId, authUserId);
        return AuthMenuPurviewMapper.INSTANCE.menuPurviewTomenuPurviewDTOList(data);
    }

    /**
     * 获取当前用户二级菜单
     */
    public List<MenuPurviewDTO> listUserSecondaryMenuPurview() {
        List<MenuPurviewDTO> fullMenu = listUserMenuPurview();

        // 获取一级菜单id
        List<Long> primaryMenuIds = fullMenu.stream()
                .filter(menu -> menu.getParentId() == 0)
                .map(MenuPurviewDTO::getId)
                .collect(Collectors.toList());

        // 使用一级菜单id过滤二级菜单
        List<MenuPurviewDTO> secondaryMenu = fullMenu.stream()
                .filter(menu -> primaryMenuIds.contains(Long.valueOf(menu.getParentId())))
                .collect(Collectors.toList());

        return secondaryMenu;
    }

    /**
     * 获取当前用户菜单
     *
     * @return
     */
    public List<AuthMenuPurview> listUserMenuPurviewById(Long tenantId, Long authUserId) {
        log.info("rpc-req authUserProvider.getAuthMenuPurviews");
        DubboResponse<List<AuthMenuPurview>> response = authUserProvider.getAuthMenuPurviews(SystemOriginEnum.COSFO_MANAGE, tenantId, authUserId);
        log.info("rpc-res authUserProvider.getAuthMenuPurviews, res={}", JSON.toJSONString(response));
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        return response.getData();
    }

    /**
     * 获取当前用户二级菜单
     */
    public List<AuthMenuPurview> listUserSecondaryMenuPurviewById(Long tenantId, Long authUserId) {
        List<AuthMenuPurview> fullMenu = listUserMenuPurviewById(tenantId, authUserId);

        // 获取一级菜单id
        List<Long> primaryMenuIds = fullMenu.stream()
                .filter(menu -> menu.getParentId() == 0)
                .map(AuthMenuPurview::getId)
                .collect(Collectors.toList());

        // 使用一级菜单id过滤二级菜单
        return fullMenu.stream()
                .filter(menu -> primaryMenuIds.contains(Long.valueOf(menu.getParentId())))
                .collect(Collectors.toList());
    }

    /**
     * 新增菜单
     *
     * @param menuPurviewDTO
     * @return
     */
    public Boolean addMenuPurview(MenuPurviewDTO menuPurviewDTO) {
        Long tenantId = UserLoginContextUtils.getTenantId();
        Long authUserId = UserLoginContextUtils.getAuthUserId();
        AuthMenuPurview menuPurview = AuthMenuPurviewMapper.INSTANCE.dtoToAuthMenuPurview(menuPurviewDTO);
        log.info("rpc-res authUserProvider.addPurviews, req={}, userId={}, tenantId={}", JSON.toJSONString(menuPurview), authUserId, tenantId);
        DubboResponse<Boolean> response = authUserProvider.addPurviews(SystemOriginEnum.COSFO_MANAGE, authUserId, tenantId, menuPurview);
        log.info("rpc-res authUserProvider.addPurviews, res={}", JSON.toJSONString(response));
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        return response.getData();
    }

    /**
     * 更新菜单
     *
     * @param menuPurviewDTO
     * @return
     */
    public Boolean updateMenuPurview(MenuPurviewDTO menuPurviewDTO) {
        Long tenantId = UserLoginContextUtils.getTenantId();
        Long authUserId = UserLoginContextUtils.getAuthUserId();
        AuthMenuPurview menuPurview = AuthMenuPurviewMapper.INSTANCE.dtoToAuthMenuPurview(menuPurviewDTO);
        log.info("rpc-res authUserProvider.updatePurviews, req={}, userId={}, tenantId={}", JSON.toJSONString(menuPurview), authUserId, tenantId);
        DubboResponse<Boolean> response = authUserProvider.updatePurviews(SystemOriginEnum.COSFO_MANAGE, authUserId, tenantId, menuPurview);
        log.info("rpc-res authUserProvider.updatePurviews, res={}", JSON.toJSONString(response));
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        return response.getData();
    }

    /**
     * 删除菜单
     *
     * @param purviewId
     * @return
     */
    public Boolean delMenuPurview(Long purviewId) {
        Long tenantId = UserLoginContextUtils.getTenantId();
        Long authUserId = UserLoginContextUtils.getAuthUserId();
        log.info("rpc-res authUserProvider.deletePurviews,userId={}, purviewId={}", authUserId, purviewId);
        DubboResponse<Boolean> response = authUserProvider.deletePurviews(SystemOriginEnum.COSFO_MANAGE, authUserId, purviewId);
        log.info("rpc-res authUserProvider.deletePurviews, res={}", JSON.toJSONString(response));
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        return response.getData();
    }


    public Boolean sortMenuPurview(List<MenuPurviewDTO> menuPurviewDTOS) {
        Long tenantId = UserLoginContextUtils.getTenantId();
        Long authUserId = UserLoginContextUtils.getAuthUserId();
        List<PurviewWeighVO> purviewWeighVOS = AuthMenuPurviewMapper.INSTANCE.dtoToPurviewWeighVOList(menuPurviewDTOS);
        log.info("rpc-res authUserProvider.updateMenusWeigh, req={}, userId={}, tenantId={}", JSON.toJSONString(purviewWeighVOS), authUserId, tenantId);
        DubboResponse<Boolean> response = authUserProvider.updateMenusWeigh(SystemOriginEnum.COSFO_MANAGE, authUserId, tenantId, purviewWeighVOS);
        log.info("rpc-res authUserProvider.updateMenusWeigh, res={}", JSON.toJSONString(response));
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        return response.getData();
    }


    /**
     * 校验手机号和密码
     *
     * @param phone
     * @param password
     * @return
     */
    public AuthUseLoginDTO checkPhonePassword(String phone, String password) {
        DubboResponse<Boolean> response = authUserProvider.checkPhonePassword(SystemOriginEnum.COSFO_MANAGE, phone, password);
        if (!response.isSuccess()) {
            if (("BIZ-" + LoginLockEnum.TEMPORARY_LOCK.name).equals(response.getCode())) {
                AuthUseLoginDTO authUseLoginDTO = JSONObject.parseObject(response.getMsg(), AuthUseLoginDTO.class);
                authUseLoginDTO.setRemainCount(Math.max(authUseLoginDTO.getTotalCount() - authUseLoginDTO.getErrorCount(), NumberConstants.ZERO));
                authUseLoginDTO.setPasswordSuccess(false);
                return authUseLoginDTO;
            }
            log.info("校验用户信息失败，用户或密码错误");
            return AuthUseLoginDTO.builder().passwordSuccess(false).build();
        }

        return AuthUseLoginDTO.builder().passwordSuccess(response.getData()).build();
    }

    public Boolean checkPhonePasswordOld(String phone, String password) {
        DubboResponse<Boolean> response = authUserProvider.checkPhonePassword(SystemOriginEnum.COSFO_MANAGE, phone, password);
        if (!response.isSuccess()) {
            log.info("校验用户信息失败，用户或密码错误");
            return false;
        }

        return response.getData();
    }

    /**
     * 登录
     *
     * @param tenantId
     * @param phone
     * @param pwd
     * @Return
     */
    public String login(Long tenantId, String phone, String pwd){
        DubboResponse<AuthLoginDto> response = authUserProvider.loginBypwd(SystemOriginEnum.COSFO_MANAGE, tenantId, phone, pwd);
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }

        AuthLoginDto data = response.getData();
        return data.getToken();
    }

    /**
     * 更新用户信息
     *
     * @param tenantAccountDTO
     * @return
     */
    public Boolean updateUser(TenantAccountDTO tenantAccountDTO) {
        UserBase userBase = UserBaseConvert.convertToUserBase(tenantAccountDTO);
        DubboResponse<UserBase> response = authUserProvider.updateUser(SystemOriginEnum.COSFO_MANAGE, userBase);
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }

        return Boolean.TRUE;
    }

    /**
     * 创建用户
     *
     * @param tenantAccountDTO
     * @return
     */
    public Long createUser(TenantAccountDTO tenantAccountDTO){
        UserBase userBase = UserBaseConvert.convertToUserBase(tenantAccountDTO);
        DubboResponse<UserBase> response = authUserProvider.createUser(SystemOriginEnum.COSFO_MANAGE, userBase);
        if (!response.isSuccess()) {
            log.error("创建用户失败");
            throw new ProviderException(response.getMsg());
        }

        UserBase data = response.getData();
        return data.getId();
    }

    /**
     * 创建用户
     *
     * @param tenantAccountDTO
     * @return
     */
    public Long synchronizedUser(TenantAccountDTO tenantAccountDTO){
        UserBase userBase = UserBaseConvert.convertToUserBase(tenantAccountDTO);
        DubboResponse<UserBase> response = authUserProvider.createUser(SystemOriginEnum.COSFO_MANAGE, userBase, Boolean.TRUE);
        if (!response.isSuccess()) {
            log.error("创建用户失败");
            throw new ProviderException(response.getMsg());
        }

        UserBase data = response.getData();
        return data.getId();
    }

    /**
     * 根据角色id获取用户idList
     *
     * @param roleId
     * @return
     */
    public List<Long> getUserIdListByRoleId(Long roleId){
        DubboResponse<List<Long>> response = authUserProvider.getUserIdListByRoleId(roleId);
        if (!response.isSuccess()) {
            log.error("查询角色绑定的用户id失败");
            throw new ProviderException(response.getMsg());
        }

        return response.getData();
    }

    /**
     * 根据角色ids获取用户idList
     *
     * @param roleIds
     * @return
     */
    public List<Long> getUserIdListByRoleIds(List<Long> roleIds){
        DubboResponse<List<Long>> response = authUserProvider.getUserIdListByRoleIds(roleIds);
        if (!response.isSuccess()) {
            log.error("查询角色绑定的用户id失败");
            throw new ProviderException(response.getMsg());
        }

        return response.getData();
    }

    /**
     * 根据用户id list 获取下
     *
     * @param userIds
     * @return
     */
    public List<AuthUserRoleDto> getUserRoleByUserIds(List<Long> userIds){
        DubboResponse<List<AuthUserRoleDto>> response = authUserProvider.getUserRoleByUserList(userIds);
        if(!response.isSuccess()){
            log.error("根据用户id查询绑定角色列表失败");
            throw new ProviderException(response.getMsg());
        }

        return response.getData();
    }

    /**
     * 根据手机号登录
     *
     * @param tenantId
     * @param phone
     * @return
     */
    public String loginByPhone(Long tenantId, String phone){
        DubboResponse<AuthLoginDto> response = authUserProvider.loginByPhone(SystemOriginEnum.COSFO_MANAGE, tenantId, phone);
        if(!response.isSuccess()){
            log.error("切换租户登录失败");
            throw new ProviderException(response.getMsg());
        }

        AuthLoginDto data = response.getData();
        return data.getToken();
    }


    /**
     * 根据手机号登录
     *
     * @param tenantId
     * @param phone
     * @return
     */
    public String loginByUsername(Long tenantId, String username){
        DubboResponse<AuthLoginDto> response = authUserProvider.loginByUsername(SystemOriginEnum.COSFO_MANAGE, tenantId, username);
        if(!response.isSuccess()){
            log.error("切换租户登录失败");
            throw new ProviderException(response.getMsg());
        }

        AuthLoginDto data = response.getData();
        return data.getToken();
    }

    public LoginContextInfoDTO getTenantInfo(String token){
        DubboResponse<ShiroUser> response = authUserProvider.shiroUser(token);
        if(!response.isSuccess()){
            log.error("获取登录信息失败");
            throw new BizException(response.getMsg());
        }

        ShiroUser shiroUser = response.getData();
        LoginContextInfoDTO loginContextInfoDTO = Convert.convertToLoginContextInfoDTO(shiroUser);
        return loginContextInfoDTO;
    }

    /**
     * 查询手机号上次修改密码时间
     * @param authUserQueryInput
     * @return
     */
    public AuthUserLastUpdatePwdTimeResp queryLastUpdatePwdTime(AuthUserQueryInput authUserQueryInput) {
        DubboResponse<AuthUserLastUpdatePwdTimeResp> response = authBaseUserProvider.queryLastUpdatePwdTime(net.xianmu.common.enums.base.auth.SystemOriginEnum.COSFO_MANAGE, authUserQueryInput);
        if (!response.isSuccess()) {
            throw new BizException(response.getMsg());
        }

        return response.getData();
    }
}
