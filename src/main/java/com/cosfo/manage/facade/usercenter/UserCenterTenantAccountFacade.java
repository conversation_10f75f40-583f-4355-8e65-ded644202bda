package com.cosfo.manage.facade.usercenter;

import com.cosfo.manage.common.util.StringUtils;
import com.github.pagehelper.PageInfo;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.common.enums.SystemOriginEnum;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.tenant.provider.TenantAccountCommandProvider;
import net.xianmu.usercenter.client.tenant.provider.TenantAccountQueryProvider;
import net.xianmu.usercenter.client.tenant.req.TenantAccountCommandReq;
import net.xianmu.usercenter.client.tenant.req.TenantAccountListQueryReq;
import net.xianmu.usercenter.client.tenant.req.TenantAccountQueryReq;
import net.xianmu.usercenter.client.tenant.resp.TenantAccountResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserCenterTenantAccountFacade {

    @DubboReference
    private TenantAccountQueryProvider tenantAccountQueryProvider;
    @DubboReference
    private TenantAccountCommandProvider tenantAccountCommandProvider;


    /**
     * 获取租户账号信息
     *
     * @param authUserId
     * @return
     */
    public TenantAccountResultResp getTenantAccountInfo(Long authUserId) {
        DubboResponse<TenantAccountResultResp> response = tenantAccountQueryProvider.getTenantAccountVO(authUserId);
        if (!response.isSuccess()) {
            throw new BizException("获取账号信息失败");
        }
        return response.getData();
    }

    /**
     * 获取租户账号信息
     *
     * @param accountId
     * @return
     */
    public TenantAccountResultResp getTenantAccountInfoById(Long accountId) {
        DubboResponse<TenantAccountResultResp> response = tenantAccountQueryProvider.getTenantAccountById(accountId);
        if (!response.isSuccess()) {
            throw new BizException("获取账号信息失败");
        }
        return response.getData();
    }

    public PageInfo<TenantAccountResultResp> getTenantAccountInfoPage(TenantAccountListQueryReq queryReq, PageQueryReq pageQueryReq) {
        DubboResponse<PageInfo<TenantAccountResultResp>> response = tenantAccountQueryProvider.getTenantAccountsPage(queryReq, pageQueryReq);
        if (!response.isSuccess()) {
            throw new BizException("获取账号信息失败");
        }
        return response.getData();

    }

    public List<TenantAccountResultResp> getAvailableTenantAccountByTenantIdsAndPhone(List<Long> tenantIds, String phone) {
        DubboResponse<List<TenantAccountResultResp>> response = tenantAccountQueryProvider.getTenantAccountByTenantIdsAndPhone(tenantIds, phone);
        if (!response.isSuccess()) {
            throw new BizException("获取账号信息失败");
        }
        return response.getData();
    }

    public List<TenantAccountResultResp> getTenantAccountByTenantIdsAndPhone(Long tenantId, String phone) {
        TenantAccountQueryReq req = new TenantAccountQueryReq();
        req.setTenantId (tenantId);
        req.setPhone (phone);
        DubboResponse<List<TenantAccountResultResp>> response = tenantAccountQueryProvider.getTenantAccounts(req);
        if (!response.isSuccess()) {
            throw new BizException("获取账号信息失败");
        }
        return response.getData();
    }

    public List<TenantAccountResultResp> getTenantAccountByTenantIdsAndEmail(Long tenantId, String email) {
        TenantAccountQueryReq req = new TenantAccountQueryReq();
        req.setTenantId (tenantId);
        req.setEmail (email);
        DubboResponse<List<TenantAccountResultResp>> response = tenantAccountQueryProvider.getTenantAccounts(req);
        if (!response.isSuccess()) {
            throw new BizException("获取账号信息失败");
        }
        return response.getData();
    }

    public List<TenantAccountResultResp> getTenantAccountsByAuthUserIds(List<Long> authUserIdList) {
        DubboResponse<List<TenantAccountResultResp>> response = tenantAccountQueryProvider.getTenantAccountsByAuthUserIds(authUserIdList);
        if (!response.isSuccess()) {
            throw new BizException("获取账号信息失败");
        }
        return response.getData();
    }

    public List<TenantAccountResultResp> getTenantAccounts(TenantAccountQueryReq req) {
        DubboResponse<List<TenantAccountResultResp>> response = tenantAccountQueryProvider.getTenantAccounts(req);
        if (!response.isSuccess()) {
            throw new BizException("获取账号信息失败");
        }
        return response.getData();
    }

    public List<TenantAccountResultResp> getTenantAccountByUserIds(List<Long> userIds) {
        DubboResponse<List<TenantAccountResultResp>> response = tenantAccountQueryProvider.getTenantAccountsByAuthUserIds(userIds);
        if (!response.isSuccess()) {
            throw new BizException("获取账号信息失败");
        }
        return response.getData();
    }


    /**
     * 删除账号
     *
     * @param commandReq
     */
    public void delTenantAccount(TenantAccountCommandReq commandReq) {
        DubboResponse response = tenantAccountCommandProvider.remove(SystemOriginEnum.COSFO_OMS, commandReq);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"删除账号失败");
            throw new BizException(errorMsg);
        }
    }


    /**
     * 更新用户账号
     *
     * @param tenantIds
     * @param req
     */
    public void updateByTenantIdsAndAuthId(List<Long> tenantIds, TenantAccountCommandReq req) {
        DubboResponse response = tenantAccountCommandProvider.updateByTenantIdsAndAuthId(net.xianmu.common.enums.base.auth.SystemOriginEnum.COSFO_MANAGE, tenantIds, req);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"更新账号处理失败");
            throw new BizException(errorMsg);
        }
    }

    /**
     * 创建账号
     *
     * @param commandReq
     */
    public Long createTenantAccount(TenantAccountCommandReq commandReq) {
        DubboResponse<Long> response = tenantAccountCommandProvider.create(SystemOriginEnum.COSFO_MANAGE, commandReq);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"创建账号处理失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }


    /**
     * 获取租户账号信息 是否查缓存
     * @param authUserId
     * @param queryCache true-查缓存 false-不查
     * @return
     */
    public TenantAccountResultResp getTenantAccountInfoFromCache(Long authUserId, boolean queryCache) {
        if(!queryCache){
            TENANT_ACCOUNT_RESULT_RESP_CACHE.refresh(authUserId);
        }

        try {
            return TENANT_ACCOUNT_RESULT_RESP_CACHE.get(authUserId);
        } catch (Exception e) {
            log.warn("获取用户信息缓存异常，authUserId={}", authUserId, e);
        }
        return null;
    }

    private final LoadingCache<Long, TenantAccountResultResp> TENANT_ACCOUNT_RESULT_RESP_CACHE = CacheBuilder.newBuilder()
            .maximumSize(50)
            .recordStats()
            .expireAfterWrite(Duration.ofSeconds(600L))//10 分钟
            .build(new CacheLoader<Long, TenantAccountResultResp>() {
                @Override
                public TenantAccountResultResp load(Long authUserId) throws Exception {
                    DubboResponse<TenantAccountResultResp> response = tenantAccountQueryProvider.getTenantAccountVO(authUserId);
                    if (!response.isSuccess()) {
                        throw new BizException("获取账号信息失败");
                    }
                    return response.getData();
                }
            });
}

