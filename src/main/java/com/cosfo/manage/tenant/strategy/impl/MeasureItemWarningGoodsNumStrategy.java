package com.cosfo.manage.tenant.strategy.impl;

import com.cosfo.manage.common.context.TenantConfigStatusEnum;
import com.cosfo.manage.product.repository.ProductStockForewarningReportRepository;
import com.cosfo.manage.tenant.model.po.TenantMeasureItemResult;
import com.cosfo.manage.tenant.strategy.TenantMeasureItemStrategy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description:
 * @author: George
 * @date: 2023-11-23
 **/
@Service("measureItemWarningGoodsNumStrategy")
public class MeasureItemWarningGoodsNumStrategy implements TenantMeasureItemStrategy {

    @Resource
    private ProductStockForewarningReportRepository productStockForewarningReportRepository;

    @Override
    public void doMeasureItem(TenantMeasureItemResult result) {
        long warningSkuCnt = productStockForewarningReportRepository.queryWarningSkuCnt(result.getTenantId());
        String itemResult = String.format("今日库存预警货品数量（%s）", warningSkuCnt);
        result.setItemResult(itemResult);
        result.setItemResultState(TenantConfigStatusEnum.NORMAL.getStatus());
    }
}
