package com.cosfo.manage.tenant.strategy.impl;

import com.cosfo.manage.common.context.TenantConfigStatusEnum;
import com.cosfo.manage.facade.ordercenter.OrderStatisticsQueryFacade;
import com.cosfo.manage.tenant.model.po.TenantMeasureItemResult;
import com.cosfo.manage.tenant.strategy.TenantMeasureItemStrategy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description:
 * @author: George
 * @date: 2023-11-13
 **/
@Service
public class MeasureItemWaitDeliveryOrderStrategy implements TenantMeasureItemStrategy {

    @Resource
    private OrderStatisticsQueryFacade orderStatisticsQueryFacade;

    @Override
    public void doMeasureItem(TenantMeasureItemResult result) {
        Integer waitDeliveryOrderQuantity = orderStatisticsQueryFacade.getWaitDeliveryQuantity(result.getTenantId());
        String itemResult = String.format("%s（%s）", result.getItemTitle(), waitDeliveryOrderQuantity);
        result.setItemResult(itemResult);
        result.setItemResultState(waitDeliveryOrderQuantity > 0 ? TenantConfigStatusEnum.ABNORMAL.getStatus() : TenantConfigStatusEnum.NORMAL.getStatus());
    }
}
