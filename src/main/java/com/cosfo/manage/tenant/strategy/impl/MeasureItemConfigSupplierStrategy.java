package com.cosfo.manage.tenant.strategy.impl;

import com.cosfo.manage.common.context.TenantConfigStatusEnum;
import com.cosfo.manage.facade.SupplierFacade;
import com.cosfo.manage.facade.dto.SupplierInfoDTO;
import com.cosfo.manage.facade.input.SupplierQueryInput;
import com.cosfo.manage.tenant.model.po.TenantMeasureItemResult;
import com.cosfo.manage.tenant.strategy.TenantMeasureItemStrategy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: George
 * @date: 2023-11-09
 **/
@Service
public class MeasureItemConfigSupplierStrategy implements TenantMeasureItemStrategy {

    @Resource
    private SupplierFacade supplierFacade;
    @Override
    public void doMeasureItem(TenantMeasureItemResult result) {
        SupplierQueryInput supplierQueryInput = new SupplierQueryInput();
        supplierQueryInput.setTenantId(result.getTenantId());
        supplierQueryInput.setPageIndex(1);
        supplierQueryInput.setPageSize(1);
        List<SupplierInfoDTO> supplierList = supplierFacade.batchQuerySupplier(supplierQueryInput);
        boolean hasConfigured = supplierList.isEmpty();
        result.setItemResultState(hasConfigured ? TenantConfigStatusEnum.ABNORMAL.getStatus() : TenantConfigStatusEnum.NORMAL.getStatus());
        result.setItemResult(result.getItemTitle());
    }
}
