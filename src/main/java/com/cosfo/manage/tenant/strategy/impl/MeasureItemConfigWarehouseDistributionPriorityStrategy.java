package com.cosfo.manage.tenant.strategy.impl;

import com.cosfo.manage.common.context.TenantConfigStatusEnum;
import com.cosfo.manage.tenant.model.po.TenantMeasureItemResult;
import com.cosfo.manage.tenant.strategy.TenantMeasureItemStrategy;
import org.springframework.stereotype.Service;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2023-11-09
 **/
@Service
public class MeasureItemConfigWarehouseDistributionPriorityStrategy implements TenantMeasureItemStrategy {

    @Override
    public void doMeasureItem(TenantMeasureItemResult result) {
        // 有默认值，不需要检查
        result.setItemResult(result.getItemTitle());
        result.setItemResultState(TenantConfigStatusEnum.NORMAL.getStatus());
    }
}
