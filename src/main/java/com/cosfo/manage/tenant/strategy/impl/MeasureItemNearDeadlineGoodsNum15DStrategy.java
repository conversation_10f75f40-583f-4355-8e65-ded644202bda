package com.cosfo.manage.tenant.strategy.impl;

import cn.hutool.core.date.DateUtil;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.common.context.TenantConfigStatusEnum;
import com.cosfo.manage.common.context.holder.TenantMetricsSummaryContextHolder;
import com.cosfo.manage.tenant.model.po.TenantMeasureItemResult;
import com.cosfo.manage.tenant.model.po.TenantMetricsSummary;
import com.cosfo.manage.tenant.strategy.TenantMeasureItemStrategy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @description:
 * @author: George
 * @date: 2023-11-23
 **/
@Service("measureItemNearDeadlineGoodsNum15DStrategy")
public class MeasureItemNearDeadlineGoodsNum15DStrategy implements TenantMeasureItemStrategy {

    @Resource
    private TenantMetricsSummaryContextHolder tenantMetricsSummaryContextHolder;

    @Override
    public void doMeasureItem(TenantMeasureItemResult result) {
        String timeTag = DateUtil.yesterday().toString("yyyyMMdd");
        TenantMetricsSummary tenantMetricsSummary = tenantMetricsSummaryContextHolder.getTenantMetricsSummary(result.getTenantId(), timeTag);
        String nearDeadlineGoodsNum = Objects.isNull(tenantMetricsSummary.getNearDeadlineGoodsNum15d()) ? StringConstants.ZERO : String.valueOf(tenantMetricsSummary.getNearDeadlineGoodsNum15d());
        String itemResult = String.format("近15天临期货品数（%s）", nearDeadlineGoodsNum);
        result.setItemResult(itemResult);
        result.setItemResultState(TenantConfigStatusEnum.NORMAL.getStatus());
    }
}
