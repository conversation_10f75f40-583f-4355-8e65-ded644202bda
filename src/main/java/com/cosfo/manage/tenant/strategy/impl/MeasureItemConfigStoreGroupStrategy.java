package com.cosfo.manage.tenant.strategy.impl;

import com.cosfo.manage.common.context.TenantConfigStatusEnum;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreGroupFacade;
import com.cosfo.manage.tenant.model.po.TenantMeasureItemResult;
import com.cosfo.manage.tenant.strategy.TenantMeasureItemStrategy;
import com.github.pagehelper.PageInfo;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreGroupQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupPageResultResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description:
 * @author: George
 * @date: 2023-11-09
 **/
@Service
public class MeasureItemConfigStoreGroupStrategy implements TenantMeasureItemStrategy {

    @Resource
    private UserCenterMerchantStoreGroupFacade userCenterMerchantStoreGroupFacade;
    @Override
    public void doMeasureItem(TenantMeasureItemResult result) {
        PageQueryReq pageQueryReq = new PageQueryReq();
        pageQueryReq.setPageIndex(1);
        pageQueryReq.setPageSize(1);

        MerchantStoreGroupQueryReq req = new MerchantStoreGroupQueryReq();
        req.setTenantId(result.getTenantId());
        PageInfo<MerchantStoreGroupPageResultResp> merchantStoreGroupPage = userCenterMerchantStoreGroupFacade.getMerchantStoreGroupPage(req, pageQueryReq);
        long storeGroupSize = merchantStoreGroupPage.getTotal();
        boolean hasConfigured = storeGroupSize == 0;
        result.setItemResult(result.getItemTitle());
        result.setItemResultState(hasConfigured ? TenantConfigStatusEnum.ABNORMAL.getStatus() : TenantConfigStatusEnum.NORMAL.getStatus());
    }
}
