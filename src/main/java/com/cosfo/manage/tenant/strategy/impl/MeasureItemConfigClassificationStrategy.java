package com.cosfo.manage.tenant.strategy.impl;

import com.cofso.item.client.req.MarketClassificationQueryReq;
import com.cofso.item.client.resp.MarketClassificationResp;
import com.cosfo.manage.common.context.TenantConfigStatusEnum;
import com.cosfo.manage.facade.MarketClassificationFacade;
import com.cosfo.manage.tenant.model.po.TenantMeasureItemResult;
import com.cosfo.manage.tenant.strategy.TenantMeasureItemStrategy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: George
 * @date: 2023-11-09
 **/
@Service
public class MeasureItemConfigClassificationStrategy implements TenantMeasureItemStrategy {

    @Resource
    private MarketClassificationFacade marketClassificationFacade;

    @Override
    public void doMeasureItem(TenantMeasureItemResult result) {
        MarketClassificationQueryReq req = new MarketClassificationQueryReq();
        req.setTenantId(result.getTenantId());
        List<MarketClassificationResp> marketClassificationResps = marketClassificationFacade.queryClassificationByCondition(req);
        boolean hasConfigured = marketClassificationResps == null || marketClassificationResps.isEmpty();
        result.setItemResult(result.getItemTitle());
        result.setItemResultState(hasConfigured ? TenantConfigStatusEnum.ABNORMAL.getStatus() : TenantConfigStatusEnum.NORMAL.getStatus());
    }
}
