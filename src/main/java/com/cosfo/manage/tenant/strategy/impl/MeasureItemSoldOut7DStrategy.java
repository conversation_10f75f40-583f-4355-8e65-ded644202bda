package com.cosfo.manage.tenant.strategy.impl;

import cn.hutool.core.util.NumberUtil;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.common.context.TenantConfigStatusEnum;
import com.cosfo.manage.market.model.po.MarketItemOnSaleSoldOutDetail;
import com.cosfo.manage.report.repository.MarketItemOnSaleSoldOutDetailRepository;
import com.cosfo.manage.tenant.model.po.TenantMeasureItemResult;
import com.cosfo.manage.tenant.strategy.TenantMeasureItemStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import static java.math.BigDecimal.ROUND_HALF_UP;

/**
 * @description:
 * @author: George
 * @date: 2023-11-09
 **/
@Service("measureItemSoldOut7DStrategy")
@Slf4j
public class MeasureItemSoldOut7DStrategy implements TenantMeasureItemStrategy {

    @Resource
    private MarketItemOnSaleSoldOutDetailRepository marketItemOnSaleSoldOutDetailRepository;

    @Override
    public void doMeasureItem(TenantMeasureItemResult tenantMeasureItemResult) {
        String yesterday = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String sevenDaysBefore = LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        MarketItemOnSaleSoldOutDetail marketItemOnSaleSoldOutDetail = marketItemOnSaleSoldOutDetailRepository.querySummary(tenantMeasureItemResult.getTenantId(), sevenDaysBefore, yesterday);
        String soldOutRate = marketItemOnSaleSoldOutDetail.getOnSaleTime() == 0 ?
                StringConstants.SEPARATING_IN_LINE
                : NumberUtil.div(marketItemOnSaleSoldOutDetail.getSoldOutTime(), NumberUtil.div(marketItemOnSaleSoldOutDetail.getOnSaleTime(), NumberConstant.HUNDRED))
                .setScale(NumberConstant.TWO, ROUND_HALF_UP).toPlainString();
        String itemResult = String.format("%s %s%%", tenantMeasureItemResult.getItemTitle(), soldOutRate);
        tenantMeasureItemResult.setItemResult(itemResult);
        tenantMeasureItemResult.setItemResultState(TenantConfigStatusEnum.NORMAL.getStatus());
    }
}
