package com.cosfo.manage.tenant.strategy.impl;

import cn.hutool.core.date.DateUtil;
import com.cosfo.manage.common.context.TenantConfigStatusEnum;
import com.cosfo.manage.common.context.holder.TenantMetricsSummaryContextHolder;
import com.cosfo.manage.tenant.model.po.TenantMeasureItemResult;
import com.cosfo.manage.tenant.model.po.TenantMetricsSummary;
import com.cosfo.manage.tenant.strategy.TenantMeasureItemStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Optional;

/**
 * @description:
 * @author: George
 * @date: 2023-11-09
 **/
@Service("measureItemNoSaleItemRate7DStrategy")
@Slf4j
public class MeasureItemNoSaleItemRate7DStrategy implements TenantMeasureItemStrategy {

    @Resource
    private TenantMetricsSummaryContextHolder tenantMetricsSummaryContextHolder;
    @Override
    public void doMeasureItem(TenantMeasureItemResult tenantMeasureItemResult) {
        String timeTag = DateUtil.yesterday().toString("yyyyMMdd");
        TenantMetricsSummary tenantMetricsSummary = tenantMetricsSummaryContextHolder.getTenantMetricsSummary(tenantMeasureItemResult.getTenantId(), timeTag);
        String noSaleItemRate = Optional.ofNullable(tenantMetricsSummary.getNoSaleItemRate7d()).map(BigDecimal::toPlainString).orElse("0");
        String itemResult = String.format("%s %s%%", tenantMeasureItemResult.getItemTitle(), noSaleItemRate);
        tenantMeasureItemResult.setItemResult(itemResult);
        tenantMeasureItemResult.setItemResultState(TenantConfigStatusEnum.NORMAL.getStatus());
    }
}
