package com.cosfo.manage.tenant.strategy.impl;

import com.cosfo.manage.common.context.TenantConfigStatusEnum;
import com.cosfo.manage.facade.WarehouseStorageQueryFacade;
import com.cosfo.manage.tenant.model.po.TenantMeasureItemResult;
import com.cosfo.manage.tenant.strategy.TenantMeasureItemStrategy;
import net.summerfarm.wnc.client.enums.WarehouseSourceEnum;
import net.summerfarm.wnc.client.resp.WarehouseStorageResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: George
 * @date: 2023-11-09
 **/
@Service
public class MeasureItemConfigWarehouseStrategy implements TenantMeasureItemStrategy {

    @Resource
    private WarehouseStorageQueryFacade warehouseStorageQueryFacade;
    @Override
    public void doMeasureItem(TenantMeasureItemResult result) {
        List<WarehouseStorageResp> warehouseStorageResps = warehouseStorageQueryFacade.queryWarehouseStorageList(result.getTenantId(), WarehouseSourceEnum.SAAS_WAREHOUSE, null);
        boolean configFlag = warehouseStorageResps == null || warehouseStorageResps.isEmpty();
        result.setItemResultState(configFlag ? TenantConfigStatusEnum.ABNORMAL.getStatus() : TenantConfigStatusEnum.NORMAL.getStatus());
        result.setItemResult(result.getItemTitle());
    }
}
