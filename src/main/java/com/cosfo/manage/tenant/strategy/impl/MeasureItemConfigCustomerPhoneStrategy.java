package com.cosfo.manage.tenant.strategy.impl;

import com.cosfo.manage.common.context.TenantConfigStatusEnum;
import com.cosfo.manage.tenant.model.po.TenantMeasureItemResult;
import com.cosfo.manage.tenant.service.TenantService;
import com.cosfo.manage.tenant.strategy.TenantMeasureItemStrategy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

/**
 * @description:
 * @author: George
 * @date: 2023-11-09
 **/
@Service
public class MeasureItemConfigCustomerPhoneStrategy implements TenantMeasureItemStrategy {

    @Resource
    private TenantService tenantService;

    @Override
    public void doMeasureItem(TenantMeasureItemResult result) {
        boolean configCustomerPhoneFlag = CollectionUtils.isEmpty(tenantService.queryTenantCustomerPhone(result.getTenantId()));
        result.setItemResult(result.getItemTitle());
        result.setItemResultState(configCustomerPhoneFlag ? TenantConfigStatusEnum.ABNORMAL.getStatus() : TenantConfigStatusEnum.NORMAL.getStatus());
    }
}
