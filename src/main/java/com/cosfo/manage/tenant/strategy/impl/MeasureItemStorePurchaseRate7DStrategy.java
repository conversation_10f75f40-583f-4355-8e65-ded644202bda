package com.cosfo.manage.tenant.strategy.impl;

import cn.hutool.core.date.DateUtil;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.common.context.TenantConfigStatusEnum;
import com.cosfo.manage.common.context.holder.TenantMetricsSummaryContextHolder;
import com.cosfo.manage.tenant.model.po.TenantMeasureItemResult;
import com.cosfo.manage.tenant.model.po.TenantMetricsSummary;
import com.cosfo.manage.tenant.strategy.TenantMeasureItemStrategy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @description:
 * @author: George
 * @date: 2023-11-13
 **/
@Service("measureItemStorePurchaseRate7DStrategy")
public class MeasureItemStorePurchaseRate7DStrategy implements TenantMeasureItemStrategy {
    @Resource
    private TenantMetricsSummaryContextHolder tenantMetricsSummaryContextHolder;

    @Override
    public void doMeasureItem(TenantMeasureItemResult result) {
        String timeTag = DateUtil.yesterday().toString("yyyyMMdd");
        TenantMetricsSummary tenantMetricsSummary = tenantMetricsSummaryContextHolder.getTenantMetricsSummary(result.getTenantId(), timeTag);
        String storePurchaseRate7D = Objects.isNull(tenantMetricsSummary.getStorePurchaseRate7d()) ? StringConstants.SEPARATING_IN_LINE : tenantMetricsSummary.getStorePurchaseRate7d().toPlainString();
        String itemResult = String.format("%s %s%%", result.getItemTitle(), storePurchaseRate7D);
        result.setItemResult(itemResult);
        result.setItemResultState(TenantConfigStatusEnum.NORMAL.getStatus());
    }
}
