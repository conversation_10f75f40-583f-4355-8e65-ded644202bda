package com.cosfo.manage.tenant.convert;

import com.cosfo.manage.tenant.model.dto.TenantAccountBussinessMsgConfigDTO;
import com.cosfo.manage.tenant.model.dto.TenantAccountDTO;
import com.cosfo.manage.tenant.model.po.TenantAccount;
import com.cosfo.manage.tenant.model.po.TenantAccountBussinessMsgConfig;
import com.cosfo.manage.tenant.model.vo.SupplierDistributorTenantAccountVO;
import com.cosfo.manage.tenant.model.vo.TenantAccountBussinessMsgConfigVO;
import com.cosfo.manage.tenant.model.vo.TenantAccountVO;
import net.xianmu.usercenter.client.tenant.req.TenantAccountCommandReq;
import net.xianmu.usercenter.client.tenant.resp.TenantAccountResultResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-05-26
 * @Description:
 */
@Mapper
public interface TenantAccountMapperConvert {

    TenantAccountMapperConvert INSTANCE = Mappers.getMapper(TenantAccountMapperConvert.class);

    /**
     * resp to TenantAccount
     *
     * @param respList
     * @return
     */
    List<TenantAccount> respListToTenantAccountList(List<TenantAccountResultResp> respList);

    /**
     * resp to TenantAccount
     *
     * @param resp
     * @return
     */
    TenantAccount respToTenantAccount(TenantAccountResultResp resp);

    TenantAccountCommandReq dtoToCommandReq(TenantAccountDTO tenantAccountDTO);

    SupplierDistributorTenantAccountVO tenantAccount2SupplierDistributorTenantAccountVO(TenantAccountVO tenantAccountVO);

    List<TenantAccountBussinessMsgConfigVO> tenantAccountBussinessMsgConfig2VOList(List<TenantAccountBussinessMsgConfig> bussinessMsgConfigList);

    TenantAccountBussinessMsgConfig dto2TenantAccountBussinessMsgConfig(TenantAccountBussinessMsgConfigDTO dto);
}
