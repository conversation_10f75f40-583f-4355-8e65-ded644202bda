package com.cosfo.manage.tenant.convert;

import com.cosfo.manage.tenant.model.dto.RoleDTO;
import com.cosfo.manage.tenant.model.vo.RoleVO;
import net.xianmu.authentication.client.dto.AuthRole;
import net.xianmu.authentication.client.input.AuthRoleUpdateVO;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/28
 */
public class RoleConvert {

    /**
     * 转化为RoleVO
     *
     * @param authRole
     */
    public static RoleVO convertToRoleVO(AuthRole authRole){
        if (authRole == null) {
            return null;
        }
        RoleVO roleVO = new RoleVO();
        roleVO.setId(authRole.getId());
        roleVO.setRemarks(authRole.getRemarks());
        roleVO.setCreateTime(authRole.getCreateTime());
        roleVO.setUpdateTime(authRole.getUpdateTime());
        roleVO.setTenantId(authRole.getTenantId());
        roleVO.setLastUpdater(authRole.getLastUpdater());
        roleVO.setSystemOrigin(authRole.getSystemOrigin());
        roleVO.setSuperAdmin(authRole.getSuperAdmin());
        roleVO.setRoleName(authRole.getRolename());
        return roleVO;
    }

    public static List<RoleVO> convertToRoleVOList(List<AuthRole> authRoleList){
        if(CollectionUtils.isEmpty(authRoleList)){
            return null;
        }

        List<RoleVO> roleVOS = authRoleList.stream().map(RoleConvert::convertToRoleVO).collect(Collectors.toList());
        return roleVOS;
    }

    public static AuthRoleUpdateVO convertToAuthRoleUpdateVO(RoleDTO roleDTO){

        if (roleDTO == null) {
            return null;
        }
        AuthRoleUpdateVO authRoleUpdateVO = new AuthRoleUpdateVO();
        authRoleUpdateVO.setMenuPurviewIds(roleDTO.getMenuPurviewIds());
        authRoleUpdateVO.setRemarks(roleDTO.getRemarks());
        authRoleUpdateVO.setCreateTime(roleDTO.getCreateTime());
        authRoleUpdateVO.setUpdateTime(roleDTO.getUpdateTime());
        authRoleUpdateVO.setLastUpdater(roleDTO.getLastUpdater());
        authRoleUpdateVO.setSystemOrigin(roleDTO.getSystemOrigin());
        authRoleUpdateVO.setSuperAdmin(roleDTO.getSuperAdmin());
        authRoleUpdateVO.setRolename(roleDTO.getRoleName());
        return authRoleUpdateVO;
    }
}
