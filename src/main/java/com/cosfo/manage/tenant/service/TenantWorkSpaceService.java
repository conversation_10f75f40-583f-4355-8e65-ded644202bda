package com.cosfo.manage.tenant.service;

import com.cosfo.manage.market.model.vo.MarketItemSalesVO;
import com.cosfo.manage.tenant.model.dto.MarketItemSalesRankingQueryDTO;
import com.cosfo.manage.tenant.model.dto.TenantWorkSpaceOverviewCardDTO;
import com.cosfo.manage.tenant.model.dto.WarningCenterCardDTO;

import java.util.List;

/**
 * @description:
 * @author: George
 * @date: 2023-10-26
 **/
public interface TenantWorkSpaceService {

    /**
     * 查询概况
     * @param tenantId
     * @return
     */
    List<TenantWorkSpaceOverviewCardDTO> queryOverview(Long tenantId);

    /**
     * 查询商品销量榜, 查询前10
     */
    List<MarketItemSalesVO> queryMarketItemSalesRanking(MarketItemSalesRankingQueryDTO query);

    /**
     * 商品销量导出
     */
    void exportMarketItemSalesRanking(MarketItemSalesRankingQueryDTO query);

    /**
     * 查询常用功能
     */
    List<String> queryCommonFunction();

    /**
     * 修改常用功能
     */
    void updateCommonFunction(List<String> functionList);

    /**
     * 查询用户二级菜单
     */
    List<String> queryUserMenuWithAuthentication();

    /**
     * 查询预警中心
     * @return
     */
    WarningCenterCardDTO queryWarningCenterCard();
}
