package com.cosfo.manage.tenant.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cosfo.manage.common.context.TenantAccountConfig;
import com.cosfo.manage.tenant.convert.TenantAccountMapperConvert;
import com.cosfo.manage.tenant.dao.TenantAccountBussinessMsgConfigDao;
import com.cosfo.manage.tenant.dao.TenantAccountReceiveMsgSwitchDao;
import com.cosfo.manage.tenant.model.dto.TenantAccountBussinessMsgConfigDTO;
import com.cosfo.manage.tenant.model.dto.TenantAccountBussinessMsgQueryDTO;
import com.cosfo.manage.tenant.model.dto.TenantAccountReceiveMsgQueryDTO;
import com.cosfo.manage.tenant.model.dto.TenantAccountReceiveMsgSwitchDTO;
import com.cosfo.manage.tenant.model.po.TenantAccountBussinessMsgConfig;
import com.cosfo.manage.tenant.model.po.TenantAccountReceiveMsgSwitch;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * * 用户关于公众号配置  service
 */
@Component
public class TenantAccountMsgService {

    @Resource
    private TenantAccountBussinessMsgConfigDao tenantAccountBussinessMsgConfigDao;
    @Resource
    private TenantAccountReceiveMsgSwitchDao tenantAccountReceiveMsgSwitchDao;
    /**
     * 查询 推送配置
     * @return
     */
    public List<TenantAccountBussinessMsgConfig> queryTenantAccountBussinessMsgConfig(TenantAccountBussinessMsgQueryDTO tenantAccountBussinessMsgConfigQueryDTO) {
        Long tenantId = tenantAccountBussinessMsgConfigQueryDTO.getTenantId ();
        Set<Long> accountIds = tenantAccountBussinessMsgConfigQueryDTO.getTenantAccountIds ();
        if(CollectionUtil.isEmpty (accountIds) || ObjectUtil.isEmpty (tenantId)){
            return Collections.emptyList ();
        }

        List<TenantAccountBussinessMsgConfig> configList = tenantAccountBussinessMsgConfigDao.queryList (tenantAccountBussinessMsgConfigQueryDTO);
        Map<Long, List<TenantAccountBussinessMsgConfig>> configMap = configList.stream ().collect (Collectors.groupingBy (TenantAccountBussinessMsgConfig::getTenantAccountId));
        List<TenantAccountBussinessMsgConfig> news = new ArrayList<> ();
        accountIds.forEach (accountId->{
            List<TenantAccountBussinessMsgConfig> bussinessMsgConfigList = configMap.get (accountId);
            List<Integer> dbBussinessTypes;
            if(CollectionUtil.isNotEmpty (bussinessMsgConfigList)){
                dbBussinessTypes = bussinessMsgConfigList.stream ().map (TenantAccountBussinessMsgConfig::getBussinessType).collect (Collectors.toList ());
            } else {
                dbBussinessTypes = Collections.emptyList ();
            }
            List<TenantAccountBussinessMsgConfig> configs = TenantAccountConfig.BussinessMsgTypeEnum.listAllBussinessType().stream ().filter (e->!dbBussinessTypes.contains (e)).collect (Collectors.toList ()).stream ().map (e-> buildDefaultTenantAccountBussinessMsgConfig (e,tenantId,accountId)).collect (Collectors.toList ());
            if(CollectionUtil.isNotEmpty (configs)){
                news.addAll (configs);
            }
        });
        if(CollectionUtil.isNotEmpty (news)){
            tenantAccountBussinessMsgConfigDao.saveBatch (news);
            configList.addAll (news);
        }

        if (ObjectUtil.isNotEmpty(tenantAccountBussinessMsgConfigQueryDTO.getAvailableStatus())) {
            configList = configList.stream().filter(e -> e.getAvailableStatus().equals(tenantAccountBussinessMsgConfigQueryDTO.getAvailableStatus())).collect(Collectors.toList());
        }
        if (ObjectUtil.isNotEmpty(tenantAccountBussinessMsgConfigQueryDTO.getBussinessType())) {
            configList = configList.stream().filter(e -> e.getBussinessType().equals(tenantAccountBussinessMsgConfigQueryDTO.getBussinessType())).collect(Collectors.toList());
        }
        if (ObjectUtil.isNotEmpty(tenantAccountBussinessMsgConfigQueryDTO.getPushHour())) {
            configList = configList.stream().filter(e -> e.getPushHour().equals(tenantAccountBussinessMsgConfigQueryDTO.getPushHour())).collect(Collectors.toList());
        }
        return configList;

    }

    private TenantAccountBussinessMsgConfig buildDefaultTenantAccountBussinessMsgConfig(Integer bussinessType, Long tenantId, Long accountId) {
        TenantAccountBussinessMsgConfig config = new TenantAccountBussinessMsgConfig ();
        config.setBussinessType(bussinessType);
        config.setTenantId(tenantId);
        config.setTenantAccountId(accountId);
        config.setAvailableStatus(TenantAccountConfig.AvailableStatusEnum.YES.getCode ());
        config.setPushHour(16);
        return config;
    }

    public void addOrUpdateTenantAccountBussinessMsgConfig(List<TenantAccountBussinessMsgConfigDTO> configs, Long tenantAccountId,Long tenantId) {
        if(CollectionUtil.isEmpty (configs)){
            return;
        }
        List<TenantAccountBussinessMsgConfig> result = new ArrayList<> ();

        Map<Integer, TenantAccountBussinessMsgConfigDTO> configMap = configs.stream().collect(Collectors.toMap(TenantAccountBussinessMsgConfigDTO::getBussinessType,e->e));

        Map<Integer, TenantAccountBussinessMsgConfig> dbMap;
        TenantAccountBussinessMsgQueryDTO tenantAccountBussinessMsgConfigQueryDTO = TenantAccountBussinessMsgQueryDTO.builder().tenantAccountIds(Collections.singleton (tenantAccountId)).tenantId(tenantId).build();
        List<TenantAccountBussinessMsgConfig> configList = tenantAccountBussinessMsgConfigDao.queryList (tenantAccountBussinessMsgConfigQueryDTO);
        if(CollectionUtil.isNotEmpty (configList)){
            dbMap  = configList.stream().collect(Collectors.toMap(TenantAccountBussinessMsgConfig::getBussinessType,e->e));
        } else {
            dbMap = Collections.emptyMap ();
        }

        configMap.forEach ((bussinessType,dto) ->{
            TenantAccountBussinessMsgConfig config = TenantAccountMapperConvert.INSTANCE.dto2TenantAccountBussinessMsgConfig (dto);
            config.setTenantId (tenantId);
            config.setTenantAccountId (tenantAccountId);
            TenantAccountBussinessMsgConfig dbConfig = dbMap.get (bussinessType);
            if(ObjectUtil.isNotEmpty (dbConfig)){
                config.setId (dbConfig.getId ());
            }
            result.add (config);
        });
        tenantAccountBussinessMsgConfigDao.saveOrUpdateBatch (result);
    }

    /**
     * 查询 推送开关
     * @return
     */
    public List<TenantAccountReceiveMsgSwitch> queryTenantAccountReceiveMsgSwitch(TenantAccountReceiveMsgQueryDTO tenantAccountReceiveMsgQueryDTO) {
        Long tenantId = tenantAccountReceiveMsgQueryDTO.getTenantId ();
        Set<Long> accountIds = tenantAccountReceiveMsgQueryDTO.getTenantAccountIds ();
        if(CollectionUtil.isEmpty (accountIds) || ObjectUtil.isEmpty (tenantId)){
            return Collections.emptyList ();
        }

        List<TenantAccountReceiveMsgSwitch> switchList = tenantAccountReceiveMsgSwitchDao.queryList (tenantAccountReceiveMsgQueryDTO);
        List<Long> dbList = switchList.stream ().map (TenantAccountReceiveMsgSwitch::getTenantAccountId).collect (Collectors.toList ());
        List<TenantAccountReceiveMsgSwitch> news = accountIds.stream ().filter (e -> !dbList.contains (e)).map (e -> {
            TenantAccountReceiveMsgSwitch tenantAccountReceiveMsgSwitch = new TenantAccountReceiveMsgSwitch ();
            tenantAccountReceiveMsgSwitch.setChannelCode(tenantAccountReceiveMsgQueryDTO.getChannelCode());
            tenantAccountReceiveMsgSwitch.setChannelType(tenantAccountReceiveMsgQueryDTO.getChannelType());
            tenantAccountReceiveMsgSwitch.setTenantId (tenantId);
            tenantAccountReceiveMsgSwitch.setTenantAccountId (e);
            tenantAccountReceiveMsgSwitch.setAvailableStatus (TenantAccountConfig.AvailableStatusEnum.YES.getCode ());
            return tenantAccountReceiveMsgSwitch;
        }).collect (Collectors.toList ());
        if(CollectionUtil.isNotEmpty (news)){
            tenantAccountReceiveMsgSwitchDao.saveBatch (news);
            switchList.addAll (news);
        }
        if (ObjectUtil.isNotEmpty(tenantAccountReceiveMsgQueryDTO.getAvailableStatus())) {
            switchList = switchList.stream().filter(e -> e.getAvailableStatus().equals(tenantAccountReceiveMsgQueryDTO.getAvailableStatus())).collect(Collectors.toList());
        }
        return switchList;
    }

    public void accountReceiveMsgSwitchUpdate(TenantAccountReceiveMsgSwitchDTO dto, Long tenantId) {
        TenantAccountReceiveMsgQueryDTO tenantAccountReceiveMsgQueryDTO = TenantAccountReceiveMsgQueryDTO.builder().tenantAccountIds(Collections.singleton (dto.getTenantAccountId ()))
                .tenantId(tenantId)
                .channelCode(dto.getChannelCode())
                .channelType(dto.getChannelType()).build();
        List<TenantAccountReceiveMsgSwitch> switchList = queryTenantAccountReceiveMsgSwitch (tenantAccountReceiveMsgQueryDTO);
        TenantAccountReceiveMsgSwitch tenantAccountReceiveMsgSwitch = switchList.get (0);
        if(!dto.getAvailableStatus ().equals (tenantAccountReceiveMsgSwitch.getAvailableStatus ())) {
            tenantAccountReceiveMsgSwitch.setAvailableStatus (dto.getAvailableStatus ());
            tenantAccountReceiveMsgSwitchDao.updateById (tenantAccountReceiveMsgSwitch);
        }
    }
}
