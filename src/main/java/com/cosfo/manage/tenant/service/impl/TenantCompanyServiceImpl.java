package com.cosfo.manage.tenant.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.cosfo.manage.common.context.TenantEnums;
import com.cosfo.manage.common.context.usercenter.BusinessInformationTypeEnum;
import com.cosfo.manage.facade.usercenter.UserCenterBusinessInfoFacade;
import com.cosfo.manage.facade.usercenter.UserCenterTenantFacade;
import com.cosfo.manage.tenant.convert.TenantCompanyMapperConvert;
import com.cosfo.manage.tenant.model.po.HuiFuBase;
import com.cosfo.manage.tenant.model.po.TenantCompany;
import com.cosfo.manage.tenant.service.HuiFuBaseService;
import com.cosfo.manage.tenant.service.TenantCompanyService;
import net.xianmu.usercenter.client.businessInfo.req.BusinessInformationQueryReq;
import net.xianmu.usercenter.client.businessInfo.resp.BusinessInformationResultResp;
import net.xianmu.usercenter.client.tenant.req.TenantQueryReq;
import net.xianmu.usercenter.client.tenant.resp.TenantAndBusinessInfoResultResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: fansongsong
 * @Date: 2023-05-25
 * @Description:
 */
@Service
public class TenantCompanyServiceImpl implements TenantCompanyService {

    @Resource
    private UserCenterBusinessInfoFacade userCenterBusinessInfoFacade;
    @Resource
    private UserCenterTenantFacade userCenterTenantFacade;
    @Resource
    private HuiFuBaseService huiFuBaseService;

    @Override
    public TenantCompany selectByTenantId(Long tenantId) {
        BusinessInformationResultResp businessInformationResultResp = userCenterBusinessInfoFacade.getBusinessInfo(tenantId);
        if (Objects.isNull(businessInformationResultResp)) {
            return null;
        }
        TenantCompany tenantCompany = TenantCompanyMapperConvert.INSTANCE.respToTenantCompany(businessInformationResultResp);
        HuiFuBase huiFuBase = huiFuBaseService.getByTenantId(tenantId);
        if (Objects.isNull(huiFuBase)) {
            return tenantCompany;
        }
        tenantCompany.setOpeningBank(huiFuBase.getOpeningBank());
        tenantCompany.setAccountName(huiFuBase.getAccountName());
        tenantCompany.setAccountNumber(huiFuBase.getAccountNumber());
        return tenantCompany;
    }

    @Override
    public TenantCompany selectSimpleCompanyByTenantId(Long tenantId) {
        BusinessInformationResultResp businessInformationResultResp = userCenterBusinessInfoFacade.getBusinessInfo(tenantId);
        if (Objects.isNull(businessInformationResultResp)) {
            return null;
        }
        return TenantCompanyMapperConvert.INSTANCE.respToTenantCompany(businessInformationResultResp);
    }

    @Override
    public List<Long> selectByCompanyName(String companyName) {
        TenantQueryReq tenantQueryReq = new TenantQueryReq();
        tenantQueryReq.setCompanyName(companyName);
        tenantQueryReq.setType(TenantEnums.type.SUPPLIER.getCode());

        List<TenantAndBusinessInfoResultResp> resultRespList = userCenterTenantFacade.getTenantAndCompanyByQuery(tenantQueryReq);
        if (CollectionUtil.isEmpty(resultRespList)) {
            return Collections.EMPTY_LIST;
        }
        return resultRespList.stream().map(TenantAndBusinessInfoResultResp::getTenantId).collect(Collectors.toList());
    }

    @Override
    public List<BusinessInformationResultResp> batchQueryByTenantIds(List<Long> tenantIds) {
        BusinessInformationQueryReq businessInformationQueryReq = new BusinessInformationQueryReq();
        businessInformationQueryReq.setType(BusinessInformationTypeEnum.BRAND_USER_TYPE.getCode());
        businessInformationQueryReq.setBizIdList(tenantIds);
        List<BusinessInformationResultResp> businessInfos = userCenterBusinessInfoFacade.getBusinessInfos(businessInformationQueryReq);
        if (CollectionUtil.isEmpty(businessInfos)) {
            return Collections.emptyList();
        }
        return businessInfos;
    }

}
