package com.cosfo.manage.tenant.service.impl;


import com.alibaba.excel.util.CollectionUtils;
import com.cosfo.manage.common.context.ExcelTypeEnum;
import com.cosfo.manage.common.context.GoodsTypeEnum;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.facade.ProductFacade;
import com.cosfo.manage.market.model.po.MarketItemCompositeMarket;
import com.cosfo.manage.market.model.po.MarketItemOnSaleSoldOutDetail;
import com.cosfo.manage.market.service.MarketItemCompositeMarketService;
import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import com.cosfo.manage.report.model.dto.MarketItemSoldOutExcelDTO;
import com.cosfo.manage.report.repository.MarketItemOnSaleSoldOutDetailRepository;
import com.cosfo.manage.report.service.ExcelGenerator;
import org.joda.time.Duration;
import org.joda.time.Period;
import org.joda.time.format.PeriodFormatter;
import org.joda.time.format.PeriodFormatterBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class MarketItemSoldOutExcelGenerator implements ExcelGenerator<Void> {

    @Resource
    private CommonService commonService;
    @Resource
    private MarketItemCompositeMarketService marketItemCompositeMarketService;
    @Resource
    private MarketItemOnSaleSoldOutDetailRepository marketItemOnSaleSoldOutDetailRepository;
    @Resource
    private ProductFacade productFacade;

    @Override
    public String getExcelName(Void query) {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return String.format("前7日售罄时长截至%s.xlsx", yesterday.format(formatter));
    }

    @Override
    public String generateExcelAndReturnFilePath(Long tenantId, Void query) {
        List<MarketItemSoldOutExcelDTO> marketItemSoldOutExcelDTOS = queryMarketItemSoldOut(tenantId);
        return commonService.exportExcel(marketItemSoldOutExcelDTOS, ExcelTypeEnum.MARKET_ITEM_ON_SALE_SOLD_OUT.getName());
    }


    private List<MarketItemSoldOutExcelDTO> queryMarketItemSoldOut(Long tenantId) {
        // 获取售罄商品信息
        String yesterday = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String sevenDaysBefore = LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        List<MarketItemOnSaleSoldOutDetail> marketItemOnSaleSoldOutDetails = marketItemOnSaleSoldOutDetailRepository
                .selectByTenantIdAndTimeTag(tenantId, sevenDaysBefore, yesterday);

        if (CollectionUtils.isEmpty(marketItemOnSaleSoldOutDetails)) {
            return new ArrayList<>();
        }

        Map<Long, List<MarketItemOnSaleSoldOutDetail>> marketItemOnSaleSoldOutDetailMap = marketItemOnSaleSoldOutDetails.stream()
                .collect(Collectors.groupingBy(MarketItemOnSaleSoldOutDetail::getItemId));
        List<Long> marketItemIds = new ArrayList<>(marketItemOnSaleSoldOutDetailMap.keySet());

        // 商品信息
        List<MarketItemCompositeMarket> marketItemCompositeMarkets = marketItemCompositeMarketService
                .queryByMarketItemIds(tenantId, marketItemIds);
        Map<Long, MarketItemCompositeMarket> marketItemCompositeMarketMap = marketItemCompositeMarkets.stream()
                .collect(Collectors.toMap(MarketItemCompositeMarket::getItemId, Function.identity()));

        // 货品信息
        Map<Long, Long> skuIds = marketItemCompositeMarkets.stream().filter(m -> m.getSkuId() != null)
                .collect(Collectors.toMap(MarketItemCompositeMarket::getItemId, MarketItemCompositeMarket::getSkuId));
        Map<Long, ProductSkuDTO> skuDTOMap;
        if (!CollectionUtils.isEmpty(skuIds)) {
            skuDTOMap = productFacade.listSkuByIds(new ArrayList<>(skuIds.values()), tenantId).stream()
                    .collect(Collectors.toMap(ProductSkuDTO::getId, Function.identity()));
        } else {
            skuDTOMap = Collections.emptyMap();
        }

        return marketItemCompositeMarketMap.values().stream().map(itemDetails -> {
            Long itemId = itemDetails.getItemId();
            Long skuId = skuIds.getOrDefault(itemId, null);
            ProductSkuDTO product = skuDTOMap.getOrDefault(skuId, null);
            List<MarketItemOnSaleSoldOutDetail> details = marketItemOnSaleSoldOutDetailMap.get(itemId);
            Integer totalSoldOutTime = details.stream().map(MarketItemOnSaleSoldOutDetail::getSoldOutTime).reduce(0, Integer::sum);
            Integer totalOnSaleTime = details.stream().map(MarketItemOnSaleSoldOutDetail::getOnSaleTime).reduce(0, Integer::sum);

            MarketItemSoldOutExcelDTO excelDTO = new MarketItemSoldOutExcelDTO();
            excelDTO.setFirstClassification(itemDetails.getFirstClassificationName());
            excelDTO.setSecondClassification(itemDetails.getSecondClassificationName());
            excelDTO.setTitle(itemDetails.getTitle());
            excelDTO.setSpecification(itemDetails.getSpecification());
            excelDTO.setItemId(itemId);
            excelDTO.setSalePrice(details.get(0).getSalesPrice());
            excelDTO.setSoldOutTime(formatTime(totalSoldOutTime));
            excelDTO.setOnSaleTime(formatTime(totalOnSaleTime));
            excelDTO.setSkuId(skuId);
            excelDTO.setProductSource(GoodsTypeEnum.getShowDescByCode(itemDetails.getGoodsType()));

            // 无仓商品没有货品信息
            if (product == null) {
                return excelDTO;
            }
            excelDTO.setProductTitle(product.getTitle());
            excelDTO.setProductSpecification(product.getSpecification());
            excelDTO.setProductFirstCategory(product.getFirstCategory());
            excelDTO.setProductSecondCategory(product.getSecondCategory());
            excelDTO.setProductThirdCategory(product.getThirdCategory());

            return excelDTO;
        }).collect(Collectors.toList());
    }


    private String formatTime(int totalSeconds) {
        if (totalSeconds == 0) {
            return "0秒";
        }

        Duration duration = Duration.standardSeconds(totalSeconds);
        Period period = duration.toPeriod().normalizedStandard();

        PeriodFormatter formatter = new PeriodFormatterBuilder()
                .appendHours().appendSuffix("小时")
                .appendMinutes().appendSuffix("分")
                .appendSeconds().appendSuffix("秒")
                .printZeroNever()
                .toFormatter();

        return formatter.print(period);
    }


}
