package com.cosfo.manage.tenant.service.impl;

import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.tenant.model.po.TenantDataValue;
import com.cosfo.manage.tenant.repository.TenantDataValueRepository;
import com.cosfo.manage.tenant.service.TenantDataValueService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.oss.common.util.OssGetUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URL;
import java.util.Optional;

@Service
@Slf4j
public class TenantDataValueServiceImpl implements TenantDataValueService {

   @Resource
   private TenantDataValueRepository tenantDataValueRepository;

    @Override
    public void authorize(Long tenantId) {
        TenantDataValue tenantDataValue = tenantDataValueRepository.selectByTenantId(tenantId);
        if (tenantDataValue == null) {
            tenantDataValue = new TenantDataValue();
            tenantDataValue.setTenantId(tenantId);
            tenantDataValue.setAuthorizerAuthUserId(UserLoginContextUtils.getAuthUserId());
            tenantDataValueRepository.save(tenantDataValue);
        } else {
            tenantDataValue.setAuthorizerAuthUserId(UserLoginContextUtils.getAuthUserId());
            tenantDataValueRepository.updateById(tenantDataValue);
        }
    }

    @Override
    public Long queryAuthorizer(Long tenantId) {
        TenantDataValue tenantDataValue = tenantDataValueRepository.selectByTenantId(tenantId);

        return tenantDataValue == null ? null : tenantDataValue.getAuthorizerAuthUserId();
    }

    @Override
    public String queryReportUrl(Long tenantId) {
        TenantDataValue tenantDataValue = tenantDataValueRepository.selectByTenantId(tenantId);
        if (tenantDataValue == null || StringUtils.isEmpty(tenantDataValue.getAuthorizerAuthUserId())) {
            throw new BizException("请先授权");
        }
        return Optional.of(tenantDataValue)
                .map(TenantDataValue::getReportLocation)
                .filter(StringUtils::isNotEmpty)
                .map(OssGetUtil::getSignatureUrl) // 加签链接
                .map(URL::toString)
                .orElse(null);
    }
}
