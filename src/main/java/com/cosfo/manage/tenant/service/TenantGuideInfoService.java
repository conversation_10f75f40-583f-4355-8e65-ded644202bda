package com.cosfo.manage.tenant.service;

import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.tenant.model.input.TenantGuideInfoInput;
import com.cosfo.manage.tenant.model.input.TenantScoreInfoInput;
import com.cosfo.manage.tenant.model.vo.TenantGuideInfoVO;
import com.cosfo.manage.tenant.model.vo.TenantScoreInfoVO;

import java.util.List;

/**
 * <p>
 * 租户登录引导信息问题 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-19
 */
public interface TenantGuideInfoService {

    /**
     * 引导信息查询
     * @param phone
     * @return
     */
    List<TenantGuideInfoVO> queryGuideInfoDetail(String phone, String stepNum);

    /**
     * 引导信息上报
     * @param loginContextInfoDTO
     * @param tenantGuideInfoInput
     */
    void guideInfoReport(LoginContextInfoDTO loginContextInfoDTO, TenantGuideInfoInput tenantGuideInfoInput);

    /**
     * 当前引导问题状态
     * @param phone
     * @return
     */
    TenantGuideInfoVO queryGuideInfoStatus(String phone);

    TenantScoreInfoVO scoreDetail(String phone);

    /**
     * 评分
     * @param merchantInfoDTO
     * @param tenantGuideInfoInput
     */
    void upsertScore(LoginContextInfoDTO merchantInfoDTO, TenantScoreInfoInput tenantScoreInfoInput);
}
