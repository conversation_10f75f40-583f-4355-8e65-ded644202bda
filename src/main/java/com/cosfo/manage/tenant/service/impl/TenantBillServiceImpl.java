package com.cosfo.manage.tenant.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.common.excel.easyexcel.ExcelLargeDataSetExporter;
import com.cosfo.common.util.TimeUtils;
import com.cosfo.manage.bill.mapper.PaymentItemMapper;
import com.cosfo.manage.bill.mapper.PaymentMapper;
import com.cosfo.manage.bill.model.dto.PaymentItemDTO;
import com.cosfo.manage.bill.model.dto.ReceiveAndOutMoneyDTO;
import com.cosfo.manage.bill.model.po.Payment;
import com.cosfo.manage.bill.model.po.PaymentItem;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.constant.HuiFuPaymentConstant;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.common.context.BillExchangeDescEnum;
import com.cosfo.manage.common.context.BillExchangeEnum;
import com.cosfo.manage.common.context.BillPayIncomeEnum;
import com.cosfo.manage.common.context.ExcelTypeEnum;
import com.cosfo.manage.common.context.FileDownloadTypeEnum;
import com.cosfo.manage.common.context.OrderPayTypeEnum;
import com.cosfo.manage.common.executor.ExecutorFactory;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.common.util.PageInfoHelper;
import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.facade.ordercenter.OrderAfterSaleQueryFacade;
import com.cosfo.manage.facade.ordercenter.OrderQueryFacade;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreFacade;
import com.cosfo.manage.merchant.model.po.MerchantStore;
import com.cosfo.manage.merchant.service.MerchantStoreService;
import com.cosfo.manage.order.mapper.payment.RefundMapper;
import com.cosfo.manage.order.model.dto.OrderDTO;
import com.cosfo.manage.order.model.po.payment.Refund;
import com.cosfo.manage.order.service.OrderBusinessService;
import com.cosfo.manage.tenant.mapper.TenantBillMapper;
import com.cosfo.manage.tenant.model.dto.TenantBillExcelDTO;
import com.cosfo.manage.tenant.model.input.TenantBillInput;
import com.cosfo.manage.tenant.model.vo.TenantBillVO;
import com.cosfo.manage.tenant.service.TenantBillService;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/26
 */
@Service
@Slf4j
public class TenantBillServiceImpl implements TenantBillService {
    @Resource
    private TenantBillMapper tenantBillMapper;
    @Resource
    private CommonService commonService;
    @Resource
    private OrderBusinessService orderBusinessService;
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private PaymentItemMapper paymentItemMapper;
    @Resource
    private MerchantStoreService merchantStoreService;
    @Resource
    private UserCenterMerchantStoreFacade userCenterMerchantStoreFacade;
    @Resource
    private RefundMapper refundMapper;
    @Resource
    private OrderQueryFacade orderQueryFacade;
    @Resource
    private OrderAfterSaleQueryFacade orderAfterSaleQueryFacade;

    @Override
    public CommonResult<PageInfo<TenantBillVO>> listPage(LoginContextInfoDTO requestContextInfoDTO, TenantBillInput query) {
        query.setTenantId(requestContextInfoDTO.getTenantId());
        query.setPaymentType(query.getActualType());
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<TenantBillVO> list = queryListBill(query);
        for (TenantBillVO tenantBillVO : list) {
            if (ObjectUtil.isNull(tenantBillVO.getRecordNo())) {
                continue;
            }
            // 手续费显示负数
            if (tenantBillVO.getRecordNo().contains(BillExchangeDescEnum.PAY.getDesc())) {
                if (ObjectUtil.isNotNull(tenantBillVO.getBillFee())) {
                    tenantBillVO.setBillFee(tenantBillVO.getBillFee().multiply(HuiFuPaymentConstant.NEGATIVE));
                } else {
                    tenantBillVO.setBillFee(Constants.ZERO);
                }
            }
            // 退款金额显示为负数
            if (tenantBillVO.getRecordNo().contains(BillExchangeDescEnum.REFUND.getDesc())) {
                tenantBillVO.setBillPrice(tenantBillVO.getBillPrice().multiply(HuiFuPaymentConstant.NEGATIVE));
            }
        }
        return CommonResult.ok(PageInfoHelper.createPageInfo(list, query.getPageSize()));
    }

    @Override
    public CommonResult<ReceiveAndOutMoneyDTO> countMoney(LoginContextInfoDTO requestContextInfoDTO, TenantBillInput query) {
        query.setTenantId(requestContextInfoDTO.getTenantId());
        query.setPaymentType(query.getActualType());
        List<TenantBillVO> list = queryListBill(query);
//        // 记录查询条件
//        Integer queryType = query.getType();
        ReceiveAndOutMoneyDTO receiveAndOutMoneyDTO = new ReceiveAndOutMoneyDTO();
        // 收入
        BigDecimal countInoutMoney = Constants.ZERO;
        // 支出
        BigDecimal countOutMoney = Constants.ZERO;
        BigDecimal allFeeMoney = Constants.ZERO;
        for (TenantBillVO tenantBillVO : list) {
            if (ObjectUtil.isNotNull(tenantBillVO.getBillFee())) {
                allFeeMoney = allFeeMoney.add(tenantBillVO.getBillFee());
            }
            if (ObjectUtil.isNotNull(tenantBillVO.getType()) && BillPayIncomeEnum.INCOME.getCode().equals(tenantBillVO.getType())) {
                countInoutMoney = countInoutMoney.add(tenantBillVO.getBillPrice());
            }
            if (ObjectUtil.isNotNull(tenantBillVO.getType()) && BillPayIncomeEnum.PAY.getCode().equals(tenantBillVO.getType())) {
                countOutMoney = countOutMoney.add(tenantBillVO.getBillPrice());
            }
        }
        // 减去所有手续费
        countInoutMoney = countInoutMoney.subtract(allFeeMoney);
        receiveAndOutMoneyDTO.setReceiveMoney(countInoutMoney);
        receiveAndOutMoneyDTO.setOutMoney(countOutMoney);
//        if (ObjectUtil.isNotNull(queryType)){
//            // 如果指定了收支类型，则将另一种收支类型置为0
//            if (BillPayIncomeEnum.INCOME.getCode().equals(queryType)){
//                receiveAndOutMoneyDTO.setOutMoney(Constants.ZERO);
//            }else {
//                receiveAndOutMoneyDTO.setReceiveMoney(Constants.ZERO);
//            }
//        }
        // 收支总数
        receiveAndOutMoneyDTO.setAllMoney(countInoutMoney.add(countOutMoney));
        return CommonResult.ok(receiveAndOutMoneyDTO);
    }

    @Override
    public CommonResult export(LoginContextInfoDTO requestContextInfoDTO, TenantBillInput query) {
        // 转换查询条件进行存储
        HashMap<String, String> queryParamsMap = new LinkedHashMap<>(NumberConstants.TEN);
        // 交易对象名称
        if (!StringUtils.isBlank(query.getTradingObject())) {
            queryParamsMap.put(HuiFuPaymentConstant.STORE_NAME, query.getTradingObject());
        }
        // 交易日期
        if (!StringUtils.isBlank(query.getStartTime()) && !StringUtils.isBlank(query.getEndTime())) {
            queryParamsMap.put(HuiFuPaymentConstant.EXCHANGE_DATE, query.getStartTime() + StringConstants.SEPARATING_IN_LINE + query.getEndTime());
        }
        // 收支类型
        if (Objects.nonNull(query.getType())) {
            queryParamsMap.put(HuiFuPaymentConstant.TYPE, BillPayIncomeEnum.getDesc(query.getType()));
        }
        // 订单编号
        if (!StringUtils.isBlank(query.getRecordNo())) {
            queryParamsMap.put(HuiFuPaymentConstant.ORDER_NO, query.getRecordNo());
        }
        // 交易类型
        if (Objects.nonNull(query.getExchangeType())) {
            queryParamsMap.put(HuiFuPaymentConstant.EXCHANGE_TYPE, BillExchangeEnum.getDesc(String.valueOf(query.getExchangeType())));
        }
        // 支付方式
        if (Objects.nonNull(query.getActualType())) {
            query.setPaymentType(query.getActualType());
        }
        if (Objects.nonNull(query.getPaymentType())) {
            queryParamsMap.put(HuiFuPaymentConstant.PAY_TYPE, OrderPayTypeEnum.getDesc(query.getPaymentType()));
        }
        // 银行流水号
        if (Objects.nonNull(query.getBankOrderId())) {
            queryParamsMap.put(HuiFuPaymentConstant.BILL_NO, query.getBankOrderId());
        }
        query.setTenantId(requestContextInfoDTO.getTenantId());

        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
        recordDTO.setBizType(FileDownloadTypeEnum.EXCHANGE_STATEMENT.getType());
        recordDTO.setTenantId(requestContextInfoDTO.getTenantId());
        recordDTO.setFileName(ExcelTypeEnum.EXCHANGE_INFO.getFileName());
        recordDTO.setParams(queryParamsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(queryParamsMap));
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO).asyncWriteWithOssResp(query, e -> writeDownloadCenter(e, recordDTO.getFileName()));

//        // 生成文件下载记录
//        FileDownloadRecord record = new FileDownloadRecord();
//        record.setTenantId(requestContextInfoDTO.getTenantId());
//        record.setParams(queryParamsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(queryParamsMap));
//        record.setStatus(FileDownloadStatusEnum.PROCESSING.getStatus());
//        record.setType(FileDownloadTypeEnum.EXCHANGE_STATEMENT.getType());
//        fileDownloadRecordService.generateFileDownloadRecord(record);
//
//        // 异步导出excel
//        ExecutorFactory.generateExcelExecutor.execute(() -> {
//            try {
//                generateTenantBillExportFileStream(query, record.getId());
//            } catch (Exception e) {
//                log.error("交易流水明细导出失败", e);
//                fileDownloadRecordService.updateFailStatus(record.getId());
//            }
//        });

        return CommonResult.ok();
    }

    public DownloadCenterOssRespDTO writeDownloadCenter(TenantBillInput tenantBillInput, String fileName) {
        // 1、表格处理
        String filePath = generateTenantBillExportFileStream(tenantBillInput);

        // 2、文件上传至oss
        OssUploadResult uploadResult = null;
        try {
            uploadResult = OssUploadUtil.upload(fileName, FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
        } catch (IOException e) {
            log.error("filePath={}", filePath, e);
            throw new BizException("读取文件报错");
        } finally {
            commonService.deleteFile(filePath);
        }
        // 3、返回文件地址
        DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
        downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
        downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
        return downloadCenterOssRespDTO;
    }

    public String generateTenantBillExportFileStream(TenantBillInput tenantBillInput) {
        if (tenantBillInput.getStartTime() == null) {
            String monthStartDate = TimeUtils.getMonthStartDate(TimeUtils.getThreeMonthBeforeTime(), null);
            tenantBillInput.setStartTime(monthStartDate);
            if (ObjectUtil.isNotNull(tenantBillInput.getEndTime())) {
                // 由于between查询有边界问题，所以这里截止时间自动+1天
                Date endTime = TimeUtils.getBeforeTime(TimeUtils.changeString2Date(tenantBillInput.getEndTime()), -1);
                tenantBillInput.setEndTime(TimeUtils.changeDate2String(endTime));
            }
        }
        // 如果交易类型不为空
        if (ObjectUtil.isNotNull(tenantBillInput.getExchangeType())) {
            tenantBillInput.setExchangeTypeDesc(BillExchangeDescEnum.getDesc(String.valueOf(tenantBillInput.getExchangeType())));
        }
        Long tenantId = tenantBillInput.getTenantId();

        ExcelLargeDataSetExporter<TenantBillVO, TenantBillExcelDTO> exporterHandler = new ExcelLargeDataSetExporter<TenantBillVO, TenantBillExcelDTO>(ExcelTypeEnum.EXCHANGE_INFO.getName()) {
            @Override
            protected List<TenantBillExcelDTO> convert(TenantBillVO data) {
                return null;
            }

            @Override
            protected int getSize() {
                return 100;
            }

            @Override
            protected List<TenantBillExcelDTO> convertBatch(Collection<TenantBillVO> dataCollection) {
                return convertTenantBillExcelDTO(Lists.newArrayList(dataCollection), tenantBillInput);
            }
        };

        if (Objects.nonNull(tenantBillInput.getTradingObject())) {
            List<MerchantStore> merchantStores = merchantStoreService.selectByStoreNameLike(tenantId, tenantBillInput.getTradingObject());
            List<Long> storeIds = merchantStores.stream().map(MerchantStore::getId).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(storeIds)) {
//                commonService.generateAndUploadExcel(Collections.EMPTY_LIST, ExcelTypeEnum.EXCHANGE_INFO, fileDownloadRecordId);
                return commonService.exportExcel(Collections.EMPTY_LIST, ExcelTypeEnum.EXCHANGE_INFO.getName());
            }
            tenantBillInput.setStoreIds(storeIds);
        }

        // 银行流水号不为空
        if (ObjectUtil.isNotNull(tenantBillInput.getBankOrderId())) {
            List<String> orderNos = queryOrderNosByBankOrderId(tenantBillInput.getBankOrderId());
            if (CollectionUtils.isEmpty(orderNos)) {
//                commonService.generateAndUploadExcel(Collections.EMPTY_LIST, ExcelTypeEnum.EXCHANGE_INFO, fileDownloadRecordId);
                return commonService.exportExcel(Collections.EMPTY_LIST, ExcelTypeEnum.EXCHANGE_INFO.getName());
            }
            tenantBillInput.setRecordNos(orderNos);
        }

        tenantBillMapper.exportList(tenantBillInput, tenantId, exporterHandler);
        String filePath = exporterHandler.finish(true);
        return filePath;
//        commonService.uploadExcelAndUpdateDownloadStatus(filePath, null, ExcelTypeEnum.EXCHANGE_INFO, fileDownloadRecordId);
    }

    private List<TenantBillExcelDTO> convertTenantBillExcelDTO(List<TenantBillVO> list, TenantBillInput tenantBillInput) {
        Long tenantId = tenantBillInput.getTenantId();

        // 获取店铺信息
        List<Long> storeIds = list.stream().map(TenantBillVO::getStoreId).distinct().collect(Collectors.toList());
        List<MerchantStoreResultResp> merchantStoreList = userCenterMerchantStoreFacade.getMerchantStoreList(storeIds);
        Map<Long, String> storeMap = merchantStoreList.stream().collect(Collectors.toMap(MerchantStoreResultResp::getId, MerchantStoreResultResp::getStoreName, (v1, v2) -> v1));

        List<Long> orderIds = new ArrayList<>();
        // 批量查询订单
        Set<String> orderNos = list.stream().filter(tenantBillVO -> tenantBillVO.getRecordNo().contains(BillExchangeDescEnum.PAY.getDesc())).map(TenantBillVO::getRecordNo).collect(Collectors.toSet());
        List<OrderDTO> orderDTOS = orderBusinessService.queryByOrderNos(new ArrayList<String>(orderNos), tenantId);
        Map<String, OrderDTO> orderMap = new HashMap<>(NumberConstants.SIXTEEN);
        if (!CollectionUtils.isEmpty(orderDTOS)) {
            orderMap = orderDTOS.stream().collect(Collectors.toMap(OrderDTO::getOrderNo, item -> item));
            List<Long> ids = orderDTOS.stream().map(OrderDTO::getId).collect(Collectors.toList());
            orderIds.addAll(ids);
        }

        // 批量查询售后单
        Set<String> afterSaleNos = list.stream().filter(tenantBillVO -> tenantBillVO.getRecordNo().contains(BillExchangeDescEnum.REFUND.getDesc())).map(TenantBillVO::getRecordNo).collect(Collectors.toSet());
        List<OrderAfterSaleResp> orderAfterSaleResps = orderAfterSaleQueryFacade.queryByNos(Lists.newArrayList(afterSaleNos));
        Map<String, OrderAfterSaleResp> orderAfterSaleDTOMap = orderAfterSaleResps.stream().collect(Collectors.toMap(OrderAfterSaleResp::getAfterSaleOrderNo, item -> item));
        List<Long> ids = orderAfterSaleResps.stream().map(OrderAfterSaleResp::getOrderId).distinct().collect(Collectors.toList());
        orderIds.addAll(ids);

        // 批量查询支付流水记录
        Map<Long, PaymentItemDTO> paymentItemDTOMap = new HashMap<>(NumberConstants.SIXTEEN);
        if (!CollectionUtils.isEmpty(orderIds)) {
            List<PaymentItemDTO> paymentItemDTOS = paymentMapper.querySuccessPaymentByOrderIds(orderIds, tenantId);
            if (!CollectionUtils.isEmpty(paymentItemDTOS)) {
                paymentItemDTOMap = paymentItemDTOS.stream().collect(Collectors.toMap(PaymentItemDTO::getOrderId, item -> item, (v1, v2) -> v2));

            }
        }

        for (TenantBillVO tenantBillVO : list) {
            tenantBillVO.setTradingObject(storeMap.get(tenantBillVO.getStoreId()));
            // 订单的帐单
            if (!StringUtils.isEmpty(tenantBillVO.getRecordNo()) && tenantBillVO.getRecordNo().contains(BillExchangeDescEnum.PAY.getDesc())) {
                tenantBillVO.setExchangeType(BillExchangeEnum.PAY.getCode());
                OrderDTO orderDTO = orderMap.get(tenantBillVO.getRecordNo());
                tenantBillVO.setBankOrderId(StringConstants.EMPTY);
                if (ObjectUtil.isNotNull(orderDTO)) {
                    Long orderId = orderDTO.getId();
                    PaymentItemDTO paymentItemDTO = paymentItemDTOMap.get(orderDTO.getId());
                    if (ObjectUtil.isNull(paymentItemDTO)) {
                        continue;
                    }

                    tenantBillVO.setBankOrderId(paymentItemDTO.getTransactionId());
                }
            }
            // 售后的帐单
            if (!StringUtils.isEmpty(tenantBillVO.getRecordNo()) && tenantBillVO.getRecordNo().contains(BillExchangeDescEnum.REFUND.getDesc())) {
                tenantBillVO.setExchangeType(BillExchangeEnum.REFUND.getCode());
                OrderAfterSaleResp orderAfterSaleDTO = orderAfterSaleDTOMap.get(tenantBillVO.getRecordNo());
                tenantBillVO.setBankOrderId(StringConstants.EMPTY);
                if (orderAfterSaleDTO == null) {
                    continue;
                }
                PaymentItemDTO paymentItemDTO = paymentItemDTOMap.get(orderAfterSaleDTO.getOrderId());
                if (ObjectUtil.isNull(paymentItemDTO)) {
                    continue;
                }

                tenantBillVO.setBankOrderId(paymentItemDTO.getTransactionId());
            }
        }

        for (TenantBillVO tenantBillVO : list) {
            if (ObjectUtil.isNull(tenantBillVO.getRecordNo())) {
                continue;
            }
            // 消费手续费显示负数
            if (tenantBillVO.getRecordNo().contains(BillExchangeDescEnum.PAY.getCode())) {
                if (ObjectUtil.isNotNull(tenantBillVO.getBillFee())) {
                    tenantBillVO.setBillFee(tenantBillVO.getBillFee().multiply(HuiFuPaymentConstant.NEGATIVE));
                }
            }
            // 退款金额显示为负数
            if (tenantBillVO.getRecordNo().contains(BillExchangeDescEnum.REFUND.getDesc())) {
                tenantBillVO.setBillPrice(tenantBillVO.getBillPrice().multiply(HuiFuPaymentConstant.NEGATIVE));
            }
        }
        List<TenantBillExcelDTO> listExport = new ArrayList<>();
        for (TenantBillVO tenantBillVO : list) {
            TenantBillExcelDTO tenantBillExcelDTO = new TenantBillExcelDTO();
            tenantBillExcelDTO.setCreateTime(TimeUtils.changeDate2String(tenantBillVO.getCreateTime(), TimeUtils.FORMAT));
            tenantBillExcelDTO.setBillNo(tenantBillVO.getBillNo());
            tenantBillExcelDTO.setBankOrderId(tenantBillVO.getBankOrderId());
            tenantBillExcelDTO.setTradingObject(tenantBillVO.getTradingObject());
            tenantBillExcelDTO.setType(BillPayIncomeEnum.INCOME.getCode().equals(tenantBillVO.getType()) ? BillPayIncomeEnum.INCOME.getDesc() : BillPayIncomeEnum.PAY.getDesc());
            tenantBillExcelDTO.setExchangeType(BillExchangeEnum.PAY.getCode().equals(tenantBillVO.getExchangeType()) ? BillExchangeEnum.PAY.getDesc() : BillExchangeEnum.REFUND.getDesc());
            tenantBillExcelDTO.setBillPrice(tenantBillVO.getBillPrice());
            tenantBillExcelDTO.setBillFee(tenantBillVO.getBillFee());
            // 在线支付方式统一显示为微信
            tenantBillExcelDTO.setPaymentType(OrderPayTypeEnum.getDesc(tenantBillVO.getPaymentType()));
            tenantBillExcelDTO.setOrderNo(tenantBillVO.getRecordNo());
            listExport.add(tenantBillExcelDTO);
        }
        return listExport;
    }

    public void generateTenantBillExportFile(TenantBillInput tenantBillInput, Long fileDownloadRecordId) {
        List<TenantBillVO> list = queryListBill(tenantBillInput);
        for (TenantBillVO tenantBillVO : list) {
            if (ObjectUtil.isNull(tenantBillVO.getRecordNo())) {
                continue;
            }
            // 消费手续费显示负数
            if (tenantBillVO.getRecordNo().contains(BillExchangeDescEnum.PAY.getCode())) {
                if (ObjectUtil.isNotNull(tenantBillVO.getBillFee())) {
                    tenantBillVO.setBillFee(tenantBillVO.getBillFee().multiply(HuiFuPaymentConstant.NEGATIVE));
                }
            }
            // 退款金额显示为负数
            if (tenantBillVO.getRecordNo().contains(BillExchangeDescEnum.REFUND.getDesc())) {
                tenantBillVO.setBillPrice(tenantBillVO.getBillPrice().multiply(HuiFuPaymentConstant.NEGATIVE));
            }
        }
        List<TenantBillExcelDTO> listExport = new ArrayList<>();
        for (TenantBillVO tenantBillVO : list) {
            TenantBillExcelDTO tenantBillExcelDTO = new TenantBillExcelDTO();
            tenantBillExcelDTO.setCreateTime(TimeUtils.changeDate2String(tenantBillVO.getCreateTime(), TimeUtils.FORMAT));
            tenantBillExcelDTO.setBillNo(tenantBillVO.getBillNo());
            tenantBillExcelDTO.setBankOrderId(tenantBillVO.getBankOrderId());
            tenantBillExcelDTO.setTradingObject(tenantBillVO.getTradingObject());
            tenantBillExcelDTO.setType(BillPayIncomeEnum.INCOME.getCode().equals(tenantBillVO.getType()) ? BillPayIncomeEnum.INCOME.getDesc() : BillPayIncomeEnum.PAY.getDesc());
            tenantBillExcelDTO.setExchangeType(BillExchangeEnum.PAY.getCode().equals(tenantBillVO.getExchangeType()) ? BillExchangeEnum.PAY.getDesc() : BillExchangeEnum.REFUND.getDesc());
            tenantBillExcelDTO.setBillPrice(tenantBillVO.getBillPrice());
            tenantBillExcelDTO.setBillFee(tenantBillVO.getBillFee());
            // 在线支付方式统一显示为微信
            tenantBillExcelDTO.setPaymentType(OrderPayTypeEnum.getDesc(tenantBillVO.getPaymentType()));
            tenantBillExcelDTO.setOrderNo(tenantBillVO.getRecordNo());
            listExport.add(tenantBillExcelDTO);
        }
        // 写入excel
        commonService.generateAndUploadExcel(listExport, ExcelTypeEnum.EXCHANGE_INFO, fileDownloadRecordId);
    }

    private List<TenantBillVO> queryListBill(TenantBillInput tenantBillInput) {
//        if(tenantBillInput.getStartTime() == null){
//            String monthStartDate = TimeUtils.getMonthStartDate(TimeUtils.getThreeMonthBeforeTime(), null);
//            tenantBillInput.setStartTime(monthStartDate);
//            if (ObjectUtil.isNotNull(tenantBillInput.getEndTime())){
//                // 由于between查询有边界问题，所以这里截止时间自动+1天
//                Date endTime = TimeUtils.getBeforeTime(TimeUtils.changeString2Date(tenantBillInput.getEndTime()), -1);
//                tenantBillInput.setEndTime(TimeUtils.changeDate2String(endTime));
//            }
//        }
        if (StringUtils.isEmpty(tenantBillInput.getStartTime())) {
            tenantBillInput.setStartTime(TimeUtils.getThreeMonthBeforeTime());
        }
        if (StringUtils.isEmpty(tenantBillInput.getEndTime())) {
            tenantBillInput.setEndTime(TimeUtils.changeDate2String(new Date()));
        }
        // 如果交易类型不为空
        if (ObjectUtil.isNotNull(tenantBillInput.getExchangeType())) {
            tenantBillInput.setExchangeTypeDesc(BillExchangeDescEnum.getDesc(String.valueOf(tenantBillInput.getExchangeType())));
        }
        Long tenantId = tenantBillInput.getTenantId();
        if (Objects.nonNull(tenantBillInput.getTradingObject())) {
            List<MerchantStore> merchantStores = merchantStoreService.selectByStoreNameLike(tenantId, tenantBillInput.getTradingObject());
            List<Long> storeIds = merchantStores.stream().map(MerchantStore::getId).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(storeIds)) {
                return Collections.EMPTY_LIST;
            }
            tenantBillInput.setStoreIds(storeIds);
        }

        // 银行流水号不为空
        if (ObjectUtil.isNotNull(tenantBillInput.getBankOrderId())) {
            List<String> orderNos = queryOrderNosByBankOrderId(tenantBillInput.getBankOrderId());
            if (CollectionUtils.isEmpty(orderNos)) {
                return Collections.EMPTY_LIST;
            }
            tenantBillInput.setRecordNos(orderNos);
        }


        List<TenantBillVO> list = tenantBillMapper.list(tenantBillInput, tenantId);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.EMPTY_LIST;
        }

        // 获取店铺信息
        List<Long> storeIds = list.stream().map(TenantBillVO::getStoreId).distinct().collect(Collectors.toList());
        List<MerchantStoreResultResp> merchantStoreList = userCenterMerchantStoreFacade.getMerchantStoreList(storeIds);
        Map<Long, String> storeMap = merchantStoreList.stream().collect(Collectors.toMap(MerchantStoreResultResp::getId, MerchantStoreResultResp::getStoreName, (v1, v2) -> v1));

        List<Long> orderIds = new ArrayList<>();
        // 批量查询订单
        Set<String> orderNos = list.stream().filter(tenantBillVO -> tenantBillVO.getRecordNo().contains(BillExchangeDescEnum.PAY.getDesc())).map(TenantBillVO::getRecordNo).collect(Collectors.toSet());
        List<OrderDTO> orderDTOS = orderBusinessService.queryByOrderNos(new ArrayList<String>(orderNos), tenantId);
        Map<String, OrderDTO> orderMap = new HashMap<>(NumberConstants.SIXTEEN);
        if (!CollectionUtils.isEmpty(orderDTOS)) {
            orderMap = orderDTOS.stream().collect(Collectors.toMap(OrderDTO::getOrderNo, item -> item));
            List<Long> ids = orderDTOS.stream().map(OrderDTO::getId).collect(Collectors.toList());
            orderIds.addAll(ids);
        }

        // 批量查询售后单
        Set<String> afterSaleNos = list.stream().filter(tenantBillVO -> tenantBillVO.getRecordNo().contains(BillExchangeDescEnum.REFUND.getDesc())).map(TenantBillVO::getRecordNo).collect(Collectors.toSet());
        List<OrderAfterSaleResp> orderAfterSaleResps = orderAfterSaleQueryFacade.queryByNos(Lists.newArrayList(afterSaleNos));
        Map<String, OrderAfterSaleResp> orderAfterSaleDTOMap = orderAfterSaleResps.stream().collect(Collectors.toMap(OrderAfterSaleResp::getAfterSaleOrderNo, item -> item));
        List<Long> ids = orderAfterSaleResps.stream().map(OrderAfterSaleResp::getOrderId).distinct().collect(Collectors.toList());
        orderIds.addAll(ids);

        // 批量查询支付流水记录
        Map<Long, PaymentItemDTO> paymentItemDTOMap = new HashMap<>(NumberConstants.SIXTEEN);
        if (!CollectionUtils.isEmpty(orderIds)) {
            List<PaymentItemDTO> paymentItemDTOS = paymentMapper.querySuccessPaymentByOrderIds(orderIds, tenantId);
            if (!CollectionUtils.isEmpty(paymentItemDTOS)) {
                paymentItemDTOMap = paymentItemDTOS.stream().collect(Collectors.toMap(PaymentItemDTO::getOrderId, item -> item, (v1, v2) -> v2));
            }
        }

        for (TenantBillVO tenantBillVO : list) {
            tenantBillVO.setTradingObject(storeMap.get(tenantBillVO.getStoreId()));
            // 订单的帐单
            if (!StringUtils.isEmpty(tenantBillVO.getRecordNo()) && tenantBillVO.getRecordNo().contains(BillExchangeDescEnum.PAY.getDesc())) {
                tenantBillVO.setExchangeType(BillExchangeEnum.PAY.getCode());
                OrderDTO orderDTO = orderMap.get(tenantBillVO.getRecordNo());
                tenantBillVO.setBankOrderId(StringConstants.EMPTY);
                if (ObjectUtil.isNotNull(orderDTO)) {
                    Long orderId = orderDTO.getId();
                    PaymentItemDTO paymentItemDTO = paymentItemDTOMap.get(orderDTO.getId());
                    if (ObjectUtil.isNull(paymentItemDTO)) {
                        continue;
                    }

                    tenantBillVO.setBankOrderId(paymentItemDTO.getTransactionId());
                }
            }
            // 售后的帐单
            if (!StringUtils.isEmpty(tenantBillVO.getRecordNo()) && tenantBillVO.getRecordNo().contains(BillExchangeDescEnum.REFUND.getDesc())) {
                tenantBillVO.setExchangeType(BillExchangeEnum.REFUND.getCode());
                OrderAfterSaleResp orderAfterSaleDTO = orderAfterSaleDTOMap.get(tenantBillVO.getRecordNo());
                tenantBillVO.setBankOrderId(StringConstants.EMPTY);
                PaymentItemDTO paymentItemDTO = paymentItemDTOMap.get(orderAfterSaleDTO.getOrderId());
                if (ObjectUtil.isNull(paymentItemDTO)) {
                    continue;
                }

                tenantBillVO.setBankOrderId(paymentItemDTO.getTransactionId());
            }
            // 特殊退款账单
            if (!StringUtils.isEmpty(tenantBillVO.getRecordNo()) && tenantBillVO.getRecordNo().startsWith(BillExchangeDescEnum.SPECIAL_REFUND.getDesc())) {
                tenantBillVO.setExchangeType(BillExchangeEnum.REFUND.getCode());

                Refund refund = refundMapper.selectByRefundNo(tenantBillVO.getRecordNo());
                if (refund == null) {
                    continue;
                }
                Payment payment = paymentMapper.selectById(refund.getPaymentId());
                if (payment == null) {
                    continue;
                }
                tenantBillVO.setBankOrderId(payment.getTransactionId());
            }
        }
        return list;
    }


    /**
     * 根据银行流水号transactionId查询orderNos
     *
     * @param bankOrderId
     * @return
     */
    private List<String> queryOrderNosByBankOrderId(String bankOrderId) {
        // 银行流水号不为空
        if (ObjectUtil.isNotNull(bankOrderId)) {
            Payment payment = paymentMapper.selectByTransactionId(bankOrderId);
            if (payment == null) {
                return null;
            }

            List<PaymentItem> paymentItemList = paymentItemMapper.selectByPaymentId(payment.getId());
            if (CollectionUtils.isEmpty(paymentItemList)) {
                return null;
            }

            Set<Long> orderIdSet = paymentItemList.stream().map(e -> e.getOrderId()).collect(Collectors.toSet());
            List<OrderResp> orderResps = orderQueryFacade.queryByIds(Lists.newArrayList(orderIdSet));
            if (CollectionUtils.isEmpty(orderResps)) {
                return null;
            }

            List<String> orderNos = orderResps.stream().map(e -> e.getOrderNo()).collect(Collectors.toList());
            return orderNos;
        }

        return null;
    }
}
