package com.cosfo.manage.tenant.service.impl;


import com.alibaba.excel.util.CollectionUtils;
import com.cosfo.manage.common.context.ExcelTypeEnum;
import com.cosfo.manage.common.context.StoreTypeEnum;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.merchant.model.dto.MerchantStoreAccountDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreExcelDataDTO;
import com.cosfo.manage.merchant.model.po.MerchantStore;
import com.cosfo.manage.merchant.model.po.MerchantStoreAccount;
import com.cosfo.manage.merchant.model.po.MerchantStorePurchaseActivityDetail;
import com.cosfo.manage.merchant.service.MerchantStoreAccountService;
import com.cosfo.manage.merchant.service.MerchantStoreService;
import com.cosfo.manage.report.model.dto.MerchantStorePurchaseActivityExcelDTO;
import com.cosfo.manage.report.repository.MarketItemNoSaleDetailRepository;
import com.cosfo.manage.report.repository.MerchantStorePurchaseActivityDetailRepository;
import com.cosfo.manage.report.service.ExcelGenerator;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class MerchantStorePurchaseActivityExcelGenerator implements ExcelGenerator<Void> {

    @Resource
    private CommonService commonService;
    @Resource
    private MerchantStorePurchaseActivityDetailRepository merchantStorePurchaseActivityDetailRepository;
    @Resource
    private MerchantStoreService merchantStoreService;
    @Resource
    private MerchantStoreAccountService merchantStoreAccountService;

    @Override
    public String getExcelName(Void query) {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return String.format("门店采购活跃率截至%s.xlsx", yesterday.format(formatter));
    }

    @Override
    public String generateExcelAndReturnFilePath(Long tenantId, Void query) {
        List<MerchantStorePurchaseActivityExcelDTO> merchantStorePurchaseActivityExcelDTOS = queryMerchantStorePurchaseActivity(tenantId);
        return commonService.exportExcel(merchantStorePurchaseActivityExcelDTOS, ExcelTypeEnum.MERCHANT_STORE_PURCHASE_ACTIVITY.getName());
    }

    private List<MerchantStorePurchaseActivityExcelDTO> queryMerchantStorePurchaseActivity(Long tenantId) {

        String yesterday = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String thirtyDaysBefore = LocalDate.now().minusDays(30).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        List<MerchantStorePurchaseActivityDetail> merchantStorePurchaseActivityDetails =
                merchantStorePurchaseActivityDetailRepository.selectByTenantIdAndTimeTag(tenantId, thirtyDaysBefore, yesterday);

        if (CollectionUtils.isEmpty(merchantStorePurchaseActivityDetails)) {
            return new ArrayList<>();
        }

        List<Long> storeIds = merchantStorePurchaseActivityDetails.stream()
                .map(MerchantStorePurchaseActivityDetail::getStoreId).collect(Collectors.toList());
        Map<Long, MerchantStoreDTO> merchantStores = merchantStoreService.batchQueryDetailByStoreIds(storeIds, tenantId)
                .stream().collect(Collectors.toMap(MerchantStoreDTO::getId, Function.identity()));


        return merchantStorePurchaseActivityDetails.stream().map(activityDetail -> {
            MerchantStoreDTO store = merchantStores.get(activityDetail.getStoreId());
            MerchantStorePurchaseActivityExcelDTO excelDTO = new MerchantStorePurchaseActivityExcelDTO();

            excelDTO.setId(store.getId());
            excelDTO.setName(store.getStoreName());
            StoreTypeEnum storeType = StoreTypeEnum.getByCode(store.getType());
            if (storeType != null) {
                excelDTO.setType(storeType.getDesc());
            }
            excelDTO.setProvince(store.getProvince());
            excelDTO.setCity(store.getCity());
            excelDTO.setArea(store.getArea());

            // 查询店长号码
            MerchantStoreAccountDTO merchantStoreAccount = merchantStoreAccountService.selectManager(tenantId, store.getId());
            if (merchantStoreAccount != null) {
                excelDTO.setPhone(merchantStoreAccount.getPhone());
            }
            excelDTO.setHasPurchaseIn7Days(activityDetail.getPurchasedAmount7d() > 0 ? "是" : "否");
            excelDTO.setHasPurchaseIn30Days(activityDetail.getPurchasedAmount30d() > 0 ? "是" : "否");
            return excelDTO;
        }).collect(Collectors.toList());
    }
}
