package com.cosfo.manage.tenant.service.impl;

import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.tenant.mapper.TenantAgreementMapper;
import com.cosfo.manage.tenant.model.po.TenantAgreement;
import com.cosfo.manage.tenant.service.TenantAgreementService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/28 15:29
 */
@Service
public class TenantAgreementServiceImpl implements TenantAgreementService {

    @Resource
    private TenantAgreementMapper tenantAgreementMapper;

    @Override
    public ResultDTO listAll(Integer type) {
        TenantAgreement agreement = tenantAgreementMapper.selectByType(type);
        return ResultDTO.success(agreement);
    }
}
