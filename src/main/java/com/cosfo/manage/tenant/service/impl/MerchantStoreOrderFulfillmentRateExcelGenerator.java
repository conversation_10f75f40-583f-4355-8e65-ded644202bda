package com.cosfo.manage.tenant.service.impl;


import com.cosfo.manage.common.context.ExcelTypeEnum;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.report.model.dto.TenantOrderFulfillmentRateExcelDTO;
import com.cosfo.manage.report.repository.TenantMetricsSummaryRepository;
import com.cosfo.manage.report.service.ExcelGenerator;
import com.cosfo.manage.tenant.model.po.TenantMetricsSummary;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class MerchantStoreOrderFulfillmentRateExcelGenerator implements ExcelGenerator<Void> {

    @Resource
    private CommonService commonService;
    @Resource
    private TenantMetricsSummaryRepository tenantMetricsSummaryRepository;

    @Override
    public String getExcelName(Void query) {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return String.format("发货及时性统计截至%s.xlsx", yesterday.format(formatter));
    }

    @Override
    public String generateExcelAndReturnFilePath(Long tenantId, Void query) {
        List<TenantOrderFulfillmentRateExcelDTO> tenantOrderFulfillmentRateExcelDTOS = queryTenantOrderFulfillmentRate(tenantId);
        return commonService.exportExcel(tenantOrderFulfillmentRateExcelDTOS, ExcelTypeEnum.MERCHANT_STORE_ORDER_FULFILLMENT_RATE.getName());
    }

    private List<TenantOrderFulfillmentRateExcelDTO> queryTenantOrderFulfillmentRate(Long tenantId) {
        String yesterday = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String thirtyDaysBefore = LocalDate.now().minusDays(30).format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        List<TenantMetricsSummary> tenantMetricsSummaries = tenantMetricsSummaryRepository
                .selectByTenantIdAndDate(tenantId, thirtyDaysBefore, yesterday);

        return tenantMetricsSummaries.stream().map(tenantMetricsSummary -> {
            TenantOrderFulfillmentRateExcelDTO excelDTO = new TenantOrderFulfillmentRateExcelDTO();

            excelDTO.setDate(dataFormatConvert(tenantMetricsSummary.getTimeTag()));
            excelDTO.setOrderFulfillmentRate1Day(percentFormatConvert(tenantMetricsSummary.getOrderFulfillmentRate1d()));
            excelDTO.setOrderFulfillmentRate3Day(percentFormatConvert(tenantMetricsSummary.getOrderFulfillmentRate3d()));
            excelDTO.setOrderFulfillmentRate7Day(percentFormatConvert(tenantMetricsSummary.getOrderFulfillmentRate7d()));

            return excelDTO;
        }).collect(Collectors.toList());
    }

    private String dataFormatConvert(String date) {
        return LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyyMMdd"))
                .format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
    }

    private String percentFormatConvert(BigDecimal percent) {
        return percent.toPlainString() + "%";
    }

}
