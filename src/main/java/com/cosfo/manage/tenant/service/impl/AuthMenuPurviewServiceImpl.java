package com.cosfo.manage.tenant.service.impl;

import com.cosfo.manage.common.config.WhitelistConfig;
import com.cosfo.manage.common.converter.AuthMenuPurviewMapper;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.facade.AuthPurviewFacade;
import com.cosfo.manage.facade.AuthUserFacade;
import com.cosfo.manage.merchant.model.dto.AuthQueryDTO;
import com.cosfo.manage.tenant.model.dto.MenuPurviewDTO;
import com.cosfo.manage.tenant.model.vo.MenuPurviewVO;
import com.cosfo.manage.tenant.service.AuthMenuPurviewService;
import com.cosfo.manage.tenant.service.TenantPrivilegesInfoService;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.AuthMenuPurview;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AuthMenuPurviewServiceImpl implements AuthMenuPurviewService {
    @Resource
    private AuthUserFacade authUserFacade;
    @Resource
    private WhitelistConfig whitelistConfig;
    @Resource
    private AuthPurviewFacade authPurviewFacade;
    @Resource
    private TenantPrivilegesInfoService tenantPrivilegesInfoService;

    LoadingCache<String, List<String>> AUTH_CACHE = Caffeine.newBuilder()
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .maximumSize(2)
            .build(key -> {
                AuthQueryDTO authQueryDTOByKey = AuthQueryDTO.getAuthQueryDTOByKey(key);
                if (Objects.isNull(authQueryDTOByKey)) {
                    return Collections.emptyList();
                }
                return listUserMenuPurviewUrl(authQueryDTOByKey);
            });

    @Override
    public List<MenuPurviewVO> listUserMenuPurview() {
        List<MenuPurviewDTO> menuPurviewDTOS = authUserFacade.listUserMenuPurview();
        List<MenuPurviewDTO> result = whitelistConfig.whitelistFilter(menuPurviewDTOS, UserLoginContextUtils.getTenantId());
        return AuthMenuPurviewMapper.INSTANCE.menuPurviewTomenuPurviewVOList(result);
    }

    @Override
    public List<MenuPurviewVO> listUserMenuPurview(Long tenantId, Long authUserId) {
        List<MenuPurviewDTO> menuPurviewDTOS = authUserFacade.listUserMenuPurview(tenantId, authUserId);
        // 获取当前试用菜单
        Set<Long> purviewIds = tenantPrivilegesInfoService.queryFlagPurviewIds(tenantId);
        menuPurviewDTOS.forEach(menuPurviewDTO -> {
            if (purviewIds.contains(menuPurviewDTO.getId())) {
                menuPurviewDTO.setFlagType(1);
            }
        });

        return AuthMenuPurviewMapper.INSTANCE.menuPurviewTomenuPurviewVOList(menuPurviewDTOS);
    }

    @Override
    public List<String> listUserMenuPurviewUrl(AuthQueryDTO authQueryDTO) {
        Long tenantId = authQueryDTO.getTenantId();
        Long authUserId = authQueryDTO.getAuthUserId();
        List<AuthMenuPurview> menuPurviewVOS = authUserFacade.listUserMenuPurviewById(tenantId, authUserId);
        return menuPurviewVOS.stream().map(AuthMenuPurview::getUrl).distinct().collect(Collectors.toList());
    }

    @Override
    public Pair<Boolean, Boolean> checkAuthByCache(AuthQueryDTO authQueryDTO, String firstAuthUrl, String secondAuthUrl) {
        String key = authQueryDTO.builderKey();
        List<String> authList = AUTH_CACHE.get(key);
        Boolean first = StringUtils.isNotEmpty(firstAuthUrl) && authList.contains(firstAuthUrl);
        Boolean second = StringUtils.isNotEmpty(secondAuthUrl) && authList.contains(secondAuthUrl);
        return Pair.of(first, second);
    }

//    @Override
//    public List<MenuPurviewVO> listMenuPurviewTree() {
//        List<MenuPurviewDTO> purviewList = authUserFacade.listMenuPurviewTree();
//        List<MenuPurviewDTO> result = whitelistConfig.whitelistFilter(purviewList, UserLoginContextUtils.getTenantId());
//        return AuthMenuPurviewMapper.INSTANCE.menuPurviewTomenuPurviewVOList(result);
//    }

    @Override
    public List<MenuPurviewVO> listMenuPurviewTree(Long tenantId) {
        List<MenuPurviewDTO> authTenantMenuPurviewDto = authPurviewFacade.getAuthTenantMenuPurviewDto(SystemOriginEnum.COSFO_MANAGE, tenantId);
        return AuthMenuPurviewMapper.INSTANCE.menuPurviewTomenuPurviewVOList(authTenantMenuPurviewDto);
    }

//    @Override
//    public List<MenuPurviewVO> listMenuPurview() {
//        List<MenuPurviewDTO> purviewList = authUserFacade.listMenuPurview();
//        return AuthMenuPurviewMapper.INSTANCE.menuPurviewTomenuPurviewVOList(purviewList.stream().filter(m->m.getType() != 0).collect(Collectors.toList()));
//    }

    @Override
    public List<MenuPurviewVO> listMenuPurview(Long tenantId) {
        List<MenuPurviewDTO> authTenantMenuPurviewDto = authPurviewFacade.getAuthTenantMenuPurview(SystemOriginEnum.COSFO_MANAGE, tenantId);
        return AuthMenuPurviewMapper.INSTANCE.menuPurviewTomenuPurviewVOList(authTenantMenuPurviewDto.stream().filter(m->m.getType() != 0).collect(Collectors.toList()));
    }

    @Override
    public Boolean addMenuPurview(MenuPurviewDTO menuPurviewDTO) {
        return authUserFacade.addMenuPurview(menuPurviewDTO);
    }

    @Override
    public Boolean updateMenuPurview(MenuPurviewDTO menuPurviewDTO) {
        return authUserFacade.updateMenuPurview(menuPurviewDTO);
    }

    @Override
    public Boolean delMenuPurview(MenuPurviewDTO menuPurviewDTO) {
        return authUserFacade.delMenuPurview(menuPurviewDTO.getId());
    }

    @Override
    public Boolean sortMenuPurview(List<MenuPurviewDTO> purviewDTOS) {
        return authUserFacade.sortMenuPurview(purviewDTOS);
    }
}
