package com.cosfo.manage.tenant.service.impl;


import cn.hutool.core.lang.Pair;
import com.cosfo.manage.common.context.ExcelTypeEnum;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.common.util.Global;
import com.cosfo.manage.market.model.po.MarketItemCompositeMarket;
import com.cosfo.manage.market.repository.MarketItemCompositeGoodsRepository;
import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import com.cosfo.manage.report.model.dto.GoodsExpirationSummaryExcelDTO;
import com.cosfo.manage.report.model.po.GoodsExpirationSummary;
import com.cosfo.manage.report.repository.GoodsExpirationSummaryRepository;
import com.cosfo.manage.report.service.ExcelGenerator;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class TenantGoodsExpirationSummaryGenerator implements ExcelGenerator<Void> {

    @Resource
    private CommonService commonService;
    @Resource
    private MarketItemCompositeGoodsRepository marketItemCompositeGoodsRepository;
    @Resource
    private GoodsExpirationSummaryRepository goodsExpirationSummaryRepository;

    @Override
    public String getExcelName(Void query) {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return String.format("近30天内过期货品截至%s.xlsx", yesterday.format(formatter));
    }

    @Override
    public String generateExcelAndReturnFilePath(Long tenantId, Void query) {
        String timeTag = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        List<GoodsExpirationSummaryExcelDTO> goodsExpirationSummaries = queryGoodsExpirationSummary(tenantId, timeTag);
        return commonService.exportExcel(goodsExpirationSummaries, ExcelTypeEnum.GOODS_EXPIRATION_SUMMARY.getName());
    }

    private List<GoodsExpirationSummaryExcelDTO> queryGoodsExpirationSummary(Long tenantId, String timeTag) {
        List<GoodsExpirationSummary> goodsExpirationSummaries = goodsExpirationSummaryRepository.listByTenantAndTimeTag(tenantId, timeTag);
        Pair<List<Long>, List<Long>> idsPair = getIds(goodsExpirationSummaries);
        Pair<Map<Long, MarketItemCompositeMarket>, Map<Long, ProductSkuDTO>> itemSkuMapPair = marketItemCompositeGoodsRepository.queryByIds(tenantId, idsPair.getKey(), idsPair.getValue());
        Map<Long, MarketItemCompositeMarket> marketInfoMap = itemSkuMapPair.getKey();
        Map<Long, ProductSkuDTO> skuInfoMap = itemSkuMapPair.getValue();
        // 组装数据
        ProductSkuDTO emptySku = new ProductSkuDTO();
        MarketItemCompositeMarket emptyMarket = new MarketItemCompositeMarket();
        return goodsExpirationSummaries.stream().map(item -> {
            ProductSkuDTO productSkuDTO = skuInfoMap.getOrDefault(item.getSkuId(), emptySku);
            MarketItemCompositeMarket marketItem = marketInfoMap.getOrDefault(item.getItemId(), emptyMarket);
            GoodsExpirationSummaryExcelDTO goodsExpirationSummaryExcelDTO = new GoodsExpirationSummaryExcelDTO();
            goodsExpirationSummaryExcelDTO.setExpirationDate(item.getExpirationDate());
            goodsExpirationSummaryExcelDTO.setExpirationBatchStock(item.getExpirationBatchStock());
            goodsExpirationSummaryExcelDTO.setEndingBatchStock(item.getEndingBatchStock());
            goodsExpirationSummaryExcelDTO.setWarehouseName(item.getWarehouseName());
            goodsExpirationSummaryExcelDTO.setBatch(item.getBatch());
            goodsExpirationSummaryExcelDTO.setTitle(productSkuDTO.getTitle());
            goodsExpirationSummaryExcelDTO.setSkuId(item.getSkuId());
            goodsExpirationSummaryExcelDTO.setSpecification(Global.subSpecification(productSkuDTO.getSpecification()));
            goodsExpirationSummaryExcelDTO.setFirstCategory(productSkuDTO.getFirstCategory());
            goodsExpirationSummaryExcelDTO.setSecondCategory(productSkuDTO.getSecondCategory());
            goodsExpirationSummaryExcelDTO.setThirdCategory(productSkuDTO.getThirdCategory());
            goodsExpirationSummaryExcelDTO.setItemId(marketItem.getItemId());
            goodsExpirationSummaryExcelDTO.setItemTitle(marketItem.getTitle());
            goodsExpirationSummaryExcelDTO.setItemSpecification(Global.subSpecification(marketItem.getSpecification()));
            goodsExpirationSummaryExcelDTO.setSalePrice(item.getSalePrice());
            goodsExpirationSummaryExcelDTO.setFirstClassificationName(marketItem.getFirstClassificationName());
            goodsExpirationSummaryExcelDTO.setSecondClassificationName(marketItem.getSecondClassificationName());
            return goodsExpirationSummaryExcelDTO;
        }).collect(Collectors.toList());
    }

    private Pair<List<Long>, List<Long>> getIds(List<GoodsExpirationSummary> goodsExpirationSummaries) {
        return Pair.of(goodsExpirationSummaries.stream().map(GoodsExpirationSummary::getItemId).distinct().collect(Collectors.toList()),
                goodsExpirationSummaries.stream().map(GoodsExpirationSummary::getSkuId).distinct().collect(Collectors.toList()));
    }
}
