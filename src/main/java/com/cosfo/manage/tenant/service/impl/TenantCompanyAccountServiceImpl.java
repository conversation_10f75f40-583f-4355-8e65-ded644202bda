package com.cosfo.manage.tenant.service.impl;

import com.cosfo.manage.tenant.dao.TenantCompanyAccountDao;
import com.cosfo.manage.tenant.model.po.TenantCompanyAccount;
import com.cosfo.manage.tenant.service.TenantCompanyAccountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class TenantCompanyAccountServiceImpl implements TenantCompanyAccountService {

    @Resource
    private TenantCompanyAccountDao tenantCompanyAccountDao;

    /**
     * 获取供应商打款账号
     *
     * @param supplierIds
     * @param tenantId
     * @return
     */
    @Override
    public Map<Long, TenantCompanyAccount> queryAccountMap(Set<Long> supplierIds, Long tenantId) {
        return tenantCompanyAccountDao.queryAccountMap(supplierIds, tenantId);
    }

    /**
     * 获取供应商对品牌方打款账号
     *
     * @param supplierId 供应商
     * @param tenantId   品牌方
     * @return
     */
    @Override
    public TenantCompanyAccount getAccount(Long supplierId, Long tenantId) {
        return tenantCompanyAccountDao.getAccount(supplierId, tenantId);
    }
}
