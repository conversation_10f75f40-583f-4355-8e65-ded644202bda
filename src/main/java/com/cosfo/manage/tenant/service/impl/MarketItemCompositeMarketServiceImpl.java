package com.cosfo.manage.tenant.service.impl;

import com.cofso.item.client.resp.MarketItemClassificationResp;
import com.cosfo.manage.facade.MarketClassificationFacade;
import com.cosfo.manage.market.mapper.MarketItemMapper;
import com.cosfo.manage.market.model.po.Market;
import com.cosfo.manage.market.model.po.MarketItem;
import com.cosfo.manage.market.model.po.MarketItemCompositeMarket;
import com.cosfo.manage.market.repository.MarketRepository;
import com.cosfo.manage.market.service.MarketItemCompositeMarketService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class MarketItemCompositeMarketServiceImpl implements MarketItemCompositeMarketService {

    @Resource
    private MarketClassificationFacade marketClassificationFacade;
    @Resource
    private MarketRepository marketRepository;
    @Resource
    private MarketItemMapper marketItemMapper;

    @Override
    public List<MarketItemCompositeMarket> queryByMarketItemIds(Long tenantId, Collection<Long> marketItemIds) {
        if (CollectionUtils.isEmpty(marketItemIds)) {
            return new ArrayList<>();
        }

        // key -> marketItemId, value -> marketItem
        Map<Long, MarketItem> marketItems = getMarketItems(marketItemIds);
        // key -> marketItemId, value -> marketId
        Map<Long, Long> marketIds = extractMarketIds(marketItems);
        // key -> marketId, value -> classification
        Map<Long, MarketItemClassificationResp> classificationMap = marketClassificationFacade
                .queryClassificationByMarketIds(tenantId, new ArrayList<>(marketIds.values()));
        // key -> marketId, value -> market
        Map<Long, Market> marketMap = getMarketMap(marketIds);

        return assembleCompositeMarkets(marketItems, marketIds, classificationMap, marketMap);
    }


    // ---------------------------- 以下是辅助方法 ----------------------------

    private List<MarketItemCompositeMarket> assembleCompositeMarkets(Map<Long, MarketItem> marketItems,
                                                                     Map<Long, Long> marketIds,
                                                                     Map<Long, MarketItemClassificationResp> classificationMap,
                                                                     Map<Long, Market> marketMap) {
        return marketItems.values().stream().map(marketItem -> {
            Long marketId = marketIds.get(marketItem.getId());

            MarketItemCompositeMarket compositeMarket = new MarketItemCompositeMarket();
            compositeMarket.setItemId(marketItem.getId());
            compositeMarket.setMarketId(marketId);
            compositeMarket.setSkuId(marketItem.getSkuId());
            compositeMarket.setTitle(marketMap.get(marketId).getTitle());
            compositeMarket.setFirstClassificationName(classificationMap.get(marketId).getFirstClassificationName());
            compositeMarket.setSecondClassificationName(classificationMap.get(marketId).getSecondClassificationName());
            compositeMarket.setSpecification(marketItem.getSpecification());
            compositeMarket.setGoodsType(marketItem.getGoodsType());

            return compositeMarket;
        }).collect(Collectors.toList());
    }

    private Map<Long, MarketItem> getMarketItems(Collection<Long> itemIds) {
        return marketItemMapper.selectBatchIds(itemIds).stream()
                .collect(Collectors.toMap(MarketItem::getId, Function.identity()));
    }

    private Map<Long, Long> extractMarketIds(Map<Long, MarketItem> marketItems) {
        return marketItems.values().stream()
                .collect(Collectors.toMap(MarketItem::getId, MarketItem::getMarketId));
    }

    private Map<Long, Market> getMarketMap(Map<Long, Long> marketIds) {
        return marketRepository.listByIds(new ArrayList<>(marketIds.values())).stream()
                .collect(Collectors.toMap(Market::getId, Function.identity()));
    }

}
