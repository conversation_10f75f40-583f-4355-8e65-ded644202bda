package com.cosfo.manage.tenant.service.impl;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.cosfo.manage.common.context.TenantStoreConfigEnum;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreFacade;
import com.cosfo.manage.tenant.model.po.TenantStoreCommonConfig;
import com.cosfo.manage.tenant.model.vo.TenantStoreConfigVO;
import com.cosfo.manage.tenant.repository.TenantStoreCommonConfigRepository;
import com.cosfo.manage.tenant.service.TenantStoreConfigService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: xiaowk
 * @time: 2024/11/25 下午2:51
 */
@Slf4j
@Service
public class TenantStoreConfigServiceImpl implements TenantStoreConfigService {

    /**
     * 新增门店配送单展示价格-默认关闭的租户
     */
    @NacosValue(value = "${deliveryNote.not.printPrice.tenantIds:2}", autoRefreshed = true)
    public Set<Long> deliveryNoteNotPrintPriceTenantIds;
    @Resource
    private TenantStoreCommonConfigRepository tenantStoreCommonConfigRepository;
    @Resource
    private UserCenterMerchantStoreFacade userCenterMerchantStoreFacade;

    @Override
    public TenantStoreConfigVO selectTenantStoreConfig(Long storeId, String configKey) {
        return queryTenantStoreConfigList(Lists.newArrayList(storeId), configKey).get(0);
    }

    @Override
    public List<TenantStoreConfigVO> queryTenantStoreConfigList(List<Long> storeIds, String configKey) {
        TenantStoreConfigEnum.StoreConfig storeConfigEnum = TenantStoreConfigEnum.StoreConfig.getConfigEnum(configKey);
        if (storeConfigEnum == null) {
            throw new BizException("租户门店配置不存在");
        }

        List<TenantStoreConfigVO> resultList = new ArrayList<>();

        List<TenantStoreCommonConfig> configList = tenantStoreCommonConfigRepository.queryByStoreIdsAndConfigKey(storeIds, configKey);
        Map<Long, TenantStoreCommonConfig> storeCommonConfigMap = configList.stream().collect(Collectors.toMap(TenantStoreCommonConfig::getStoreId, Function.identity(), (v1, v2) -> v1));

        Map<Long, Long> storeId2TenantIdMap = new HashMap<>();

        // 需要初始化的门店配置
        List<Long> needInsertStoreIds = storeIds.stream().filter(e -> storeCommonConfigMap.get(e) == null).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(needInsertStoreIds)) {
            List<MerchantStoreResultResp> storeResultResps = userCenterMerchantStoreFacade.getMerchantStoreList(needInsertStoreIds);
            storeId2TenantIdMap = storeResultResps.stream().collect(Collectors.toMap(MerchantStoreResultResp::getId, MerchantStoreResultResp::getTenantId, (v1, v2) -> v1));

            Map<Long, Long> finalStoreId2TenantIdMap = storeId2TenantIdMap;
            List<TenantStoreCommonConfig> needInsertConfig = needInsertStoreIds.stream().map(storeId -> {
                TenantStoreCommonConfig tenantStoreCommonConfig = new TenantStoreCommonConfig();
                tenantStoreCommonConfig.setTenantId(finalStoreId2TenantIdMap.get(storeId));
                tenantStoreCommonConfig.setStoreId(storeId);
                tenantStoreCommonConfig.setConfigKey(storeConfigEnum.getConfigKey());
                tenantStoreCommonConfig.setConfigValue(getStoreConfigDefaultValue(storeConfigEnum, tenantStoreCommonConfig.getTenantId()));
                tenantStoreCommonConfig.setConfigDesc(storeConfigEnum.getConfigDesc());
                return tenantStoreCommonConfig;
            }).collect(Collectors.toList());

            tenantStoreCommonConfigRepository.batchSave(needInsertConfig);
        }

        for (Long storeId : storeIds) {
            TenantStoreConfigVO tenantStoreConfigVO = new TenantStoreConfigVO();

            TenantStoreCommonConfig existRecord = storeCommonConfigMap.get(storeId);
            if (existRecord == null) {
                // 返回默认值
                tenantStoreConfigVO.setTenantId(storeId2TenantIdMap.get(storeId));
                tenantStoreConfigVO.setStoreId(storeId);
                tenantStoreConfigVO.setConfigKey(storeConfigEnum.getConfigKey());
                tenantStoreConfigVO.setConfigValue(getStoreConfigDefaultValue(storeConfigEnum, tenantStoreConfigVO.getTenantId()));
                tenantStoreConfigVO.setConfigDesc(storeConfigEnum.getConfigDesc());
            } else {
                tenantStoreConfigVO.setTenantId(existRecord.getTenantId());
                tenantStoreConfigVO.setStoreId(existRecord.getStoreId());
                tenantStoreConfigVO.setConfigKey(existRecord.getConfigKey());
                tenantStoreConfigVO.setConfigValue(existRecord.getConfigValue());
                tenantStoreConfigVO.setConfigDesc(existRecord.getConfigDesc());

            }

            resultList.add(tenantStoreConfigVO);
        }

        return resultList;
    }

    /**
     * 获取门店配置的默认值
     * @param storeConfigEnum
     * @param tenantId
     * @return
     */
    private String getStoreConfigDefaultValue(TenantStoreConfigEnum.StoreConfig storeConfigEnum, Long tenantId) {
        if (storeConfigEnum == null) {
            return null;
        }

        if (storeConfigEnum == TenantStoreConfigEnum.StoreConfig.DELIVERY_NOTE_PRINT_PRICE) {
            // 新增门店 配送单不打印价格的租户
            if (!CollectionUtils.isEmpty(deliveryNoteNotPrintPriceTenantIds) && deliveryNoteNotPrintPriceTenantIds.contains(tenantId)) {
                return TenantStoreConfigEnum.SwitchEnum.CLOSE.getCode();
            }
        }

        return storeConfigEnum.getDefaultValue();
    }

    @Override
    public void saveOrUpdateTenantStoreConfig(TenantStoreConfigVO tenantStoreConfigVO) {
        TenantStoreConfigEnum.StoreConfig storeConfigEnum = TenantStoreConfigEnum.StoreConfig.getConfigEnum(tenantStoreConfigVO.getConfigKey());
        if (storeConfigEnum == null) {
            throw new BizException("租户门店配置不存在");
        }

        Long tenantId = tenantStoreConfigVO.getTenantId();
        Long storeId = tenantStoreConfigVO.getStoreId();
        TenantStoreCommonConfig tenantStoreCommonConfig = tenantStoreCommonConfigRepository.selectByStoreIdAndConfigKey(tenantId, storeId, tenantStoreConfigVO.getConfigKey());
        if (tenantStoreCommonConfig == null) {
            TenantStoreCommonConfig insertObj = new TenantStoreCommonConfig();
            insertObj.setTenantId(tenantId);
            insertObj.setStoreId(storeId);
            insertObj.setConfigKey(storeConfigEnum.getConfigKey());
            insertObj.setConfigValue(tenantStoreConfigVO.getConfigValue());
            insertObj.setConfigDesc(storeConfigEnum.getConfigDesc());
            tenantStoreCommonConfigRepository.saveRecord(insertObj);

        } else {
            TenantStoreCommonConfig updateObj = new TenantStoreCommonConfig();
            updateObj.setId(tenantStoreCommonConfig.getId());
            updateObj.setConfigValue(tenantStoreConfigVO.getConfigValue());
            tenantStoreCommonConfigRepository.updateRecordById(updateObj);
        }

    }
}
