package com.cosfo.manage.tenant.service.impl;

import com.cosfo.manage.common.context.TenantConfigEnum;
import com.cosfo.manage.common.util.AuthTreeUtil;
import com.cosfo.manage.facade.AuthPurviewFacade;
import com.cosfo.manage.tenant.model.dto.MenuPurviewDTO;
import com.cosfo.manage.tenant.model.po.TenantFunctionSet;
import com.cosfo.manage.tenant.model.po.TenantPrivilegesConfig;
import com.cosfo.manage.tenant.model.vo.FunctionSetVO;
import com.cosfo.manage.tenant.model.vo.TenantCommonConfigVO;
import com.cosfo.manage.tenant.model.vo.TenantPrivilegesInfoVO;
import com.cosfo.manage.tenant.repository.TenantFunctionSetRepository;
import com.cosfo.manage.tenant.repository.TenantPrivilegesConfigRepository;
import com.cosfo.manage.tenant.service.TenantCommonConfigService;
import com.cosfo.manage.tenant.service.TenantPrivilegesInfoService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class TenantPrivilegesInfoServiceImpl implements TenantPrivilegesInfoService {

    @Resource
    private TenantPrivilegesConfigRepository tenantPrivilegesConfigRepository;
    @Resource
    private TenantFunctionSetRepository tenantFunctionSetRepository;
    @Resource
    private TenantCommonConfigService tenantCommonConfigService;
    @Resource
    private AuthPurviewFacade authPurviewFacade;


    @Override
    public TenantPrivilegesInfoVO getPrivilegesInfo(Long tenantId) {
        TenantPrivilegesInfoVO privilegesInfoVO = new TenantPrivilegesInfoVO();
        List<TenantPrivilegesConfig> tenantPrivilegesConfigs = tenantPrivilegesConfigRepository.listByTenantId(tenantId);
        TenantCommonConfigVO tenantCommonConfigVO = tenantCommonConfigService.selectTenantConfig(tenantId, TenantConfigEnum.TenantConfig.TENANT_CUSTOMER_MANAGER.getConfigKey());
        // 获取当前租户菜单
        List<MenuPurviewDTO> menuPurviewDTOS = authPurviewFacade.getAuthTenantMenuPurview(SystemOriginEnum.COSFO_MANAGE, tenantId);
        // 获取当前版本
        Optional<TenantPrivilegesConfig> any = tenantPrivilegesConfigs.stream().filter(config -> config.getConfigType() == 0).findAny();
        if (!any.isPresent()) {
            throw new BizException("当前未购买任何版本,请联系客户经理");
        }
        TenantPrivilegesConfig tenantPrivilegesConfig = any.get();
        List<Long> functionSetIds = tenantPrivilegesConfigs.stream().map(TenantPrivilegesConfig::getFunctionSetId).collect(Collectors.toList());
        List<TenantFunctionSet> tenantFunctionSets = tenantFunctionSetRepository.listBySetIds(functionSetIds);
        Map<Long, TenantFunctionSet> functionSetMap = tenantFunctionSets.stream().collect(Collectors.toMap(TenantFunctionSet::getId, set -> set));
        privilegesInfoVO.setSaleVersion(functionSetMap.get(tenantPrivilegesConfig.getFunctionSetId()).getSaleVersion());
        privilegesInfoVO.setExpireDate(tenantPrivilegesConfig.getExpirationTime());
        privilegesInfoVO.setIsExpired(LocalDate.now().isAfter(tenantPrivilegesConfig.getExpirationTime()));
        privilegesInfoVO.setCustomerManager(tenantCommonConfigVO.getConfigValue());
        //处理已购买功能
        List<FunctionSetVO> collect = tenantPrivilegesConfigs.stream().map(config -> {
            FunctionSetVO functionSetVO = new FunctionSetVO();
            TenantFunctionSet tenantFunctionSet = functionSetMap.get(config.getFunctionSetId());
            functionSetVO.setId(config.getFunctionSetId());
            functionSetVO.setName(tenantFunctionSet.getName());
            functionSetVO.setDesc(tenantFunctionSet.getFuncDesc());

            functionSetVO.setExpireDate(config.getExpirationTime());
            functionSetVO.setExpired(LocalDate.now().isAfter(config.getExpirationTime()));

            List<MenuPurviewDTO> purviewDtos = menuPurviewDTOS.stream().filter(authMenuPurview -> tenantFunctionSet.getPurviewIds().contains(authMenuPurview.getId())).collect(Collectors.toList());
            functionSetVO.setPurviewList(AuthTreeUtil.convertTree(purviewDtos));
            return functionSetVO;
        }).collect(Collectors.toList());
        privilegesInfoVO.setFuncSetList(collect);
        return privilegesInfoVO;
    }

    @Override
    public Set<Long> queryFlagPurviewIds(Long tenantId) {
        // 获取试用功能
        List<TenantPrivilegesConfig> tenantPrivilegesConfigs = tenantPrivilegesConfigRepository.listAddByTenantId(tenantId, 1, 1);
        if(CollectionUtils.isEmpty(tenantPrivilegesConfigs)) {
            return Collections.emptySet();
        }
        List<Long> funcSetIds = tenantPrivilegesConfigs.stream().map(TenantPrivilegesConfig::getFunctionSetId).collect(Collectors.toList());
        List<TenantFunctionSet> tenantFunctionSets = tenantFunctionSetRepository.listBySetIds(funcSetIds);
        //获取所有 purviewIds
        Set<Long> purviewIds = tenantFunctionSets.stream()
                .flatMap(tenantFunctionSet -> tenantFunctionSet.getPurviewIds().stream())
                .collect(Collectors.toSet());
        return purviewIds;
    }
}
