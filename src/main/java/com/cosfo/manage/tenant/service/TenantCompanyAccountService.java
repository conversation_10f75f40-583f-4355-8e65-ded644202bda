package com.cosfo.manage.tenant.service;

import com.cosfo.manage.tenant.model.po.TenantCompanyAccount;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface TenantCompanyAccountService {

    /**
     * 获取供应商打款账号
     * @param supplierIds
     * @param tenantId
     * @return
     */
    Map<Long, TenantCompanyAccount> queryAccountMap(Set<Long> supplierIds, Long tenantId);


    /**
     * 获取供应商对品牌方打款账号
     * @param supplierId 供应商
     * @param tenantId 品牌方
     * @return
     */
    TenantCompanyAccount getAccount(Long supplierId, Long tenantId);
}
