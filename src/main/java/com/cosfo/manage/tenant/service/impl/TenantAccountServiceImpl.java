package com.cosfo.manage.tenant.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.common.util.TimeUtils;
import com.cosfo.manage.common.config.CustomConfig;
import com.cosfo.manage.common.config.GrayReleaseConfig;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.constant.SmsConstants;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.common.context.RedisKeyEnum;
import com.cosfo.manage.common.context.TenantAccountEnums;
import com.cosfo.manage.common.context.TenantConfigEnum;
import com.cosfo.manage.common.context.TenantEnums;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTOEnum;
import com.cosfo.manage.common.sms.SmsSender;
import com.cosfo.manage.common.sms.model.Sms;
import com.cosfo.manage.common.sms.model.SmsSenderFactory;
import com.cosfo.manage.common.util.EmailMessageSenderUtil;
import com.cosfo.manage.common.util.PageInfoConverter;
import com.cosfo.manage.common.util.PageInfoHelper;
import com.cosfo.manage.common.util.RedisUtils;
import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.common.util.email.EmailMessageSender;
import com.cosfo.manage.facade.AuthUserFacade;
import com.cosfo.manage.facade.AuthWechatFacade;
import com.cosfo.manage.facade.auth.AuthMockLoginFacade;
import com.cosfo.manage.facade.dto.AuthUseLoginDTO;
import com.cosfo.manage.facade.dto.SupplierInfoDTO;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantInfoFacade;
import com.cosfo.manage.facade.usercenter.UserCenterTenantAccountFacade;
import com.cosfo.manage.facade.usercenter.UserCenterTenantFacade;
import com.cosfo.manage.msg.service.NoticeService;
import com.cosfo.manage.supplier.service.SupplierService;
import com.cosfo.manage.tenant.convert.RoleConvert;
import com.cosfo.manage.tenant.convert.TenantAccountConvert;
import com.cosfo.manage.tenant.convert.TenantAccountMapperConvert;
import com.cosfo.manage.tenant.convert.TenantMapperConvert;
import com.cosfo.manage.tenant.dao.TenantAccountSupplierMappingDao;
import com.cosfo.manage.tenant.model.dto.*;
import com.cosfo.manage.tenant.model.po.TenantAccount;
import com.cosfo.manage.tenant.model.po.TenantAccountBussinessMsgConfig;
import com.cosfo.manage.tenant.model.po.TenantAccountReceiveMsgSwitch;
import com.cosfo.manage.tenant.model.po.TenantAccountSupplierMapping;
import com.cosfo.manage.tenant.model.po.TenantCommonConfig;
import com.cosfo.manage.tenant.model.vo.*;
import com.cosfo.manage.tenant.service.AuthRoleService;
import com.cosfo.manage.tenant.service.TenantAccountMsgService;
import com.cosfo.manage.tenant.service.TenantAccountService;
import com.cosfo.manage.tenant.service.TenantCommonConfigService;
import com.cosfo.manage.tenant.service.TenantService;
import com.cosfo.message.client.enums.ChannelTypeEnum;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.AuthLoginDto;
import net.xianmu.authentication.client.dto.AuthRole;
import net.xianmu.authentication.client.dto.AuthUserRoleDto;
import net.xianmu.authentication.client.dto.user.AuthUserLastUpdatePwdTimeResp;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.user.AuthUserQueryInput;
import net.xianmu.common.enums.base.auth.WxOfficialAccountsChannelEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.log.config.BizLogRecordContext;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.tenant.req.TenantAccountCommandReq;
import net.xianmu.usercenter.client.tenant.req.TenantAccountListQueryReq;
import net.xianmu.usercenter.client.tenant.req.TenantAccountQueryReq;
import net.xianmu.usercenter.client.tenant.req.TenantQueryReq;
import net.xianmu.usercenter.client.tenant.resp.MerchantResultResp;
import net.xianmu.usercenter.client.tenant.resp.TenantAccountResultResp;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/27
 */
@Slf4j
@Service
public class TenantAccountServiceImpl implements TenantAccountService {
//    @Resource
//    private TenantAccountDao tenantAccountDao;
//    @Resource
//    private TenantDao tenantDao;
    @Resource
    private AuthUserFacade authUserFacade;
    @Resource
    private SmsSenderFactory smsSenderFactory;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private TenantAccountSupplierMappingDao tenantAccountSupplierMappingDao;
    @Resource
    private TenantCommonConfigService tenantCommonConfigService;
    @Resource
    private SupplierService supplierService;
    @Lazy
    @Resource
    private NoticeService noticeService;
    @Resource
    private TenantService tenantService;
    @Resource
    private UserCenterTenantFacade userCenterTenantFacade;
    @Resource
    private UserCenterTenantAccountFacade userCenterTenantAccountFacade;
    @Resource
    private TenantAccountMsgService tenantAccountMsgService;
    @Resource
    private AuthWechatFacade authWechatFacade;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private AuthRoleService authRoleService;
    @Resource
    private CustomConfig customConfig;
    @Resource
    private UserCenterMerchantInfoFacade userCenterMerchantInfoFacade;
    @Resource
    private AuthMockLoginFacade authMockLoginFacade;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private GrayReleaseConfig grayReleaseConfig;
    @Resource
    private EmailMessageSender emailMessageSender;

    @Override
    public TenantAccountPreLoginVO preLogin(TenantAccountPreLoginDTO tenantAccountPreLoginDTO) {
        TenantAccountPreLoginVO tenantAccountPreLoginVO = new TenantAccountPreLoginVO();
        // 校验用户密码是否正确
        AuthUseLoginDTO authUseLoginDTO = authUserFacade.checkPhonePassword(tenantAccountPreLoginDTO.getPhone(), tenantAccountPreLoginDTO.getPassword());
        fillTenantAccountLoginVO(tenantAccountPreLoginVO, authUseLoginDTO);
        if (!authUseLoginDTO.getPasswordSuccess() && Objects.nonNull(authUseLoginDTO.getErrorCount())) {
            return tenantAccountPreLoginVO;
        }
        if (!authUseLoginDTO.getPasswordSuccess()) {
            throw new BizException(ResultDTOEnum.USER_OR_PASSWORD_WRONG.getMessage());
        }

        // 查询品牌方租户
        List<TenantResultResp> tenants = userCenterTenantFacade.getTenantsByQuery(TenantQueryReq.builder().type(TenantEnums.type.BRAND_PARTY.getCode()).build());
        List<Long> tenantIds = tenants.stream().map(TenantResultResp::getId).collect(Collectors.toList());

        // 校验是否多租户
        List<TenantAccount> tenantAccounts = selectByPhone(tenantAccountPreLoginDTO.getPhone(), tenantIds);
        if (CollectionUtils.isEmpty(tenantAccounts)) {
            throw new BizException(ResultDTOEnum.ACCOUNT_FAILURE.getMessage());
        }

        tenantAccountPreLoginVO.setMultiTenant(Boolean.TRUE);
        // 校验是否单租户
        if (NumberConstants.ONE.equals(tenantAccounts.size())) {
            tenantAccountPreLoginVO.setMultiTenant(Boolean.FALSE);
            tenantAccountPreLoginVO.setDefaultTenantId(tenantAccounts.get(NumberConstants.ZERO).getTenantId());
        }

        // 查询密码上次修改时间，比较时间差
        Integer changePasswordIntervalDays = customConfig.getChangePasswordIntervalDays();
        Boolean needChangePassword = queryUserNeedChangePassword(tenantAccountPreLoginDTO.getPhone(), changePasswordIntervalDays);
        tenantAccountPreLoginVO.setIntervalDay(changePasswordIntervalDays);
        tenantAccountPreLoginVO.setNeedChangePassword(needChangePassword);
        Boolean popAlert = needChangePassword && Objects.isNull(redisUtils.get(RedisKeyEnum.CM00010.join(tenantAccountPreLoginDTO.getPhone())));
        tenantAccountPreLoginVO.setPopAlert(popAlert);
        return tenantAccountPreLoginVO;
    }

    @Override
    public TenantAccountPreLoginVO preLoginOld(TenantAccountPreLoginDTO tenantAccountPreLoginDTO) {
        // 校验用户密码是否正确
        Boolean checkPhonePassword = authUserFacade.checkPhonePasswordOld(tenantAccountPreLoginDTO.getPhone(), tenantAccountPreLoginDTO.getPassword());
        if (!checkPhonePassword) {
            throw new BizException(ResultDTOEnum.USER_OR_PASSWORD_WRONG.getMessage());
        }

        // 查询品牌方租户
        List<TenantResultResp> tenants = userCenterTenantFacade.getTenantsByQuery(TenantQueryReq.builder().type(TenantEnums.type.BRAND_PARTY.getCode()).build());
        List<Long> tenantIds = tenants.stream().map(TenantResultResp::getId).collect(Collectors.toList());

        // 校验是否多租户
        List<TenantAccount> tenantAccounts = selectByPhone(tenantAccountPreLoginDTO.getPhone(), tenantIds);
        if (CollectionUtils.isEmpty(tenantAccounts)) {
            throw new BizException(ResultDTOEnum.ACCOUNT_FAILURE.getMessage());
        }

        TenantAccountPreLoginVO tenantAccountPreLoginVO = new TenantAccountPreLoginVO();
        tenantAccountPreLoginVO.setMultiTenant(Boolean.TRUE);
        // 校验是否单租户
        if (NumberConstants.ONE.equals(tenantAccounts.size())) {
            tenantAccountPreLoginVO.setMultiTenant(Boolean.FALSE);
            tenantAccountPreLoginVO.setDefaultTenantId(tenantAccounts.get(NumberConstants.ZERO).getTenantId());
        }

        return tenantAccountPreLoginVO;
    }

    /**
     * 查询用户是否需要修改密码
     *
     * @param phone
     * @param changePasswordIntervalDays
     * @return
     */
    private Boolean queryUserNeedChangePassword(String phone, Integer changePasswordIntervalDays) {
        if(StringUtils.isBlank(phone)) {
            log.info("手机号不存在！");
            return false;
        }
        AuthUserQueryInput authUserQueryInput = new AuthUserQueryInput();
        authUserQueryInput.setPhone(phone);
        AuthUserLastUpdatePwdTimeResp authUserLastUpdatePwdTimeResp = authUserFacade.queryLastUpdatePwdTime(authUserQueryInput);
        LocalDateTime lastUpdatePwdTime = Optional.ofNullable(authUserLastUpdatePwdTimeResp).map(AuthUserLastUpdatePwdTimeResp::getLastUpdatePwdTime).orElse(LocalDateTime.now());
        long days = Duration.between(lastUpdatePwdTime, LocalDateTime.now()).toDays();
        return days > changePasswordIntervalDays;
    }

    /**
     * 填充登录信息
     * @param tenantAccountPreLoginVO
     * @param authUseLoginDTO
     */
    private void fillTenantAccountLoginVO(TenantAccountPreLoginVO tenantAccountPreLoginVO, AuthUseLoginDTO authUseLoginDTO) {
        tenantAccountPreLoginVO.setPasswordSuccess(authUseLoginDTO.getPasswordSuccess());
        tenantAccountPreLoginVO.setErrorCount(authUseLoginDTO.getErrorCount());
        tenantAccountPreLoginVO.setTotalCount(authUseLoginDTO.getTotalCount());
        tenantAccountPreLoginVO.setRemainCount(authUseLoginDTO.getRemainCount());
        tenantAccountPreLoginVO.setLockTime(authUseLoginDTO.getLockTime());
    }

    /**
     * 填充登录信息
     * @param tenantAccountLoginVO
     * @param authUseLoginDTO
     */
    private void fillTenantAccountLoginVO(TenantAccountLoginVO tenantAccountLoginVO, AuthUseLoginDTO authUseLoginDTO) {
        tenantAccountLoginVO.setPasswordSuccess(authUseLoginDTO.getPasswordSuccess());
        tenantAccountLoginVO.setErrorCount(authUseLoginDTO.getErrorCount());
        tenantAccountLoginVO.setTotalCount(authUseLoginDTO.getTotalCount());
        tenantAccountLoginVO.setRemainCount(authUseLoginDTO.getRemainCount());
        tenantAccountLoginVO.setLockTime(authUseLoginDTO.getLockTime());
    }

    @Override
    public TenantAccountLoginVO login(TenantAccountLoginDTO tenantAccountLoginDTO) {
        TenantAccountLoginVO tenantAccountLoginVO = new TenantAccountLoginVO();
        // 校验用户密码是否正确
        AuthUseLoginDTO authUseLoginDTO = authUserFacade.checkPhonePassword(tenantAccountLoginDTO.getPhone(), tenantAccountLoginDTO.getPassword());
        fillTenantAccountLoginVO(tenantAccountLoginVO, authUseLoginDTO);
        if (!authUseLoginDTO.getPasswordSuccess() && Objects.nonNull(authUseLoginDTO.getErrorCount())) {
            return tenantAccountLoginVO;
        }
        if (!authUseLoginDTO.getPasswordSuccess()) {
            throw new BizException(ResultDTOEnum.USER_OR_PASSWORD_WRONG.getMessage());
        }

        String token = authUserFacade.login(tenantAccountLoginDTO.getTenantId(), tenantAccountLoginDTO.getPhone(), tenantAccountLoginDTO.getPassword());
        tenantAccountLoginVO.setToken(token);
        return tenantAccountLoginVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateUserInfo(TenantAccountDTO tenantAccountDTO, LoginContextInfoDTO loginContextInfoDTO) {
        tenantAccountDTO.setTenantId(loginContextInfoDTO.getTenantId());
        if (Objects.isNull(tenantAccountDTO.getId())) {
            tenantAccountDTO.setAuthBaseId(loginContextInfoDTO.getAuthUserId());
            tenantAccountDTO.setOperatorPhone(loginContextInfoDTO.getPhone());
            tenantAccountDTO.setPhone(loginContextInfoDTO.getPhone());

            TenantAccount tenantAccount = selectByAuthUserId(tenantAccountDTO.getAuthBaseId());
            BizLogRecordContext.put("oldNickName",tenantAccount.getNickname());
            BizLogRecordContext.put("accountPhone",StrUtil.isNotBlank(tenantAccount.getPhone()) ? tenantAccount.getPhone() : tenantAccount.getEmail());
            BizLogRecordContext.put("oldAccountStatus",tenantAccount.getStatus());
        } else if (!checkHavingSuperAdmin(loginContextInfoDTO.getAuthUserId())) {
            throw new BizException("不属于超级管理员账号，沒有查看权限");
        }

        if (Objects.isNull(tenantAccountDTO.getAuthBaseId())) {
            // 查询账号
            TenantAccount tenantAccount = selectById(tenantAccountDTO.getId());
            // 判空校验
            if (Objects.isNull(tenantAccount)) {
                throw new BizException("账号不存在");
            }

            tenantAccountDTO.setAuthBaseId(tenantAccount.getAuthUserId());
            tenantAccountDTO.setPhone(tenantAccount.getPhone());
            tenantAccountDTO.setOperatorPhone(loginContextInfoDTO.getPhone());

            BizLogRecordContext.put("oldNickName",tenantAccount.getNickname());
            BizLogRecordContext.put("accountPhone",StrUtil.isNotBlank(tenantAccount.getPhone()) ? tenantAccount.getPhone() : tenantAccount.getEmail());
            BizLogRecordContext.put("oldAccountStatus",tenantAccount.getStatus());
        }

        // 查询品牌方租户
//        List<Integer> types = Arrays.asList(TenantEnums.type.BRAND_PARTY.getCode());
//        List<Tenant> tenants = tenantDao.queryByTenantType(types);
        List<TenantResultResp> tenants = userCenterTenantFacade.getTenantsByQuery(TenantQueryReq.builder().type(TenantEnums.type.BRAND_PARTY.getCode()).build());
        List<Long> tenantIds = tenants.stream().map(TenantResultResp::getId).collect(Collectors.toList());
        // 更新当前租户下用户信息
        tenantAccountDTO.setOperatorTime(LocalDateTime.now());

        // 放入日志context
        putRoleContext(tenantAccountDTO);

        update(tenantAccountDTO, tenantIds);
        // 供应商配送员角色
        updateSupplierDistributorMapping(tenantAccountDTO);
        // 更新auth服务用户信息
        return authUserFacade.updateUser(tenantAccountDTO);
    }

    private void putRoleContext(TenantAccountDTO tenantAccountDTO) {
        try {
            List<AuthUserRoleDto> authUserRoleDtos = authUserFacade.getUserRoleByUserIds(Collections.singletonList(tenantAccountDTO.getAuthBaseId()));
            AuthUserRoleDto authUserRoleDto = authUserRoleDtos.stream()
                .filter(i -> i.getId().equals(tenantAccountDTO.getAuthBaseId()))
                .findFirst()
                .orElseGet(AuthUserRoleDto::new);
            Map<String, String> oldAccount = new HashMap<>();
            oldAccount.put("phone", Optional.ofNullable(BizLogRecordContext.get("accountPhone")).orElse("").toString());
            oldAccount.put("nickName", Optional.ofNullable(BizLogRecordContext.get("oldNickName")).orElse("").toString());
            oldAccount.put("status", Optional.ofNullable(BizLogRecordContext.get("oldAccountStatus")).orElse("").toString());
            List<AuthRole> roles = authUserRoleDto.getRoles();
            if (!CollectionUtils.isEmpty(roles)) {
                oldAccount.put("roleId", roles.get(0).getId().toString());
            }
            BizLogRecordContext.put("oldAccount", oldAccount);

            Map<String, String> newAccount = new HashMap<>();
            newAccount.put("phone", Optional.ofNullable(tenantAccountDTO.getPhone()).orElse(oldAccount.get("phone")));
            newAccount.put("nickName", Optional.ofNullable(tenantAccountDTO.getNickname()).orElse(oldAccount.get("nickName")));
            if (Objects.isNull(tenantAccountDTO.getStatus())) {
                newAccount.put("status", oldAccount.get("status"));
            } else {
                newAccount.put("status", tenantAccountDTO.getStatus().toString());
            }
            List<Long> roleIds = tenantAccountDTO.getRoleIds();
            if (!CollectionUtils.isEmpty(roleIds)) {
                newAccount.put("roleId", roleIds.get(0).toString());
            }else {
                newAccount.put("roleId", oldAccount.get("roleId"));
            }
            BizLogRecordContext.put("newAccount", newAccount);

            Map<String, Object> content = new HashMap<>();
            content.put("oldAccount", oldAccount);
            content.put("newAccount", newAccount);
            BizLogRecordContext.put("content", content);
        } catch (Exception e) {
            log.error("获取日志上下文失败！", e);
        }
    }

    /**
     * 更新账号和供应商之间的映射
     * @param tenantAccountDTO 账号信息
     */
    private void updateSupplierDistributorMapping(TenantAccountDTO tenantAccountDTO) {
        List<Long> supplierIds = tenantAccountDTO.getSupplierIds();
        if (CollectionUtils.isEmpty(supplierIds)) {
            return;
        }
        removeTenantAccountSupplierMapping(tenantAccountDTO.getId());
        if(includeSupplierDistributorRoleFlag(tenantAccountDTO)) {
            createSupplierDistributorMapping (tenantAccountDTO);
        }
    }

    @Override
    public Boolean create(TenantAccountDTO tenantAccountDTO) {
        //查询该租户下初始化的供应商角色
        TenantCommonConfig config = tenantCommonConfigService.selectByTenantIdAndConfigKey(tenantAccountDTO.getTenantId(), TenantConfigEnum.TenantConfig.SUPPLIER_DISTRIBUTOR_ROLE_ID.getConfigKey());
        if (config == null) {
            return false;
        }
        String supplierDistributorIdStr = config.getConfigValue();
        Long supplierDistributorId = Long.valueOf(supplierDistributorIdStr);
        List<Long> roleIds = tenantAccountDTO.getRoleIds();

        boolean includeSupplierDistributorRole =  roleIds.contains(supplierDistributorId);
        if (includeSupplierDistributorRole) {
            List<TenantAccountSupplierMapping> mappings = tenantAccountSupplierMappingDao.queryBySupplierIdList(tenantAccountDTO.getSupplierIds (), tenantAccountDTO.getTenantId ());
            if(CollectionUtil.isNotEmpty (mappings) && mappings.size () > 9){
                throw new BizException ("最多添加10个管理员");
            }
            String phone = tenantAccountDTO.getPhone();
            String errorMessage = "手机号";
            List<TenantAccountResultResp> dbs;
            if(StrUtil.isNotBlank(phone)) {
                errorMessage = "手机号";
                dbs = userCenterTenantAccountFacade.getTenantAccountByTenantIdsAndPhone (tenantAccountDTO.getTenantId (), phone).stream ().filter (e->e.getDeletedFlag ().equals (TenantAccountEnums.DeletedFlag.EFFECTIVE.getCode ())).collect(Collectors.toList());
            } else {
                dbs = userCenterTenantAccountFacade.getTenantAccountByTenantIdsAndEmail (tenantAccountDTO.getTenantId (), tenantAccountDTO.getEmail ()).stream ().filter (e->e.getDeletedFlag ().equals (TenantAccountEnums.DeletedFlag.EFFECTIVE.getCode ())).collect(Collectors.toList());
                errorMessage = "邮箱";
            }

            if(CollectionUtil.isNotEmpty (dbs)){
                TenantAccountResultResp tenantAccountResultResp = dbs.get (0);
                Integer status = tenantAccountResultResp.getStatus ();
                if(Objects.equals (TenantAccountEnums.status.FAILURE.getCode (), status)){
                    throw new BizException ("该" + errorMessage + "已经被占用，请更换");
                }
                List<AuthUserRoleDto> authUserRoleDtos = authUserFacade.getUserRoleByUserIds(Arrays.asList(tenantAccountResultResp.getAuthUserId ()));
                Long id = tenantAccountResultResp.getId ();
                if(CollectionUtil.isNotEmpty (authUserRoleDtos)){
                    boolean present = authUserRoleDtos.stream ().anyMatch (authUserRoleDto -> authUserRoleDto.getRoles ().stream().map (AuthRole::getId).collect(Collectors.toList()).contains (supplierDistributorId));
                    if(!present){
                        throw new BizException ("该" + errorMessage + "已经被占用，请更换");
                    }
                }
                if(CollectionUtil.isNotEmpty (mappings) ){
                    if(mappings.stream().anyMatch (e-> Objects.equals (id, e.getAccountId ()))) {
                        throw new BizException ("该" + errorMessage + "已经是该供应商的管理员啦，不可重复添加");
                    }else{
                        tenantAccountDTO.setId (id);
                        createSupplierDistributorMapping(tenantAccountDTO);
                    }
                }else{
                    tenantAccountDTO.setId (id);
                    createSupplierDistributorMapping(tenantAccountDTO);
                }
            }else{
                TenantAccountCommandReq commandReq = TenantAccountMapperConvert.INSTANCE.dtoToCommandReq (tenantAccountDTO);
                commandReq.setSystemOrigin (SystemOriginEnum.COSFO_MANAGE.getType ());
                String password = createPassword (tenantAccountDTO.getPhone ());
                commandReq.setLoginPassword (password);
                Long tenantAccountId = userCenterTenantAccountFacade.createTenantAccount (commandReq);
                // 异步短信通知
                noticeUser (tenantAccountDTO.getTenantId (), tenantAccountDTO.getPhone (), password, tenantAccountDTO.getEmail());
                // 供应商映射
                tenantAccountDTO.setId (tenantAccountId);
                createSupplierDistributorMapping(tenantAccountDTO);
            }
        }else {
            TenantAccountCommandReq commandReq = TenantAccountMapperConvert.INSTANCE.dtoToCommandReq (tenantAccountDTO);
            commandReq.setSystemOrigin (SystemOriginEnum.COSFO_MANAGE.getType ());
            String password = createPassword (tenantAccountDTO.getPhone ());
            commandReq.setLoginPassword (password);
            userCenterTenantAccountFacade.createTenantAccount (commandReq);
            // 异步短信通知
            noticeUser (tenantAccountDTO.getTenantId (), tenantAccountDTO.getPhone (), password, tenantAccountDTO.getEmail());
        }

//        check(tenantAccountDTO);
//        // 判断账号是否存在，存在更新账号
//        TenantAccount tenantAccount = tenantAccountDao.selectByPhoneAndTenantId(tenantAccountDTO.getTenantId(), tenantAccountDTO.getPhone());
//        String password = createPassword(tenantAccountDTO.getPhone());
//
//        if (Objects.isNull(tenantAccount)) {
//            // 发送短信通知用户注册密码或把密码返回
//            tenantAccountDTO.setLoginPassword(password);
//            // 创建auth服务账号
//            Long authUserId = authUserFacade.createUser(tenantAccountDTO);
//            // 创建账号
//            tenantAccount = new TenantAccount();
//            tenantAccount.setId(authUserId);
//            tenantAccount.setAuthUserId(authUserId);
//            tenantAccount.setPhone(tenantAccountDTO.getPhone());
//            tenantAccount.setNickname(tenantAccountDTO.getNickname());
//            tenantAccount.setOperatorPhone(tenantAccountDTO.getOperatorPhone());
//            tenantAccount.setOperatorTime(LocalDateTime.now());
//            tenantAccount.setStatus(TenantAccountEnums.status.EFFECTIVE.getCode());
//            tenantAccount.setTenantId(tenantAccountDTO.getTenantId());
//            tenantAccountDao.save(tenantAccount);
//            // 新增账号和供应商之间的映射
//            tenantAccountDTO.setId(tenantAccount.getId());
//            createSupplierDistributorMapping(tenantAccountDTO);
//
//            // 异步短信通知
//            noticeUser(tenantAccountDTO.getTenantId(), tenantAccountDTO.getPhone(), password);
//            return Boolean.TRUE;
//        }
//
//        if (TenantAccountEnums.DeletedFlag.EFFECTIVE.getCode().equals(tenantAccount.getDeletedFlag())) {
//            throw new BizException("账号已存在");
//        }
//
//        // 更新
//        // 更新auth
//        tenantAccountDTO.setAuthBaseId(tenantAccount.getAuthUserId());
//        tenantAccountDTO.setTenantId(tenantAccount.getTenantId());
//        tenantAccountDTO.setStatus(TenantAccountEnums.status.EFFECTIVE.getCode());
//        tenantAccountDTO.setLoginPassword(password);
//        authUserFacade.updateUser(tenantAccountDTO);
//        // 更新自己
//        tenantAccount.setNickname(tenantAccountDTO.getNickname());
//        tenantAccount.setDeletedFlag(TenantAccountEnums.DeletedFlag.EFFECTIVE.getCode());
//        tenantAccount.setStatus(TenantAccountEnums.status.EFFECTIVE.getCode());
//        // TODO: 2023/5/26 待完善
//        tenantAccountDao.saveOrUpdate(tenantAccount);
//
//        // 供应商映射
//        tenantAccountDTO.setId(tenantAccount.getId());
//        createSupplierDistributorMapping(tenantAccountDTO);
//
//        // 异步通知
//        noticeUser(tenantAccount.getTenantId(), tenantAccount.getPhone(), password);
        return Boolean.TRUE;
    }

    private void noticeUser(Long tenantId, String phone, String password, String email) {
        TenantResultResp tenant = userCenterTenantFacade.getTenantById(tenantId);
        if(Objects.equals(tenant.getAccountLoginType(), 0)) {
            this.noticeUserByPhone(tenantId, phone, password);
        } else {
            this.noticeUserByEmail(tenantId, email, password);
        }
    }

    private void noticeUserByPhone(Long tenantId, String phone, String password) {
        TenantAccountQueryReq tenantAccountQueryReq = new TenantAccountQueryReq();
        tenantAccountQueryReq.setPhone(phone);
        tenantAccountQueryReq.setDeletedFlag(TenantAccountEnums.DeletedFlag.EFFECTIVE.getCode());
        List<TenantAccountResultResp> tenantAccounts = userCenterTenantAccountFacade.getTenantAccounts(tenantAccountQueryReq);
//        List<TenantAccount> tenantAccounts = selectByPhone(phone, null);
        if (CollectionUtils.isEmpty(tenantAccounts)) {
            return;
        }
        TenantDTO tenantDTO = tenantService.queryTenantById(tenantId);
        Long sceneId = tenantAccounts.size() == 1 ? SmsConstants.TENANT_ACCOUNT_OPEN : SmsConstants.TENANT_ACCOUNT_OPEN_AGAIN;
        List<String> args = tenantAccounts.size() == 1 ? Arrays.asList(phone, tenantDTO.getTenantName(), phone, password) : Arrays.asList(phone, tenantDTO.getTenantName(), phone);
        Sms sms = new Sms();
        sms.setPhone(phone);
        sms.setSceneId(sceneId);
        sms.setArgs(args);
        noticeService.sendSmsCode(sms);
    }



    private void noticeUserByEmail(Long tenantId, String email, String password) {
        TenantAccountQueryReq tenantAccountQueryReq = new TenantAccountQueryReq();
        tenantAccountQueryReq.setEmail(email);
        tenantAccountQueryReq.setDeletedFlag(TenantAccountEnums.DeletedFlag.EFFECTIVE.getCode());
        List<TenantAccountResultResp> tenantAccounts = userCenterTenantAccountFacade.getTenantAccounts(tenantAccountQueryReq);
        if (CollectionUtils.isEmpty(tenantAccounts)) {
            return;
        }
        emailMessageSender.sendUserPassword(email, email, password);
    }



    private void check(TenantAccountDTO tenantAccountDTO) {
        List<Long> roleIds = tenantAccountDTO.getRoleIds();
        if (CollectionUtils.isEmpty(roleIds)) {
            throw new ParamsException("请输入账号所对应的权限");
        }
        TenantCommonConfig config = tenantCommonConfigService.selectByTenantIdAndConfigKey(tenantAccountDTO.getTenantId(), TenantConfigEnum.TenantConfig.SUPPLIER_DISTRIBUTOR_ROLE_ID.getConfigKey());
        if (config == null) {
            return;
        }
        String configValue = config.getConfigValue();
        Long supplierDistributorRoleId = Long.valueOf(configValue);
        boolean containsSupplierDistributorRole = tenantAccountDTO.getRoleIds().contains(supplierDistributorRoleId);
        List<Long> supplierIds = tenantAccountDTO.getSupplierIds();
        if (containsSupplierDistributorRole && CollectionUtils.isEmpty(supplierIds)) {
            throw new ParamsException("请选择对应的供应商");
        }
    }

    private boolean includeSupplierDistributorRoleFlag (TenantAccountDTO tenantAccountDTO) {
        //查询该租户下初始化的供应商角色
        TenantCommonConfig config = tenantCommonConfigService.selectByTenantIdAndConfigKey(tenantAccountDTO.getTenantId(), TenantConfigEnum.TenantConfig.SUPPLIER_DISTRIBUTOR_ROLE_ID.getConfigKey());
        if (config == null) {
            return false;
        }
        String supplierDistributorIdStr = config.getConfigValue();
        Long supplierDistributorId = Long.valueOf(supplierDistributorIdStr);
        List<Long> roleIds = tenantAccountDTO.getRoleIds();
        return roleIds.contains(supplierDistributorId);
    }
    /**
     * 新建账号和供应商之间的映射
     * @param tenantAccountDTO 账号信息
     */
    public void createSupplierDistributorMapping(TenantAccountDTO tenantAccountDTO) {
        List<Long> supplierIds = tenantAccountDTO.getSupplierIds();
        List<TenantAccountSupplierMapping> mappings = tenantAccountSupplierMappingDao.queryByAccountId(tenantAccountDTO.getId());
        List<Long> alreadyExistedSupplierIds = mappings.stream().map(TenantAccountSupplierMapping::getSupplierId).collect(Collectors.toList());

        transactionTemplate.execute(status -> {
            List<TenantAccountSupplierMapping> mappingList = supplierIds.stream().filter(el -> !alreadyExistedSupplierIds.contains(el)).map(el -> {
                TenantAccountSupplierMapping mapping = new TenantAccountSupplierMapping();
                mapping.setTenantId(tenantAccountDTO.getTenantId());
                mapping.setAccountId(tenantAccountDTO.getId());
                mapping.setSupplierId(el);
                return mapping;
            }).collect(Collectors.toList());
            tenantAccountSupplierMappingDao.saveBatch(mappingList);
            tenantAccountMsgService.addOrUpdateTenantAccountBussinessMsgConfig (tenantAccountDTO.getConfigVOS (),tenantAccountDTO.getId(),tenantAccountDTO.getTenantId ());
            return null;
        });
    }

    private String createPassword(String phone) {
        // 获取年份
        String year = TimeUtils.changeDate2String(new Date(), TimeUtils.FORMAT_YEAR_ONLY);
        // 获取手机后四位
        String substring = RandomStringUtils.randomNumeric(4);
        if(StrUtil.isNotBlank(phone)) {
            substring = phone.substring(phone.length() - NumberConstants.FOUR);
        }
        StringBuffer stringBuffer = new StringBuffer(year).append(StringConstants.CHARACTER).append(substring);
        // 生成随机四位大小写字符串
        String randomStr = RandomStringUtils.randomAlphabetic(4);
        while (!StringUtils.checkAccountPassword(stringBuffer.toString() + randomStr)) {
            randomStr = RandomStringUtils.randomAlphabetic(4);
        }
        stringBuffer.append(randomStr);
        return stringBuffer.toString();
    }
    @Override
    public TenantAccountVO getTenantAccountVO(Long authUserId) {

        // 查询用户信息
        TenantAccount tenantAccount = selectByAuthUserId(authUserId);
        TenantAccountVO tenantAccountVO = TenantAccountConvert.convertToTenantAccountVO(tenantAccount);


        // 查询租户信息
        TenantResultResp tenant = userCenterTenantFacade.getTenantById(tenantAccount.getTenantId());

        // 查询绑定的角色
        List<AuthUserRoleDto> authUserRoleDtos = authUserFacade.getUserRoleByUserIds(Arrays.asList(authUserId));
        Map<Long, AuthUserRoleDto> authUserRoleDtoMap = authUserRoleDtos.stream().collect(Collectors.toMap(AuthUserRoleDto::getId, item -> item));
        getTenantAccountVO(tenantAccountVO, authUserRoleDtoMap);
        // 供应商角色返回对应的供应商ids
        List<TenantAccountSupplierMapping> tenantAccountSupplierMappingList = tenantAccountSupplierMappingDao.queryByAccountId(tenantAccount.getId());
        if (!CollectionUtils.isEmpty(tenantAccountSupplierMappingList)) {
            tenantAccountVO.setSupplierDistributorFlag(Boolean.TRUE);
            List<Long> supplierIds = tenantAccountSupplierMappingList.stream().map(TenantAccountSupplierMapping::getSupplierId).collect(Collectors.toList());
            List<SupplierInfoDTO> supplierInfoDTOS = supplierService.batchQuerySupplierList(tenantAccount.getTenantId(), supplierIds);
            TenantAccountSupplierMappingVO tenantAccountSupplierMappingVO = new TenantAccountSupplierMappingVO();
            tenantAccountSupplierMappingVO.setSupplierList(supplierInfoDTOS);
            tenantAccountSupplierMappingVO.setSupplierDistributorFlag(Boolean.TRUE);
            tenantAccountVO.setTenantAccountSupplierMappingVO(tenantAccountSupplierMappingVO);
            //查询 是否关注
            Map<String, String> authUserMap = authWechatFacade.queryAuthMapRespByPhones4FTGYL (Collections.singletonList (tenantAccount.getPhone ()), tenantAccount.getTenantId ());
            fillFtgylWatchFlag(tenantAccountVO,authUserMap);
        }

        // 查询密码上次修改时间，比较时间差
        Integer changePasswordIntervalDays = customConfig.getChangePasswordIntervalDays();
        Boolean needChangePassword = queryUserNeedChangePassword(tenantAccountVO.getPhone(), changePasswordIntervalDays);
        tenantAccountVO.setIntervalDay(changePasswordIntervalDays);
        tenantAccountVO.setNeedChangePassword(needChangePassword);
        Boolean popAlert = needChangePassword && Objects.isNull(redisUtils.get(RedisKeyEnum.CM00010.join(tenantAccountVO.getPhone())));
        tenantAccountVO.setPopAlert(popAlert);
        tenantAccountVO.setAccountLoginType(tenant.getAccountLoginType());
        return tenantAccountVO;
    }

    @Override
    public List<TenantAccountVO> selectByAuthUserIds(List<Long> authUserIds) {
        List<TenantAccountResultResp> respList = userCenterTenantAccountFacade.getTenantAccountsByAuthUserIds(authUserIds);
        List<TenantAccountVO> tenantAccountVOList = respList.stream().map(tenantAccount -> {
            TenantAccountVO tenantAccountVO = new TenantAccountVO();
            BeanUtils.copyProperties(tenantAccount, tenantAccountVO);
            return tenantAccountVO;
        }).collect(Collectors.toList());
        return tenantAccountVOList;
    }

    @Override
    public TenantAccountVO getSaasTenantAccountVO(Long authUserId) {        // 查询用户信息
        TenantAccount tenantAccount = selectByAuthUserId(authUserId);
        TenantAccountVO tenantAccountVO = TenantAccountConvert.convertToTenantAccountVO(tenantAccount);
        return tenantAccountVO;
    }

    private TenantAccountVO getTenantAccountVO(TenantAccountVO tenantAccountVO, Map<Long, AuthUserRoleDto> authUserRoleDtoMap) {
        if (authUserRoleDtoMap.containsKey(tenantAccountVO.getAuthUserId())) {
            AuthUserRoleDto authUserRoleDto = authUserRoleDtoMap.get(tenantAccountVO.getAuthUserId());
            List<AuthRole> roles = authUserRoleDto.getRoles();
            List<RoleVO> roleVOS = RoleConvert.convertToRoleVOList(roles);
            tenantAccountVO.setRoleVos(roleVOS);
        }

        return tenantAccountVO;
    }


    @Override
    public PageInfo<TenantAccountVO> page(TenantAccountListQueryDTO tenantAccountListQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        Long tenantId = loginContextInfoDTO.getTenantId();
        tenantAccountListQueryDTO.setTenantId(tenantId);
        if (Objects.nonNull(tenantAccountListQueryDTO.getRoleId())) {
            List<Long> authUserIds = authUserFacade.getUserIdListByRoleId(tenantAccountListQueryDTO.getRoleId());
            if (CollectionUtils.isEmpty(authUserIds)) {
                return PageInfoHelper.createPageInfo(new ArrayList<>(), tenantAccountListQueryDTO.getPageSize());
            }

            tenantAccountListQueryDTO.setAuthUserIds(authUserIds);
        }else{
            RoleQueryDTO roleQueryDTO = new RoleQueryDTO ();
            roleQueryDTO.setPageIndex (1);
            roleQueryDTO.setPageSize (1000);
            PageInfo<RoleVO> roleVOPageInfo = authRoleService.queryRolePageWithOutSupplierDistributorRoleId (roleQueryDTO, loginContextInfoDTO);
            List<RoleVO> list = roleVOPageInfo.getList ();
            if (CollectionUtils.isEmpty(list)) {
                return PageInfoHelper.createPageInfo(new ArrayList<>(), tenantAccountListQueryDTO.getPageSize());
            }
            List<Long> authUserIds = authUserFacade.getUserIdListByRoleIds(list.stream ().map (RoleVO::getId).collect (Collectors.toList ()));
            if (CollectionUtils.isEmpty(authUserIds)) {
                return PageInfoHelper.createPageInfo(new ArrayList<>(), tenantAccountListQueryDTO.getPageSize());
            }
            tenantAccountListQueryDTO.setAuthUserIds(authUserIds);
        }

//        PageHelper.startPage(tenantAccountListQueryDTO.getPageIndex(), tenantAccountListQueryDTO.getPageSize());
//        List<TenantAccount> tenantAccounts = tenantAccountDao.listByCondition(tenantAccountListQueryDTO);
        TenantAccountListQueryReq queryReq = new TenantAccountListQueryReq();
        queryReq.setTenantId(tenantAccountListQueryDTO.getTenantId());
        queryReq.setPhone(tenantAccountListQueryDTO.getPhone());
        queryReq.setNickName(tenantAccountListQueryDTO.getNickName());
        queryReq.setStatus(tenantAccountListQueryDTO.getStatus());
        queryReq.setAuthUserIds(tenantAccountListQueryDTO.getAuthUserIds());
        queryReq.setNickId(tenantAccountListQueryDTO.getNickId());
        queryReq.setUsername(tenantAccountListQueryDTO.getEmail());

        PageQueryReq pageQueryReq = new PageQueryReq();
        pageQueryReq.setPageIndex(tenantAccountListQueryDTO.getPageIndex());
        pageQueryReq.setPageSize(tenantAccountListQueryDTO.getPageSize());

        PageInfo<TenantAccountResultResp> tenantAccountInfoPage = userCenterTenantAccountFacade.getTenantAccountInfoPage(queryReq, pageQueryReq);
        List<TenantAccountResultResp> respList = tenantAccountInfoPage.getList();
        List<TenantAccount> tenantAccounts = TenantAccountMapperConvert.INSTANCE.respListToTenantAccountList(respList);
        if (CollectionUtils.isEmpty(tenantAccounts)) {
            return PageInfoHelper.createPageInfo(new ArrayList<>(), tenantAccountListQueryDTO.getPageSize());
        }

//        PageInfo pageInfo = PageInfoHelper.createPageInfo(tenantAccounts, tenantAccountListQueryDTO.getPageSize());
        List<Long> authUserIds = tenantAccounts.stream().map(TenantAccount::getAuthUserId).collect(Collectors.toList());
        // 查询绑定的角色
        List<AuthUserRoleDto> authUserRoleDtos = authUserFacade.getUserRoleByUserIds(authUserIds);
        Map<Long, AuthUserRoleDto> authUserRoleDtoMap = authUserRoleDtos.stream().collect(Collectors.toMap(AuthUserRoleDto::getId, item -> item));
        List<TenantAccountVO> tenantAccountVOS = tenantAccounts.stream().map(tenantAccount -> {
            TenantAccountVO tenantAccountVO = TenantAccountConvert.convertToTenantAccountVO(tenantAccount);
            return getTenantAccountVO(tenantAccountVO, authUserRoleDtoMap);
        }).collect(Collectors.toList());

//        pageInfo.setList(tenantAccountVOS);
//        return pageInfo;
        return PageInfoConverter.toPageInfoTransfer(tenantAccountInfoPage,tenantAccountVOS);
    }

    @Override
    public void remove(Long tenantAccountId, Long tenantId) {
        // id和tenantId查询
        // 查询用户
        TenantAccount tenantAccount = selectByIdAndTenantId(tenantAccountId, tenantId);
        // 判断是否为空
        if (Objects.isNull(tenantAccount)) {
            throw new BizException("该账号不存在");
        }

        BizLogRecordContext.put("accountPhone", StrUtil.isNotBlank(tenantAccount.getPhone()) ? tenantAccount.getPhone() : tenantAccount.getEmail());

        // 查询绑定的角色
        List<AuthUserRoleDto> authUserRoleDtos = authUserFacade.getUserRoleByUserIds(Arrays.asList(tenantAccount.getAuthUserId()));
        Map<Long, AuthUserRoleDto> authUserRoleDtoMap = authUserRoleDtos.stream().collect(Collectors.toMap(AuthUserRoleDto::getId, item -> item));
        if (authUserRoleDtoMap.containsKey(tenantAccount.getAuthUserId())) {
            AuthUserRoleDto authUserRoleDto = authUserRoleDtoMap.get(tenantAccount.getAuthUserId());
            List<AuthRole> roles = authUserRoleDto.getRoles();
            // 判断是否属于超级管理员，超级管理员角色不可变更
            boolean match = roles.stream().anyMatch(authRole -> TenantAccountEnums.Role.SUPER_ADMIN.getCode().equals(authRole.getSuperAdmin().intValue()));
            if (match) {
                throw new BizException("该用户属于超级管理员，不可删除该用户");
            }
        }

        // 删除供应商配送员映射
        removeTenantAccountSupplierMapping(tenantAccountId);

        // 变更状态
        remove(tenantAccountId);
        TenantAccountDTO tenantAccountDTO = new TenantAccountDTO();
        tenantAccountDTO.setAuthBaseId(tenantAccount.getAuthUserId());
        authUserFacade.updateUser(tenantAccountDTO);
    }

    private void removeTenantAccountSupplierMapping(Long accountId) {
        List<TenantAccountSupplierMapping> mappings = tenantAccountSupplierMappingDao.queryByAccountId(accountId);
        if (CollectionUtils.isEmpty(mappings)) {
            return;
        }
        tenantAccountSupplierMappingDao.removeByIds(mappings.stream().map(TenantAccountSupplierMapping::getId).collect(Collectors.toList()));
    }

    @Override
    public Boolean sendCode(String phone) {
        RLock lock = redissonClient.getLock(RedisKeyEnum.CM00011.join(phone));
        if (!lock.tryLock()) {
            throw new BizException(phone + "正在发送验证码，请稍后再试");
        }
        try {
            String cacheKey = RedisKeyEnum.CM00012.join(phone);
            if (Objects.nonNull(redisTemplate.opsForValue().get(cacheKey))) {
                throw new BizException(phone + "近一分钟已发送验证码，请稍后再试");
            }
            String code = String.valueOf(SmsSender.buildRandom(6));
            Sms sms = new Sms();
            sms.setPhone(phone);
            sms.setSceneId(SmsConstants.SCENE_ID);
            sms.setArgs(Arrays.asList(code));

            // 发送验证码
            boolean success = smsSenderFactory.getSmsSender().sendSms(sms);
            if (!success) {
                throw new BizException(ResultDTOEnum.SEND_CODE_FAIL.getMessage());
            }

            // 缓存
            String key = SmsConstants.CODE_PREFIX + phone;
            redisUtils.set(key, code, SmsConstants.EXPIRATION_TIME);
            // 设置发送标记
            redisTemplate.opsForValue().set(cacheKey, String.valueOf(NumberConstants.ONE), grayReleaseConfig.getSmsSendIntervalTime(), TimeUnit.MILLISECONDS);

            log.info("phone：{} code：{}", phone, code);
        } finally {
            lock.unlock();
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean sendCode(SendCodeDTO sendCodeDTO) {
        String phone = sendCodeDTO.getPhone();
        String email = sendCodeDTO.getEmail();
        if(StringUtils.isBlank(phone) && StringUtils.isBlank(email)) {
            throw new BizException("手机号或邮箱必须填写一个");
        }
        if(phone != null) {
            return sendCode(phone);
        }
        return sendCodeByEmail(email);
    }

    public Boolean sendCodeByEmail(String email) {
        RLock lock = redissonClient.getLock(RedisKeyEnum.CM00011.join(email));
        if (!lock.tryLock()) {
            throw new BizException(email + "正在发送验证码，请稍后再试");
        }
        try {
            String cacheKey = RedisKeyEnum.CM00012.join(email);
            if (Objects.nonNull(redisTemplate.opsForValue().get(cacheKey))) {
                throw new BizException(email + "近一分钟已发送验证码，请稍后再试");
            }
            String code = String.valueOf(SmsSender.buildRandom(6));
           
            // 发送验证码
            boolean success =  emailMessageSender.sendVerificationCode(email, code);;
            if (!success) {
                throw new BizException(ResultDTOEnum.SEND_CODE_FAIL.getMessage());
            }

            // 缓存
            String key = SmsConstants.CODE_PREFIX + email;
            redisUtils.set(key, code, SmsConstants.EXPIRATION_TIME);
            // 设置发送标记
            redisTemplate.opsForValue().set(cacheKey, String.valueOf(NumberConstants.ONE), grayReleaseConfig.getSmsSendIntervalTime(), TimeUnit.MILLISECONDS);

            log.info("email：{} code：{}", email, code);
        } finally {
            lock.unlock();
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean examineCode(SendCodeDTO sendCodeDTO) {
        String phone = sendCodeDTO.getPhone();
        String email = sendCodeDTO.getEmail();
        String examineCode = sendCodeDTO.getCode();
        // 无缓存
        String key = !StringUtils.isBlank(phone) ? SmsConstants.CODE_PREFIX + phone : SmsConstants.CODE_PREFIX + email;
        Object codeObj = redisUtils.get(key);
        if (Objects.isNull(codeObj)) {
            throw new BizException("验证码已失效，请重新获取验证码");
        }
        // 验证码错误
        if (!Objects.equals(codeObj.toString(), examineCode)) {
            throw new BizException("验证码不正确");
        }
        // 更改密码
        if (!StringUtils.isEmpty(sendCodeDTO.getLoginPassword())) {
            TenantAccountDTO tenantAccountDTO = new TenantAccountDTO();
            tenantAccountDTO.setPhone(phone);
            tenantAccountDTO.setEmail(email);
            tenantAccountDTO.setLoginPassword(sendCodeDTO.getLoginPassword());
            updatePassword(tenantAccountDTO);
        }

        // 绑定门店信息
        return Boolean.TRUE;
    }

    @Override
    public Boolean updatePassword(TenantAccountDTO tenantAccountDTO) {
        boolean success = StringUtils.checkAccountPassword(tenantAccountDTO.getLoginPassword());
        if (!success) {
            throw new BizException("密码格式不正确(8-20字符，需包含大小写字母和数字)");
        }

        List<TenantAccount> tenantAccounts;

        if(org.apache.commons.lang3.StringUtils.isNotBlank(tenantAccountDTO.getPhone())) {
            List<TenantResultResp> tenants = userCenterTenantFacade.getTenantsByQuery(TenantQueryReq.builder().type(TenantEnums.type.BRAND_PARTY.getCode()).build());
            List<Long> tenantIds = tenants.stream().map(TenantResultResp::getId).collect(Collectors.toList());
            // 校验是否多租户
            tenantAccounts = selectByPhone(tenantAccountDTO.getPhone(), tenantIds);
        } else {
            tenantAccounts = selectBrandAccountByEmail(tenantAccountDTO.getEmail());
        }

        if (CollectionUtils.isEmpty(tenantAccounts)) {
            throw new BizException(ResultDTOEnum.ACCOUNT_FAILURE.getMessage());
        }

        Long authUserId = tenantAccounts.get(NumberConstants.ZERO).getAuthUserId();
        tenantAccountDTO.setAuthBaseId(authUserId);
        authUserFacade.updateUser(tenantAccountDTO);
        return Boolean.TRUE;
    }

    @Override
    public TenantAccountVO queryAccountInfo(LoginContextInfoDTO merchantInfoDTO, Long userId) {
        if (!checkHavingSuperAdmin(merchantInfoDTO.getAuthUserId())) {
            throw new BizException("不属于超级管理员账号，沒有查看权限");
        }

        // 查询账号信息
        TenantAccount tenantAccount = selectById(userId);
        if (Objects.isNull(tenantAccount)) {
            throw new BizException("账号不存在");
        }
        if (merchantInfoDTO.getTenantId() != null && !Objects.equals(merchantInfoDTO.getTenantId(), tenantAccount.getTenantId())) {
            log.warn("查询账号租户信息不一致,当前登录信息={}, 账号信息={}", JSON.toJSONString(merchantInfoDTO), JSON.toJSONString(tenantAccount));
            throw new BizException("账号不存在");
        }

        TenantAccountVO tenantAccountVO = TenantAccountConvert.convertToTenantAccountVO(tenantAccount);
        // 查询绑定的角色
        List<AuthUserRoleDto> authUserRoleDtos = authUserFacade.getUserRoleByUserIds(Arrays.asList(tenantAccount.getAuthUserId()));
        Map<Long, AuthUserRoleDto> authUserRoleDtoMap = authUserRoleDtos.stream().collect(Collectors.toMap(AuthUserRoleDto::getId, item -> item));
        getTenantAccountVO(tenantAccountVO, authUserRoleDtoMap);

        // 供应商角色返回对应的供应商ids
        List<TenantAccountSupplierMapping> tenantAccountSupplierMappingList = tenantAccountSupplierMappingDao.queryByAccountId(userId);
        if (!CollectionUtils.isEmpty(tenantAccountSupplierMappingList)) {
            List<Long> supplierIds = tenantAccountSupplierMappingList.stream().map(TenantAccountSupplierMapping::getSupplierId).collect(Collectors.toList());
            List<SupplierInfoDTO> supplierInfoDTOS = supplierService.batchQuerySupplierList(tenantAccount.getTenantId(), supplierIds);
            tenantAccountVO.setSupplierDistributorFlag(Boolean.TRUE);
            tenantAccountVO.setSupplierIds(supplierInfoDTOS.stream().map(SupplierInfoDTO::getSupplierId).collect(Collectors.toList()));
        }
        return tenantAccountVO;
    }

    private Boolean checkHavingSuperAdmin(Long authUserId) {
        List<AuthUserRoleDto> authUserRoleDtos = authUserFacade.getUserRoleByUserIds(Collections.singletonList(authUserId));
        Map<Long, AuthUserRoleDto> authUserRoleDtoMap = authUserRoleDtos.stream().collect(Collectors.toMap(AuthUserRoleDto::getId, item -> item));
        AuthUserRoleDto authUserRoleDto = authUserRoleDtoMap.get(authUserId);
        if (Objects.nonNull(authUserRoleDto)) {
            List<AuthRole> roles = authUserRoleDto.getRoles();
            // 判断是否属于超级管理员，超级管理员角色不可变更
            return roles.stream().anyMatch(authRole -> TenantAccountEnums.Role.SUPER_ADMIN.getCode().equals(authRole.getSuperAdmin().intValue()));
        }

        return Boolean.FALSE;
    }

    @Override
    public TenantAccountLoginVO switchTenant(Long tenantId, LoginContextInfoDTO merchantInfoDTO) {
        // 退出登录
        Subject subject = SecurityUtils.getSubject();
        subject.logout();
        // 登录
        String phone = merchantInfoDTO.getPhone();
        String username = merchantInfoDTO.getUsername();
        String token;
        if(phone != null) {
            token = authUserFacade.loginByPhone(tenantId, phone);
        } else {
            token = authUserFacade.loginByUsername(tenantId, username);
        }
        TenantAccountLoginVO tenantAccountLoginVO = new TenantAccountLoginVO();
        tenantAccountLoginVO.setToken(token);
        return tenantAccountLoginVO;
    }

    @Override
    public List<TenantAccount> listTenantAccount(Long tenantId) {
        TenantAccountQueryReq tenantAccountQueryReq = new TenantAccountQueryReq();
        tenantAccountQueryReq.setTenantId(tenantId);
        List<TenantAccountResultResp> tenantAccounts = userCenterTenantAccountFacade.getTenantAccounts(tenantAccountQueryReq);
        return TenantAccountMapperConvert.INSTANCE.respListToTenantAccountList(tenantAccounts);

//        LambdaQueryWrapper<TenantAccount> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(TenantAccount::getTenantId, tenantId);
//        List<TenantAccount> list = tenantAccountDao.list(queryWrapper);
//        return list;
    }

    @Override
    public Map<Long, TenantAccount> queryAccountMap(Set<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return new HashMap<>();
        }
//        LambdaQueryWrapper<TenantAccount> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.in(TenantAccount::getAuthUserId, userIds);
//        List<TenantAccount> list = tenantAccountDao.list(queryWrapper);
        List<TenantAccountResultResp> tenantAccountResultRespList = userCenterTenantAccountFacade.getTenantAccountByUserIds(Lists.newArrayList(userIds));
        List<TenantAccount> list = TenantAccountMapperConvert.INSTANCE.respListToTenantAccountList(tenantAccountResultRespList);
        return list.stream().collect(Collectors.toMap(TenantAccount::getAuthUserId, t -> t));
    }

    @Override
    public void synchronizedUser() {
        // 查询所有品牌方用户
//        List<Integer> types = Arrays.asList(TenantEnums.type.BRAND_PARTY.getCode());
//        List<Tenant> tenants = tenantDao.queryByTenantType(types);
        List<TenantResultResp> tenants = userCenterTenantFacade.getTenantsByQuery(TenantQueryReq.builder().type(TenantEnums.type.BRAND_PARTY.getCode()).build());
        // 同步账号
        tenants.forEach(tenant -> {
            // TODO
            if (!tenant.getId().equals(2L)) {
                // TODO 增加校验，防止账号重复，增加打印日志，手动维护错误数据

                // 新增超级管理员角色
                RoleDTO roleDTO = new RoleDTO();
                roleDTO.setRoleName("超级管理员");
                roleDTO.setRemarks("超级管理员");
                roleDTO.setSystemOrigin(SystemOriginEnum.COSFO_MANAGE.getType().byteValue());
                roleDTO.setSuperAdmin(NumberConstants.ONE.byteValue());
                Long roleId = authUserFacade.addHistoryRole(roleDTO, tenant.getId());

                TenantAccountDTO tenantAccountDTO = new TenantAccountDTO();
                tenantAccountDTO.setPhone(tenant.getPhone());
                tenantAccountDTO.setLoginPassword(tenant.getPassword());
                tenantAccountDTO.setStatus(TenantAccountEnums.status.EFFECTIVE.getCode());
                tenantAccountDTO.setNickname(tenant.getTenantName() + "管理员");
                tenantAccountDTO.setTenantId(tenant.getId());
                // 绑定超级管理员角色
                List<Long> roleIds = Arrays.asList(roleId);
                tenantAccountDTO.setRoleIds(roleIds);
                // 创建auth服务账号
                Long authUserId = authUserFacade.synchronizedUser(tenantAccountDTO);
                // 创建账号
//                // TODO 封装起来
//                TenantAccount tenantAccount = new TenantAccount();
//                tenantAccount.setAuthUserId(authUserId);
//                tenantAccount.setPhone(tenantAccountDTO.getPhone());
//                tenantAccount.setNickname(tenantAccountDTO.getNickname());
//                tenantAccount.setOperatorPhone(tenantAccountDTO.getOperatorPhone());
//                tenantAccount.setOperatorTime(LocalDateTime.now());
//                tenantAccount.setStatus(TenantAccountEnums.status.EFFECTIVE.getCode());
//                tenantAccount.setTenantId(tenantAccountDTO.getTenantId());
//                tenantAccountDao.save(tenantAccount);
                TenantAccountCommandReq commandReq = new TenantAccountCommandReq();
                commandReq.setAuthUserId(authUserId);
                commandReq.setPhone(tenantAccountDTO.getPhone());
                commandReq.setNickname(tenantAccountDTO.getNickname());
                commandReq.setOperatorPhone(tenantAccountDTO.getOperatorPhone());
                commandReq.setUpdater("系统");
                commandReq.setOperatorTime(LocalDateTime.now());
                commandReq.setStatus(TenantAccountEnums.status.EFFECTIVE.getCode());
                commandReq.setTenantId(tenantAccountDTO.getTenantId());
                userCenterTenantAccountFacade.createTenantAccount(commandReq);
            }
        });
        //
    }


    @Override
    public List<TenantAccount> selectByPhone(String phone, List<Long> tenantIds) {
        List<TenantAccountResultResp> respList;
        if(CollectionUtil.isEmpty (tenantIds)){
            TenantAccountQueryReq req = new TenantAccountQueryReq ();
            req.setPhone (phone);
            respList = userCenterTenantAccountFacade.getTenantAccounts (req);
        }else {
            respList = userCenterTenantAccountFacade.getAvailableTenantAccountByTenantIdsAndPhone (tenantIds, phone);
        }
        return TenantAccountMapperConvert.INSTANCE.respListToTenantAccountList(respList);
    }


    @Override
    public List<TenantAccount> selectBrandAccountByEmail(String email) {
        TenantAccountQueryReq req = new TenantAccountQueryReq ();
        req.setEmail (email);
        List<TenantAccountResultResp>  tenantAccountResultResps = userCenterTenantAccountFacade.getTenantAccounts (req);
        List<TenantAccount> tenantAccounts = TenantAccountMapperConvert.INSTANCE.respListToTenantAccountList(tenantAccountResultResps);
        if(CollUtil.isEmpty(tenantAccounts)) {
            return tenantAccounts;
        }

        List<TenantResultResp> tenants = userCenterTenantFacade.getTenantsByQuery(TenantQueryReq.builder().type(TenantEnums.type.BRAND_PARTY.getCode()).build());
        List<Long> tenantIds = tenants.stream().map(TenantResultResp::getId).collect(Collectors.toList());
        tenantAccounts = tenantAccounts.stream().filter(dto -> tenantIds.contains(dto.getTenantId())).collect(Collectors.toList());
        return tenantAccounts;
    }

    @Override
    public void update(TenantAccountDTO tenantAccountDTO, List<Long> tenantIds) {
        TenantAccountCommandReq tenantAccountCommandReq = new TenantAccountCommandReq();
        tenantAccountCommandReq.setAuthUserId(tenantAccountDTO.getAuthBaseId());
        tenantAccountCommandReq.setNickname(tenantAccountDTO.getNickname());
        tenantAccountCommandReq.setProfilePicture(tenantAccountDTO.getProfilePicture());
        tenantAccountCommandReq.setStatus(tenantAccountDTO.getStatus());
        tenantAccountCommandReq.setOperatorPhone(tenantAccountDTO.getOperatorPhone());
        LoginContextInfoDTO merchantInfoDTO = UserLoginContextUtils.getMerchantInfoDTO();
        String updater = merchantInfoDTO == null ? "系统" : merchantInfoDTO.getNickName();
        tenantAccountCommandReq.setUpdater(updater);
        tenantAccountCommandReq.setOperatorTime(tenantAccountDTO.getOperatorTime());
        userCenterTenantAccountFacade.updateByTenantIdsAndAuthId(tenantIds, tenantAccountCommandReq);
    }

    @Override
    public TenantAccount selectByAuthUserId(Long authUserId) {
        TenantAccountResultResp resp = userCenterTenantAccountFacade.getTenantAccountInfo(authUserId);
        return TenantAccountMapperConvert.INSTANCE.respToTenantAccount(resp);
    }

    @Override
    public TenantAccount selectByIdAndTenantId(Long id, Long tenantId) {
        TenantAccountResultResp tenantAccountResultResp = userCenterTenantAccountFacade.getTenantAccountInfoById(id);
        TenantAccount tenantAccount = TenantAccountMapperConvert.INSTANCE.respToTenantAccount(tenantAccountResultResp);
        if (Objects.nonNull(tenantAccount) && Objects.equals(tenantAccount.getTenantId(), tenantId)) {
            return tenantAccount;
        }
        return null;
    }

    @Override
    public TenantAccount selectById(Long id) {
        TenantAccountResultResp tenantAccountResultResp = userCenterTenantAccountFacade.getTenantAccountInfoById(id);
        TenantAccount tenantAccount = TenantAccountMapperConvert.INSTANCE.respToTenantAccount(tenantAccountResultResp);
        return tenantAccount;
    }

    @Override
    public void remove(Long id) {
        // 用户中心会变更该条数据的status、deletedFlag
        TenantAccountCommandReq commandReq = new TenantAccountCommandReq();
        commandReq.setId(id);
        userCenterTenantAccountFacade.delTenantAccount(commandReq);
    }
    @Override
    public List<SupplierDistributorTenantAccountVO> supplierDistributorAccountList(SupplierDistributorTenantAccountQueryDTO queryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        Long tenantId = loginContextInfoDTO.getTenantId ();
        List<TenantAccountSupplierMapping> mappings = tenantAccountSupplierMappingDao.queryBySupplierIdList(Collections.singletonList (queryDTO.getSupplierId ()),tenantId);
        if(CollectionUtil.isEmpty (mappings)){
            return Collections.emptyList ();
        }
        List<Long> accountIds = mappings.stream ().map (TenantAccountSupplierMapping::getAccountId).collect (Collectors.toList ());

        //查询 分页
        TenantAccountQueryReq queryReq = new TenantAccountQueryReq();
        queryReq.setTenantId(tenantId);
        queryReq.setIdList (new ArrayList<> (accountIds));
        List<TenantAccountResultResp> respList = userCenterTenantAccountFacade.getTenantAccounts (queryReq);
        if(CollectionUtil.isEmpty (respList)){
            return Collections.emptyList ();
        }
        List<TenantAccountVO> tenantAccountVOS = TenantAccountConvert.convertToTenantAccountVOList (respList);

        //查询 是否关注
        List<String> phones = tenantAccountVOS.stream ().map (TenantAccountVO::getPhone).collect (Collectors.toList ());
        Map<String, String> authUserMap = authWechatFacade.queryAuthMapRespByPhones4FTGYL (phones, tenantId);

        // 查询账号消息通知开启状态
        TenantAccountReceiveMsgQueryDTO tenantAccountReceiveMsgQueryDTO = TenantAccountReceiveMsgQueryDTO.builder().tenantAccountIds(new HashSet<> (accountIds))
                .tenantId(tenantId)
                .channelCode(WxOfficialAccountsChannelEnum.FTGYL.channelCode)
                .channelType(ChannelTypeEnum.WECHAT_OFFICIAL_ACCOUNT.getValue()).build();
        List<TenantAccountReceiveMsgSwitch> switchList = tenantAccountMsgService.queryTenantAccountReceiveMsgSwitch (tenantAccountReceiveMsgQueryDTO);
        Map<Long, Integer> switchMap = switchList.stream ().collect (Collectors.toMap (TenantAccountReceiveMsgSwitch::getTenantAccountId, TenantAccountReceiveMsgSwitch::getAvailableStatus));

        // 查询满足开启付款通知配置的账户配置
        TenantAccountBussinessMsgQueryDTO tenantAccountBussinessMsgConfigQueryDTO = TenantAccountBussinessMsgQueryDTO.builder().tenantAccountIds(new HashSet<> (accountIds)).tenantId(tenantId).build();
        List<TenantAccountBussinessMsgConfig> configList = tenantAccountMsgService.queryTenantAccountBussinessMsgConfig (tenantAccountBussinessMsgConfigQueryDTO);
        Map<Long, List<TenantAccountBussinessMsgConfig>> configMap = configList.stream ().collect (Collectors.groupingBy (TenantAccountBussinessMsgConfig::getTenantAccountId));

        List<SupplierDistributorTenantAccountVO> reslut = tenantAccountVOS.stream ().map (e -> {
            fillFtgylWatchFlag (e, authUserMap);
            SupplierDistributorTenantAccountVO vo = buildSupplierDistributorTenantAccountVO (e, configMap, switchMap);
            return vo;
        }).collect (Collectors.toList ());
        // 使用自定义Comparator对tenantAccountVOS进行排序
        reslut.sort ((vo1, vo2) -> {
            // 获取vo1和vo2在mappings中的索引
            int index1 = getIndexById (accountIds, vo1.getId ());
            int index2 = getIndexById (accountIds, vo2.getId ());
            // 根据索引的顺序进行比较
            return Integer.compare (index1, index2);
        });

        return reslut;
    }
    // 根据id获取在mappings中的索引
    private static int getIndexById(List<Long> sortOrder, Long id) {
        for (int i = 0; i < sortOrder.size (); i++) {
            if (Objects.equals (sortOrder.get (i), id)) {
                return i;
            }
        }
        return -1;
    }

    @Override
    public void accountReceiveMsgSwitchUpdate(TenantAccountReceiveMsgSwitchDTO dto, LoginContextInfoDTO merchantInfoDTO) {
        String channelCode = dto.getChannelCode();
        Integer channelType = dto.getChannelType();
        if(StringUtils.isEmpty (channelCode)){
            dto.setChannelCode(WxOfficialAccountsChannelEnum.FTGYL.channelCode);
        }
        if(StringUtils.isEmpty (channelType)){
            dto.setChannelType(ChannelTypeEnum.WECHAT_OFFICIAL_ACCOUNT.getValue());
        }
        // 查询账号消息通知开启状态
        tenantAccountMsgService.accountReceiveMsgSwitchUpdate(dto,merchantInfoDTO.getTenantId ());
    }
    @Override
    public void closeWechatCare(WechatCareQrDTO dto) {
        authWechatFacade.closeWechatCare4FTGYLByPhone(dto.getPhone (),dto.getTenantId ());
    }

    @Override
    public String getWxCareQr(WechatCareQrDTO dto) {
        return authWechatFacade.getWxCareQr4FTGYL(dto.getPhone (),dto.getTenantId ());
    }

    @Override
    public void deleteSupplierDistributorAccount(TenantAccountDTO tenantAccountDTO) {
        List<Long> supplierIds = tenantAccountDTO.getSupplierIds ();
        List<TenantAccountSupplierMapping> tenantAccountSupplierMappings = tenantAccountSupplierMappingDao.queryByTenantAccountIds (tenantAccountDTO.getTenantId (), Collections.singletonList (tenantAccountDTO.getId ()));
        if(CollectionUtil.isNotEmpty (tenantAccountSupplierMappings) && CollectionUtil.isNotEmpty (supplierIds) ){
            List<Long> ids = tenantAccountSupplierMappings.stream ().filter (e -> supplierIds.contains (e.getSupplierId ())).map (TenantAccountSupplierMapping::getId).collect (Collectors.toList ());
            if(CollectionUtil.isNotEmpty (ids)){
                tenantAccountSupplierMappingDao.removeByIds (ids);
            }
        }
    }

    @Override
    public List<TenantAccountVO> getTenantAccountByTenantIdsAndPhone(TenantAccountListQueryDTO dto) {
        List<TenantAccountResultResp> respList = userCenterTenantAccountFacade.getTenantAccountByTenantIdsAndPhone (dto.getTenantId (), dto.getPhone ()).stream ().filter (e->e.getDeletedFlag ().equals (TenantAccountEnums.DeletedFlag.EFFECTIVE.getCode ())).collect(Collectors.toList());
        if(CollectionUtil.isEmpty (respList)){
            return Collections.emptyList ();
        }

        Set<Long> tenantAccountSupplierIds = new HashSet<> ();
        // 供应商角色返回对应的供应商ids
        List<TenantAccountSupplierMapping> tenantAccountSupplierMappingList = tenantAccountSupplierMappingDao.queryByTenantAccountIds(dto.getTenantId (),respList.stream ().map (TenantAccountResultResp::getId).collect(Collectors.toList()));
        if (!CollectionUtils.isEmpty(tenantAccountSupplierMappingList)) {
            tenantAccountSupplierIds = tenantAccountSupplierMappingList.stream ().map (TenantAccountSupplierMapping::getAccountId).collect (Collectors.toSet ());
        }

        List<TenantAccount> tenantAccounts = TenantAccountMapperConvert.INSTANCE.respListToTenantAccountList(respList);

        List<Long> authUserIds = tenantAccounts.stream().map(TenantAccount::getAuthUserId).collect(Collectors.toList());
        // 查询绑定的角色
        List<AuthUserRoleDto> authUserRoleDtos = authUserFacade.getUserRoleByUserIds(authUserIds);
        Map<Long, AuthUserRoleDto> authUserRoleDtoMap = authUserRoleDtos.stream().collect(Collectors.toMap(AuthUserRoleDto::getId, item -> item));
        Set<Long> finalTenantAccountSupplierIds = tenantAccountSupplierIds;
        List<TenantAccountVO> tenantAccountVOS = tenantAccounts.stream().map(tenantAccount -> {
            TenantAccountVO tenantAccountVO = TenantAccountConvert.convertToTenantAccountVO(tenantAccount);
            if(finalTenantAccountSupplierIds.contains (tenantAccount.getId ())) {
                tenantAccountVO.setSupplierDistributorFlag (Boolean.TRUE);
            }else{
                tenantAccountVO.setSupplierDistributorFlag (Boolean.FALSE);
            }
            return getTenantAccountVO(tenantAccountVO, authUserRoleDtoMap);
        }).collect(Collectors.toList());



        return tenantAccountVOS;
    }

    @Override
    public void noPrompt(TenantAccountPromptDTO tenantAccountPromptDTO) {
        redisUtils.set(RedisKeyEnum.CM00010.join(tenantAccountPromptDTO.getPhone()), NumberConstant.ONE, TimeUnit.DAYS.toMillis(customConfig.getNoPromptDays()));
    }

    @Override
    public PageInfo<SuperAccountTenantVO> querySuperAccountPage(SuperAccountQueryDTO superAccountQueryDTO) {
        TenantAccountQueryDTO queryDTO = new TenantAccountQueryDTO();
        queryDTO.setPageSize(superAccountQueryDTO.getPageSize());
        queryDTO.setPageIndex(superAccountQueryDTO.getPageIndex());
        queryDTO.setName(superAccountQueryDTO.getName());
        PageInfo<TenantDTO> tenantDTOPageInfo = tenantService.queryTenantInfoByPage(queryDTO);
        // 补充门店名称
        Map<Long, String> merchartNameMap = getMerchartNameMap(tenantDTOPageInfo);
        return TenantMapperConvert.INSTANCE.tenantDtoToSuperAccountTenantVOPageInfo(tenantDTOPageInfo, merchartNameMap);
    }

    @Override
    public TenantAccountLoginVO switchSuperAccount(SuperAccountLoginDTO superAccountLoginDTO, LoginContextInfoDTO loginContextInfoDTO) {
        AuthLoginDto authLoginDto = authMockLoginFacade.mockLogin(superAccountLoginDTO.getTargetTenantId(), loginContextInfoDTO.getAuthUserId(), customConfig.getSuperTokenExpireTime());
        TenantAccountLoginVO loginVO = new TenantAccountLoginVO();
        loginVO.setToken(authLoginDto.getToken());
        return loginVO;
    }

    @Override
    public TenantAccountVO getSuperAccountInfo(LoginContextInfoDTO loginContextInfoDTO) {
        log.info("当前用户为超级账号，info={}", JSON.toJSONString(loginContextInfoDTO));
        // 查询用户信息
        TenantAccount tenantAccount = selectByAuthUserId(loginContextInfoDTO.getAuthUserId());
        TenantAccountVO tenantAccountVO = TenantAccountConvert.convertToTenantAccountVO(tenantAccount);
        tenantAccountVO.setIsSuperAccount(Boolean.TRUE);
        tenantAccountVO.setTenantId(loginContextInfoDTO.getTenantId());
        return tenantAccountVO;
    }

    private Map<Long, String> getMerchartNameMap(PageInfo<TenantDTO> tenantDTOPageInfo) {
        if (tenantDTOPageInfo == null || CollectionUtils.isEmpty(tenantDTOPageInfo.getList())) {
            return Collections.emptyMap();
        }
        List<Long> tenantIds = tenantDTOPageInfo.getList().stream().map(TenantDTO::getId).collect(Collectors.toList());
        List<MerchantResultResp> merchantResultRespList = userCenterMerchantInfoFacade.getMerchantByTenantIds(tenantIds);
        if (CollectionUtils.isEmpty(merchantResultRespList)) {
            return Collections.emptyMap();
        }
        return merchantResultRespList.stream().collect(Collectors.toMap(MerchantResultResp::getTenantId, MerchantResultResp::getMerchantName));
    }

    /**
     * 设置 是否关注 帆台供应链 公众号
     * @param e
     * @param authUserMap
     */
    private void fillFtgylWatchFlag(TenantAccountVO e, Map<String, String> authUserMap) {
        e.setFtgylWatchFlag (ObjectUtil.isNotEmpty (authUserMap.get (e.getPhone ())));
    }

    private SupplierDistributorTenantAccountVO buildSupplierDistributorTenantAccountVO(TenantAccountVO tenantAccountVO, Map<Long, List<TenantAccountBussinessMsgConfig>> configMap, Map<Long, Integer> switchMap) {
        SupplierDistributorTenantAccountVO vo = TenantAccountMapperConvert.INSTANCE.tenantAccount2SupplierDistributorTenantAccountVO (tenantAccountVO);
        vo.setWechatOfficialAccountFTGTLAavailableStatus(switchMap.get (vo.getId ()));
        List<TenantAccountBussinessMsgConfig> bussinessMsgConfigList = configMap.get (vo.getId ());
        vo.setConfigVOS (TenantAccountMapperConvert.INSTANCE.tenantAccountBussinessMsgConfig2VOList (bussinessMsgConfigList));
        return vo;
    }
}
