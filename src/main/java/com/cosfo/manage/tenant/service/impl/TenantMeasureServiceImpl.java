package com.cosfo.manage.tenant.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.manage.common.config.BusinessTimeConfig;
import com.cosfo.manage.common.constant.MqMessageType;
import com.cosfo.manage.common.constant.MqTagConstant;
import com.cosfo.manage.common.context.MeasureItemTypeEnum;
import com.cosfo.manage.common.context.TenantConfigStatusEnum;
import com.cosfo.manage.common.context.TenantMeasureStatusEnum;
import com.cosfo.manage.common.context.holder.TenantMetricsSummaryContextHolder;
import com.cosfo.manage.common.factory.TenantMeasureItemFactory;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.report.repository.TenantMetricsSummaryRepository;
import com.cosfo.manage.tenant.model.dto.TenantMeasureItemResultDTO;
import com.cosfo.manage.tenant.model.dto.TenantMeasureModuleDTO;
import com.cosfo.manage.tenant.model.dto.TenantMeasureReportDTO;
import com.cosfo.manage.tenant.model.dto.TenantMeasureReportQueryDTO;
import com.cosfo.manage.tenant.model.po.TenantMeasureItem;
import com.cosfo.manage.tenant.model.po.TenantMeasureItemResult;
import com.cosfo.manage.tenant.model.po.TenantMeasureReport;
import com.cosfo.manage.tenant.model.input.TenantConfigInput;
import com.cosfo.manage.tenant.model.po.*;
import com.cosfo.manage.tenant.repository.TenantMeasureItemRepository;
import com.cosfo.manage.tenant.repository.TenantMeasureItemResultRepository;
import com.cosfo.manage.tenant.repository.TenantMeasureReportRepository;
import com.cosfo.manage.tenant.service.TenantCommonConfigService;
import com.cosfo.manage.tenant.service.TenantMeasureService;
import com.cosfo.summerfarm.mq.SummerfarmMQTopic;
import com.cosfo.summerfarm.mq.msg.SummerfarmMsgModel;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: George
 * @date: 2023-11-09
 **/
@Service
@Slf4j
public class TenantMeasureServiceImpl implements TenantMeasureService {

    @Resource
    private TenantMeasureReportRepository tenantMeasureReportRepository;
    @Resource
    private TenantMeasureItemResultRepository tenantMeasureItemResultRepository;
    @Resource
    private TenantMeasureItemRepository tenantMeasureItemRepository;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private TenantMeasureItemFactory tenantMeasureItemFactory;
    @Lazy
    @Resource
    private TenantMeasureService tenantMeasureService;
    @Resource
    private TenantMetricsSummaryRepository tenantMetricsSummaryRepository;
    @Resource
    private TenantMetricsSummaryContextHolder tenantMetricsSummaryContextHolder;
    @Resource
    private BusinessTimeConfig businessTimeConfig;
    public static final String STEP_GUIDE_KEY = "tenant_measure_step_guide";

    @Resource
    private TenantCommonConfigService tenantCommonConfigService;
    @Resource
    private PlatformTransactionManager transactionManager;

    /**
     * 默认查询度量时间范围
     */
    public static final Long TIME_RANGE = 7L;

    @Override
    public Long measure(Long tenantId) {
        // 查询需要检测的项
        List<TenantMeasureItem> tenantMeasureItemList = tenantMeasureItemRepository.list();
        if (CollectionUtils.isEmpty(tenantMeasureItemList)) {
            throw new BizException("未配置检测项");
        }

        // 查询最近一次的检测报告
        TenantMeasureReport lastReport = tenantMeasureReportRepository.queryLastReport(tenantId, null);
        if (lastReport != null && Objects.equals(lastReport.getStatus(), TenantMeasureStatusEnum.PROCESSING.getStatus())) {
            throw new BizException(200, "度量检测中，请勿重复操作");
        }

        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(def);
        TenantMeasureReport report = new TenantMeasureReport();
        try {
            report.setTenantId(tenantId);
            report.setStatus(TenantMeasureStatusEnum.PROCESSING.getStatus());
            report.setMeasureItemNum(tenantMeasureItemList.size());
            tenantMeasureReportRepository.save(report);

            List<TenantMeasureItemResult> measureItemResults = Lists.newArrayListWithCapacity(tenantMeasureItemList.size());
            for (TenantMeasureItem item : tenantMeasureItemList) {
                TenantMeasureItemResult measureItemResult = getTenantMeasureItemResult(report, item);
                measureItemResults.add(measureItemResult);
            }
            tenantMeasureItemResultRepository.saveBatch(measureItemResults);
            transactionManager.commit(status);
        } catch (Exception e) {
            transactionManager.rollback(status);
            log.error("度量异常，请稍后再试", e);
            throw e;
        }

        Long id = report.getId();
        // 发送MQ消息
        sendMqMsg(id);
        return id;
    }

    private void sendMqMsg(Long id) {
        SummerfarmMsgModel summerfarmMsgModel = new SummerfarmMsgModel();
        summerfarmMsgModel.setMsgType(MqMessageType.MEASURE);
        summerfarmMsgModel.setMsgData(id);
        mqProducer.send(SummerfarmMQTopic.SAAS_MANAGE, MqTagConstant.TENANT_MEASURE, JSONObject.toJSONString(summerfarmMsgModel));

        // 延迟取消 默认3分钟
        summerfarmMsgModel.setMsgType(MqMessageType.MEASURE_DELAY_CANCEL);
        mqProducer.sendDelay(SummerfarmMQTopic.SAAS_MANAGE, MqTagConstant.TENANT_MEASURE_CANCEL, JSONObject.toJSONString(summerfarmMsgModel), businessTimeConfig.getTenantMeasureTimeOut() * 1000 * 60);
    }

    private static TenantMeasureItemResult getTenantMeasureItemResult(TenantMeasureReport report, TenantMeasureItem tenantMeasureItem) {
        TenantMeasureItemResult measureItemResult = new TenantMeasureItemResult();
        measureItemResult.setTenantId(report.getTenantId());
        measureItemResult.setReportId(report.getId());
        measureItemResult.setItemId(tenantMeasureItem.getId());
        measureItemResult.setItemType(tenantMeasureItem.getType());
        measureItemResult.setItemRoute(tenantMeasureItem.getRoute());
        measureItemResult.setItemResult(tenantMeasureItem.getTitle());
        measureItemResult.setItemTitle(tenantMeasureItem.getTitle());
        measureItemResult.setStatus(TenantMeasureStatusEnum.TO_BE_PROCESSED.getStatus());
        measureItemResult.setFirstModuleName(tenantMeasureItem.getFirstModuleName());
        measureItemResult.setSecondModuleName(tenantMeasureItem.getSecondModuleName());
        return measureItemResult;
    }

    /**
     * 实际度量
     * @param id
     * @return
     */
    @Override
    public void doMeasure(Long id) {
        log.info("开始处理id:[{}]度量检测", id);
        TenantMeasureReport report = tenantMeasureReportRepository.getById(id);
        if (report == null) {
            log.error("参数异常，未查询到对应的度量记录");
            return;
        }
        if (!Objects.equals(report.getStatus(), TenantMeasureStatusEnum.PROCESSING.getStatus())) {
            log.error("度量记录非处理中，不做处理");
            return;
        }
        // 查询度量项记录
        List<TenantMeasureItemResult> measureItemResults = tenantMeasureItemResultRepository.listByReportId(report.getTenantId(), report.getId());

        // 提前缓存租户维度指标
        generateTenantMetricsCache(report.getTenantId());

        for (TenantMeasureItemResult measureItemResult : measureItemResults) {
            try {
                // do per measure item work using strategy pattern
                log.info("开始处理[{}]度量检测项", measureItemResult.getItemTitle());
                tenantMeasureItemFactory.getStrategy(measureItemResult.getItemId()).doMeasureItem(measureItemResult);
                measureItemResult.setStatus(TenantMeasureStatusEnum.FINISHED.getStatus());
                log.info("处理[{}]度量检测项完毕", measureItemResult.getItemTitle());
            } catch (Exception e) {
                log.error("处理[{}]度量检测项异常", measureItemResult.getItemTitle(), e);
                measureItemResult.setStatus(TenantMeasureStatusEnum.FAIL.getStatus());
            }
            tenantMeasureService.updateById(measureItemResult);
        }

        long immediateOptimizationNum = measureItemResults.stream().filter(item -> Objects.equals(item.getItemResultState(), TenantConfigStatusEnum.ABNORMAL.getStatus())).count();
        report.setImmediateOptimizationNum((int) immediateOptimizationNum);
        report.setStatus(TenantMeasureStatusEnum.FINISHED.getStatus());
        tenantMeasureReportRepository.updateById(report);
        // 更新度量报告状态
        log.info("id:[{}]度量检测完毕", id);
    }

    /**
     * 缓存租户维度指标
     * @param tenantId
     */
    private void generateTenantMetricsCache(Long tenantId) {
        String timeTag = DateUtil.yesterday().toString("yyyyMMdd");
        TenantMetricsSummary tenantMetricsSummary = tenantMetricsSummaryContextHolder.getTenantMetricsSummary(tenantId, timeTag);
        log.info("缓存key:[{}], 缓存内容：[{}]", tenantId + "_" + timeTag, JSONObject.toJSONString(tenantMetricsSummary));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateById(TenantMeasureItemResult result) {
        return tenantMeasureItemResultRepository.updateById(result);
    }

    @Override
    public TenantMeasureReportDTO queryMeasureResult(TenantMeasureReportQueryDTO tenantMeasureReportQueryDTO) {
        // 预处理查询条件
        preProcessQueryDTO(tenantMeasureReportQueryDTO);
        // 1、如果id为空 说明没有度量过 则返回上一次的度量结果 如果过期了则返回需要度量的项
        Long reportId = tenantMeasureReportQueryDTO.getId();
        if (reportId == null) {
            TenantMeasureReport lastReport = tenantMeasureReportRepository.queryLastReport(tenantMeasureReportQueryDTO.getTenantId(), LocalDateTime.now().minusDays(TIME_RANGE).toString());
            if (lastReport == null) {
                return initTenantMeasureReportDTO();
            }
            reportId = lastReport.getId();
        }

        // 2、查询该度量结果
        TenantMeasureReportDTO tenantMeasureReportDTO = new TenantMeasureReportDTO();
        TenantMeasureReport report = tenantMeasureReportRepository.getById(reportId);
        if (report == null) {
            throw new BizException("未查询到度量结果");
        }
        tenantMeasureReportDTO.setMeasureIdentification(true);
        tenantMeasureReportDTO.setMeasureItemNums(report.getMeasureItemNum());
        tenantMeasureReportDTO.setImmediateOptimizationNums(report.getImmediateOptimizationNum());
        tenantMeasureReportDTO.setStatus(report.getStatus());

        List<Integer> itemTypes = tenantMeasureReportQueryDTO.getItemTypes();
        if (!CollectionUtils.isEmpty(itemTypes) && itemTypes.contains(MeasureItemTypeEnum.IMMEDIATE_OPTIMIZATION.getType())) {
            tenantMeasureReportQueryDTO.setItemResultState(TenantConfigStatusEnum.ABNORMAL.getStatus());
        }
        List<TenantMeasureItemResult> measureItemResults = tenantMeasureItemResultRepository.listByCondition(report.getTenantId(), report.getId(), tenantMeasureReportQueryDTO.getItemType(), tenantMeasureReportQueryDTO.getItemResultState());
//        List<TenantMeasureItemResult> measureItemResults = tenantMeasureItemResultRepository.listByReportIdAndType(report.getTenantId(), report.getId(), tenantMeasureReportQueryDTO.getItemTypes(), tenantMeasureReportQueryDTO.getItemResultState());

        if (CollectionUtils.isEmpty(measureItemResults)) {
            return new TenantMeasureReportDTO();
        }
        assemblyMeasureResult(measureItemResults, tenantMeasureReportDTO);
        return tenantMeasureReportDTO;
    }

    /**
     * 预处理查询
     * @param tenantMeasureReportQueryDTO
     */
    private void preProcessQueryDTO(TenantMeasureReportQueryDTO tenantMeasureReportQueryDTO) {
        List<Integer> itemTypes = tenantMeasureReportQueryDTO.getItemTypes();
        if (CollectionUtils.isEmpty(itemTypes)) {
            return;
        }
        if (itemTypes.contains(MeasureItemTypeEnum.DATA_REPORT.getType())) {
            tenantMeasureReportQueryDTO.setItemType(MeasureItemTypeEnum.DATA_REPORT.getType());
        }
        if (itemTypes.contains(MeasureItemTypeEnum.IMMEDIATE_OPTIMIZATION.getType())) {
            tenantMeasureReportQueryDTO.setItemResultState(TenantConfigStatusEnum.NORMAL.getStatus());
        }
    }

    /**
     * 返回需要度量的项
     * @return
     */
    private TenantMeasureReportDTO initTenantMeasureReportDTO() {
        TenantMeasureReportDTO tenantMeasureReportDTO = new TenantMeasureReportDTO();
        tenantMeasureReportDTO.setMeasureIdentification(false);
        tenantMeasureReportDTO.setMeasureItemNums(0);
        tenantMeasureReportDTO.setImmediateOptimizationNums(0);
        tenantMeasureReportDTO.setStatus(TenantMeasureStatusEnum.TO_BE_PROCESSED.getStatus());

        List<TenantMeasureItem> needMeasureItemList = tenantMeasureItemRepository.list();
        if (CollectionUtils.isEmpty(needMeasureItemList)) {
            return tenantMeasureReportDTO;
        }
        List<TenantMeasureItemResult> initMeasureItemResult = convert2TenantMeasureItemResult(needMeasureItemList);
        assemblyMeasureResult(initMeasureItemResult, tenantMeasureReportDTO);
        return tenantMeasureReportDTO;
    }

    /**
     * 转换为度量项结果
     * @param measureItems
     * @return
     */
    private List<TenantMeasureItemResult> convert2TenantMeasureItemResult(List<TenantMeasureItem> measureItems) {
        return measureItems.stream().map(item -> {
            TenantMeasureItemResult measureItemResult = new TenantMeasureItemResult();
            measureItemResult.setItemId(item.getId());
            measureItemResult.setItemType(item.getType());
            measureItemResult.setItemRoute(item.getRoute());
            measureItemResult.setItemTitle(item.getTitle());
            measureItemResult.setItemResult(item.getTitle());
            measureItemResult.setStatus(TenantMeasureStatusEnum.TO_BE_PROCESSED.getStatus());
            measureItemResult.setFirstModuleName(item.getFirstModuleName());
            measureItemResult.setSecondModuleName(item.getSecondModuleName());
            return measureItemResult;
        }).collect(Collectors.toList());
    }

    private void assemblyMeasureResult(List<TenantMeasureItemResult> measureItemResults, TenantMeasureReportDTO tenantMeasureReportDTO) {
        List<TenantMeasureModuleDTO> firstMeasureModuleList = Lists.newArrayList();

        // 先根据一级模块分组
        Map<String, List<TenantMeasureItemResult>> firstModuleMap = measureItemResults
                .stream()
                .collect(Collectors.groupingBy(
                        TenantMeasureItemResult::getFirstModuleName,
                        LinkedHashMap::new,  // 使用LinkedHashMap来保持插入顺序
                        Collectors.toList()
                ));

        // 遍历一级模块分组
        firstModuleMap.forEach((firstModuleName, list) -> {
            TenantMeasureModuleDTO firstMeasureModuleDTO = new TenantMeasureModuleDTO();
            firstMeasureModuleDTO.setModuleName(firstModuleName);
            firstMeasureModuleDTO.setItemNums(list.size());

            // 根据二级模块分组
            Map<String, List<TenantMeasureItemResult>> secondModuleMap = list
                    .stream()
                    .collect(Collectors.groupingBy(
                            TenantMeasureItemResult::getSecondModuleName,
                            LinkedHashMap::new,  // 使用LinkedHashMap来保持插入顺序
                            Collectors.toList()
                    ));

            List<TenantMeasureModuleDTO> secondMeasureModuleList = secondModuleMap
                    .entrySet()
                    .stream()
                    .map(entry -> {
                        String secondModuleName = entry.getKey();
                        List<TenantMeasureItemResultDTO> thirdMeasureItemResultList = entry
                                .getValue()
                                .stream()
                                .map(item -> {
                                    TenantMeasureItemResultDTO thirdMeasureItemResultDTO = new TenantMeasureItemResultDTO();
                                    thirdMeasureItemResultDTO.setItemId(item.getItemId());
                                    thirdMeasureItemResultDTO.setItemResult(item.getItemResult());
                                    thirdMeasureItemResultDTO.setItemRoute(item.getItemRoute());
                                    thirdMeasureItemResultDTO.setItemType(item.getItemType());
                                    thirdMeasureItemResultDTO.setStatus(item.getStatus());
                                    thirdMeasureItemResultDTO.setItemResultState(item.getItemResultState());
                                    return thirdMeasureItemResultDTO;
                                }).collect(Collectors.toList());

                        TenantMeasureModuleDTO secondMeasureModuleDTO = new TenantMeasureModuleDTO();
                        secondMeasureModuleDTO.setModuleName(secondModuleName);
                        secondMeasureModuleDTO.setItemResultList(thirdMeasureItemResultList);
                        return secondMeasureModuleDTO;
                    }).collect(Collectors.toList());

            firstMeasureModuleDTO.setChildModuleList(secondMeasureModuleList);
            firstMeasureModuleList.add(firstMeasureModuleDTO);
        });
        tenantMeasureReportDTO.setMeasureModuleList(firstMeasureModuleList);
    }

    @Override
    public void cancelMeasure(Long id) {
        log.info("开始取消id:[{}]度量检测", id);
        TenantMeasureReport report = tenantMeasureReportRepository.getById(id);
        if (report == null) {
            log.error("参数异常，未查询到对应的度量记录");
            return;
        }
        if (Objects.equals(report.getStatus(), TenantMeasureStatusEnum.FINISHED.getStatus())) {
            log.info("id:[{}]度量检测已完成，结束取消流程", id);
            return;
        }
        if(Objects.equals(report.getStatus(), TenantMeasureStatusEnum.PROCESSING.getStatus())) {
            report.setStatus(TenantMeasureStatusEnum.FAIL.getStatus());
            tenantMeasureReportRepository.updateById(report);
            log.info("id:[{}]度量检测超时，取消成功", id);
        }
        log.info("id:[{}]度量检测超时，流程结束", id);
    }

    @Override
    public Boolean queryHasStepGuide() {
        Long tenantId = UserLoginContextUtils.getTenantId();
        TenantCommonConfig tenantCommonConfig = tenantCommonConfigService.selectByTenantIdAndConfigKey(tenantId, STEP_GUIDE_KEY);
        if (tenantCommonConfig != null) {
            return Boolean.valueOf(tenantCommonConfig.getConfigValue());
        }

        return Boolean.FALSE;
    }

    @Override
    public void recordStepGuide(Boolean hasStepGuide) {
        Long tenantId = UserLoginContextUtils.getTenantId();
        TenantCommonConfig tenantCommonConfig = tenantCommonConfigService.selectByTenantIdAndConfigKey(tenantId, STEP_GUIDE_KEY);
        if (tenantCommonConfig == null) {
            tenantCommonConfig = new TenantCommonConfig();
            tenantCommonConfig.setTenantId(tenantId);
            tenantCommonConfig.setConfigKey(STEP_GUIDE_KEY);
            tenantCommonConfig.setConfigValue(hasStepGuide.toString());
            tenantCommonConfig.setConfigDesc("是否进行过度量仪逐步引导");
            tenantCommonConfigService.insert(tenantCommonConfig);
        } else {
            TenantConfigInput tenantConfigInput = new TenantConfigInput();
            tenantConfigInput.setConfigCode(STEP_GUIDE_KEY);
            tenantConfigInput.setConfigValue(hasStepGuide.toString());

            tenantCommonConfigService.updateTenantConfig(tenantId, tenantConfigInput);
        }
    }
}
