package com.cosfo.manage.tenant.service;

import com.cosfo.manage.merchant.model.dto.AuthQueryDTO;
import com.cosfo.manage.tenant.model.dto.MenuPurviewDTO;
import com.cosfo.manage.tenant.model.vo.MenuPurviewVO;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AuthMenuPurviewService {

//    /**
//     * 获取菜单树
//     * @return
//     */
//    List<MenuPurviewVO> listMenuPurviewTree();

    /**
     * 获取租户菜单树
     * @param tenantId
     * @return
     */
    List<MenuPurviewVO> listMenuPurviewTree(Long tenantId);

//    /**
//     * @deprecated
//     * 获取关联权限
//     * @return
//     */
//    List<MenuPurviewVO> listMenuPurview();

    /**
     * 获取租户菜单树
     * @param tenantId
     * @return
     */
    List<MenuPurviewVO> listMenuPurview(Long tenantId);


    /**
     * 获取当前用户菜单
     * @return
     */
    List<MenuPurviewVO> listUserMenuPurview();

    /**
     * 获取当前用户菜单
     * @param tenantId
     * @param authUserId
     * @return
     */
    List<MenuPurviewVO> listUserMenuPurview(Long tenantId, Long authUserId);

    /**
     * 获取当前用户菜单URL
     * @return
     */
    List<String> listUserMenuPurviewUrl(AuthQueryDTO authQueryDTO);

    /**
     * 校验是否需要权限
     * @param authQueryDTO
     * @param firstAuthUrl
     * @param secondAuthUrl
     * @return
     */
    Pair<Boolean, Boolean> checkAuthByCache(AuthQueryDTO authQueryDTO, String firstAuthUrl, String secondAuthUrl);


    /**
     * 新增菜单
     * @param menuPurviewDTO
     * @return
     */
    Boolean addMenuPurview(MenuPurviewDTO menuPurviewDTO);

    /**
     * 编辑菜单
     * @param menuPurviewDTO
     * @return
     */
    Boolean updateMenuPurview(MenuPurviewDTO menuPurviewDTO);

    /**
     * 删除菜单
     * @param menuPurviewDTO
     * @return
     */
    Boolean delMenuPurview(MenuPurviewDTO menuPurviewDTO);

    /**
     * 排序
     * @param purviewDTOS
     * @return
     */
    Boolean sortMenuPurview(List<MenuPurviewDTO> purviewDTOS);
}
