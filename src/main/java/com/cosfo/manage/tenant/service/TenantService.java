package com.cosfo.manage.tenant.service;

import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.tenant.model.dto.*;
import com.cosfo.manage.tenant.model.input.TenantConfigInput;
import com.cosfo.manage.tenant.model.input.TenantUnfairPriceStrategyInput;
import com.cosfo.manage.tenant.model.po.Tenant;
import com.cosfo.manage.tenant.model.vo.*;
import com.github.pagehelper.PageInfo;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface TenantService {
    /**
     * 查询供应商信息
     *
     * @param supplierTenantIds
     * @return
     */
    List<Tenant> querySupplierInfoBySupplierTenantIds(List<Long> supplierTenantIds);

    /**
     * 查询租户Id
     *
     * @param tenantId
     * @return
     */
    TenantDTO queryTenantById(Long tenantId);

    /**
     * 查询租户信息
     *
     * @param type 租户类型
     */
    ResultDTO<List<TenantVO>> queryTenantInfo(Integer type);

    /**
     * 获取租户信息
     *
     * @param token
     * @return
     */
    TenantInfoDTO getTenantInfo(String token);

    /**
     * 租户列表
     *
     * @param tenantQueryDTO
     * @return
     */
    List<TenantDTO> list(TenantQueryDTO tenantQueryDTO);

    /**
     * 分页查询绑定品牌方列表
     *
     * @param bindTenantQueryDTO
     * @return
     */
    PageInfo<LoginAccountTenantVO> queryBindTenantByPhone(BindTenantQueryDTO bindTenantQueryDTO);

    List<TenantResultResp> listByIds(Set<Long> ids);

    /**
     * 获取供应商列表
     * @return
     */
    List<SupplierTenantVO> querySupplierList();

    /**
     * 获取当前品牌方信息
     * @param tenantId
     * @return
     */
    TenantCompanyDetailVO queryTenantCompanyDetail(Long tenantId);



    /**
     * 获取供应商map
     * @param ids
     * @return
     */
    Map<Long, SupplierTenantVO> querySupplierMap(Set<Long> ids);

    /**
     * 查询品牌方客服电话
     * @param tenantId
     * @return
     */
    List<TenantCustomerPhoneVO> queryTenantCustomerPhone(Long tenantId);


    /**
     * 修改品牌方客服电话
     * @param customerPhoneModifyDTO
     * @return
     */
    Boolean modifyTenantCustomerPhone(CustomerPhoneModifyDTO customerPhoneModifyDTO);

    /**
     * 查询鲜沐大客户对应的租户信息
     *
     * @param adminId
     * @return
     */
    TenantDTO selectByAdminId(Long adminId);

    /**
     * 设置租户的默认价格倒挂规则
     * @param tenantId
     * @param input
     */
    void upsertUnfairPriceStrategy(Long tenantId, TenantUnfairPriceStrategyInput input);

    /**
     * 查询租户的默认价格倒挂规则
     * @param tenantId
     * @return
     */
    Integer queryUnfairPriceStrateg(Long tenantId);

    /**
     * 查询私采率
     * @param tenantId
     * @return
     */
    String queryPrivateProcurement(Long tenantId);
    /**
     * 更新私采率
     * @param tenantId
     * @return
     */
    void saveOrUpdatePrivateProcurement(Long tenantId, TenantConfigInput tenantConfigInput);

    /**
     * 获取租户分页列表
     * @param queryDTO 查询条件
     * @return 租户分页列表
     */
    PageInfo<TenantDTO> queryTenantInfoByPage(TenantAccountQueryDTO queryDTO);

    /**
     * 获取租户基础信息
     * @param tenantId
     * @return
     */
    TenantDTO getTenantBaseInfo(Long tenantId);
}
