package com.cosfo.manage.tenant.service.impl;

import cn.hutool.core.lang.Pair;
import com.cosfo.manage.common.constant.XianmuSupplyTenant;
import com.cosfo.manage.common.context.TenantAccountConfig;
import com.cosfo.manage.facade.ordercenter.OrderItemQueryFacade;
import com.cosfo.manage.facade.usercenter.UserCenterTenantAccountFacade;
import com.cosfo.manage.order.model.vo.OrderItemVO;
import com.cosfo.manage.tenant.dao.TenantAccountSupplierMappingDao;
import com.cosfo.manage.tenant.model.dto.TenantAccountBussinessMsgQueryDTO;
import com.cosfo.manage.tenant.model.dto.TenantAccountReceiveMsgQueryDTO;
import com.cosfo.manage.tenant.model.po.TenantAccount;
import com.cosfo.manage.tenant.model.po.TenantAccountBussinessMsgConfig;
import com.cosfo.manage.tenant.model.po.TenantAccountReceiveMsgSwitch;
import com.cosfo.manage.tenant.model.po.TenantAccountSupplierMapping;
import com.cosfo.manage.tenant.service.TenantAccountMsgService;
import com.cosfo.manage.tenant.service.TenantAccountService;
import com.cosfo.manage.tenant.service.TenantAccountSupplierMappingService;
import com.cosfo.message.client.enums.ChannelTypeEnum;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.enums.base.auth.WxOfficialAccountsChannelEnum;
import net.xianmu.usercenter.client.tenant.req.TenantAccountQueryReq;
import net.xianmu.usercenter.client.tenant.resp.TenantAccountResultResp;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description: 租户账号和供应商之间的映射服务处
 * @author: George
 * @date: 2023-05-12
 **/
@Slf4j
@Service
public class TenantAccountSupplierMappingServiceImpl implements TenantAccountSupplierMappingService {

    @Resource
    private TenantAccountSupplierMappingDao tenantAccountSupplierMappingDao;
    @Resource
    private TenantAccountService tenantAccountService;
    @Resource
    private TenantAccountMsgService tenantAccountMsgService;
    @Resource
    private UserCenterTenantAccountFacade userCenterTenantAccountFacade;
    @Resource
    private OrderItemQueryFacade orderItemQueryFacade;

    @Override
    public List<Long> queryByAccountId(Long accountId) {
        List<TenantAccountSupplierMapping> mappings = tenantAccountSupplierMappingDao.queryByAccountId(accountId);
        if (CollectionUtils.isEmpty(mappings)) {
            return Collections.emptyList();
        }
        return mappings.stream().map(TenantAccountSupplierMapping::getSupplierId).collect(Collectors.toList());
    }

    @Override
    public List<Long> queryByAuthUserId(Long authUserId) {
        TenantAccount tenantAccount = tenantAccountService.selectByAuthUserId(authUserId);
        if (tenantAccount == null) {
            return Collections.emptyList();
        }
        return queryByAccountId(tenantAccount.getId());
    }

    @Override
    public Set<Long> queryBySupplierIdList(List<Long> supplierIds, Long tenantId) {
        List<TenantAccountSupplierMapping> mappings = tenantAccountSupplierMappingDao.queryBySupplierIdList(supplierIds, tenantId);
        if (CollectionUtils.isEmpty(mappings)) {
            return Collections.emptySet();
        }
        return mappings.stream().map(TenantAccountSupplierMapping::getAccountId).collect(Collectors.toSet());
    }


    /**
     * 查询需要通知的账号信息
     *
     * @param orderId
     * @param tenantId
     * @param bussinessMsgTypeEnum
     * @return
     */
    @Override
    public Pair<List<TenantAccountResultResp>, List<OrderItemVO>> queryNeedNotifyTenantAccount(Long orderId, Long tenantId, TenantAccountConfig.BussinessMsgTypeEnum bussinessMsgTypeEnum) {
        List<OrderItemVO> snapshotList = orderItemQueryFacade.queryOrderItemByOrderId(orderId);
        List<OrderItemVO> orderItemSnapshotDTOS = snapshotList.stream()
                .filter(snapshot -> !XianmuSupplyTenant.TENANT_ID.equals(snapshot.getSupplierTenantId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderItemSnapshotDTOS)) {
            log.info("orderId:{}无需要推送的供应商", orderId);
            return Pair.of(Collections.emptyList(), Collections.emptyList());
        }

        List<Long> supplierTenantIds = orderItemSnapshotDTOS.stream().map(OrderItemVO::getSupplierTenantId).collect(Collectors.toList());
        Set<Long> tenantAccountIdList = queryBySupplierIdList(supplierTenantIds, tenantId);
        if (CollectionUtils.isEmpty(tenantAccountIdList)) {
            log.info("orderId:{}，tenantAccountIdList不存在，无需推送", orderId);
            return Pair.of(Collections.emptyList(), Collections.emptyList());
        }

        // 判断是否绑定
        // 查询满足开启付款通知配置的账户配置
        TenantAccountBussinessMsgQueryDTO tenantAccountBussinessMsgConfigQueryDTO = TenantAccountBussinessMsgQueryDTO.builder().tenantAccountIds(tenantAccountIdList)
                .tenantId(tenantId)
                .availableStatus(TenantAccountConfig.AvailableStatusEnum.YES.getCode()).build();
        if (Objects.nonNull(bussinessMsgTypeEnum)) {
            tenantAccountBussinessMsgConfigQueryDTO.setBussinessType(bussinessMsgTypeEnum.getCode());
        }
        List<TenantAccountBussinessMsgConfig> tenantAccountBussinessMsgConfigs = tenantAccountMsgService.queryTenantAccountBussinessMsgConfig(tenantAccountBussinessMsgConfigQueryDTO);
        if (CollectionUtils.isEmpty(tenantAccountBussinessMsgConfigs)) {
            log.info("orderId:{}，tenantAccountBussinessMsgConfigs不存在开启数据，无需推送", orderId);
            return Pair.of(Collections.emptyList(), Collections.emptyList());
        }
        tenantAccountIdList = tenantAccountBussinessMsgConfigs.stream().map(TenantAccountBussinessMsgConfig::getTenantAccountId).collect(Collectors.toSet());

        // 查询账号消息通知开启状态
        TenantAccountReceiveMsgQueryDTO tenantAccountReceiveMsgQueryDTO = TenantAccountReceiveMsgQueryDTO.builder().tenantAccountIds(tenantAccountIdList)
                .tenantId(tenantId)
                .channelCode(WxOfficialAccountsChannelEnum.FTGYL.channelCode)
                .availableStatus(TenantAccountConfig.AvailableStatusEnum.YES.getCode())
                .channelType(ChannelTypeEnum.WECHAT_OFFICIAL_ACCOUNT.getValue()).build();
        List<TenantAccountReceiveMsgSwitch> tenantAccountReceiveMsgSwitchList = tenantAccountMsgService.queryTenantAccountReceiveMsgSwitch(tenantAccountReceiveMsgQueryDTO);
        if (CollectionUtils.isEmpty(tenantAccountReceiveMsgSwitchList)) {
            log.info("orderId:{}，tenantAccountReceiveMsgSwitchList，账号消息不存在开启数据，无需推送", orderId);
            return Pair.of(Collections.emptyList(), Collections.emptyList());
        }
        tenantAccountIdList = tenantAccountReceiveMsgSwitchList.stream().map(TenantAccountReceiveMsgSwitch::getTenantAccountId).collect(Collectors.toSet());
        TenantAccountQueryReq tenantAccountQueryReq = new TenantAccountQueryReq();
        tenantAccountQueryReq.setIdList(new ArrayList<>(tenantAccountIdList));
        tenantAccountQueryReq.setTenantId(tenantId);
        List<TenantAccountResultResp> tenantAccounts = userCenterTenantAccountFacade.getTenantAccounts(tenantAccountQueryReq);
        return Pair.of(tenantAccounts, orderItemSnapshotDTOS);
    }

    @Override
    public List<TenantAccountSupplierMapping> queryByTenantAccountIds(Long tenantId, List<Long> tenantAccountIds) {
        return tenantAccountSupplierMappingDao.queryByTenantAccountIds(tenantId, tenantAccountIds);
    }
}
