package com.cosfo.manage.tenant.service;

import com.cosfo.manage.tenant.model.dto.TenantMeasureReportDTO;
import com.cosfo.manage.tenant.model.dto.TenantMeasureReportQueryDTO;
import com.cosfo.manage.tenant.model.po.TenantMeasureItemResult;

/**
 * @description:
 * @author: George
 * @date: 2023-11-09
 **/
public interface TenantMeasureService {

    /**
     * 度量接口
     * @param tenantId
     * @return
     */
    Long measure(Long tenantId);

    /**
     * 实际度量
     * @param id
     * @return
     */
    void doMeasure(Long id);

    /**
     * 更新度量状态
     * @param tenantMeasureItemResult
     * @return
     */
    boolean updateById(TenantMeasureItemResult tenantMeasureItemResult);

    /**
     * 查询度量结果
     * @param tenantMeasureReportQueryDTO
     * @return
     */
    TenantMeasureReportDTO queryMeasureResult(TenantMeasureReportQueryDTO tenantMeasureReportQueryDTO);

    /**
     * 延迟取消度量 默认3min
     * @param id
     */
    void cancelMeasure(Long id);

    /**
     * 查询是否有过逐步式引导
     */
    Boolean queryHasStepGuide();

    /**
     * 逐步式引导记录
     */
    void recordStepGuide(Boolean hasStepGuide);
}
