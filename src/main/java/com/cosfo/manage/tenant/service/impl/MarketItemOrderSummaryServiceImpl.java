package com.cosfo.manage.tenant.service.impl;

import com.cosfo.manage.bill.mapper.PaymentItemMapper;
import com.cosfo.manage.bill.model.po.Payment;
import com.cosfo.manage.bill.model.po.PaymentItem;
import com.cosfo.manage.bill.repository.PaymentRepository;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.context.PaymentEnum;
import com.cosfo.manage.common.context.RedisKeyEnum;
import com.cosfo.manage.facade.ordercenter.OrderItemQueryFacade;
import com.cosfo.manage.market.mapper.MarketItemMapper;
import com.cosfo.manage.market.mapper.MarketItemOrderSummaryMapper;
import com.cosfo.manage.market.model.po.MarketItem;
import com.cosfo.manage.market.model.po.MarketItemOrderSummary;
import com.cosfo.manage.market.service.MarketItemOrderSummaryService;
import com.cosfo.ordercenter.client.req.OrderItemQueryReq;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MarketItemOrderSummaryServiceImpl implements MarketItemOrderSummaryService {

    @Resource
    private PaymentItemMapper paymentItemMapper;
    @Resource
    private MarketItemMapper marketItemMapper;
    @Resource
    private MarketItemOrderSummaryMapper marketItemOrderSummaryMapper;
    @Resource
    private PaymentRepository paymentRepository;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private OrderItemQueryFacade orderItemQueryFacade;

    @Override
    public void generateMarketItemOrderSummaryByPayment(Payment payment) {
        if (!PaymentEnum.Status.SUCCESS.getCode().equals(payment.getStatus())) {
            log.info("支付单状态不是支付成功, paymentId: {}", payment.getId());
            return;
        }

        // 幂等验证
        String redisKey = RedisKeyEnum.CM00007.join(payment.getId().toString());
        RLock lock = redissonClient.getLock(redisKey);
        if (!lock.tryLock()) {
            log.info("生成销量榜 - 获取锁失败, paymentId: {}", payment.getId());
            return;
        }
        try {
            String cacheKey = RedisKeyEnum.CM00008.join(payment.getId().toString());
            Object cache = redisTemplate.opsForValue().get(cacheKey);
            if (cache != null) {
                log.info("生成销量榜 - 已经处理过, paymentId: {}", payment.getId());
                return;
            }

            // 生成商品订单汇总
            log.info("开始生成商品订单汇总, paymentId: {}", payment.getId());

            List<OrderItemAndSnapshotResp> orderItems = getOrderItemsByPayment(payment);
            generateMarketItemOrderSummaryByOrderItems(orderItems, payment);

            // 设置缓存
            redisTemplate.opsForValue().set(cacheKey, String.valueOf(NumberConstant.ONE), NumberConstant.ONE, TimeUnit.DAYS);
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void initMarketItemOrderSummary(LocalDateTime startDateTime, LocalDateTime endDateTime) {
        validateDateTime(startDateTime, endDateTime);
        List<Payment> payments = paymentRepository.selectSuccessPaymentByTime(startDateTime, endDateTime);

        log.info("初始化商品销量汇总表, 开始时间: {}, 结束时间: {}, 支付单数量: {}", startDateTime, endDateTime, payments.size());
        batchGenerateMarketItemOrderSummaryByPayments(payments);
    }

    private void batchGenerateMarketItemOrderSummaryByPayments(List<Payment> payments) {
        if (payments.isEmpty()) {
            return;
        }
        Map<Payment, List<OrderItemAndSnapshotResp>> paymentToOrderItemsMap = payments.stream()
                .collect(Collectors.toMap(payment -> payment, this::getOrderItemsByPayment));

        // get total size of OrderItemAndSnapshotDTO in the map
        int itemAmount = paymentToOrderItemsMap.values().stream().mapToInt(List::size).sum();

        List<MarketItemOrderSummary> marketItemOrderSummaries = new ArrayList<>(itemAmount);

        for (Map.Entry<Payment, List<OrderItemAndSnapshotResp>> entry : paymentToOrderItemsMap.entrySet()) {
            Payment payment = entry.getKey();
            List<OrderItemAndSnapshotResp> orderItemAndSnapshotDTOS = entry.getValue();

            List<MarketItemOrderSummary> orderSummaries = orderItemAndSnapshotDTOS.stream()
                    .map(this::generateMarketItemOrderSummary)
                    .collect(Collectors.toList());
            batchSetOrderTime(orderSummaries, payment);
            marketItemOrderSummaries.addAll(orderSummaries);
        }

        batchSetSkuIds(marketItemOrderSummaries);

        marketItemOrderSummaryMapper.insertBatch(marketItemOrderSummaries);
        log.info("初始化商品销量汇总表完成, 总计插入{}条数据", marketItemOrderSummaries.size());
    }

    private List<OrderItemAndSnapshotResp> getOrderItemsByPayment(Payment payment) {
        List<PaymentItem> paymentItems = paymentItemMapper.selectByPaymentId(payment.getId());

        List<Long> orderIds = paymentItems.stream().map(PaymentItem::getOrderId).distinct().collect(Collectors.toList());
        OrderItemQueryReq orderItemQueryReq = new OrderItemQueryReq();
        orderItemQueryReq.setOrderIds(orderIds);
        orderItemQueryReq.setTenantId(payment.getTenantId());
        return orderItemQueryFacade.queryOrderItemList(orderItemQueryReq);
    }

    private void generateMarketItemOrderSummaryByOrderItems(List<OrderItemAndSnapshotResp> orderItems, Payment payment) {
        List<MarketItemOrderSummary> marketItemOrderSummaries = orderItems.stream()
                .map(this::generateMarketItemOrderSummary).collect(Collectors.toList());

        // 批量设置skuId
        batchSetSkuIds(marketItemOrderSummaries);
        // 批量设置交易时间
        batchSetOrderTime(marketItemOrderSummaries, payment);

        marketItemOrderSummaryMapper.insertBatch(marketItemOrderSummaries);
    }

    private MarketItemOrderSummary generateMarketItemOrderSummary(OrderItemAndSnapshotResp orderItem) {
        MarketItemOrderSummary marketItemOrderSummary = new MarketItemOrderSummary();
        marketItemOrderSummary.setTenantId(orderItem.getTenantId());
        marketItemOrderSummary.setItemId(orderItem.getItemId());
        marketItemOrderSummary.setOrderAmount(orderItem.getTotalPrice());
        marketItemOrderSummary.setOrderQuantity(orderItem.getAmount());

        return marketItemOrderSummary;
    }

    private void batchSetSkuIds(List<MarketItemOrderSummary> marketItemOrderSummaries) {
        List<Long> itemIds = marketItemOrderSummaries.stream().map(MarketItemOrderSummary::getItemId).distinct().collect(Collectors.toList());
        Map<Long, MarketItem> marketItemMap = marketItemMapper.selectBatchIds(itemIds).stream().collect(Collectors.toMap(MarketItem::getId, marketItem -> marketItem));
        for (MarketItemOrderSummary marketItemOrderSummary : marketItemOrderSummaries) {
            MarketItem marketItem = marketItemMap.get(marketItemOrderSummary.getItemId());
            if (marketItem == null) {
                log.info("找不到对应的商品, itemId: {}", marketItemOrderSummary.getItemId());
                continue;
            }
            marketItemOrderSummary.setSkuId(marketItem.getSkuId());
        }
    }

    private void batchSetOrderTime(List<MarketItemOrderSummary> marketItemOrderSummaries, Payment payment) {
        LocalDateTime orderTime = payment.getSuccessTime() != null ? payment.getSuccessTime() : payment.getCreateTime();
        for (MarketItemOrderSummary marketItemOrderSummary : marketItemOrderSummaries) {
            marketItemOrderSummary.setOrderTime(LocalDate.from(orderTime));
        }
    }

    private void validateDateTime(LocalDateTime startDatetime, LocalDateTime endDatetime) {
        if (startDatetime == null || endDatetime == null) {
            throw new IllegalArgumentException("开始时间和结束时间不能为空");
        }
        if (startDatetime.isAfter(endDatetime)) {
            throw new IllegalArgumentException("开始时间不能大于结束时间");
        }
        // 最多查询30天
        if (startDatetime.plusDays(31).isBefore(endDatetime)) {
            throw new IllegalArgumentException("查询时间范围不能超过31天");
        }
    }

}
