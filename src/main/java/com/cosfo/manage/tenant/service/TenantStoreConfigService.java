package com.cosfo.manage.tenant.service;

import com.cosfo.manage.tenant.model.vo.TenantStoreConfigVO;

import java.util.List;

/**
 * 租户门店配置服务类
 *
 * @author: xiaowk
 * @date: 2024/11/25 下午2:35
 */
public interface TenantStoreConfigService {

    /**
     * 根据configkey查询租户门店相应配置
     * @param storeId
     * @param configKey
     * @return
     */
    TenantStoreConfigVO selectTenantStoreConfig(Long storeId, String configKey);

    List<TenantStoreConfigVO> queryTenantStoreConfigList(List<Long> storeIds, String configKey);

    /**
     * 根据configkey更新租户门店相应配置
     * @param tenantStoreConfigVO
     */
    void saveOrUpdateTenantStoreConfig(TenantStoreConfigVO tenantStoreConfigVO);

}
