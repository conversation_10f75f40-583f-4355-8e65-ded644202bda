package com.cosfo.manage.tenant.service;

import com.cosfo.manage.tenant.model.input.TenantConfigInput;
import com.cosfo.manage.tenant.model.po.TenantCommonConfig;
import com.cosfo.manage.tenant.model.input.TenantNumberConfigInput;
import com.cosfo.manage.tenant.model.vo.TenantCommonConfigVO;
import com.cosfo.manage.tenant.model.vo.TenantNumberCommonConfigVO;

/**
 * <p>
 * 租户公共配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-19
 */
public interface TenantCommonConfigService {

    /**
     * 根据configkey查询租户相应配置
     * @param tenantId
     * @param configKey
     * @return
     */
    TenantCommonConfigVO selectTenantConfig(Long tenantId, String configKey);

    /**
     * 根据configkey更新租户相应配置
     * @param tenantId
     * @param tenantConfigInput
     */
    void updateTenantConfig(Long tenantId, TenantConfigInput tenantConfigInput);

    /**
     * 根据configkey查询租户相应配置
     * @param tenantId
     * @param configKey
     * @return
     */
    TenantNumberCommonConfigVO queryTenantConfig(Long tenantId, String configKey);

    /**
     * 根据configkey更新租户相应配置
     * @param tenantId
     * @param tenantConfigInput
     */
    void numberUpdate(Long tenantId, TenantNumberConfigInput tenantConfigInput);

    /**
     * 根据租户和config-key查询
     * @param tenantId
     * @param configKey
     * @return
     */
    TenantCommonConfig selectByTenantIdAndConfigKey(Long tenantId, String configKey);

    /**
     * 插入
     *
     * @param tenantCommonConfig 租户公共配置
     */
    void insert(TenantCommonConfig tenantCommonConfig);

}
