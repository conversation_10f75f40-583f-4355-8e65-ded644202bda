package com.cosfo.manage.tenant.service.impl;

import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.common.context.ExcelTypeEnum;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.report.model.dto.SupplierMetricsSummaryExcelDTO;
import com.cosfo.manage.report.model.po.SupplierMetricsSummary;
import com.cosfo.manage.report.repository.SupplierMetricsSummaryRepository;
import com.cosfo.manage.report.service.ExcelGenerator;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: George
 * @date: 2023-11-28
 **/
@Service("supplierToWarehouseAccuracyGenerator")
public class SupplierToWarehouseAccuracyGenerator implements ExcelGenerator<Void> {

    @Resource
    private SupplierMetricsSummaryRepository supplierMetricsSummaryRepository;
    @Resource
    private CommonService commonService;

    @Override
    public String getExcelName(Void query) {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return String.format("近30天供应商到仓准确率截至%s.xlsx", yesterday.format(formatter));
    }

    @Override
    public String generateExcelAndReturnFilePath(Long tenantId, Void query) {
        String timeTag = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        List<SupplierMetricsSummaryExcelDTO> supplierMetricsSummaries =  querySupplierMetricsSummary(tenantId, timeTag);
        return commonService.exportExcel(supplierMetricsSummaries, ExcelTypeEnum.SUPPLIER_TO_WAREHOUSE_ACCURACY.getName());
    }

    private List<SupplierMetricsSummaryExcelDTO> querySupplierMetricsSummary(Long tenantId, String timeTag) {
        List<SupplierMetricsSummary> supplierMetricsSummaries = supplierMetricsSummaryRepository.listByTenantIdAndTimeTag(tenantId, timeTag, "to_warehouse_accuracy");
        if (CollectionUtils.isEmpty(supplierMetricsSummaries)) {
            return Collections.emptyList();
        }

        return supplierMetricsSummaries.stream().map(item ->{
            SupplierMetricsSummaryExcelDTO supplierMetricsSummaryExcelDTO = new SupplierMetricsSummaryExcelDTO();
            supplierMetricsSummaryExcelDTO.setSupplierName(item.getSupplierName());
            supplierMetricsSummaryExcelDTO.setSupplierNo(item.getSupplierNo());
            supplierMetricsSummaryExcelDTO.setSupplierType(item.getSupplierType());
            String accuracy = Optional.ofNullable(item.getToWarehouseAccuracy()).map(value -> value.toPlainString() + StringConstants.PERCENT).orElse(StringConstants.SEPARATING_IN_LINE);
            supplierMetricsSummaryExcelDTO.setToWarehouseAccuracy(accuracy);
            supplierMetricsSummaryExcelDTO.setPurchaseTasksNum30d(item.getPurchaseTasksNum30d());
            supplierMetricsSummaryExcelDTO.setReceivedEqualIncomingTasksNum(item.getReceivedEqualIncomingTasksNum());
            return supplierMetricsSummaryExcelDTO;
        }).collect(Collectors.toList());
    }
}
