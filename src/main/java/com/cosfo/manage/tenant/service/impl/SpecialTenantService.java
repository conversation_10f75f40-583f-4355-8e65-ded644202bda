package com.cosfo.manage.tenant.service.impl;

import com.cosfo.manage.common.config.ChageeNacosConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 特殊定制化租户 查询服务
 * <AUTHOR>
 * @Date 2025/4/15 15:21
 * @Version 1.0
 */
@Slf4j
@Component
public class SpecialTenantService {

    @Resource
    private ChageeNacosConfig chageeNacosConfig;

    /**
     * 门店新增时是否需要 根据联系人创建账号信息
     * 2025-04-17逻辑： 不是霸王茶姬的租户，创建时都需要校验账号信息
     * @param tenantId 租户id
     * @return true 需要校验账号信息, false 不需要校验账号信息
     */
    public boolean createMerchantAllowEmptyAccount(Long tenantId) {
        return chageeNacosConfig.judgeIsTenantOfChagee(tenantId);
    }

}
