package com.cosfo.manage.tenant.service.impl;

import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.tenant.convert.TenantDeliveryFeeAreaConvert;
import com.cosfo.manage.tenant.dao.TenantDeliveryFeeAreaDao;
import com.cosfo.manage.tenant.mapper.TenantDeliveryFeeRuleMapper;
import com.cosfo.manage.tenant.model.dto.TenantDeliveryFeeAreaRuleDTO;
import com.cosfo.manage.tenant.model.dto.TenantDeliveryFeeAreaRuleGroupDTO;
import com.cosfo.manage.tenant.model.dto.TenantDeliveryFeeRuleDTO;
import com.cosfo.manage.tenant.model.po.TenantDeliveryFeeArea;
import com.cosfo.manage.tenant.model.po.TenantDeliveryFeeRule;
import com.cosfo.manage.tenant.service.TenantDeliveryFeeRuleService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/26
 */
@Service
public class TenantDeliveryFeeRuleServiceImpl implements TenantDeliveryFeeRuleService{
    @Resource
    private TenantDeliveryFeeRuleMapper tenantDeliveryFeeRuleMapper;
    @Resource
    private TenantDeliveryFeeAreaDao tenantDeliveryFeeAreaDao;

    @Override
    public TenantDeliveryFeeRuleDTO queryTenantDeliveryFeeRule(Long tenantId) {
        TenantDeliveryFeeRule tenantDeliveryFeeRule = tenantDeliveryFeeRuleMapper.selectByTenantId(tenantId);
        TenantDeliveryFeeRuleDTO tenantDeliveryFeeRuleDTO = new TenantDeliveryFeeRuleDTO();
        BeanUtils.copyProperties(tenantDeliveryFeeRule, tenantDeliveryFeeRuleDTO);
        // 查询区域运费规则
        List<TenantDeliveryFeeArea> tenantDeliveryFeeAreas = tenantDeliveryFeeAreaDao.queryByTenantIdAndRuleId(tenantId, tenantDeliveryFeeRuleDTO.getId());

        if (!CollectionUtils.isEmpty(tenantDeliveryFeeAreas)) {
            List<TenantDeliveryFeeAreaRuleDTO> tenantDeliveryFeeAreaRuleDTOS = tenantDeliveryFeeAreas.stream().map(
                    TenantDeliveryFeeAreaConvert::convertToDTO
            ).collect(Collectors.toList());

            Map<Integer, List<TenantDeliveryFeeAreaRuleDTO>> tenantDeliveryFeeAreaRuleDTOMap = tenantDeliveryFeeAreaRuleDTOS.stream().collect(Collectors.groupingBy(TenantDeliveryFeeAreaRuleDTO::getGroupId));
            List<TenantDeliveryFeeAreaRuleGroupDTO> tenantDeliveryFeeAreaRuleGroupDTOS = tenantDeliveryFeeAreaRuleDTOMap.values().stream().map(tenantDeliveryFeeAreaRuleDTOList -> {
                TenantDeliveryFeeAreaRuleGroupDTO tenantDeliveryFeeAreaRuleGroupDTO = new TenantDeliveryFeeAreaRuleGroupDTO();
                TenantDeliveryFeeAreaRuleDTO areaRuleDTO = tenantDeliveryFeeAreaRuleDTOList.get(NumberConstants.ZERO);
                tenantDeliveryFeeAreaRuleGroupDTO.setGroupId(areaRuleDTO.getGroupId());
                tenantDeliveryFeeAreaRuleGroupDTO.setDefaultPrice(areaRuleDTO.getDefaultPrice());
                tenantDeliveryFeeAreaRuleGroupDTO.setSize(tenantDeliveryFeeAreaRuleDTOList.size());
                tenantDeliveryFeeAreaRuleGroupDTO.setDeliveryFeeAreaRuleDTOList(areaRuleDTO.getDeliveryFeeAreaRuleDTOList());
                tenantDeliveryFeeAreaRuleGroupDTO.setTenantDeliveryFeeAreaRuleDTOS(tenantDeliveryFeeAreaRuleDTOList);
                return tenantDeliveryFeeAreaRuleGroupDTO;
            }).collect(Collectors.toList());

            tenantDeliveryFeeRuleDTO.setTenantDeliveryFeeAreaRuleGroupDTOS(tenantDeliveryFeeAreaRuleGroupDTOS);
        }
        return tenantDeliveryFeeRuleDTO;
    }
}
