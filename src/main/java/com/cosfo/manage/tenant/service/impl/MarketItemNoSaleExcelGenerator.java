package com.cosfo.manage.tenant.service.impl;

import cn.hutool.core.date.DateUtil;
import com.cosfo.manage.common.context.ExcelTypeEnum;
import com.cosfo.manage.common.context.MarketItemNoSaleDetailTypeEnum;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.market.model.po.MarketItemCompositeMarket;
import com.cosfo.manage.market.model.po.MarketItemNoSaleDetail;
import com.cosfo.manage.market.service.MarketItemCompositeMarketService;
import com.cosfo.manage.report.model.dto.MarketItemNoSaleExcelDTO;
import com.cosfo.manage.report.repository.MarketItemNoSaleDetailRepository;
import com.cosfo.manage.report.service.ExcelGenerator;
import com.cosfo.manage.tenant.model.dto.MarketItemNoSaleExportQueryDTO;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class MarketItemNoSaleExcelGenerator implements ExcelGenerator<MarketItemNoSaleExportQueryDTO> {

    @Resource
    private CommonService commonService;
    @Resource
    private MarketItemNoSaleDetailRepository marketItemNoSaleDetailRepository;
    @Resource
    private MarketItemCompositeMarketService marketItemCompositeMarketService;


    @Override
    public String getExcelName(MarketItemNoSaleExportQueryDTO query) {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return String.format("前%d日连续滞销商品%s.xlsx", query.getDuration(), yesterday.format(formatter));
    }

    @Override
    public String generateExcelAndReturnFilePath(Long tenantId, MarketItemNoSaleExportQueryDTO query) {
        List<MarketItemNoSaleExcelDTO> marketItemNoSaleExcelDTOS = queryMarketItemNoSale(tenantId, query);
        return commonService.exportExcel(marketItemNoSaleExcelDTOS, ExcelTypeEnum.MARKET_ITEM_NO_SALE.getName());
    }

    private List<MarketItemNoSaleExcelDTO> queryMarketItemNoSale(Long tenantId, MarketItemNoSaleExportQueryDTO query) {
        MarketItemNoSaleDetailTypeEnum type = MarketItemNoSaleDetailTypeEnum.getByDuration(query.getDuration());
        if (type == null) {
            return new ArrayList<>();
        }
        String timeTag = DateUtil.yesterday().toString("yyyyMMdd");
        List<MarketItemNoSaleDetail> marketItemNoSaleDetails = marketItemNoSaleDetailRepository
                .selectByTenantIdAndTypeAndTag(tenantId, type.getType(), timeTag);
        if (CollectionUtils.isEmpty(marketItemNoSaleDetails)) {
            return new ArrayList<>();
        }

        List<Long> itemIds = marketItemNoSaleDetails.stream().map(MarketItemNoSaleDetail::getItemId).collect(Collectors.toList());
        Map<Long, String> itemIdSalePriceMap = marketItemNoSaleDetails.stream()
                .collect(Collectors.toMap(
                        MarketItemNoSaleDetail::getItemId,
                        item -> Optional.ofNullable(item.getSalePrice()).map(BigDecimal::toPlainString).orElse(""),
                        (item1, item2) -> item1)
                );


        List<MarketItemCompositeMarket> marketItemCompositeMarkets = marketItemCompositeMarketService.queryByMarketItemIds(tenantId, itemIds);
        return marketItemCompositeMarkets.stream().map(m -> {
            MarketItemNoSaleExcelDTO excelDTO = new MarketItemNoSaleExcelDTO();
            excelDTO.setTitle(m.getTitle());
            excelDTO.setFirstClassification(m.getFirstClassificationName());
            excelDTO.setSecondClassification(m.getSecondClassificationName());
            excelDTO.setSpecification(m.getSpecification());
            excelDTO.setItemId(m.getItemId().toString());
            excelDTO.setSalePrice(itemIdSalePriceMap.get(m.getItemId()));

            return excelDTO;
        }).collect(Collectors.toList());
    }
}
