package com.cosfo.manage.tenant.service;

import com.cosfo.manage.cache.InMemoryCache;
import com.cosfo.manage.tenant.model.po.TenantAuthConnection;

import java.util.List;

public interface TenantAuthConnectionService {

    /**
     * 新增授权方关系
     * @return
     */
    int insertAuthorizer(String appId, Long tenantId);

    @InMemoryCache
    TenantAuthConnection getTenantAuthConnectionByHuifuId(String huifuId);

    /**
     * 根据appId查询授权关系
     * @return
     */
    TenantAuthConnection selectAuthorizer(String appId);

    /**
     * 根据租户id获取授权信息
     * @param tenantId
     * @return
     */
    TenantAuthConnection selectAuthorizerByTenantId(Long tenantId);

    /**
     * 修改授权关系
     * @return
     */
    int updateAuthorizer(Long id, String appId, Long tenantId,Integer status);

    /**
     * 修改授权关联状态
     * @return
     */
    int updateAuthorizerStatus(TenantAuthConnection authConnection);

}
