package com.cosfo.manage.tenant.service.impl;

import com.cosfo.manage.cache.InMemoryCache;
import com.cosfo.manage.tenant.mapper.TenantAuthConnectionMapper;
import com.cosfo.manage.tenant.model.po.TenantAuthConnection;
import com.cosfo.manage.tenant.service.TenantAuthConnectionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service
@Slf4j
public class TenantAuthConnectionServiceImpl implements TenantAuthConnectionService {

    @Resource
    TenantAuthConnectionMapper tenantAuthConnectionMapper;

    @Override
    public int insertAuthorizer(String appId, Long tenantId) {
        TenantAuthConnection tenantAuthConnection = new TenantAuthConnection();
        tenantAuthConnection.setAppId(appId);
        tenantAuthConnection.setTenantId(tenantId);
        tenantAuthConnection.setStatus(1);
        tenantAuthConnection.setCreateTime(new Date());
        return tenantAuthConnectionMapper.insertSelective(tenantAuthConnection);
    }

    @InMemoryCache
    @Override
    public TenantAuthConnection getTenantAuthConnectionByHuifuId(String huifuId) {
        if (StringUtils.isBlank(huifuId)) {
            return null;
        }
        return tenantAuthConnectionMapper.selectByHuiFuId(huifuId);
    }

    @Override
    public TenantAuthConnection selectAuthorizer(String appId) {
        return tenantAuthConnectionMapper.selectByAppId(appId);
    }

    @Override
    public TenantAuthConnection selectAuthorizerByTenantId(Long tenantId) {
        return tenantAuthConnectionMapper.selectByTenantId(tenantId);
    }

    @Override
    public int updateAuthorizer(Long id, String appId, Long tenantId, Integer status) {
        TenantAuthConnection tenantAuthConnection = new TenantAuthConnection();
        tenantAuthConnection.setId(id);
        tenantAuthConnection.setAppId(appId);
        tenantAuthConnection.setTenantId(tenantId);
        tenantAuthConnection.setStatus(status);
        return tenantAuthConnectionMapper.updateByPrimaryKeySelective(tenantAuthConnection);
    }

    @Override
    public int updateAuthorizerStatus(TenantAuthConnection authConnection) {
        return tenantAuthConnectionMapper.updateByPrimaryKeySelective(authConnection);
    }
}
