package com.cosfo.manage.tenant.service;

import com.cosfo.manage.tenant.model.vo.TenantPrivilegesInfoVO;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface TenantPrivilegesInfoService {

    /**
     * 获取租户当前权益
     *
     * @param tenantId
     * @return
     */
    TenantPrivilegesInfoVO getPrivilegesInfo(Long tenantId);


    /**
     * 获取租户试用标签
     * @param tenantId
     * @return
     */
    Set<Long> queryFlagPurviewIds(Long tenantId);
}
