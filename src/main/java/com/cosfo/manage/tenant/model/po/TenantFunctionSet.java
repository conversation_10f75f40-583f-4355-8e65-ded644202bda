package com.cosfo.manage.tenant.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import com.cosfo.manage.common.config.ListLongHandler;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 功能集
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-26
 */
@Getter
@Setter
@TableName(value = "tenant_function_set", autoResultMap = true)
public class TenantFunctionSet implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 功能名称
     */
    @TableField("name")
    private String name;

    /**
     * 功能描述
     */
    @TableField("func_desc")
    private String funcDesc;

    /**
     * 权限id
     */
    @TableField(value = "purview_ids", typeHandler = ListLongHandler.class)
    private List<Long> purviewIds;

    /**
     * 销售归属版本，1定销2标准3专业
     */
    @TableField("sale_version")
    private Integer saleVersion;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField("creator")
    private Long creator;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 操作人
     */
    @TableField("operator")
    private Long operator;


}
