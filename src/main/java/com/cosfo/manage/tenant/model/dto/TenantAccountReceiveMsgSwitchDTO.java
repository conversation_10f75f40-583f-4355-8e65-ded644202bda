package com.cosfo.manage.tenant.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;


@Data
public class TenantAccountReceiveMsgSwitchDTO {
    /**
     * 4-微信公众号  ，可空   默认4
     */
    private Integer channelType;

    /**
     * FTGYL-帆台供应链;可空   默认FTGYL
     */
    private String channelCode;

    /**
     * 租户accoountId
     */
    @NotNull(message = "租户accoountId不能为空")
    private Long tenantAccountId;

    /**
     * 是否可接收0=可;1=不可
     */
    @NotNull(message = "是否可接收 不能为空")
    private Integer availableStatus;
}
