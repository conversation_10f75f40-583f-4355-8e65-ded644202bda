package com.cosfo.manage.tenant.model.po;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * 汇付商户基本信息表
 * <AUTHOR>
 * @TableName hui_fu_base
 */
@Data
public class HuiFuBase implements Serializable {
    /**
     * 主键、自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 汇付商户名称
     */
    private String regName;

    /**
     * 汇付商户Id
     */
    private String huifuId;

    /**
     * 0对公1对私
     */
    private String settleType;

    /**
     * 开户行
     */
    private String openingBank;

    /**
     * 户名
     */
    private String accountName;

    /**
     * 户号
     */
    private String accountNumber;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}