package com.cosfo.manage.tenant.model.po;

import java.util.Date;
import lombok.Data;

/**
 * 描述:ten_company表的实体类
 * @version
 * @author:  Song
 * @创建时间: 2022-05-09
 */
@Data
public class TenantCompany {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 信用代码
     */
    private String creditCode;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 联系地址
     */
    private String address;

    /**
     * 公司联系手机号
     */
    private String phone;

    /**
     * 营业执照
     */
    private String businessLicense;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 开户行
     */
    private String openingBank;

    /**
     * 户名
     */
    private String accountName;

    /**
     * 户号
     */
    private String accountNumber;
}