package com.cosfo.manage.tenant.model.vo;

import com.cosfo.manage.facade.dto.AuthUseLoginDTO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/27
 */
@Data
public class TenantAccountPreLoginVO extends AuthUseLoginDTO {
    /**
     * 默认租户Id
     */
    private Long defaultTenantId;

    /**
     * 是否多租户
     */
    private Boolean multiTenant;


    /**
     * 修改密码间隔天数
     */
    private Integer intervalDay;

    /**
     * 是否需要修改密码:true:是;false:否
     */
    private Boolean needChangePassword = Boolean.FALSE;

    /**
     * 弹框 true：若lastUpdatePwdTime超过180天可以弹框
     *    false：不允许弹出修改密码弹框
     */
    private Boolean popAlert;
}
