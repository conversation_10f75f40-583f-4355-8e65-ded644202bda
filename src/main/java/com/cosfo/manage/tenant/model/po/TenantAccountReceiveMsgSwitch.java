package com.cosfo.manage.tenant.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * 用户接受消息开关表
 * @TableName tenant_account_receive_msg_switch
 */
@TableName(value ="tenant_account_receive_msg_switch")
@Data
public class TenantAccountReceiveMsgSwitch implements Serializable {
    /**
     * primary key
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 4-微信公众号
     */
    private Integer channelType;

    /**
     * FTGYL-帆台供应链;
     */
    private String channelCode;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 租户accoountId
     */
    private Long tenantAccountId;

    /**
     * 是否可接收0=可;1=不可
     */
    private Integer availableStatus;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}