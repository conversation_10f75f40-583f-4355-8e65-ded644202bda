package com.cosfo.manage.tenant.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class FunctionSetVO implements Serializable {

    private Long id;

    /**
     * 功能集名称
     */
    private String name;

    /**
     * 功能集描述
     */
    private String desc;

    /**
     * 过期时间
     */
    private LocalDate expireDate;

    /**
     * 功能列表
     */
    private List<MenuPurviewVO> purviewList;

    /**
     * 功能数量
     */
    private Integer functionCnt;

    /**
     * 是否过期
     */
    private Boolean expired;
}
