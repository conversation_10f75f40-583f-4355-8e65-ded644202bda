package com.cosfo.manage.tenant.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/27
 */
@Data
public class TenantAccountLoginDTO {
    /**
     * 手机号
     */
    private String phone;
    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String password;
    /**
     * 租户Id
     */
    @NotNull(message = "租户Id不能为空")
    private Long tenantId;
}
