package com.cosfo.manage.tenant.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

/**
 * @Author: fansongsong
 * @Date: 2023-10-17
 * @Description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class TenantAccountReceiveMsgQueryDTO {

    /**
     * 4-微信公众号
     */
    private Integer channelType;

    /**
     * FTGYL-帆台供应链;
     */
    private String channelCode;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 租户accoountId
     */
    private Set<Long> tenantAccountIds;

    /**
     * 是否可接收0=可;1=不可
     */
    private Integer availableStatus;
}
