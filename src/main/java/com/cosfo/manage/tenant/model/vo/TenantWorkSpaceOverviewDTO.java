package com.cosfo.manage.tenant.model.vo;

import lombok.Data;

/**
 * @description: 工作台概况VO
 * @author: George
 * @date: 2023-10-24
 **/
@Data
public class TenantWorkSpaceOverviewDTO {

    /**
     * 支付金额
     */
    private String paymentAmount;

    /**
     * 支付金额直营占比
     */
    private String paymentAmountDirectRate;


    /**
     * 支付门店数
     */
    private String paymentStoreCounts;

    /**
     * 支付门店数直营占比
     */
    private String paymentStoreCountsDirectRate;

    /**
     * 店单价
     */
    private String storeUnitAmount;
    /**
     * 直营店单价
     */
    private String storeUnitAmountDirect;

    /**
     * 退款金额
     */
    private String refundAmount;

    /**
     * 退款金额直营占比
     */
    private String refundAmountDirectRate;

    /**
     * 在售商品数
     */
    private String onSaleItemCounts;

    /**
     * 动销商品数
     */
    private String turnoverItemCounts;

    /**
     * 动销率
     */
    private String turnoverRate;

    /**
     * 商品动销率直营占比
     */
    private String turnoverRateDirectRate;

    /**
     * 商品付款件数
     */
    private String paymentQuantity;

    /**
     * 商品付款件数直营占比
     */
    private String paymentQuantityDirectRate;
}
