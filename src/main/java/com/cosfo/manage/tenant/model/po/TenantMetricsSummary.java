package com.cosfo.manage.tenant.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 租户指标汇总
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
@Getter
@Setter
@TableName("tenant_metrics_summary")
public class TenantMetricsSummary implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 时间标签（yyyyMMdd）
     */
    @TableField("time_tag")
    private String timeTag;

    /**
     * 7日滞销率（连续未发生付款的商品数与所有上架中的商品数的占比）
     */
    @TableField("no_sale_item_rate_7d")
    private BigDecimal noSaleItemRate7d;

    /**
     * 30日滞销率
     */
    @TableField("no_sale_item_rate_30d")
    private BigDecimal noSaleItemRate30d;

    /**
     * 7日采购活跃率（有过下单的门店数 / 经营中的总门店数）
     */
    @TableField("store_purchase_rate_7d")
    private BigDecimal storePurchaseRate7d;

    /**
     * 30日采购活跃率
     */
    @TableField("store_purchase_rate_30d")
    private BigDecimal storePurchaseRate30d;

    /**
     * 昨天已到货退款率（已到货退款&退货退款金额占比）
     */
    @TableField("refund_and_returns_rate_1d")
    private BigDecimal refundAndReturnsRate1d;

    /**
     * 7日已到货退款率
     */
    @TableField("refund_and_returns_rate_7d")
    private BigDecimal refundAndReturnsRate7d;

    /**
     * 30日已到货退款率
     */
    @TableField("refund_and_returns_rate_30d")
    private BigDecimal refundAndReturnsRate30d;

    /**
     * 次日履约率（近30天订单次日发货数 / 30天总订单数）
     */
    @TableField("order_fulfillment_rate_1d")
    private BigDecimal orderFulfillmentRate1d;

    /**
     * 3日履约率
     */
    @TableField("order_fulfillment_rate_3d")
    private BigDecimal orderFulfillmentRate3d;

    /**
     * 7日履约率
     */
    @TableField("order_fulfillment_rate_7d")
    private BigDecimal orderFulfillmentRate7d;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 7日库存周转天数
     */
    @TableField("stock_turnover_days_30d")
    private BigDecimal stockTurnoverDays30d;
    

    /**
     * 15天滞销货品数
     */
    @TableField("no_sale_goods_num_15d")
    private Integer noSaleGoodsNum15d;


    /**
     * 近15天临期货品数
     */
    @TableField("near_deadline_goods_num_15d")
    private Integer nearDeadlineGoodsNum15d;

    /**
     * 近30天过期货品数
     */
    @TableField("expired_goods_num_30d")
    private Integer expiredGoodsNum30d;

    /**
     * 近30天供应商到仓准时率
     */
    @TableField("supplier_on_time_delivery_rate_30d")
    private BigDecimal supplierOnTimeDeliveryRate30d;

    /**
     * 近30天供应商到仓准确率
     */
    @TableField("supplier_to_warehouse_accuracy_30d")
    private BigDecimal supplierToWarehouseAccuracy30d;

    /**
     * 近7天货损出库的商品件数
     */
    @TableField("damaged_goods_out_of_warehouse_num_7d")
    private Integer damagedGoodsOutOfWarehouseNum7d;

    /**
     * 近7天库存准确率
     */
    @TableField("stock_accuracy_7d")
    private BigDecimal stockAccuracy7d;

    /**
     * 近7天出库准确率
     */
    @TableField("outbound_accuracy_7d")
    private BigDecimal outboundAccuracy7d;

    /**
     * 近7天出库及时率
     */
    @TableField("outbound_timeliness_rate_7d")
    private BigDecimal outboundTimelinessRate7d;

    /**
     * 近7天入库及时率
     */
    @TableField("inbound_timeliness_rate_7d")
    private BigDecimal inboundTimelinessRate7d;
}
