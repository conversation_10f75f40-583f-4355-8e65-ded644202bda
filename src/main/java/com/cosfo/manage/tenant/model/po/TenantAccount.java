package com.cosfo.manage.tenant.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 租户账号
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TenantAccount implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 外部用户系统用户id
     */
    private Long authUserId;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像
     */
    private String profilePicture;

    /**
     * 状态0有效1失效
     */
    private Integer status;

    /**
     * 操作时间
     */
    private LocalDateTime operatorTime;

    /**
     * 操作手机号
     */
    private String operatorPhone;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * 删除标识0有效1删除
     */
    private Integer deletedFlag;


    /**
     * email
     */
    private String email;

    /**
     * 操作人
     */
    private String updater;
}
