package com.cosfo.manage.tenant.model.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/23
 */
@Data
public class MenuPurviewDTO {
    /**
     * 权限id
     */
    private Long id;

    /**
     * 父权限Id
     */
    private Integer parentId;

    /**
     * 权限名称
     */
    private String menuName;

    /**
     * 权限对应资源(URL)
     */
    @Length(max = 100, message = "权限URL长度不能超过100")
    private String url;

    /**
     * 权限描述
     */
    private String description;

    /**
     * 排序
     */
    private Integer weight;

    /**
     * 新增类型 0 菜单 1模块 2按钮
     */
    private Integer type;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后操作人ID
     */
    private String lastUpdater;

    /**
     * 最后更改时间
     */
    private Date updateTime;

    /**
     * 权限 名称
     */
    private String purviewName;

    /**
     * 关联权限
     */
    private List<String> sonPurviewList;

    /**
     * 子菜单
     */
    private List<MenuPurviewDTO> childMenuPurviewVOS;


    /**
     * 1 试用
     */
    private Integer flagType;

    /**
     * 是否过期
     */
    private Boolean expired;

    /**
     * 过期时间
     */
    private LocalDate expireDate;
}
