package com.cosfo.manage.tenant.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 租户度量报告
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
@Getter
@Setter
@TableName("tenant_measure_report")
public class TenantMeasureReport implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 立即优化数
     */
    @TableField("immediate_optimization_num")
    private Integer immediateOptimizationNum;

    /**
     * 度量项数
     */
    @TableField("measure_item_num")
    private Integer measureItemNum;

    /**
     * 1、待处理 2、处理中 3、处理完成 4、处理失败
     */
    @TableField("status")
    private Integer status;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}
