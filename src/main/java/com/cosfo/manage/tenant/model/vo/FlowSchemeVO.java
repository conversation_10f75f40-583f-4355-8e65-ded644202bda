package com.cosfo.manage.tenant.model.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
public class FlowSchemeVO {

    /**
     * 方案编码
     */
    private Long schemeId;

    /**
     * 方案名称
     */
    private String schemeName;

    /**
     * 默认方案类型 1-直营门店方案 2-加盟门店方案，例外方案表示来源默认方案类型
     */
    private Integer schemeType;

    /**
     * 是否默认流程方案0:例外方案;1:默认方案
     */
    private Integer defaultFlag;

    /**
     * 门店数量
     */
    private Integer storeNum;

    /**
     * 门店id
     */
    private Set<Long> storeIdSet;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String creatorName;

}
