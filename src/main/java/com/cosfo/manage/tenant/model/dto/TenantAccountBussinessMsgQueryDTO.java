package com.cosfo.manage.tenant.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

/**
 * @Author: fansongsong
 * @Date: 2023-10-17
 * @Description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class TenantAccountBussinessMsgQueryDTO {

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 租户accoountIds
     */
    private Set<Long> tenantAccountIds;

    /**
     * 是否可接收0=可;1=不可
     */
    private Integer availableStatus;

    /**
     * 1=付款后消息提醒;2=退款后消息提醒;3=待发货消息汇总提醒
     */
    private Integer bussinessType;

    /**
     * 发送时间
     */
    private Integer pushHour;

}
