package com.cosfo.manage.tenant.model.dto;

import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2023-11-09
 **/
@Data
public class TenantMeasureReportDTO {

    /**
     * true 度量过 false 没度量过
     */
    private boolean measureIdentification;

    /**
     * 度量项数
     */
    private Integer measureItemNums;

    /**
     * 立即优化数
     */
    private Integer immediateOptimizationNums;

    /**
     * 状态
     * @see com.cosfo.manage.common.context.TenantMeasureStatusEnum
     */
    private Integer status;

    /**
     * 度量模块列表
     */
    private List<TenantMeasureModuleDTO> measureModuleList;

}
