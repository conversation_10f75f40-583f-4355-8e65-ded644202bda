package com.cosfo.manage.tenant.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 预付交易明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TenantPrepaymentTransaction implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 供应商租户id
     */
    private Long supplierTenantId;

    /**
     * 交易流水号
     */
    private String transactionNo;

    /**
     * 收支类型 0、收入 1、支出
     */
    private Integer type;

    /**
     * 交易类型 0、预付 1、预付退款 2、直供货品消费 3、直供货品退款 4、运费 5、运费退款 6、代仓费用 7、代仓费用退款
     */
    private Integer transactionType;

    /**
     * 交易预付金额
     */
    private BigDecimal transactionAmount;

    /**
     * 关联订单单号/售后单号
     */
    private String associatedOrderNo;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;


}
