package com.cosfo.manage.tenant.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: George
 * @date: 2024-03-29
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WarningCenterCardDTO {

    /**
     * 过期商品预警数
     */
    private Integer warningOfExpiredGoods;

    /**
     * 临期商品预警数
     */
    private Integer waringOfTemporaryInsuranceGoods;

    /**
     * 存货已滞库数
     */
    private Integer stockIsOutOfStock;

    /**
     * 存货将到期数
     */
    private Integer inventoryIsDue;

    /**
     * 库存已预警数
     */
    private Integer stockIsWarning;

    /**
     * 库存已售罄数
     */
    private Integer stockIsSoldOut;

    /**
     * 低于安全库存下限数
     */
    private Integer belowLowerSafetyStockLimit;

    /**
     * 高于安全库存上限数
     */
    private Integer aboveLowerSafetyStockLimit;
}
