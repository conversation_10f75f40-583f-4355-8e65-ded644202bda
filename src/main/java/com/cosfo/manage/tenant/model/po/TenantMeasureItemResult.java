package com.cosfo.manage.tenant.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 度量项结果
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
@Getter
@Setter
@TableName("tenant_measure_item_result")
public class TenantMeasureItemResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 度量报告id
     */
    @TableField("report_id")
    private Long reportId;

    /**
     * 度量项id
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * 度量项标题
     */
    @TableField("item_title")
    private String itemTitle;

    /**
     * 度量项结果
     */
    @TableField("item_result")
    private String itemResult;

    /**
     * 度量项结果状态
     * @see com.cosfo.manage.common.context.TenantConfigStatusEnum
     */
    @TableField("item_result_state")
    private String itemResultState;

    /**
     * 1、数据报表类 2、立即优化类
     */
    @TableField("item_type")
    private Integer itemType;

    /**
     * 路由（前端跳转）
     */
    @TableField("item_route")
    private String itemRoute;

    /**
     * 1、待处理 2、处理中 3、处理完毕 4、处理失败
     */
    @TableField("status")
    private Integer status;

    /**
     * 一级模块名
     */
    @TableField("first_module_name")
    private String firstModuleName;

    /**
     * 二级模块名
     */
    @TableField("second_module_name")
    private String secondModuleName;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}
