package com.cosfo.manage.tenant.model.dto;

import lombok.Data;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2023-11-09
 **/
@Data
public class TenantMeasureItemResultDTO {

    /**
     * 度量项结果
     */
    private String itemResult;

    /**
     * 度量项结果状态
     */
    private String itemResultState;

    /**
     * 1、数据报表类 2、立即优化类
     */
    private Integer itemType;

    /**
     * 路由（前端跳转）
     */
    private String itemRoute;

    /**
     * 1、待处理 2、处理中 3、处理完毕 4、处理失败
     */
    private Integer status;

    /**
     * 度量项id
     */
    private Long itemId;
}
