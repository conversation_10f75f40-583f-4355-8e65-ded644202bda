package com.cosfo.manage.tenant.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 租户流程方案表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-25
 */
@Getter
@Setter
@TableName("tenant_flow_scheme")
public class TenantFlowScheme implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 默认方案类型 1-直营门店方案 2-加盟门店方案，例外方案表示来源默认方案类型
     */
    @TableField("scheme_type")
    private Integer schemeType;

    /**
     * 方案名称
     */
    @TableField("scheme_name")
    private String schemeName;

    /**
     * 是否默认流程方案0:例外方案;1:默认方案
     */
    @TableField("default_flag")
    private Integer defaultFlag;

    /**
     * 方案目标对象，加盟、直营门店方案存门店类型，例外方案存门店id
     */
    @TableField("scheme_target_info")
    private String schemeTargetInfo;

    /**
     * 操作人
     */
    @TableField("operator_name")
    private String operatorName;

    /**
     * 创建人
     */
    @TableField("creator_name")
    private String creatorName;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}
