package com.cosfo.manage.tenant.model.po;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * tenant_agreement
 * <AUTHOR>
@Data
public class TenantAgreement implements Serializable {
    private Integer id;

    /**
     * 0、门店注册-品牌方隐私声明 1、门店注册-隐私声明 2、门店注册-用户协议 3、品牌注册-隐私声明 4、品牌注册-用户协议
     */
    private Byte type;

    private String agreement;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Integer updateTime;

    private static final long serialVersionUID = 1L;
}
