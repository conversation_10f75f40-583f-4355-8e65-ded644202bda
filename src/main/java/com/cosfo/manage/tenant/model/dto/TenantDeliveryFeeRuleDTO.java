package com.cosfo.manage.tenant.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/26
 */
@Data
public class TenantDeliveryFeeRuleDTO {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 0、采用鲜沐运费 1、免运费 2自定义
     */
    private Integer type;
    /**
     * 默认运费金额
     */
    private BigDecimal defaultPrice;
    /**
     * 满减类型：0按金额 1按件数
     */
    private Integer freeType;
    /**
     * 满减限制
     */
    private BigDecimal freeNumber;
    /**
     * 区域运费配送规则
     */
    private List<TenantDeliveryFeeAreaRuleGroupDTO> tenantDeliveryFeeAreaRuleGroupDTOS;
}
