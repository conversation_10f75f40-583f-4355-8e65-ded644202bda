package com.cosfo.manage.tenant.model.vo;

import com.cosfo.manage.facade.dto.SupplierInfoDTO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 账号表
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/23
 */
@Data
public class TenantAccountVO {

    private Long id;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 用户名称
     */
    private String nickname;

    /**
     * 头像
     */
    private String profilePicture;

    /**
     * 角色Id
     */
    private List<RoleVO> roleVos;

    /**
     * 0有效1失效
     */
    private Integer status;

    /**
     * 操作时间
     */
    private LocalDateTime operatorTime;

    /**
     * 操作手机号
     */
    private String operatorPhone;

    /**
     * authUserId
     */
    private Long authUserId;

    /**
     * 账号供应商映射
     */
    private TenantAccountSupplierMappingVO tenantAccountSupplierMappingVO;

    /**
     * true-是供应商配送员 false-不是
     */
    private Boolean supplierDistributorFlag;

    /**
     * 供应商ids
     */
    private List<Long> supplierIds;

    /**
     * 是否关注 帆台供应链 公众号
     */
    private Boolean ftgylWatchFlag ;
    /**
     * 修改密码间隔天数
     */
    private Integer intervalDay;

    /**
     * 是否需要修改密码:true:是;false:否
     */
    private Boolean needChangePassword = Boolean.FALSE;

    /**
     * 弹框 true：若lastUpdatePwdTime超过180天可以弹框
     *    false：不允许弹出修改密码弹框
     */
    private Boolean popAlert;

    /**
     * 是否是超级账号
     */
    private Boolean isSuperAccount;

    /**
     * email
     */
    private String email;

    /**
     * 操作人
     */
    private String updater;

    /**
     * 租户账户类型:0-手机号登录，1-邮箱登录
     */
    private Integer accountLoginType;
}
