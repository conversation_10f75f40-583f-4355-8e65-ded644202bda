package com.cosfo.manage.tenant.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * 用户业务消息配置表
 * @TableName tenant_account_bussiness_msg_config
 */
@TableName(value ="tenant_account_bussiness_msg_config")
@Data
public class TenantAccountBussinessMsgConfig implements Serializable {
    /**
     * primary key
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 1=付款后消息提醒;2=退款后消息提醒;3=待发货消息汇总提醒
     */
    private Integer bussinessType;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 租户accoountId
     */
    private Long tenantAccountId;

    /**
     * 是否可接收0=可;1=不可
     */
    private Integer availableStatus;

    /**
     * 发送时间
     */
    private Integer pushHour;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}