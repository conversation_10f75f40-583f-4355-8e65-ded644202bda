package com.cosfo.manage.tenant.model.input;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Data
public class TenantScoreInfoInput {

    /**
     * 评分步骤，0-评分,1-反馈意见
     */
    @NotNull(message = "stepNum不可为空")
    private Integer stepNum;

    /**
     * 评分标题
     */
    private String title;

    /**
     * 已选项 id
     */
    private List<String> selectItems;

    /**
     * 意见反馈
     */
    private String feedback;
}
