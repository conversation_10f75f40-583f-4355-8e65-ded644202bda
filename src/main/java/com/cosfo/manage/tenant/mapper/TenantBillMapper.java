package com.cosfo.manage.tenant.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.cosfo.manage.tenant.model.input.TenantBillInput;
import com.cosfo.manage.tenant.model.po.TenantBill;
import com.cosfo.manage.tenant.model.vo.TenantBillVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface TenantBillMapper {

    /**
     * 删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(TenantBill record);

    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(TenantBill record);

    /**
     * 查询
     * @param id
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    TenantBill selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(TenantBill record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(TenantBill record);

    /**
     * 账单列表
     * @param tenantBillInput
     * @param tenantId
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<TenantBillVO> list(@Param("tenantBillInput") TenantBillInput tenantBillInput,@Param("tenantId") Long tenantId);

    /**
     * 导出账单列表
     * @param tenantBillInput
     * @param tenantId
     */
    void exportList(@Param("tenantBillInput") TenantBillInput tenantBillInput,@Param("tenantId") Long tenantId, ResultHandler<?> resultHandler);

//    /**
//     * 合计收入金额
//     * @param tenantBillInput
//     * @param tenantId
//     * @return
//     */
//    BigDecimal countMoney(@Param("tenantBillInput") TenantBillInput tenantBillInput,@Param("tenantId") Long tenantId);
}
