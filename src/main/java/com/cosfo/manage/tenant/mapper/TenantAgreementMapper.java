package com.cosfo.manage.tenant.mapper;

import com.cosfo.manage.tenant.model.po.TenantAgreement;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface TenantAgreementMapper {

    /**
     * 删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(TenantAgreement record);

    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(TenantAgreement record);

    /**
     * 查询
     * @param id
     * @return
     */
    TenantAgreement selectByPrimaryKey(Integer id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(TenantAgreement record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(TenantAgreement record);

    /**
     * 根据type查询
     * @param type
     * @return
     */
    TenantAgreement selectByType(Integer type);
}
