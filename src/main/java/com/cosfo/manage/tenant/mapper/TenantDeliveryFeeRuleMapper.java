package com.cosfo.manage.tenant.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.cosfo.manage.tenant.model.po.TenantDeliveryFeeRule;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface TenantDeliveryFeeRuleMapper {

    /**
     * 删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(TenantDeliveryFeeRule record);

    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(TenantDeliveryFeeRule record);

    /**
     * 查询
     * @param id
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    TenantDeliveryFeeRule selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(TenantDeliveryFeeRule record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(TenantDeliveryFeeRule record);

    /**
     * 查询品牌方运费规则
     *
     * @param tenantId
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    TenantDeliveryFeeRule selectByTenantId(@Param("tenantId") Long tenantId);
}
