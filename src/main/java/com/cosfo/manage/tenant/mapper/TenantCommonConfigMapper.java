package com.cosfo.manage.tenant.mapper;

import com.cosfo.manage.tenant.model.po.TenantCommonConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TenantCommonConfigMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TenantCommonConfig record);

    int insertSelective(TenantCommonConfig record);

    TenantCommonConfig selectByPrimaryKey(Long id);

    TenantCommonConfig selectByTenantIdAndConfigKey(@Param("tenantId") Long tenantId, @Param("configKey") String configKey);

    int updateConfigValueById(TenantCommonConfig record);

    int updateByPrimaryKeySelective(TenantCommonConfig record);

    int updateByPrimaryKey(TenantCommonConfig record);
}