package com.cosfo.manage.tenant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.manage.tenant.model.po.TenantDeliveryFeeArea;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface TenantDeliveryFeeAreaMapper extends BaseMapper<TenantDeliveryFeeArea> {
    int deleteByPrimaryKey(Long id);

    int insert(TenantDeliveryFeeArea record);

    int insertSelective(TenantDeliveryFeeArea record);

    TenantDeliveryFeeArea selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TenantDeliveryFeeArea record);

    int updateByPrimaryKey(TenantDeliveryFeeArea record);
}