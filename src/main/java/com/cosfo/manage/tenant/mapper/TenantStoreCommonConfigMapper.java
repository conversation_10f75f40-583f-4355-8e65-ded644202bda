package com.cosfo.manage.tenant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.manage.tenant.model.po.TenantStoreCommonConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 租户门店公共配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-25
 */
@Mapper
public interface TenantStoreCommonConfigMapper extends BaseMapper<TenantStoreCommonConfig> {


    /**
     * 批量插入
     */
    void batchInsert(@Param("list") List<TenantStoreCommonConfig> list);
}
