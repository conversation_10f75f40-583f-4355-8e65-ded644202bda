package com.cosfo.manage.tenant.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.tenant.model.po.TenantStoreCommonConfig;

import java.util.List;

/**
 * <p>
 * 租户门店公共配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-25
 */
public interface TenantStoreCommonConfigRepository extends IService<TenantStoreCommonConfig> {


    /**
     * 查询门店配置
     * @param tenantId
     * @param storeId
     * @param configKey
     * @return
     */
    TenantStoreCommonConfig selectByStoreIdAndConfigKey(Long tenantId, Long storeId, String configKey);

    List<TenantStoreCommonConfig> queryByStoreIdsAndConfigKey(List<Long> storeIds, String configKey);

    void batchSave(List<TenantStoreCommonConfig> tenantStoreCommonConfigs);

    boolean saveRecord(TenantStoreCommonConfig tenantStoreCommonConfig);

    boolean updateRecordById(TenantStoreCommonConfig tenantStoreCommonConfig);

}
