package com.cosfo.manage.tenant.repository;

import com.cosfo.manage.tenant.model.po.TenantFlowRuleAudit;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 方案流程审核项配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-25
 */
public interface TenantFlowRuleAuditRepository extends IService<TenantFlowRuleAudit> {

    List<TenantFlowRuleAudit> selectByTenantAndFlowSchemeId(Long tenantId, Long flowSchemeId);

    TenantFlowRuleAudit getByFlowSchemeIdAndBizType(Long tenantId, Long flowSchemeId, Integer bizType);
    
    List<TenantFlowRuleAudit> getByFlowSchemeIdListAndBizType(Long tenantId, Collection<Long> flowSchemeIdList, Integer bizType);

    boolean deleteBySchemeId(Long tenantId, Long flowSchemeId);
}
