package com.cosfo.manage.tenant.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.manage.tenant.model.po.TenantPrivilegesConfig;
import com.cosfo.manage.tenant.mapper.TenantPrivilegesConfigMapper;
import com.cosfo.manage.tenant.repository.TenantPrivilegesConfigRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 租户权益配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-26
 */
@Service
public class TenantPrivilegesConfigRepositoryImpl extends ServiceImpl<TenantPrivilegesConfigMapper, TenantPrivilegesConfig> implements TenantPrivilegesConfigRepository {

    @Override
    public List<TenantPrivilegesConfig> listByTenantId(Long tenantId) {
        LambdaQueryWrapper<TenantPrivilegesConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantPrivilegesConfig::getTenantId, tenantId);
        return list(queryWrapper);
    }

    @Override
    public List<TenantPrivilegesConfig> listAddByTenantId(Long tenantId, Integer flagType, Integer configType) {
        LambdaQueryWrapper<TenantPrivilegesConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantPrivilegesConfig::getTenantId, tenantId);
        queryWrapper.eq(TenantPrivilegesConfig::getConfigType, configType);
        queryWrapper.eq(TenantPrivilegesConfig::getFlagType, flagType);
        return list(queryWrapper);
    }
}
