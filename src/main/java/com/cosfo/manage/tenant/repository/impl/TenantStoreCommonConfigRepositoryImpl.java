package com.cosfo.manage.tenant.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.tenant.mapper.TenantStoreCommonConfigMapper;
import com.cosfo.manage.tenant.model.po.TenantStoreCommonConfig;
import com.cosfo.manage.tenant.repository.TenantStoreCommonConfigRepository;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 租户门店公共配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-25
 */
@Service
public class TenantStoreCommonConfigRepositoryImpl extends ServiceImpl<TenantStoreCommonConfigMapper, TenantStoreCommonConfig> implements TenantStoreCommonConfigRepository {

    @Override
    public TenantStoreCommonConfig selectByStoreIdAndConfigKey(Long tenantId, Long storeId, String configKey) {
        LambdaQueryWrapper<TenantStoreCommonConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantStoreCommonConfig::getTenantId, tenantId);
        queryWrapper.eq(TenantStoreCommonConfig::getStoreId, storeId);
        queryWrapper.eq(TenantStoreCommonConfig::getConfigKey, configKey);
        return getOne(queryWrapper);
    }

    @Override
    public List<TenantStoreCommonConfig> queryByStoreIdsAndConfigKey(List<Long> storeIds, String configKey) {
        LambdaQueryWrapper<TenantStoreCommonConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TenantStoreCommonConfig::getStoreId, storeIds);
        queryWrapper.eq(TenantStoreCommonConfig::getConfigKey, configKey);
        return list(queryWrapper);
    }

    @Override
    public void batchSave(List<TenantStoreCommonConfig> tenantStoreCommonConfigs) {
        baseMapper.batchInsert(tenantStoreCommonConfigs);
    }

    @Override
    public boolean saveRecord(TenantStoreCommonConfig tenantStoreCommonConfig) {
        return saveOrUpdate(tenantStoreCommonConfig);
    }

    @Override
    public boolean updateRecordById(TenantStoreCommonConfig tenantStoreCommonConfig) {
        return updateById(tenantStoreCommonConfig);
    }
}
