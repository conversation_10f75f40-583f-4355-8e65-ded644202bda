package com.cosfo.manage.tenant.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.tenant.mapper.TenantMeasureItemResultMapper;
import com.cosfo.manage.tenant.model.po.TenantMeasureItemResult;
import com.cosfo.manage.tenant.repository.TenantMeasureItemResultRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 度量项结果 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
@Service
public class TenantMeasureItemResultRepositoryImpl extends ServiceImpl<TenantMeasureItemResultMapper, TenantMeasureItemResult> implements TenantMeasureItemResultRepository {

    @Resource
    private TenantMeasureItemResultMapper measureItemResultMapper;
    @Override
    public boolean saveBatch(Collection<TenantMeasureItemResult> entityList) {
        return measureItemResultMapper.saveBatch(entityList);
    }

    @Override
    public List<TenantMeasureItemResult> listByReportId(Long tenantId, Long reportId) {
        LambdaQueryWrapper<TenantMeasureItemResult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantMeasureItemResult::getTenantId, tenantId)
               .eq(TenantMeasureItemResult::getReportId, reportId);
        return list(queryWrapper);
    }

    @Override
    public List<TenantMeasureItemResult> listByCondition(Long tenantId, Long id, Integer itemType, String itemResultState) {
        return measureItemResultMapper.listByCondition(tenantId, id, itemType, itemResultState);
    }
}
