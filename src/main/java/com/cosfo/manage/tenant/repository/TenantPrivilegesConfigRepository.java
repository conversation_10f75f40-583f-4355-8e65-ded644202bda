package com.cosfo.manage.tenant.repository;

import com.cosfo.manage.tenant.model.po.TenantPrivilegesConfig;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 租户权益配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-26
 */
public interface TenantPrivilegesConfigRepository extends IService<TenantPrivilegesConfig> {

    /**
     * 获取当前租户权益
     * @param tenantId
     * @return
     */
    List<TenantPrivilegesConfig> listByTenantId(Long tenantId);


    /**
     * 获取租户增购权益
     * @param tenantId
     * @return
     */
    List<TenantPrivilegesConfig> listAddByTenantId(Long tenantId, Integer flagType, Integer configType);

}
