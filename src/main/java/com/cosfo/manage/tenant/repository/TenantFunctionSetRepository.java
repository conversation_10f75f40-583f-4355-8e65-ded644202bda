package com.cosfo.manage.tenant.repository;

import com.cosfo.manage.tenant.model.po.TenantFunctionSet;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 功能集 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-26
 */
public interface TenantFunctionSetRepository extends IService<TenantFunctionSet> {

    /**
     * 获取功能集
     *
     * @param setIds
     * @return
     */
    List<TenantFunctionSet> listBySetIds(List<Long> setIds);
}
