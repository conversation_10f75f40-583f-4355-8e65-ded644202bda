package com.cosfo.manage.tenant.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.manage.tenant.model.po.TenantFunctionSet;
import com.cosfo.manage.tenant.mapper.TenantFunctionSetMapper;
import com.cosfo.manage.tenant.repository.TenantFunctionSetRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 功能集 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-26
 */
@Service
public class TenantFunctionSetRepositoryImpl extends ServiceImpl<TenantFunctionSetMapper, TenantFunctionSet> implements TenantFunctionSetRepository {

    @Override
    public List<TenantFunctionSet> listBySetIds(List<Long> setIds) {
        LambdaQueryWrapper<TenantFunctionSet> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TenantFunctionSet::getId, setIds);
        return list(queryWrapper);
    }
}
