package com.cosfo.manage.tenant.dao.impl;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.manage.tenant.model.po.TenantAccountSupplierMapping;
import com.cosfo.manage.tenant.mapper.TenantAccountSupplierMappingMapper;
import com.cosfo.manage.tenant.dao.TenantAccountSupplierMappingDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 租户账号供应商映射表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-11
 */
@Service
public class TenantAccountSupplierMappingDaoImpl extends ServiceImpl<TenantAccountSupplierMappingMapper, TenantAccountSupplierMapping> implements TenantAccountSupplierMappingDao {

    @Resource
    private TenantAccountSupplierMappingMapper tenantAccountSupplierMappingMapper;

    @Override
    public List<TenantAccountSupplierMapping> queryByAccountId(Long accountId) {
        LambdaQueryWrapper<TenantAccountSupplierMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantAccountSupplierMapping::getAccountId, accountId);
        return tenantAccountSupplierMappingMapper.selectList(queryWrapper);
    }

    @Override
    public List<TenantAccountSupplierMapping> queryBySupplierIdList(List<Long> supplierIds, Long tenantId) {
        if (CollectionUtils.isEmpty(supplierIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TenantAccountSupplierMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TenantAccountSupplierMapping::getSupplierId, supplierIds);
        queryWrapper.eq(TenantAccountSupplierMapping::getTenantId, tenantId);
        queryWrapper.orderByDesc (TenantAccountSupplierMapping::getCreateTime);
        return tenantAccountSupplierMappingMapper.selectList(queryWrapper);
    }

    @Override
    public List<TenantAccountSupplierMapping> queryByTenantAccountIds(Long tenantId, List<Long> tenantAccountIds) {
        if (CollectionUtils.isEmpty(tenantAccountIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TenantAccountSupplierMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TenantAccountSupplierMapping::getAccountId, tenantAccountIds);
        queryWrapper.eq(TenantAccountSupplierMapping::getTenantId, tenantId);
        return tenantAccountSupplierMappingMapper.selectList(queryWrapper);
    }

    @Override
    public List<Long> listAllTenantId() {
        return tenantAccountSupplierMappingMapper.listAllTenantId();
    }

    @Override
    public List<TenantAccountSupplierMapping> queryByTenantId(Long tenantId) {
        LambdaQueryWrapper<TenantAccountSupplierMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantAccountSupplierMapping::getTenantId, tenantId);
        return tenantAccountSupplierMappingMapper.selectList(queryWrapper);
    }
}
