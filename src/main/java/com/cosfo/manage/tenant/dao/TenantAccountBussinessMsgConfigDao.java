package com.cosfo.manage.tenant.dao;

import com.cosfo.manage.merchant.model.dto.TenantAccountMsgConfigDTO;
import com.cosfo.manage.tenant.model.dto.BussinessMsgPageQueryDTO;
import com.cosfo.manage.tenant.model.dto.TenantAccountBussinessMsgQueryDTO;
import com.cosfo.manage.tenant.model.po.TenantAccountBussinessMsgConfig;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 *
 */
public interface TenantAccountBussinessMsgConfigDao extends IService<TenantAccountBussinessMsgConfig> {

    /**
     * 查询符合条件的租户账户配置信息
     * @param tenantAccountBussinessMsgConfigQueryDTO
     * @return
     */
    List<TenantAccountBussinessMsgConfig> queryList(TenantAccountBussinessMsgQueryDTO tenantAccountBussinessMsgConfigQueryDTO);
}
