//package com.cosfo.manage.tenant.dao;
//
//import com.cosfo.manage.tenant.model.dto.TenantAccountDTO;
//import com.cosfo.manage.tenant.model.po.TenantAccount;
//
//import java.util.List;
//
///**
// * <p>
// * 租户账号 服务类
// * </p>
// *
// * <AUTHOR>
// * @since 2023-02-27
// */
//@Deprecated
//public interface TenantAccountDao {
////    /**
////     * 通过手机号查询品牌方
////     *
////     * @param phone
////     * @param tenantIds
////     * @return
////     */
////    List<TenantAccount> selectByPhone(String phone, List<Long> tenantIds);
////
////    /**
////     * 更新用户信息
////     *
////     * @param tenantAccountDTO
////     * @param tenantIds 品牌方租户Id
////     */
////    void update(TenantAccountDTO tenantAccountDTO, List<Long> tenantIds);
////
//    /**
//     * 根据userId查询
//     *
//     * @param authUserId
//     * @return
//     */
//    TenantAccount selectByAuthUserId(Long authUserId);
////
//////    /**
//////     * 根据条件查询
//////     *
//////     * @param tenantAccountListQueryDTO
//////     * @return
//////     */
//////    List<TenantAccount> listByCondition(TenantAccountListQueryDTO tenantAccountListQueryDTO);
////
//////    /**
//////     * 禁用/启用用户
//////     *
//////     * @param tenantAccountId
//////     * @param status
//////     */
//////    void updateStatus(Long tenantAccountId, Integer status);
////
////    /**
////     * 查询
////     *
////     * @param id
////     * @param tenantId
////     * @return
////     */
////    TenantAccount selectByIdAndTenantId(Long id, Long tenantId);
////
////    /**
////     * 查询
////     *
////     * @param id
////     */
////    TenantAccount selectById(Long id);
////
////    /**
////     * 删除
////     *
////     * @param id
////     */
////    void remove(Long id);
////
//////    /**
//////     * 查询
//////     *
//////     * @param tenantId
//////     * @param phone
//////     * @return
//////     */
//////    TenantAccount selectByPhoneAndTenantId(Long tenantId, String phone);
////
////    /**
////     * 批量查询
////     *
////     * @param authUserIds
////     * @return
////     */
////    List<TenantAccount> selectByAuthUserIds(List<Long> authUserIds);
//}
