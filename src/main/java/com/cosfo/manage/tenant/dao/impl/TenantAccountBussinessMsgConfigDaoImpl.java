package com.cosfo.manage.tenant.dao.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.util.PageInfoConverter;
import com.cosfo.manage.file.model.po.CommonResource;
import com.cosfo.manage.merchant.model.dto.TenantAccountMsgConfigDTO;
import com.cosfo.manage.merchant.model.po.MerchantStoreBalanceChangeRecord;
import com.cosfo.manage.tenant.mapper.TenantAccountBussinessMsgConfigMapper;
import com.cosfo.manage.tenant.model.dto.BussinessMsgPageQueryDTO;
import com.cosfo.manage.tenant.model.dto.TenantAccountBussinessMsgQueryDTO;
import com.cosfo.manage.tenant.model.po.TenantAccountBussinessMsgConfig;
import com.cosfo.manage.tenant.dao.TenantAccountBussinessMsgConfigDao;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
@Service
public class TenantAccountBussinessMsgConfigDaoImpl extends ServiceImpl<TenantAccountBussinessMsgConfigMapper, TenantAccountBussinessMsgConfig>
        implements TenantAccountBussinessMsgConfigDao {

    @Override
    public List<TenantAccountBussinessMsgConfig> queryList(TenantAccountBussinessMsgQueryDTO tenantAccountBussinessMsgConfigQueryDTO) {
        LambdaQueryWrapper<TenantAccountBussinessMsgConfig> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(Objects.nonNull(tenantAccountBussinessMsgConfigQueryDTO.getTenantId()), TenantAccountBussinessMsgConfig::getTenantId, tenantAccountBussinessMsgConfigQueryDTO.getTenantId());
        queryWrapper.in(CollectionUtil.isNotEmpty(tenantAccountBussinessMsgConfigQueryDTO.getTenantAccountIds()), TenantAccountBussinessMsgConfig::getTenantAccountId, tenantAccountBussinessMsgConfigQueryDTO.getTenantAccountIds());
        return list(queryWrapper);
    }

}




