//package com.cosfo.manage.tenant.dao.impl;
//
//import cn.hutool.core.collection.CollectionUtil;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import com.cosfo.manage.common.constant.NumberConstant;
//import com.cosfo.manage.common.context.TenantAccountEnums;
//import com.cosfo.manage.common.context.TenantEnums;
//import com.cosfo.manage.common.util.StringUtils;
//import com.cosfo.manage.facade.usercenter.UserCenterTenantAccountFacade;
//import com.cosfo.manage.tenant.convert.TenantAccountMapperConvert;
//import com.cosfo.manage.tenant.dao.TenantAccountDao;
//import com.cosfo.manage.tenant.mapper.TenantAccountMapper;
//import com.cosfo.manage.tenant.model.dto.TenantAccountDTO;
//import com.cosfo.manage.tenant.model.dto.TenantAccountListQueryDTO;
//import com.cosfo.manage.tenant.model.po.TenantAccount;
//import com.google.common.collect.Lists;
//import net.xianmu.usercenter.client.tenant.req.TenantAccountCommandReq;
//import net.xianmu.usercenter.client.tenant.req.TenantAccountQueryReq;
//import net.xianmu.usercenter.client.tenant.resp.TenantAccountResultResp;
//import org.springframework.stereotype.Service;
//import org.springframework.util.CollectionUtils;
//
//import javax.annotation.Resource;
//import java.util.Collections;
//import java.util.List;
//import java.util.Objects;
//
///**
// * <p>
// * 租户账号 服务实现类
// * </p>
// *
// * <AUTHOR>
// * @since 2023-02-27
// */
//@Service
//public class TenantAccountDaoImpl implements TenantAccountDao {
//
////    @Resource
////    private UserCenterTenantAccountFacade userCenterTenantAccountFacade;
////
////    @Override
////    public List<TenantAccount> selectByPhone(String phone, List<Long> tenantIds) {
////        List<TenantAccountResultResp> respList = userCenterTenantAccountFacade.getTenantAccountByTenantIdsAndPhone(tenantIds, phone);
////        return TenantAccountMapperConvert.INSTANCE.respListToTenantAccountList(respList);
//////        LambdaQueryWrapper<TenantAccount> query = new LambdaQueryWrapper<>();
//////        query.eq(TenantAccount::getPhone, phone);
//////        query.eq(TenantAccount::getStatus, TenantAccountEnums.status.EFFECTIVE.getCode());
//////        query.in(!CollectionUtils.isEmpty(tenantIds), TenantAccount::getTenantId, tenantIds);
//////        return list(query);
////    }
////
////    @Override
////    public void update(TenantAccountDTO tenantAccountDTO, List<Long> tenantIds) {
////        TenantAccountCommandReq tenantAccountCommandReq = new TenantAccountCommandReq();
////        tenantAccountCommandReq.setAuthUserId(tenantAccountDTO.getAuthBaseId());
////        tenantAccountCommandReq.setNickname(tenantAccountDTO.getNickname());
////        tenantAccountCommandReq.setProfilePicture(tenantAccountDTO.getProfilePicture());
////        tenantAccountCommandReq.setStatus(tenantAccountDTO.getStatus());
////        tenantAccountCommandReq.setOperatorPhone(tenantAccountDTO.getOperatorPhone());
////        tenantAccountCommandReq.setOperatorTime(tenantAccountDTO.getOperatorTime());
////        userCenterTenantAccountFacade.updateByTenantIdsAndAuthId(tenantIds, tenantAccountCommandReq);
////
//////        LambdaUpdateWrapper<TenantAccount> updateWrapper = new LambdaUpdateWrapper<>();
//////        updateWrapper.eq(TenantAccount::getAuthUserId, tenantAccountDTO.getAuthBaseId());
//////        updateWrapper.in(TenantAccount::getTenantId, tenantIds);
//////        updateWrapper.set(!StringUtils.isEmpty(tenantAccountDTO.getNickname()), TenantAccount::getNickname, tenantAccountDTO.getNickname());
//////        updateWrapper.set(!StringUtils.isEmpty(tenantAccountDTO.getProfilePicture()), TenantAccount::getProfilePicture, tenantAccountDTO.getProfilePicture());
//////        updateWrapper.set(Objects.nonNull(tenantAccountDTO.getStatus()), TenantAccount::getStatus, tenantAccountDTO.getStatus());
//////        updateWrapper.set(TenantAccount::getOperatorPhone, tenantAccountDTO.getOperatorPhone());
//////        updateWrapper.set(TenantAccount::getOperatorTime, tenantAccountDTO.getOperatorTime());
//////        update(updateWrapper);
////    }
//
//    @Override
//    public TenantAccount selectByAuthUserId(Long authUserId) {
//        TenantAccountResultResp resp = userCenterTenantAccountFacade.getTenantAccountInfo(authUserId);
//        return TenantAccountMapperConvert.INSTANCE.respToTenantAccount(resp);
////        LambdaQueryWrapper<TenantAccount> query = new LambdaQueryWrapper<>();
////        query.eq(TenantAccount::getAuthUserId, authUserId);
////        return getOne(query);
//    }
////
//////    @Override
//////    public List<TenantAccount> listByCondition(TenantAccountListQueryDTO tenantAccountListQueryDTO) {
//////        TenantAccountQueryReq tenantAccountQueryReq = new TenantAccountQueryReq();
//////        tenantAccountQueryReq.setTenantId(tenantAccountListQueryDTO.getTenantId());
//////        tenantAccountQueryReq.setPhone(tenantAccountListQueryDTO.getPhone());
//////        tenantAccountQueryReq.setNickName(tenantAccountListQueryDTO.getNickName());
//////        tenantAccountQueryReq.setStatus(tenantAccountListQueryDTO.getStatus());
//////        // TODO: 2023/5/26 待RPC完善解耦
////////        query.in(Objects.nonNull(tenantAccountListQueryDTO.getRoleId()), TenantAccount::getAuthUserId, tenantAccountListQueryDTO.getAuthUserIds());
////////        query.eq(Objects.nonNull(tenantAccountListQueryDTO.getNickId()), TenantAccount::getId, tenantAccountListQueryDTO.getNickId());
////////        query.eq(TenantAccount::getDeletedFlag, TenantAccountEnums.DeletedFlag.EFFECTIVE.getCode());
//////        List<TenantAccountResultResp> respList = userCenterTenantAccountFacade.getTenantAccounts(tenantAccountQueryReq);
//////        return TenantAccountMapperConvert.INSTANCE.respListToTenantAccountList(respList);
//////
////////        LambdaQueryWrapper<TenantAccount> query = new LambdaQueryWrapper<>();
////////        query.eq(TenantAccount::getTenantId, tenantAccountListQueryDTO.getTenantId());
////////        query.like(!StringUtils.isEmpty(tenantAccountListQueryDTO.getPhone()), TenantAccount::getPhone, tenantAccountListQueryDTO.getPhone());
////////        query.like(!StringUtils.isEmpty(tenantAccountListQueryDTO.getNickName()), TenantAccount::getNickname, tenantAccountListQueryDTO.getNickName());
////////        query.eq(Objects.nonNull(tenantAccountListQueryDTO.getStatus()), TenantAccount::getStatus, tenantAccountListQueryDTO.getStatus());
////////        query.in(Objects.nonNull(tenantAccountListQueryDTO.getRoleId()), TenantAccount::getAuthUserId, tenantAccountListQueryDTO.getAuthUserIds());
////////        query.eq(Objects.nonNull(tenantAccountListQueryDTO.getNickId()), TenantAccount::getId, tenantAccountListQueryDTO.getNickId());
////////        query.eq(TenantAccount::getDeletedFlag, TenantAccountEnums.DeletedFlag.EFFECTIVE.getCode());
////////        return list(query);
//////    }
//////
//////    @Override
//////    public void updateStatus(Long tenantAccountId, Integer status) {
//////        TenantAccountCommandReq tenantAccountCommandReq = new TenantAccountCommandReq();
//////        tenantAccountCommandReq.setStatus(status);
//////        userCenterTenantAccountFacade.updateTenantAccount();
//////        LambdaUpdateWrapper<TenantAccount> updateWrapper = new LambdaUpdateWrapper<>();
//////        updateWrapper.eq(TenantAccount::getId, tenantAccountId);
//////        updateWrapper.set(TenantAccount::getStatus, status);
//////        update(updateWrapper);
//////    }
////
////    @Override
////    public TenantAccount selectByIdAndTenantId(Long id, Long tenantId) {
////        TenantAccountResultResp tenantAccountResultResp = userCenterTenantAccountFacade.getTenantAccountInfoById(id);
////        TenantAccount tenantAccount = TenantAccountMapperConvert.INSTANCE.respToTenantAccount(tenantAccountResultResp);
////        if (Objects.nonNull(tenantAccount) && Objects.equals(tenantAccount.getTenantId(), tenantId)) {
////            return tenantAccount;
////        }
////        return null;
////
//////        LambdaQueryWrapper<TenantAccount> query = new LambdaQueryWrapper<>();
//////        query.eq(TenantAccount::getTenantId, tenantId);
//////        query.eq(TenantAccount::getId, id);
//////        return getOne(query);
////    }
////
////    @Override
////    public TenantAccount selectById(Long id) {
////        TenantAccountResultResp tenantAccountResultResp = userCenterTenantAccountFacade.getTenantAccountInfoById(id);
////        TenantAccount tenantAccount = TenantAccountMapperConvert.INSTANCE.respToTenantAccount(tenantAccountResultResp);
////        return tenantAccount;
////    }
////
////    @Override
////    public void remove(Long id) {
////        // 用户中心会变更该条数据的status、deletedFlag
////        TenantAccountCommandReq commandReq = new TenantAccountCommandReq();
////        commandReq.setId(id);
////        userCenterTenantAccountFacade.delTenantAccount(commandReq);
////
//////        LambdaUpdateWrapper<TenantAccount> updateWrapper = new LambdaUpdateWrapper<>();
//////        updateWrapper.eq(TenantAccount::getId, id);
//////        updateWrapper.set(TenantAccount::getDeletedFlag, TenantAccountEnums.DeletedFlag.FAILURE.getCode());
//////        updateWrapper.set(TenantAccount::getStatus, TenantAccountEnums.status.FAILURE.getCode());
//////        update(updateWrapper);
////    }
////
////    @Override
////    public TenantAccount selectByPhoneAndTenantId(Long tenantId, String phone) {
////        List<TenantAccountResultResp> respList = userCenterTenantAccountFacade.getTenantAccountByTenantIdsAndPhone(Lists.newArrayList(tenantId), phone);
////        if (CollectionUtil.isNotEmpty(respList)) {
////            TenantAccountResultResp tenantAccount = respList.get(NumberConstant.ZERO);
////            return TenantAccountMapperConvert.INSTANCE.respToTenantAccount(tenantAccount);
////        }
////        return null;
////
//////        LambdaQueryWrapper<TenantAccount> query = new LambdaQueryWrapper<>();
//////        query.eq(TenantAccount::getPhone, phone);
//////        query.eq(TenantAccount::getTenantId, tenantId);
//////        return getOne(query);
////    }
////
////    @Override
////    public List<TenantAccount> selectByAuthUserIds(List<Long> authUserIds) {
////        List<TenantAccountResultResp> respList = userCenterTenantAccountFacade.getTenantAccountsByAuthUserIds(authUserIds);
////        return TenantAccountMapperConvert.INSTANCE.respListToTenantAccountList(respList);
//////        LambdaQueryWrapper<TenantAccount> query = new LambdaQueryWrapper<>();
//////        query.in(TenantAccount::getAuthUserId, authUserIds);
//////        return list(query);
////    }
//}