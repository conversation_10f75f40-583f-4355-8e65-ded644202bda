package com.cosfo.manage.tenant.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.cosfo.manage.tenant.dao.TenantCompanyAccountDao;
import com.cosfo.manage.tenant.mapper.TenantCompanyAccountMapper;
import com.cosfo.manage.tenant.model.po.TenantCompanyAccount;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 供应商对品牌收款账户 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-21
 */
@Service
public class TenantCompanyAccountDaoImpl extends ServiceImpl<TenantCompanyAccountMapper, TenantCompanyAccount> implements TenantCompanyAccountDao {

    @Override
    public TenantCompanyAccount getAccount(Long supplierId, Long tenantId) {
        LambdaQueryWrapper<TenantCompanyAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantCompanyAccount::getSupplierTenantId, supplierId);
        queryWrapper.eq(TenantCompanyAccount::getTenantId, tenantId);
        return getOne(queryWrapper);
    }

    /**
     * 获取供应商打款账号
     *
     * @param supplierIds
     * @param tenantId
     * @return
     */
    @Override
    public Map<Long, TenantCompanyAccount> queryAccountMap(Set<Long> supplierIds, Long tenantId) {
        QueryWrapper<TenantCompanyAccount> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("supplier_tenant_id, opening_bank, account_name, account_number");
        queryWrapper.eq("tenant_id", tenantId);
        queryWrapper.groupBy("supplier_tenant_id");
        List<TenantCompanyAccount> list = list(queryWrapper);
        return list.stream().collect(Collectors.toMap(TenantCompanyAccount::getSupplierTenantId, t->t));
    }
}
