package com.cosfo.manage.tenant.dao;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/27
 */
@Deprecated
public interface TenantDao{
//
//    /**
//     * 根据类型查询租户
//     *
//     * @param types
//     * @return
//     */
//    List<Tenant> queryByTenantType(List<Integer> types);
//
//    /**
//     * 根据名称和id查询品牌方
//     *
//     * @param tenantIds
//     * @param name
//     * @return
//     */
//    List<Tenant> listByTenantIdListAndName(List<Long> tenantIds, String name);
}
