package com.cosfo.manage.tenant.dao;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.cosfo.manage.tenant.model.po.TenantAccountSupplierMapping;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 租户账号供应商映射表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-11
 */
public interface TenantAccountSupplierMappingDao extends IService<TenantAccountSupplierMapping> {

    /**
     * 根据账号id查询
     * @param accountId
     * @return
     */
    List<TenantAccountSupplierMapping> queryByAccountId(Long accountId);

    /**
     * 根据供应商id查询
     * @param supplierIds
     * @param tenantId
     * @return
     */
    List<TenantAccountSupplierMapping> queryBySupplierIdList(List<Long> supplierIds, Long tenantId);

    List<TenantAccountSupplierMapping> queryByTenantAccountIds(Long tenantId, List<Long> tenantAccountIds);

    List<Long> listAllTenantId();

    List<TenantAccountSupplierMapping> queryByTenantId(Long tenantId);
}
