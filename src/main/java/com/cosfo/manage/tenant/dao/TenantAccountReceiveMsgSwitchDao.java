package com.cosfo.manage.tenant.dao;

import com.cosfo.manage.tenant.model.dto.TenantAccountReceiveMsgQueryDTO;
import com.cosfo.manage.tenant.model.po.TenantAccountReceiveMsgSwitch;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 *
 */
public interface TenantAccountReceiveMsgSwitchDao extends IService<TenantAccountReceiveMsgSwitch> {

    List<TenantAccountReceiveMsgSwitch> queryList(TenantAccountReceiveMsgQueryDTO tenantAccountReceiveMsgQueryDTO);
}
