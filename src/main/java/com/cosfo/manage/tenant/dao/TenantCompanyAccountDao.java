package com.cosfo.manage.tenant.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.tenant.model.po.TenantCompanyAccount;

import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * <p>
 * 供应商对品牌收款账户 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-21
 */
public interface TenantCompanyAccountDao extends IService<TenantCompanyAccount> {

    /**
     * 获取供应商对品牌方打款账号
     * @param supplierId 供应商
     * @param tenantId 品牌方
     * @return
     */
    TenantCompanyAccount getAccount(Long supplierId, Long tenantId);

    /**
     * 获取供应商打款账号
     * @param supplierIds
     * @param tenantId
     * @return
     */
    Map<Long, TenantCompanyAccount> queryAccountMap(Set<Long> supplierIds, Long tenantId);
}
