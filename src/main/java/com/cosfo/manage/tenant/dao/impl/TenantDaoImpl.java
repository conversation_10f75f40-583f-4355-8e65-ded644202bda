package com.cosfo.manage.tenant.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.common.context.TenantEnums;
import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.tenant.dao.TenantDao;
import com.cosfo.manage.tenant.mapper.TenantMapper;
import com.cosfo.manage.tenant.model.po.Tenant;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/27
 */
//@Service
//public class TenantDaoImpl extends ServiceImpl<TenantMapper, Tenant> implements TenantDao {
//
//    @Override
//    public List<Tenant> queryByTenantType(List<Integer> types) {
//        LambdaQueryWrapper<Tenant> lambdaQueryWrapper = new LambdaQueryWrapper();
//        lambdaQueryWrapper.in(Tenant::getType, types);
//        lambdaQueryWrapper.eq(Tenant::getStatus, TenantEnums.status.EFFECTIVE.getCode());
//        return list(lambdaQueryWrapper);
//    }
//
//    @Override
//    public List<Tenant> listByTenantIdListAndName(List<Long> tenantIds, String name) {
//        LambdaQueryWrapper<Tenant> lambdaQueryWrapper = new LambdaQueryWrapper();
//        lambdaQueryWrapper.eq(Tenant::getType, TenantEnums.type.BRAND_PARTY.getCode());
//        lambdaQueryWrapper.like(!StringUtils.isEmpty(name), Tenant::getTenantName, name);
//        lambdaQueryWrapper.in(Tenant::getId, tenantIds);
//        lambdaQueryWrapper.eq(Tenant::getStatus, TenantEnums.status.EFFECTIVE.getCode());
//        return list(lambdaQueryWrapper);
//    }
//}
