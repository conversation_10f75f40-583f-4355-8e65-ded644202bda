package com.cosfo.manage.tenant.dao.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.tenant.mapper.TenantAccountReceiveMsgSwitchMapper;
import com.cosfo.manage.tenant.model.dto.TenantAccountReceiveMsgQueryDTO;
import com.cosfo.manage.tenant.model.po.TenantAccountReceiveMsgSwitch;
import com.cosfo.manage.tenant.dao.TenantAccountReceiveMsgSwitchDao;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
@Service
public class TenantAccountReceiveMsgSwitchDaoImpl extends ServiceImpl<TenantAccountReceiveMsgSwitchMapper, TenantAccountReceiveMsgSwitch>
    implements TenantAccountReceiveMsgSwitchDao {

    @Override
    public List<TenantAccountReceiveMsgSwitch> queryList(TenantAccountReceiveMsgQueryDTO tenantAccountReceiveMsgQueryDTO) {
        LambdaQueryWrapper<TenantAccountReceiveMsgSwitch> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(Objects.nonNull(tenantAccountReceiveMsgQueryDTO.getTenantId()), TenantAccountReceiveMsgSwitch::getTenantId, tenantAccountReceiveMsgQueryDTO.getTenantId());
        queryWrapper.in(CollectionUtil.isNotEmpty(tenantAccountReceiveMsgQueryDTO.getTenantAccountIds()), TenantAccountReceiveMsgSwitch::getTenantAccountId, tenantAccountReceiveMsgQueryDTO.getTenantAccountIds());
        queryWrapper.eq(Objects.nonNull(tenantAccountReceiveMsgQueryDTO.getChannelCode()), TenantAccountReceiveMsgSwitch::getChannelCode, tenantAccountReceiveMsgQueryDTO.getChannelCode());
        queryWrapper.eq(Objects.nonNull(tenantAccountReceiveMsgQueryDTO.getChannelType()), TenantAccountReceiveMsgSwitch::getChannelType, tenantAccountReceiveMsgQueryDTO.getChannelType());
        return list(queryWrapper);
    }
}




