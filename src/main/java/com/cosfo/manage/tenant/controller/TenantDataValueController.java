package com.cosfo.manage.tenant.controller;

import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.tenant.service.TenantDataValueService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 客户数据价值
 */
@RestController
@RequestMapping("/tenant/data-value")
public class TenantDataValueController extends BaseController {

    @Resource
    private TenantDataValueService tenantDataValueService;

    /**
     * 授权
     */
    @PostMapping(value = "/upsert/authorize")
    public CommonResult<Void> authorize() {
        tenantDataValueService.authorize(getMerchantInfoDTO().getTenantId());
        return CommonResult.ok();
    }

    /**
     * 查询授权
     */
    @PostMapping(value = "/query/authorize")
    public CommonResult<Boolean> queryAuthorize() {
        Long authorizer = tenantDataValueService.queryAuthorizer(getMerchantInfoDTO().getTenantId());
        return CommonResult.ok(StringUtils.isNotEmpty(authorizer));
    }

    /**
     * 查询报告地址,返回url
     * 需要用户先授权
     */
    @PostMapping(value = "/query/report")
    public CommonResult<String> queryReportUrl() {
        String url = tenantDataValueService.queryReportUrl(getMerchantInfoDTO().getTenantId());
        return CommonResult.ok(url);
    }
}
