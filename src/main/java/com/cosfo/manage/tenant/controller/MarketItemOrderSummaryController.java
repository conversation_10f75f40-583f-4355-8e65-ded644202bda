package com.cosfo.manage.tenant.controller;

import com.cosfo.manage.bill.mapper.PaymentMapper;
import com.cosfo.manage.bill.model.po.Payment;
import com.cosfo.manage.common.model.dto.DateTimeQueryDTO;
import com.cosfo.manage.market.service.MarketItemOrderSummaryService;
import com.cosfo.manage.tenant.model.dto.MarketItemSalesRankingQueryDTO;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.joda.time.DateTime;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@RestController
@RequestMapping("/market-item-order-summary")
public class MarketItemOrderSummaryController {

    @Resource
    private MarketItemOrderSummaryService marketItemOrderSummaryService;
    @Resource
    private PaymentMapper paymentMapper;

    /**
     * 初始化商品销量汇总表
     */
    @RequestMapping(value = "/init/market-item-sales", method = RequestMethod.POST)
    public CommonResult<?> initMarketItemSales(@RequestBody DateTimeQueryDTO query) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDateTime startDateTime = LocalDateTime.parse(query.getStartDateTime(), formatter);
        LocalDateTime endDateTime = LocalDateTime.parse(query.getEndDateTime(), formatter);

        marketItemOrderSummaryService.initMarketItemOrderSummary(startDateTime, endDateTime);
        return CommonResult.ok();
    }

    /**
     * 根据支付单id生成商品销量汇总表
     * 兜底接口
     */
    @RequestMapping(value = "/upsert/market-item-sales", method = RequestMethod.POST)
    public CommonResult<?> generateMarketItemSales(Long paymentId) {
        Payment payment = paymentMapper.selectByPrimaryKey(paymentId);
        if (payment == null) {
            return CommonResult.fail(ResultStatusEnum.NOT_FOUND, "支付单不存在");
        }
        marketItemOrderSummaryService.generateMarketItemOrderSummaryByPayment(payment);
        return CommonResult.ok();
    }
}
