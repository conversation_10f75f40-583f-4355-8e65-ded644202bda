package com.cosfo.manage.tenant.controller;

import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.market.model.vo.MarketItemSalesVO;
import com.cosfo.manage.market.service.MarketItemOrderSummaryService;
import com.cosfo.manage.stock.model.vo.StockMonitorQueryVO;
import com.cosfo.manage.stock.model.vo.StockVO;
import com.cosfo.manage.tenant.model.dto.CommonFunctionDTO;
import com.cosfo.manage.tenant.model.dto.MarketItemSalesRankingQueryDTO;
import com.cosfo.manage.tenant.model.dto.TenantWorkSpaceOverviewCardDTO;
import com.cosfo.manage.tenant.model.dto.WarningCenterCardDTO;
import com.cosfo.manage.tenant.service.TenantWorkSpaceService;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: 工作台
 * @author: George
 * @date: 2023-10-24
 **/
@RestController
@RequestMapping("/tenant/work-space")
public class TenantWorkSpaceController {

    @Resource
    private TenantWorkSpaceService tenantWorkSpaceService;
    @Resource
    private MarketItemOrderSummaryService marketItemOrderSummaryService;

    /**
     * 工作台概况
     * @return
     */
    @RequestMapping(value = "/overview", method = RequestMethod.POST)
    public CommonResult<List<TenantWorkSpaceOverviewCardDTO>> queryOverview() {
        Long tenantId = UserLoginContextUtils.getTenantId();
        return CommonResult.ok(tenantWorkSpaceService.queryOverview(tenantId));
    }

    /**
     * 库存监控
     * @return
     */
    @RequestMapping(value = "/stock-monitor", method = RequestMethod.POST)
    public CommonResult<List<StockVO>> queryStockMonitor(@RequestBody StockMonitorQueryVO stockMonitorQueryVO) {
        return CommonResult.ok();
    }

    /**
     * 查询商品销量榜, 查询前10
     */
    @RequestMapping(value = "/query/market-item-sales-ranking", method = RequestMethod.POST)
    public CommonResult<List<MarketItemSalesVO>> queryMarketItemSalesRanking(
            @RequestBody(required = true) MarketItemSalesRankingQueryDTO query) {
        return CommonResult.ok(tenantWorkSpaceService.queryMarketItemSalesRanking(query));
    }

    /**
     * 商品销量导出
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = {"/export/market-item-sales", "/export-async/market-item-sales"}, method = RequestMethod.POST)
    public CommonResult<?> exportMarketItemSales(
            @RequestBody(required = true) MarketItemSalesRankingQueryDTO query) {
        tenantWorkSpaceService.exportMarketItemSalesRanking(query);
        return CommonResult.ok();
    }

    /**
     * 查询常用功能
     */
    @RequestMapping(value = "/query/common-function", method = RequestMethod.POST)
    public CommonResult<CommonFunctionDTO> queryCommonFunction() {
        List<String> commonFunction = tenantWorkSpaceService.queryCommonFunction();
        CommonFunctionDTO commonFunctionDTO = new CommonFunctionDTO();
        commonFunctionDTO.setFunctionList(commonFunction);
        return CommonResult.ok(commonFunctionDTO);
    }

    /**
     * 修改常用功能
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/common-function", method = RequestMethod.POST)
    public CommonResult<?> updateCommonFunction(@RequestBody CommonFunctionDTO commonFunction) {
        tenantWorkSpaceService.updateCommonFunction(commonFunction.getFunctionList());
        return CommonResult.ok();
    }

    /**
     * 查询用户query权限菜单
     *
     * @return query菜单 示例: ["cosfo_manage:store-data:query","cosfo_manage:store-warn:query"]
     */
    @RequestMapping(value = "/query/query-menu", method = RequestMethod.POST)
    public CommonResult<List<String>> queryQueryMenu() {
        return CommonResult.ok(tenantWorkSpaceService.queryUserMenuWithAuthentication());
    }


    /**
     * 查询预警中心卡片
     * @return
     */
    @RequestMapping(value = "/query/warning-center-card", method = RequestMethod.POST)
    public CommonResult<WarningCenterCardDTO> queryWarningCenterCard() {
        return CommonResult.ok(tenantWorkSpaceService.queryWarningCenterCard());
    }
}
