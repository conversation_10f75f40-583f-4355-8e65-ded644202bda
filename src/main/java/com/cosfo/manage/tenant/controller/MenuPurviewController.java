package com.cosfo.manage.tenant.controller;

import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.tenant.model.dto.MenuPurviewDTO;
import com.cosfo.manage.tenant.model.vo.MenuPurviewVO;
import com.cosfo.manage.tenant.service.AuthMenuPurviewService;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 菜单管理
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/23
 */
@RestController
@RequestMapping("/menu/purview")
public class MenuPurviewController {

    @Resource
    private AuthMenuPurviewService authMenuPurviewService;

    /**
     * 菜单树
     *
     * @return
     */
    @PostMapping("/tree")
    public CommonResult<List<MenuPurviewVO>> tree(){
        Long tenantId = UserLoginContextUtils.getTenantId();
        return CommonResult.ok(authMenuPurviewService.listMenuPurviewTree(tenantId));
    }

    /**
     * 关联权限列表
     * @return
     */
    @PostMapping("/query/list")
    public CommonResult<List<MenuPurviewVO>> list(){
        Long tenantId = UserLoginContextUtils.getTenantId();
        return CommonResult.ok(authMenuPurviewService.listMenuPurview(tenantId));
    }

    /**
     * 获取当前用户菜单
     * @return
     */
    @PostMapping("/query/user/list")
    public CommonResult<List<MenuPurviewVO>> listUser(){
        LoginContextInfoDTO merchantInfoDTO = UserLoginContextUtils.getMerchantInfoDTO();
        Long tenantId = UserLoginContextUtils.getTenantId();
        Long authUserId = UserLoginContextUtils.getAuthUserId();
        if (merchantInfoDTO != null && merchantInfoDTO.getIsSuperAccount() != null && Boolean.TRUE.equals(merchantInfoDTO.getIsSuperAccount()) ){
            authUserId = merchantInfoDTO.getMockAuthUserId();
        }
        return CommonResult.ok(authMenuPurviewService.listUserMenuPurview(tenantId, authUserId));
    }

    /**
     * 新增菜单/权限
     *
     * @param menuPurviewDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/upsert/add")
    public CommonResult<Boolean> add(@Valid @RequestBody MenuPurviewDTO menuPurviewDTO){
        return CommonResult.ok(authMenuPurviewService.addMenuPurview(menuPurviewDTO));
    }

    /**
     * 修改菜单/权限
     *
     * @param menuPurviewDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/upsert/update")
    public CommonResult<Boolean> update(@Valid @RequestBody MenuPurviewDTO menuPurviewDTO){
        return CommonResult.ok(authMenuPurviewService.updateMenuPurview(menuPurviewDTO));
    }

    /**
     * 删除菜单/权限
     *
     * @param menuPurviewDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/upsert/delete")
    public CommonResult<Boolean> delete(@RequestBody MenuPurviewDTO menuPurviewDTO){
        return CommonResult.ok(authMenuPurviewService.delMenuPurview(menuPurviewDTO));
    }

    /**
     * 排序
     *
     * @param menuPurviewDTOS
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/upsert/sort")
    public CommonResult<Boolean> sort(@RequestBody List<MenuPurviewDTO> menuPurviewDTOS){
        return CommonResult.ok(authMenuPurviewService.sortMenuPurview(menuPurviewDTOS));
    }
}
