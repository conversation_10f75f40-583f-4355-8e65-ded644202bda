package com.cosfo.manage.tenant.controller;

import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.tenant.model.dto.MarketItemNoSaleExportQueryDTO;
import com.cosfo.manage.tenant.model.dto.StepGuideDTO;
import com.cosfo.manage.tenant.model.dto.TenantMeasureReportDTO;
import com.cosfo.manage.tenant.model.dto.TenantMeasureReportQueryDTO;
import com.cosfo.manage.tenant.service.TenantMeasureExportService;
import com.cosfo.manage.tenant.service.TenantMeasureService;
import net.xianmu.authentication.common.aspect.TenantPrivilegesPermission;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @description: 度量仪
 * @author: George
 * @date: 2023-11-09
 **/
@RestController
@RequestMapping("/tenant/measure")
public class TenantMeasureController {

    @Resource
    private TenantMeasureService tenantMeasureService;
    @Resource
    private TenantMeasureExportService tenantMeasureExportService;

    /**
     * 度量接口
     * @return 度量id
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping
    public CommonResult<Long> measure() {
        Long tenantId = UserLoginContextUtils.getTenantId();
        return CommonResult.ok(tenantMeasureService.measure(tenantId));
    }

    /**
     * 实际度量（预留出的接口 实际无调用）
     * @param id
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/doMeasure")
    public CommonResult doMeasure(@RequestParam Long id) {
        tenantMeasureService.doMeasure(id);
        return CommonResult.ok();
    }


    /**
     * 查询度量结果
     * @param tenantMeasureReportQueryDTO
     * @return
     */
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:measure:query", expireError = true)
    @PostMapping("/query")
    public CommonResult<TenantMeasureReportDTO> report(@RequestBody TenantMeasureReportQueryDTO tenantMeasureReportQueryDTO) {
        tenantMeasureReportQueryDTO.setTenantId(UserLoginContextUtils.getTenantId());
        return CommonResult.ok(tenantMeasureService.queryMeasureResult(tenantMeasureReportQueryDTO));
    }

    // ---------------------------- 逐步引导 ----------------------------

    /**
     * 查询是否有过逐步式引导
     */
    @RequestMapping(value = "/query/step-guide", method = RequestMethod.POST)
    public CommonResult<StepGuideDTO> queryHasStepGuide() {
        StepGuideDTO stepGuideDTO = new StepGuideDTO();
        stepGuideDTO.setHasStepGuide(tenantMeasureService.queryHasStepGuide());
        return CommonResult.ok(stepGuideDTO);
    }

    /**
     * 逐步式引导记录
     */
    @RequestMapping(value = "/upsert/step-guide", method = RequestMethod.POST)
    public CommonResult<?> recordStepGuide() {
        tenantMeasureService.recordStepGuide(true);
        return CommonResult.ok();
    }

    // ---------------------------- 逐步引导 ----------------------------


    // ----------------------------- 导出 -------------------------------

    /**
     * 滞销商品导出
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = {"/export/market-item-no-sale", "/export-async/market-item-no-sale"} , method = RequestMethod.POST)
    public CommonResult<?> exportMarketItemNoSale(@RequestBody MarketItemNoSaleExportQueryDTO query) {
        tenantMeasureExportService.exportMarketItemNoSale(query);
        return CommonResult.ok();
    }

    /**
     * 商品售罄详情导出
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = {"/export/market-item-sold-out", "/export-async/market-item-sold-out"}, method = RequestMethod.POST)
    public CommonResult<?> exportMarketItemSoldOut() {
        tenantMeasureExportService.exportMarketItemSoldOut();
        return CommonResult.ok();
    }

    /**
     * 门店采购活跃详情导出
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = {"/export/store-purchase-activity", "/export-async/store-purchase-activity"}, method = RequestMethod.POST)
    public CommonResult<?> exportStorePurchaseActivity() {
        tenantMeasureExportService.exportStorePurchaseActivity();
        return CommonResult.ok();
    }

    /**
     * 履约率详情导出
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = {"/export/order-fulfillment-rate", "/export-async/order-fulfillment-rate"}, method = RequestMethod.POST)
    public CommonResult<?> exportOrderFulfillmentRate() {
        tenantMeasureExportService.exportOrderFulfillmentRate();
        return CommonResult.ok();
    }

    // ----------------------------- 导出 -------------------------------

    /**
     * 库存周转天数导出
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/export-async/stock-turnover-days", method = RequestMethod.POST)
    public CommonResult exportStockTurnoverDays() {
        tenantMeasureExportService.exportStockTurnoverDays();
        return CommonResult.ok();
    }

    /**
     * 滞销货品导出
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/export-async/no-sale-goods", method = RequestMethod.POST)
    public CommonResult exportNoSaleGoods() {
        tenantMeasureExportService.exportNoSaleGoods();
        return CommonResult.ok();
    }

    /**
     * 临期货品导出
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/export-async/temp-goods", method = RequestMethod.POST)
    public CommonResult exportTempGoods() {
        tenantMeasureExportService.exportTempGoods();
        return CommonResult.ok();
    }

    /**
     * 过期货品导出
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/export-async/expired-goods", method = RequestMethod.POST)
    public CommonResult exportExpiredGoods() {
        tenantMeasureExportService.exportExpiredGoods();
        return CommonResult.ok();
    }

    /**
     * 供应商到仓准时率
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/export-async/supplier-arrival-rate", method = RequestMethod.POST)
    public CommonResult exportSupplierArrivalRate() {
        tenantMeasureExportService.exportSupplierArrivalRate();
        return CommonResult.ok();
    }

    /**
     * 供应商到仓准确率
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/export-async/supplier-arrival-accuracy", method = RequestMethod.POST)
    public CommonResult exportSupplierArrivalAccuracy() {
        tenantMeasureExportService.exportSupplierArrivalAccuracy();
        return CommonResult.ok();
    }
}
