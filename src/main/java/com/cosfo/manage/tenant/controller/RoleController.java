package com.cosfo.manage.tenant.controller;

import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.tenant.model.dto.RoleDTO;
import com.cosfo.manage.tenant.model.dto.RoleQueryDTO;
import com.cosfo.manage.tenant.model.vo.RoleVO;
import com.cosfo.manage.tenant.service.AuthRoleService;
import com.github.pagehelper.PageInfo;
import net.xianmu.authentication.common.aspect.TenantPrivilegesPermission;
import net.xianmu.common.result.CommonResult;
import net.xianmu.log.annation.BizLogRecord;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 角色模块
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/23
 */
@RestController
@RequestMapping("/role")
public class RoleController extends BaseController {

    @Resource
    private AuthRoleService authRoleService;

    /**
     * 角色列表
     *
     * @param roleQueryDTO
     * @return
     */
    @PostMapping("query/list")
    public CommonResult<PageInfo<RoleVO>> list(@RequestBody RoleQueryDTO roleQueryDTO){
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        return CommonResult.ok(authRoleService.queryRolePage(roleQueryDTO, loginContextInfoDTO));
    }

    /**
     * 角色列表
     *
     * @param roleQueryDTO
     * @return
     */
    @PostMapping("query/listWithOutSupplierDistributorRoleId")
    public CommonResult<PageInfo<RoleVO>> listWithOutSupplierDistributorRoleId(@RequestBody RoleQueryDTO roleQueryDTO){
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        return CommonResult.ok(authRoleService.queryRolePageWithOutSupplierDistributorRoleId(roleQueryDTO, loginContextInfoDTO));
    }

    /**
     * 查询 配送员角色id
     *
     * @param
     * @return
     */
    @PostMapping("query/supplierDistributorRoleId")
    public CommonResult<Long> querySupplierDistributorRoleId(){
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        return CommonResult.ok(authRoleService.querySupplierDistributorRoleId( loginContextInfoDTO));
    }



    /**
     * 新增角色
     *
     * @param roleDTO
     * @return
     */
//    @RequiresPermissions(value = {"cosfo_manage:role:add"})
    @PostMapping("upsert/add")
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:user-role:add", expireError = true)
    @BizLogRecord(operationName = "新增设置角色", bizKey = "#roleId", bizKeyTenantId = "#tenantId", content = "T(com.alibaba.fastjson.JSONObject).toJSONString(#content)")
    public CommonResult<List<Long>> add(@RequestBody RoleDTO roleDTO){
        return CommonResult.ok(authRoleService.addAuthRole(roleDTO));
    }

    /**
     * 修改角色
     *
     * @param roleDTO
     * @return
     */
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:user-role:update", expireError = true)
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("upsert/update")
    @BizLogRecord(operationName = "变更设置角色", bizKey = "#roleId", bizKeyTenantId = "#tenantId", content = "T(com.alibaba.fastjson.JSONObject).toJSONString(#content)")
    public CommonResult<List<Long>> update(@RequestBody RoleDTO roleDTO){
        return CommonResult.ok(authRoleService.updateAuthRole(roleDTO));
    }

    /**
     * 删除角色
     *
     * @param roleDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:user-role:delete", expireError = true)
    @PostMapping("/upsert/delete")
    @BizLogRecord(operationName = "删除设置角色", bizKey = "#roleId", bizKeyTenantId = "#tenantId", content = "T(com.alibaba.fastjson.JSONObject).toJSONString(#content)")
    public CommonResult<Boolean> delete(@RequestBody RoleDTO roleDTO){
        return CommonResult.ok(authRoleService.delAuthRole(roleDTO));
    }

    /**
     * 角色详情
     *
     * @return
     */
    @PostMapping("query/detail")
    public CommonResult<RoleVO> detail(@RequestBody RoleDTO roleDTO){
        LoginContextInfoDTO merchantInfoDTO = getMerchantInfoDTO();
        return CommonResult.ok(authRoleService.getAuthRole(Long.valueOf(roleDTO.getId()), merchantInfoDTO));
    }
}
