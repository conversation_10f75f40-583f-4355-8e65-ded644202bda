package com.cosfo.manage.tenant.controller;


import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.tenant.model.input.TenantGuideInfoInput;
import com.cosfo.manage.tenant.model.input.TenantScoreInfoInput;
import com.cosfo.manage.tenant.model.vo.TenantGuideInfoVO;
import com.cosfo.manage.tenant.model.vo.TenantScoreInfoVO;
import com.cosfo.manage.tenant.service.TenantGuideInfoService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 租户登录引导信息问题
 *
 * <AUTHOR>
 * @since 2023-03-19
 */
@RestController
@RequestMapping("/tenant/guide-info")
public class TenantGuideInfoController extends BaseController {

    @Resource
    private TenantGuideInfoService tenantGuideInfoService;

    @RequestMapping(value = "/query/detail", method = RequestMethod.POST)
    public ResultDTO<List<TenantGuideInfoVO>> detail(@RequestBody TenantGuideInfoInput tenantGuideInfoInput) {
        return ResultDTO.success(tenantGuideInfoService.queryGuideInfoDetail(getMerchantInfoDTO().getPhone(), tenantGuideInfoInput.getStepNum()));
    }


    @RequestMapping(value = "/upsert/report", method = RequestMethod.POST)
    public ResultDTO<TenantGuideInfoVO> report(@RequestBody TenantGuideInfoInput tenantGuideInfoInput) {
        tenantGuideInfoService.guideInfoReport(getMerchantInfoDTO(), tenantGuideInfoInput);
        return ResultDTO.success();
    }


    @RequestMapping(value = "/query/status", method = RequestMethod.POST)
    public ResultDTO<TenantGuideInfoVO> status() {
        return ResultDTO.success(tenantGuideInfoService.queryGuideInfoStatus(getMerchantInfoDTO().getPhone()));
    }

    /**
     * 满意度评分查询
     * @return
     */
    @RequestMapping(value = "/score/detail", method = RequestMethod.POST)
    public ResultDTO<TenantScoreInfoVO> scoreDetail() {
        return ResultDTO.success(tenantGuideInfoService.scoreDetail(getMerchantInfoDTO().getPhone()));
    }

    /**
     * 满意度提交评分操作
     * @param tenantScoreInfoInput
     * @return
     */
    @RequestMapping(value = "/upsert/score", method = RequestMethod.POST)
    public ResultDTO<Void> upsertScore(@RequestBody @Valid TenantScoreInfoInput tenantScoreInfoInput) {
        tenantGuideInfoService.upsertScore(getMerchantInfoDTO(), tenantScoreInfoInput);
        return ResultDTO.success();
    }
}
