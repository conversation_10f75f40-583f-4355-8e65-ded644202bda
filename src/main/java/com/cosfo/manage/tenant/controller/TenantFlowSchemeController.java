package com.cosfo.manage.tenant.controller;


import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.tenant.model.input.FlowSchemeInput;
import com.cosfo.manage.tenant.model.input.FlowSchemeStoreBelongInput;
import com.cosfo.manage.tenant.model.input.FlowSchemeUpdateInput;
import com.cosfo.manage.tenant.model.vo.FlowSchemeDetailVO;
import com.cosfo.manage.tenant.model.vo.FlowSchemeStoreBelongVO;
import com.cosfo.manage.tenant.model.vo.FlowSchemeVO;
import com.cosfo.manage.tenant.service.TenantFlowSchemeService;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 流程设置
 *
 * @author: xiaowk
 * @date: 2023/12/20 下午2:39
 */
@RestController
@RequestMapping("/tenant/flow")
public class TenantFlowSchemeController extends BaseController {

    @Resource
    private TenantFlowSchemeService tenantFlowSchemeService;

    /**
     * 查询方案列表
     *
     * @param flowSchemeInput
     * @return
     */
    @RequestMapping(value = "/scheme/query/list", method = RequestMethod.POST)
    public CommonResult<List<FlowSchemeVO>> list(@RequestBody FlowSchemeInput flowSchemeInput) {
        flowSchemeInput.setTenantId(getMerchantInfoDTO().getTenantId());
        return CommonResult.ok(tenantFlowSchemeService.listFlowScheme(flowSchemeInput));
    }


    /**
     * 流程方案详情 - 方案下的流程审核规则
     *
     * @param flowSchemeInput
     * @return
     */
    @RequestMapping(value = "/scheme/query/detail", method = RequestMethod.POST)
    public CommonResult<FlowSchemeDetailVO> detail(@RequestBody FlowSchemeInput flowSchemeInput) {
        FlowSchemeDetailVO flowSchemeDetailVO = tenantFlowSchemeService.schemeDetail(getMerchantInfoDTO().getTenantId(), flowSchemeInput.getSchemeId());
        return CommonResult.ok(flowSchemeDetailVO);
    }


    /**
     * 默认、例外方案保存或更新
     *
     * @param flowSchemeUpdateInput
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/scheme/upsert/update", method = RequestMethod.POST)
    public CommonResult<Void> updateScheme(@RequestBody FlowSchemeUpdateInput flowSchemeUpdateInput) {
        tenantFlowSchemeService.updateScheme(getMerchantInfoDTO(), flowSchemeUpdateInput);
        return CommonResult.ok();
    }

    /**
     * 例外方案删除
     *
     * @param flowSchemeInput
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/extra-scheme/upsert/delete", method = RequestMethod.POST)
    public CommonResult<Void> deleteScheme(@RequestBody FlowSchemeInput flowSchemeInput) {
        tenantFlowSchemeService.deleteScheme(getMerchantInfoDTO().getTenantId(), flowSchemeInput.getSchemeId());
        return CommonResult.ok();
    }

    /**
     * 查询门店归属例外方案
     *
     * @param flowSchemeStoreBelongInput
     * @return
     */
    @RequestMapping(value = "/scheme/query/store-belong", method = RequestMethod.POST)
    public CommonResult<List<FlowSchemeStoreBelongVO>> queryExtraSchemeByStoreIds(@RequestBody FlowSchemeStoreBelongInput flowSchemeStoreBelongInput) {
        List<FlowSchemeStoreBelongVO> list = tenantFlowSchemeService.queryExtraSchemeByStoreIds(getMerchantInfoDTO().getTenantId(), flowSchemeStoreBelongInput.getStoreIds());
        return CommonResult.ok(list);
    }

}