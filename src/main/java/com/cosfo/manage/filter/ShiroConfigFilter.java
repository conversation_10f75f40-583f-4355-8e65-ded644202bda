package com.cosfo.manage.filter;

import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.Filter;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/27
 */
@Configuration
public class ShiroConfigFilter {

    /**
     * ShiroFilter是整个Shiro的入口点，用于拦截需要安全控制的请求进行处理
     */
    @Bean("shiroFilter")
    public ShiroFilterFactoryBean shiroFilter(SecurityManager securityManager) {
        ShiroFilterFactoryBean shiroFilter = new ShiroFilterFactoryBean();
        shiroFilter.setSecurityManager(securityManager);
        //校验失败自己的登陆页面
        shiroFilter.setLoginUrl("/tenant/user/query/login");
        Map<String, String> filterMap = new LinkedHashMap<>();
        /**
         * 公众号 回调
         */
        filterMap.put("/auth/wx/authGet/**", "anon");
        filterMap.put("/auth/wx/**", "anon");


        filterMap.put("/ok", "anon");
        filterMap.put("/order/upset/fix-order", "anon");
        filterMap.put("/weixin/authEvent", "anon");
        filterMap.put("/weixin/tenant/authcallback", "anon");
        filterMap.put("/weixin/*/callback", "anon");
        filterMap.put("/system/*", "anon");
        filterMap.put("/template/*", "anon");
        filterMap.put("/template/*/*", "anon");
        filterMap.put("/tenantAgreement/listAll", "anon");
        filterMap.put("/merchant/store/export-template", "anon");
        filterMap.put("/productSpu/upsert/deal-agent-sku", "anon");
        filterMap.put("/storeBill/deal/history-bill", "anon");
        filterMap.put("/static-resources/**", "anon");
        filterMap.put("/pages/managelogin/**", "anon");
        filterMap.put("/pages/management/**", "authc");
        filterMap.put("/admin/dingTalkSync", "anon");
        filterMap.put("/plugins/**", "anon");
        filterMap.put("/bundle/**", "anon");
        filterMap.put("/static/**", "anon");
        filterMap.put("/themes/**", "anon");
        filterMap.put("/common/**", "anon");
        filterMap.put("/v2/api-docs", "anon");
        filterMap.put("/index.html", "anon");
        filterMap.put("/admin/send/code", "anon");
        filterMap.put("/admin/update/password", "anon");
        filterMap.put("/stock-task/transfer/download", "anon");
        filterMap.put("/messageReminder/completeDelivery/download", "anon");
        // 后门接口放过拦截
        filterMap.put("/inccddaa/**", "anon");
        filterMap.put("/swagger*", "anon");
        filterMap.put("/dingding/event/**","anon");
        //外部对接，下单服务
        filterMap.put("/api/orderService/placeOrder","anon");
        filterMap.put("/api/outerQuery/**","anon");

        filterMap.put("/product/synchronized-supply-sku", "anon");
        filterMap.put("/crm-service/contact/query**", "anon");
        filterMap.put("/crm-service/follow-up-record/export/*/*", "anon");
        filterMap.put("/tenant/user/query/login", "anon");
        filterMap.put("/tenant/user/query/pre-login", "anon");
        filterMap.put("/sdk/user-login/login", "anon");
        filterMap.put("/tenant/user/query/pre-loginV2", "anon");
        filterMap.put("/tenant/user/upsert/no-prompt", "anon");
        filterMap.put("/tenant/user/update/password", "anon");
        filterMap.put("/tenant/user/query/tenant/list", "anon");
        filterMap.put("/tenant/user/sendCode", "anon");
        filterMap.put("/tenant/user/examineCode", "anon");
        filterMap.put("/product/stock/correction/sku", "anon");
        filterMap.put("/bitable/subscribe-official-website", "anon");

        filterMap.put("/product/refresh-import-template", "anon");
        filterMap.put("/order/upset/wdtPushOrder", "anon");
        filterMap.put("/merchant/store/updateStoreInfo", "anon");

        //扫码界面
        filterMap.put("/skuBatchCod/code/**","anon");
        filterMap.put("/**", "authc");

        shiroFilter.setFilterChainDefinitionMap(filterMap);

        Map<String, Filter> filterWonMap = new LinkedHashMap<>();
        filterWonMap.put("authc", new PermissionFilter());
        // filterWonMap.put("authc", new ThreadLocalTokenFilter());
        shiroFilter.setFilters(filterWonMap);
        return shiroFilter;
    }

}
