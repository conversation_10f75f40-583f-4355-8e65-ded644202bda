<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.market.mapper.MarketItemClassificationMapper">

    <resultMap type="com.cosfo.manage.market.model.po.MarketItemClassification" id="MarketItemClassificationMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
        <result property="classificationId" column="classification_id" jdbcType="INTEGER"/>
        <result property="marketId" column="market_id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--查询单个-->
    <select id="selectByPrimaryKey" resultMap="MarketItemClassificationMap">
        select id,
               tenant_id,
               classification_id,
               market_id,
               create_time,
               update_time
        from market_item_classification
        where id = #{id}
    </select>

    <select id="listAll" resultMap="MarketItemClassificationMap">
        select id,
               tenant_id,
               classification_id,
               market_id,
               create_time,
               update_time
        from market_item_classification
        <where>
            <if test="classificationId != null">
                classification_id = #{classificationId}
            </if>
        </where>
    </select>
    <select id="selectByItemId" resultType="com.cosfo.manage.market.model.po.MarketItemClassification">
        select id,
               tenant_id tenantId,
               classification_id classificationId,
               market_id marketId
        from market_item_classification
        where tenant_id = #{tenantId} and market_id = #{marketId}
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="" useGeneratedKeys="true">
        insert into market_item_classification(id, tenant_id, classification_id, market_id)
        values (#{id}, #{tenantId}, #{classificationId}, #{marketId})
    </insert>

    <!--通过主键修改数据-->
    <update id="updateByPrimaryKey">
        update market_item_classification
        <set>
            <if test="id != null">
                id = #{id},
            </if>
            <if test="tenantId != null">
                tenant_id = #{tenantId},
            </if>
            <if test="classificationId != null">
                classification_id = #{classificationId},
            </if>
            <if test="marketId != null">
                market_id = #{marketId},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from market_item_classification
        where id = #{id}
    </delete>

</mapper>

