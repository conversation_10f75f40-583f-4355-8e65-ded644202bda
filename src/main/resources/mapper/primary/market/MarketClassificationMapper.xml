<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.market.mapper.MarketClassificationMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.market.model.po.MarketClassification">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, `name`, icon, parent_id, sort, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from market_classification
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="listAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from market_classification
    <where>
      tenant_id = #{tenantId}
      <if test="name != null">
        and name = #{name}
      </if>
      <if test="parentId != null">
        and parent_id = #{parentId}
      </if>
    </where>
    order by sort
  </select>
  <select id="selectMaxSort" resultType="java.lang.Integer">
    select ifnull(max(sort), 0)
    from market_classification
    where parent_id = #{parentId}
  </select>
    <select id="selectByParentIdAndName" resultMap="BaseResultMap">
      select <include refid="Base_Column_List"/>
      from market_classification
      where tenant_id = #{tenantId} and parent_id = #{parentId} and name = #{name}
    </select>
    <select id="selectByMarketId" resultMap="BaseResultMap">
      select mc.id, mc.tenant_id, mc.`name`, mc.icon, mc.parent_id, mc.sort, mc.create_time, mc.update_time
      from market_classification mc
          inner join market_item_classification mic on mc.tenant_id = mic.tenant_id and mc.id = mic.classification_id
      where mc.tenant_id = #{tenantId} and mic.market_id = #{marketId}
    </select>
  <select id="selectByParentId" resultType="com.cosfo.manage.market.model.po.MarketClassification">
    select <include refid="Base_Column_List"></include>
    from market_classification
    where tenant_id = #{tenantId} and parent_id = #{parentId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from market_classification
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.market.model.po.MarketClassification" useGeneratedKeys="true">
    insert into market_classification (tenant_id, `name`, icon,
      parent_id, sort)
    values (#{tenantId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{icon,jdbcType=VARCHAR},
      #{parentId,jdbcType=BIGINT}, #{sort,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.market.model.po.MarketClassification" useGeneratedKeys="true">
    insert into market_classification
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="icon != null">
        icon,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="icon != null">
        #{icon,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=BIGINT},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.market.model.po.MarketClassification">
    update market_classification
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="icon != null">
        icon = #{icon,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=BIGINT},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.market.model.po.MarketClassification">
    update market_classification
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      `name` = #{name,jdbcType=VARCHAR},
      icon = #{icon,jdbcType=VARCHAR},
      parent_id = #{parentId,jdbcType=BIGINT},
      sort = #{sort,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    market_classification
    where id in
    <foreach close=")" collection="ids" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>
</mapper>
