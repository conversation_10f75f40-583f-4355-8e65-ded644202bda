<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.market.mapper.MarketItemAvailabilityChangeRecordMapper">

    <insert id="saveBatch">
        insert into market_item_availability_change_record
        (tenant_id, item_id, change_type, change_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.tenantId}, #{item.itemId}, #{item.changeType}, #{item.changeTime})
        </foreach>
    </insert>
</mapper>
