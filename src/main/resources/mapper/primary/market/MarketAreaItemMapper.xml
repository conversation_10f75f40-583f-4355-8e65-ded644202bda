<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.market.mapper.MarketAreaItemMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.market.model.po.MarketAreaItem">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="item_id" jdbcType="BIGINT" property="itemId" />
    <result column="on_sale" jdbcType="TINYINT" property="onSale" />
    <result column="price_type" property="priceType"/>
    <result column="warehouse_type" jdbcType="TINYINT" property="warehouseType" />
    <result column="delivery_type" property="deliveryType"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="mini_order_quantity" jdbcType="INTEGER" property="miniOrderQuantity" />
  </resultMap>

  <sql id="Base_Column_List">
    id, tenant_id, item_id, on_sale, price_type, warehouse_type, delivery_type, create_time, update_time,mini_order_quantity
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from market_area_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByTenantAndSkuId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from market_area_item
    where tenant_id = #{tenantId} and sku_id = #{skuId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from market_area_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.market.model.po.MarketAreaItem" useGeneratedKeys="true">
    insert into market_area_item (tenant_id, item_id,
      on_sale, price_type, warehouse_type, delivery_type, sku_id,mini_order_quantity)
    values (#{tenantId,jdbcType=BIGINT}, #{itemId,jdbcType=BIGINT},
      #{onSale,jdbcType=TINYINT}, #{priceType}, #{warehouseType,jdbcType=TINYINT}, #{deliveryType} , #{skuId,jdbcType=BIGINT}, #{miniOrderQuantity,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.market.model.po.MarketAreaItem" useGeneratedKeys="true">
    insert into market_area_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="itemId != null">
        item_id,
      </if>
      <if test="onSale != null">
        on_sale,
      </if>
      <if test="priceType != null">
        price_type,
      </if>
      <if test="warehouseType != null">
        warehouse_type,
      </if>
      <if test="deliveryType != null">
        delivery_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="miniOrderQuantity != null">
        mini_order_quantity,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="itemId != null">
        #{itemId,jdbcType=BIGINT},
      </if>
      <if test="onSale != null">
        #{onSale,jdbcType=TINYINT},
      </if>
      <if test="priceType != null">
        #{priceType},
      </if>
      <if test="warehouseType != null">
        #{warehouseType,jdbcType=TINYINT},
      </if>
      <if test="deliveryType != null">
        #{deliveryType},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="miniOrderQuantity != null">
        #{miniOrderQuantity,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.market.model.po.MarketAreaItem">
    update market_area_item
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="itemId != null">
        item_id = #{itemId,jdbcType=BIGINT},
      </if>
      <if test="onSale != null">
        on_sale = #{onSale,jdbcType=TINYINT},
      </if>
      <if test="priceType != null">
        price_type = #{priceType},
      </if>
      <if test="warehouseType != null">
        warehouse_type = #{warehouseType,jdbcType=TINYINT},
      </if>
      <if test="deliveryType != null">
        delivery_type = #{deliveryType},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="miniOrderQuantity != null">
        mini_order_quantity = #{miniOrderQuantity,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.market.model.po.MarketAreaItem">
    update market_area_item
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      item_id = #{itemId,jdbcType=BIGINT},
      on_sale = #{onSale,jdbcType=TINYINT},
      price_type = #{priceType},
      warehouse_type = #{warehouseType,jdbcType=TINYINT},
      delivery_type = #{deliveryType},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      mini_order_quantity = #{miniOrderQuantity,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryMarketAreaItemInfo" resultType="com.cosfo.manage.market.model.dto.MarketAreaItemDTO">
    select
    a.id id, a.tenant_id tenantId, a.sku_id skuId, a.item_id itemId, a.on_sale onSale,
    a.warehouse_type warehouseType, a.create_time createTime, a.delivery_type deliveryType, a.mini_order_quantity miniOrderQuantity
    from market_area_item a
    where
    a.tenant_id = #{tenantId}
    and a.sku_id in
    <foreach close=")" collection="skuIds" item="skuId" open="(" separator=",">
      #{skuId}
    </foreach>
  </select>

  <select id="queryByTenantAndSkuId" resultType="com.cosfo.manage.market.model.dto.MarketAreaItemDTO">
    select
    a.id id, a.tenant_id tenantId, a.sku_id skuId, a.item_id itemId, a.on_sale onSale,
    a.warehouse_type warehouseType, a.create_time createTime, m.type areaItemType, m.mapping_number mappingNumber,a.mini_order_quantity
    from market_area_item a
    left join market_area_item_mapping m on a.id = m.area_item_id
    where
    a.tenant_id = #{tenantId}
    and a.sku_id = #{skuId}
  </select>
</mapper>
