<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.market.mapper.MarketItemOrderSummaryMapper">

    <!-- Result Map -->
    <resultMap id="MarketItemOrderSummaryResultMap" type="com.cosfo.manage.market.model.po.MarketItemOrderSummary">
        <id property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="itemId" column="item_id"/>
        <result property="skuId" column="sku_id"/>
        <result property="orderAmount" column="order_amount"/>
        <result property="orderQuantity" column="order_quantity"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="orderTime" column="order_time"/>
    </resultMap>
    <resultMap id="MarketItemSalesVOResultMap" type="com.cosfo.manage.market.model.vo.MarketItemSalesVO">
        <result property="itemId" column="item_id"/>
        <result property="name" column="name"/>
        <result property="salesAmount" column="total_amt"/>
        <result property="salesQuantity" column="total_quantity"/>
        <!-- Map other columns as needed -->
    </resultMap>

    <!-- Common SQL -->
    <sql id="BaseColumns">
        id, tenant_id, item_id, sku_id, order_amount, order_quantity,
        create_time, update_time, order_time, sku_code, warehouse_no
    </sql>

    <!-- Insert -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO market_item_order_summary
        (tenant_id, item_id, sku_id, order_amount, order_quantity, order_time)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.tenantId},
            #{item.itemId},
            #{item.skuId},
            #{item.orderAmount},
            #{item.orderQuantity},
            #{item.orderTime}
            )
        </foreach>
    </insert>

    <!-- Select -->
    <select id="queryMarketItemSalesRankingByAmount"
            resultMap="MarketItemSalesVOResultMap">
        SELECT item_id,
               m.title                AS name,
               SUM(mios.order_amount) AS total_amt
        FROM market_item_order_summary mios
                 JOIN cosfodb.market_item mi ON mios.item_id = mi.id
                 JOIN cosfodb.market m ON m.id = mi.market_id
        WHERE mios.tenant_id = #{tenantId}
          AND mios.order_time >= #{startDate}
        GROUP BY item_id
        ORDER BY total_amt DESC
        LIMIT #{limit}
    </select>
    <select id="queryMarketItemSalesRankingByQuantity"
            resultMap="MarketItemSalesVOResultMap">
        SELECT item_id,
               m.title                  AS name,
               SUM(mios.order_quantity) AS total_quantity
        FROM market_item_order_summary mios
                 JOIN market_item mi on mios.item_id = mi.id
                 JOIN market m ON m.id = mi.market_id
        WHERE mios.tenant_id = #{tenantId}
          AND mios.order_time >= #{startDate}
        GROUP BY item_id
        ORDER BY total_quantity DESC
        LIMIT #{limit}
    </select>
    <select id="queryMarketItemSalesRanking"
            resultMap="MarketItemSalesVOResultMap">
        SELECT item_id,
               m.title                  AS name,
               sum(mios.order_amount) as total_amt, sum(mios.order_quantity) as total_quantity
        FROM market_item_order_summary mios
                 JOIN market_item mi ON mios.item_id = mi.id
                 JOIN market m ON m.id = mi.market_id
        WHERE mios.tenant_id = #{tenantId}
            AND mios.order_time >= #{startDate}
        GROUP BY item_id
        <choose>
            <when test="orderType == 'salesQuantity'">
                ORDER BY total_quantity DESC
            </when>
            <otherwise>
                ORDER BY total_amt DESC
            </otherwise>
        </choose>
    </select>

</mapper>
