<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.product.mapper.ProductSkuOrderSummaryMapper">

    <resultMap id="BaseResultMap" type="com.cosfo.manage.product.model.po.ProductSkuOrderSummary">
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="tenant_id" property="tenantId" jdbcType="BIGINT" />
        <result column="sku_id" property="skuId" jdbcType="BIGINT" />
        <result column="sku_code" property="skuCode" jdbcType="VARCHAR" />
        <result column="warehouse_no" property="warehouseNo" jdbcType="INTEGER" />
        <result column="order_quantity" property="orderQuantity" jdbcType="INTEGER" />
        <result column="order_time" property="orderTime" jdbcType="DATE" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List">
        id, tenant_id, sku_id, sku_code, warehouse_no, order_quantity, order_time, create_time, update_time
    </sql>
    <update id="updateAddOrderQuantity">
        update product_sku_order_summary
        set order_quantity = order_quantity + #{orderQuantity}
        where id = #{id}
    </update>

    <select id="querySaleAmountByTenantId"
            resultType="com.cosfo.manage.product.model.dto.ProductSkuOrderSummaryDayDTO">
        select `tenant_id`,`sku_code`,`warehouse_no`, sum(`order_quantity`) as saleQuantity
        from product_sku_order_summary
        where tenant_id = #{tenantId}
        and order_time <![CDATA[>=]]> #{startDate}
        and order_time <![CDATA[<]]> #{endDate}
        group by `tenant_id`,`sku_code`,`warehouse_no`
    </select>

    <select id="querySaleAmountByTenantIdAndSkuId"
            resultType="com.cosfo.manage.product.model.dto.ProductSkuOrderSummaryDayDTO">
        select `tenant_id` tenantId,`sku_id` skuId,`warehouse_no` warehouseNo, sum(`order_quantity`) as saleQuantity
        from product_sku_order_summary
        where tenant_id = #{tenantId}
        and order_time <![CDATA[>=]]> #{startDate}
        and order_time <![CDATA[<]]> #{endDate}
        <if test="skuIds != null and skuIds.size() > 0">
            and sku_id in
            <foreach collection="skuIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        group by `tenant_id`,`sku_id`,`warehouse_no`
    </select>

</mapper>
