<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.product.mapper.ProductAgentWarehouseMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.product.model.po.ProductAgentWarehouse">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="warehouse_id" jdbcType="BIGINT" property="warehouseId" />
    <result column="warehouse_name" jdbcType="BIGINT" property="warehouseName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, warehouse_id, warehouse_name, `status`, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from product_agent_warehouse
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from product_agent_warehouse
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.product.model.po.ProductAgentWarehouse" useGeneratedKeys="true">
    insert into product_agent_warehouse (tenant_id, warehouse_id, warehouse_name, 
      `status`, create_time, update_time
      )
    values (#{tenantId,jdbcType=BIGINT}, #{warehouseId,jdbcType=BIGINT}, #{warehouseName,jdbcType=BIGINT}, 
      #{status,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.product.model.po.ProductAgentWarehouse" useGeneratedKeys="true">
    insert into product_agent_warehouse
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="warehouseId != null">
        warehouse_id,
      </if>
      <if test="warehouseName != null">
        warehouse_name,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="warehouseId != null">
        #{warehouseId,jdbcType=BIGINT},
      </if>
      <if test="warehouseName != null">
        #{warehouseName,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.product.model.po.ProductAgentWarehouse">
    update product_agent_warehouse
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="warehouseId != null">
        warehouse_id = #{warehouseId,jdbcType=BIGINT},
      </if>
      <if test="warehouseName != null">
        warehouse_name = #{warehouseName,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.product.model.po.ProductAgentWarehouse">
    update product_agent_warehouse
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      warehouse_id = #{warehouseId,jdbcType=BIGINT},
      warehouse_name = #{warehouseName,jdbcType=BIGINT},
      `status` = #{status,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByTenantId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    product_agent_warehouse
    where tenant_id = #{tenantId} and status = 0
  </select>

  <select id="selectByWarehouseNameAndTenantId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    product_agent_warehouse
    where  tenant_id = #{tenantId} and warehouse_id = #{warehouseNo}
  </select>
</mapper>