<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.product.mapper.ProductCategoryMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.product.model.po.ProductCategory">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="parent_id" jdbcType="INTEGER" property="parentId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `name`, parent_id, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from category
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from category
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from category
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.product.model.po.ProductCategory" useGeneratedKeys="true">
    insert into category (`name`, parent_id, create_time,
      update_time)
    values (#{name,jdbcType=VARCHAR}, #{parentId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.product.model.po.ProductCategory" useGeneratedKeys="true">
    insert into category
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        `name`,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.product.model.po.ProductCategory">
    update category
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.product.model.po.ProductCategory">
    update category
    set `name` = #{name,jdbcType=VARCHAR},
      parent_id = #{parentId,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByChildCategory" resultType="com.cosfo.manage.product.model.vo.CategoryVO">
    select
    id, `name`, parent_id parentId
    from
    category
    where
    parent_id in
    <foreach collection="categoryIds" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>
  <select id="selectByParentIdAndName" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from category
    where parent_id = #{parentId} and name = #{name}
  </select>
</mapper>
