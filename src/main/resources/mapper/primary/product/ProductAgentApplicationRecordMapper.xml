<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.good.mapper.ProductAgentApplicationRecordMapper">

    <resultMap type="com.cosfo.manage.good.model.po.ProductAgentApplicationRecord" id="ProductAgentApplicationRecordMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
        <result property="agentTenantId" column="agent_tenant_id" jdbcType="INTEGER"/>
        <result property="skuId" column="sku_id" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="auditRemark" column="audit_remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_time,update_time,tenant_id,agent_tenant_id,sku_id,status,audit_remark
    </sql>

    <!-- 查询SKU最新的申请记录-->
    <select id = "selectLastRecord" resultType = "com.cosfo.manage.good.model.dto.ProductAgentRecordBySkuDTO">
        SELECT
            t2.maxUpdateTime as lastUpdateTime,t3.minCreateTime as firstApplyTime,
            t1.id,t1.create_time,t1.update_time,t1.tenant_id,t1.agent_tenant_id,t1.sku_id,t1.status,t1.audit_remark
        FROM
            `product_agent_application_record` t1
                left join (
                SELECT
                    MAX(id) as maxId, `sku_id`  as maxSkuId,max(update_time) as maxUpdateTime
                FROM
                    `product_agent_application_record`
                group by `sku_id`
            ) t2 on t2.maxId = t1.id
                left join (
                SELECT
                    MIN(id) as minId, `sku_id`  as minSkuId,MIN( `create_time`) as minCreateTime
                FROM
                    `product_agent_application_record`
                group by `sku_id`
            ) t3 on t3.minSkuId = t2.maxSkuId and t3.minskuId = t1.`sku_id`
        where
            t2.maxId is not null and t3.minId is not null
            and t1.`status` = #{status}
            <if test="tenantIds != null and tenantIds.size() > 0 ">
                and t1.`tenant_id` in
                <foreach collection="tenantIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="skuIds != null and skuIds.size() > 0 ">
                and t1.`sku_id` in
                <foreach collection="skuIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <choose>
                <when test="orderBy != null and orderBy != ''">
                    order by ${orderBy}
                </when>
                <!-- 审核中默认最早在前，其它状态默认最新在前-->
                <when test="status == 0">
                    order by t1.`update_time`
                </when>
                <otherwise>
                    order by t1.`update_time` desc
                </otherwise>
            </choose>
    </select>


    <select id="getOneByid" resultType="com.cosfo.manage.good.model.po.ProductAgentApplicationRecord">
        select
        <include refid="Base_Column_List"/>
        from `product_agent_application_record`
        where id = #{id}
    </select>


</mapper>

