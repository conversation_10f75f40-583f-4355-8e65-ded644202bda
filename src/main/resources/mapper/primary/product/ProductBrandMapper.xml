<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.product.mapper.ProductBrandMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.product.model.po.ProductBrand">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `name`, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from brand
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="listAll" resultType="com.cosfo.manage.product.model.po.ProductBrand">
    select id, name
    from brand
    <where>
      <if test="name != null and name != ''">
          name like concat(#{name},'%')
      </if>
    </where>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from brand
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.product.model.po.ProductBrand" useGeneratedKeys="true">
    insert into brand (`name`, create_time, update_time
      )
    values (#{name,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.product.model.po.ProductBrand" useGeneratedKeys="true">
    insert into brand
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        `name`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.product.model.po.ProductBrand">
    update brand
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.product.model.po.ProductBrand">
    update brand
    set `name` = #{name,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="listByCategoryId" resultType="com.cosfo.manage.product.model.vo.ProductBrandVO">
    select
      b.id, b.name
    from brand_category_mapping m
           left join brand b on m.brand_id = b.id
    where m.category_id = #{categoryId}
  </select>
  <select id="fuzzyByName" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from brand
    where name like concat('%', #{brandName} ,'%')
  </select>
    <select id="selectByName" resultMap="BaseResultMap">
      select <include refid="Base_Column_List"/>
      from brand
      where name = #{name}
    </select>
</mapper>
