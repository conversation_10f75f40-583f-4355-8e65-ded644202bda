<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.product.mapper.ProductPricingSupplyMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.product.model.po.ProductPricingSupply">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="supply_sku_id" jdbcType="BIGINT" property="supplySkuId" />
    <result column="supply_tenant_id" jdbcType="BIGINT" property="supplyTenantId" />
    <result column="associated" jdbcType="TINYINT" property="associated" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <resultMap id="DTOMap" type="com.cosfo.manage.product.model.dto.ProductPricingSupplyDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="supply_sku_id" jdbcType="BIGINT" property="supplySkuId" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="supply_tenant_id" jdbcType="BIGINT" property="supplyTenantId" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="supply_type" jdbcType="INTEGER" property="supplyType" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="main_picture" jdbcType="VARCHAR" property="mainPicture" />
    <result column="brand_id" jdbcType="VARCHAR" property="brandId" />
    <result column="brand_name" jdbcType="VARCHAR" property="brandName" />
    <result column="category_id" property="categoryId"/>
    <result column="deleted" property="deleted"/>
    <result column="specification" property="specification"/>
    <result column="specification_unit" property="specificationUnit"/>
    <result column="supplySku" property="supplySku"/>
    <result column="associated" property="isAssociateProduct"/>
    <result column="start_time" property="startTime"/>
    <result column="end_time" property="endTime"/>
  </resultMap>

  <sql id="Base_Column_List">
    id, tenant_id, sku_id, supply_sku_id, supply_tenant_id, create_time, update_time, associated
  </sql>
  <select id="listQuotation" resultMap="DTOMap">
    select distinct pps.id, pps.supply_sku_id, sk.sku supplySku, sp.main_picture,sp.title,sp.brand_id,sp.category_id, pps.tenant_id, pps.supply_tenant_id, sk.specification, sk.specification_unit
    from product_pricing_supply pps
    left join product_sku sk on pps.supply_sku_id = sk.id
    left join product_spu sp on sk.spu_id = sp.id
    left join product_pricing_supply_city_mapping m on pps.id = m.product_pricing_supply_id
    <where>
      m.supply_type = 1
      <if test="tenantId != null">
        and pps.tenant_id = #{tenantId}
      </if>
      <if test="categoryId != null">
        and sp.category_id in
        <foreach close=")" collection="categoryIds" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="title != null and title != ''">
        and sp.title like concat('%',#{title},'%')
      </if>
      <if test="skuId != null">
        and pps.supply_sku_id = #{skuId}
      </if>
      <if test="sku != null and sku != ''">
        and sk.sku like concat('%',#{sku},'%')
      </if>
    </where>
    order by pps.update_time desc
  </select>
  <select id="selectByTenantAndSkuId" resultMap="DTOMap">
    select pps.id, pps.supply_sku_id, pps.sku_id,pps.tenant_id, sp.main_picture, sp.title, sp.brand_id, sk.specification, sk.specification_unit,
           sp.category_id, sk.sku supplySku
    from product_pricing_supply pps
    left join product_sku sk on pps.supply_sku_id = sk.id
    left join product_spu sp on sk.spu_id = sp.id
    where pps.tenant_id = #{tenantId} and pps.sku_id = #{skuId}
  </select>

  <select id="batchQueryByTenantAndSkuIds" resultMap="DTOMap">
    select pps.id, pps.sku_id, pps.tenant_id, pps.supply_sku_id, sp.main_picture, sp.title, sp.brand_id
    from product_pricing_supply pps
           left join product_sku sk on pps.supply_sku_id = sk.id
           left join product_spu sp on sk.spu_id = sp.id
    where pps.tenant_id = #{tenantId} and pps.sku_id in
    <foreach close=")" collection="skuIds" item="skuId" open="(" separator=",">
      #{skuId}
    </foreach>
  </select>

  <select id="queryEffectSkuInfo" resultType="com.cosfo.manage.product.model.dto.ProductPricingSupplyEffectDTO">
    select t.id,
           t.tenant_id as tenantId,
           t.supply_sku_id as supplySkuId,
           min(ppscm.start_time) as startTime,
           max(ppscm.end_time) as endTime,
           count(ppscm.id)as supplyCityCount,
           t.associated as havingRelated
    from product_pricing_supply t
    left join product_pricing_supply_city_mapping ppscm
    on t.id = ppscm.product_pricing_supply_id
    <where>
      t.tenant_id = #{tenantId} and ppscm.supply_type = 1 and ppscm.start_time <![CDATA[<]]> now() and ppscm.end_time <![CDATA[>]]> now()
      <if test="cityIds != null and cityIds.size() > 0">
        and ppscm.city_id in
        <foreach collection="cityIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test="associated !=null">
        and t.associated =#{associated}
      </if>
      <if test="skuId !=null">
        and t.supply_sku_id =#{skuId}
      </if>
    </where>
    GROUP BY t.supply_sku_id
    order by t.id desc


  </select>
    <select id="queryAll" resultMap="DTOMap">
      select  t.id, t.tenant_id, t.sku_id, t.supply_sku_id, t.supply_tenant_id, t.associated, ppscm.start_time, ppscm.end_time
      from product_pricing_supply t
      left join product_sku sk on t.supply_sku_id = sk.id
      left join product_spu sp on sp.id = sk.spu_id
      left join product_pricing_supply_city_mapping ppscm on t.id = ppscm.product_pricing_supply_id
      <where>
        t.tenant_id = #{tenantId} and ppscm.supply_type = 1
        <if test="title != null">
          and sp.title like concat('%',#{title},'%')
        </if>
        <if test="categoryIds != null and categoryIds.size() > 0">
          and sp.category_id in
          <foreach collection="categoryIds" open="(" close=")" separator="," item="item">
            #{item}
          </foreach>
        </if>
        <if test="cityIds != null and cityIds.size() > 0">
          and ppscm.city_id in
          <foreach collection="cityIds" open="(" close=")" separator="," item="item">
            #{item}
          </foreach>
        </if>
        <if test="effectTime != null">
          and ppscm.start_time <![CDATA[<]]> #{effectTime} and ppscm.end_time <![CDATA[>]]> #{effectTime}
        </if>
        <if test="brandIds != null and brandIds.size() > 0">
          and sp.brand_id in
          <foreach collection="brandIds" open="(" close=")" separator="," item="item">
            #{item}
          </foreach>
        </if>
        <if test="brandName != null ">
          and sp.brand_name like concat('%',#{brandName},'%')
        </if>
        <if test="associated !=null">
          and t.associated =#{associated}
        </if>
        <if test="skuId !=null">
          and sk.id =#{skuId}
        </if>
      </where>
      group by t.id
      order by t.id desc
    </select>

  <select id="queryEffectTime" resultMap="DTOMap">
    select t.id, ppscm.start_time, ppscm.end_time
    from product_pricing_supply t
    left join product_pricing_supply_city_mapping ppscm on t.id = ppscm.product_pricing_supply_id
    <where>
      t.tenant_id = #{tenantId} and ppscm.supply_type = 1
      <if test="supplyIds != null and supplyIds.size() > 0">
        and t.id in
        <foreach collection="supplyIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
    </where>
  </select>

  <select id="selectBySupplierSkuId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from product_pricing_supply
    where supply_sku_id = #{supplierSkuId} and tenant_id = #{tenantId}
  </select>




  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from product_pricing_supply
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from product_pricing_supply
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.product.model.po.ProductPricingSupply" useGeneratedKeys="true">
    insert into product_pricing_supply (tenant_id, sku_id, supply_sku_id, supply_tenant_id)
    values (#{tenantId,jdbcType=BIGINT}, #{skuId,jdbcType=BIGINT}, #{supplySkuId,jdbcType=BIGINT}, #{supplyTenantId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.product.model.po.ProductPricingSupply" useGeneratedKeys="true">
    insert into product_pricing_supply
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="skuId != null">
        sku_id,
      </if>
      <if test="supplySkuId != null">
        supply_sku_id,
      </if>
      <if test="supplyTenantId != null">
        supply_tenant_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=BIGINT},
      </if>
      <if test="supplySkuId != null">
        #{supplySkuId,jdbcType=BIGINT},
      </if>
      <if test="supplyTenantId != null">
        #{supplyTenantId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.product.model.po.ProductPricingSupply">
    update product_pricing_supply
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        sku_id = #{skuId,jdbcType=BIGINT},
      </if>
      <if test="supplySkuId != null">
        supply_sku_id = #{supplySkuId,jdbcType=BIGINT},
      </if>
      <if test="supplyTenantId != null">
        supply_tenant_id = #{supplyTenantId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.product.model.po.ProductPricingSupply">
    update product_pricing_supply
    set tenant_id = #{tenantId,jdbcType=BIGINT},
        sku_id = #{skuId,jdbcType=BIGINT},
        supply_sku_id = #{supplySkuId,jdbcType=BIGINT},
        supply_tenant_id = #{supplyTenantId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="querySupplyCity" resultType="com.cosfo.manage.product.model.vo.ProductPricingSupplyCityMappingDTO">
    select
      m.id, m.product_pricing_supply_id productPricingSupplyId, m.city_id cityId, m.`type`, m.supply_type supplyType, m.price, m.start_time startTime, m.end_time endTime,
      m.create_time createTime, m.update_time updateTime, m.deleted, p.tenant_id tenantId, p.sku_id skuId, p.supply_tenant_id supplyTenantId, p.supply_sku_id supplySkuId,
      c.name cityName
    from product_pricing_supply p
    left join product_pricing_supply_city_mapping m on p.id = m.product_pricing_supply_id
    left join common_location_city c on m.city_id = c.id
    where p.supply_sku_id = #{supplySkuId}
    and p.tenant_id = #{tenantId} and m.start_time &lt;= now() and m.end_time &gt;= now()
  </select>

  <select id="querySupplyCityBySkuId" resultType="com.cosfo.manage.product.model.vo.ProductPricingSupplyCityMappingDTO">
    select
      m.id, m.product_pricing_supply_id productPricingSupplyId, m.city_id cityId, m.`type`, m.supply_type supplyType, m.price, m.start_time startTime, m.end_time endTime,
      m.create_time createTime, m.update_time updateTime, m.deleted, p.tenant_id tenantId, p.supply_tenant_id supplyTenantId, p.supply_sku_id supplySkuId,
      c.name cityName
    from product_pricing_supply p
           left join product_pricing_supply_city_mapping m on p.id = m.product_pricing_supply_id
           left join common_location_city c on m.city_id = c.id
    where p.tenant_id = #{tenantId} and m.start_time &lt;= now() and m.end_time &gt;= now()
    <if test="skuIds != null and skuIds.size() > 0">
      and p.supply_sku_id in
      <foreach collection="skuIds" open="(" close=")" separator="," item="item">
        #{item}
      </foreach>
    </if>
  </select>

  <update id="updateAssociated">
    update product_pricing_supply
    set associated = #{associated}
    where tenant_id = #{tenantId} and supply_sku_id = #{supplySkuId}
  </update>
  <select id="exportList" resultType="com.cosfo.manage.product.model.vo.ProductPricingSupplyExportVO" fetchSize="1000" resultSetType="FORWARD_ONLY">
    select
    p.tenant_id tenantId, p.id, city.id citySupplyPriceId, p.supply_sku_id supplySkuId,p.supply_tenant_id supplyTenantId, city.price, city.type,if(city.start_time &lt;= now() and
    city.end_time &gt;= now(),0,1) expireStatus,if(p.associated = 0 ,'未采用', '采用中') as usingStatusStr, city.start_time startTime, city.end_time endTime,city.city_id cityId
    from product_pricing_supply p
    left join product_pricing_supply_city_mapping city on city.product_pricing_supply_id = p.id
    <where>
      city.deleted = 0 and p.supply_sku_id is not null
      <if test="supplyQueryDTO.skuId != null">
        and p.supply_sku_id = #{supplyQueryDTO.skuId}
      </if>
      <if test="supplyQueryDTO.supplySkuIds != null and supplyQueryDTO.supplySkuIds.size() > 0">
        and p.supply_sku_id in
        <foreach collection="supplyQueryDTO.supplySkuIds" open="(" close=")" separator=","
                 item="item">
          #{item}
        </foreach>
      </if>
      <if test="supplyQueryDTO.cityIds != null and supplyQueryDTO.cityIds.size() > 0">
        and city.city_id in
        <foreach collection="supplyQueryDTO.cityIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test="supplyQueryDTO.tenantId != null">
        and p.tenant_id = #{supplyQueryDTO.tenantId}
      </if>
<!--      <if test="supplyQueryDTO.expireStatus != null">-->
<!--        <choose>-->
<!--          <when test="supplyQueryDTO.expireStatus == 0">-->
            and city.start_time &lt;= now() and city.end_time &gt;= now()
<!--          </when>-->
<!--          <when test="supplyQueryDTO.expireStatus == 1">-->
<!--            and city.end_time &lt;= now()-->
<!--          </when>-->
<!--          <when test="supplyQueryDTO.expireStatus == 2">-->
<!--            and city.start_time &gt;= now()-->
<!--          </when>-->
<!--        </choose>-->
<!--      </if>-->
<!--      <if test="supplyQueryDTO.usingStatus != null">-->
<!--        <choose>-->
<!--          <when test="supplyQueryDTO.usingStatus == 0">-->
<!--            and p.sku_id is not null-->
<!--          </when>-->
<!--          <when test="supplyQueryDTO.usingStatus == 1">-->
<!--            and p.sku_id is null-->
<!--          </when>-->
<!--        </choose>-->
<!--      </if>-->
      <if test="supplyQueryDTO.associated !=null">
        and p.associated =#{supplyQueryDTO.associated}
      </if>
    </where>
    order by city.id desc
  </select>
  <select id="querySupplyCityCountBySkuIds"
          resultType="com.cosfo.manage.product.model.dto.SkuSupplyCityCountDTO">
    select
    p.supply_sku_id supplySkuId,count(m.id) as cityCount
    from product_pricing_supply p
    left join product_pricing_supply_city_mapping m on p.id = m.product_pricing_supply_id
    where p.supply_sku_id is not null and m.deleted = 0  and p.tenant_id =  #{tenantId}  and m.start_time &lt;= now() and m.end_time &gt;= now()
    and p.supply_sku_id in
    <foreach collection="supplySkuIds" open="(" close=")" separator=","
             item="item">
      #{item}
    </foreach>
    GROUP BY p.supply_sku_id
  </select>

    <select id="queryBySupplierSkuId" resultType="com.cosfo.manage.product.model.po.ProductPricingSupply">
        select <include refid="Base_Column_List"/>
        from product_pricing_supply
        where supply_sku_id = #{supplierSkuId} and supply_tenant_id = #{supplyTenantId}
    </select>

    <select id="querySupplyCityBySkuIdAndSupplyTenantId" resultType="com.cosfo.manage.product.model.vo.ProductPricingSupplyCityMappingDTO">
        select
            m.id, m.product_pricing_supply_id productPricingSupplyId, m.city_id cityId, m.`type`, m.supply_type supplyType, m.price, m.start_time startTime, m.end_time endTime,
            m.create_time createTime, m.update_time updateTime, m.deleted, p.tenant_id tenantId, p.sku_id skuId, p.supply_tenant_id supplyTenantId, p.supply_sku_id supplySkuId,
            c.name cityName
        from product_pricing_supply p
                 left join product_pricing_supply_city_mapping m on p.id = m.product_pricing_supply_id
                 left join common_location_city c on m.city_id = c.id
        where p.supply_sku_id = #{supplySkuId}
          and p.supply_tenant_id = #{supplyTenantId}
        and m.start_time <![CDATA[<]]> now() and m.end_time <![CDATA[>]]> now()
        and m.supply_type = 1
            <if test="cityNames != null and cityNames.size() > 0">
                and c.name  in
                <foreach collection="cityNames" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
    </select>


    <select id="queryEffectSupplyCityBySkuId" resultType="com.cosfo.manage.product.model.vo.ProductPricingSupplyCityMappingDTO">
        select
        m.id, m.product_pricing_supply_id productPricingSupplyId, m.city_id cityId, m.`type`, m.supply_type supplyType, m.price, m.start_time startTime, m.end_time endTime,
        m.create_time createTime, m.update_time updateTime, m.deleted, p.tenant_id tenantId, p.supply_tenant_id supplyTenantId, p.supply_sku_id supplySkuId,
        c.name cityName
        from product_pricing_supply p
        left join product_pricing_supply_city_mapping m on p.id = m.product_pricing_supply_id
        left join common_location_city c on m.city_id = c.id
        where p.tenant_id = #{tenantId}
        and m.supply_type = 1
        and m.start_time <![CDATA[<]]> now() and m.end_time <![CDATA[>]]> now()
        <if test="skuIds != null and skuIds.size() > 0">
            and p.supply_sku_id in
            <foreach collection="skuIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>

  <select id="queryXianmuSkuBySupplySkuIds" resultMap="BaseResultMap">
    select
    t.id,
    t.supply_sku_id
    from product_pricing_supply t
    left join product_pricing_supply_city_mapping ppscm
    on t.id = ppscm.product_pricing_supply_id
    <where>
      t.tenant_id = #{tenantId} and ppscm.supply_type = 1
      <if test="supplySkuIds != null and supplySkuIds.size() > 0">
        and t.supply_sku_id in
        <foreach collection="supplySkuIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
    </where>
  </select>

</mapper>
