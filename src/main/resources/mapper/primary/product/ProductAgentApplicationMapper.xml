<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.product.mapper.ProductAgentApplicationMapper">

    <select id="listAll" resultType="com.cosfo.manage.product.model.dto.ProductAgentApplicationDTO">
        select t.id, t.tenant_id tenantId, t.title, t.category_id categoryId, t.storage_location storageLocation, t.storage_temperature storageTemperature, t.guarantee_period guaranteePeriod,
        t.guarantee_unit guaranteeUnit, t.supplier_spu_id supplierSpuId
        from product_agent_application t
        inner join product_agent_application_item item on t.tenant_id = item.tenant_id and t.id = item.application_id
        <where>
            <if test="title != null">
                and t.title like concat('%',#{title},'%')
            </if>
            <if test="categoryIds != null and categoryIds.size() > 0">
                and t.category_id in
                <foreach collection="categoryIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="status != null">
                and item.status = #{status}
            </if>
        </where>
        group by t.id
        order by t.id desc
    </select>
</mapper>
