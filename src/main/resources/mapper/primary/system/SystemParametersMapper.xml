<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.system.mapper.SystemParametersMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.system.model.po.SystemParameters">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="param_key" jdbcType="VARCHAR" property="paramKey" />
    <result column="param_value" jdbcType="VARCHAR" property="paramValue" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, param_key, param_value, description, create_time, update_time, account_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from system_parameters
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.system.model.po.SystemParameters" useGeneratedKeys="true">
    insert into system_parameters
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="paramKey != null">
        param_key,
      </if>
      <if test="paramValue != null">
        param_value,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="paramKey != null">
        #{paramKey,jdbcType=VARCHAR},
      </if>
      <if test="paramValue != null">
        #{paramValue,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.system.model.po.SystemParameters">
    update system_parameters
    <set>
      <if test="paramKey != null">
        param_key = #{paramKey,jdbcType=VARCHAR},
      </if>
      <if test="paramValue != null">
        param_value = #{paramValue,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.system.model.po.SystemParameters">
    update system_parameters
    set param_key = #{paramKey,jdbcType=VARCHAR},
      param_value = #{paramValue,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByTokenJson" parameterType="com.cosfo.manage.system.model.po.SystemParameters">
    update system_parameters
    <set>
      <if test="tokenJson != null">
        param_value = #{tokenJson,jdbcType=VARCHAR},
      </if>
    </set>
    where param_key = #{paramKey,jdbcType=VARCHAR}
  </update>
  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from system_parameters
  </select>

  <select id="selectByTpTicket" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from system_parameters
    where param_key = #{ticketKey,jdbcType=VARCHAR}
  </select>
    <select id="selectByKey" resultMap="BaseResultMap">
      select <include refid="Base_Column_List"/>
      from system_parameters
      where param_key = #{key}
    </select>
  <select id="selectByParameterKey" resultType="com.cosfo.manage.system.model.po.SystemParameters">
    select
    <include refid="Base_Column_List" />
    from system_parameters
    where param_key = #{ticketKey,jdbcType=VARCHAR}
  </select>
  <select id="selectByAccountIdAndKey" resultType="com.cosfo.manage.system.model.po.SystemParameters">
    select
    <include refid="Base_Column_List" />
    from system_parameters
    where account_id = #{accountId,jdbcType=BIGINT}
    and param_key = #{paramKey,jdbcType=VARCHAR}
  </select>
</mapper>
