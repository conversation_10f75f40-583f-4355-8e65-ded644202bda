<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.bill.mapper.TransLogResultListMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.bill.model.dto.TransLogResultListDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="trans_id" jdbcType="VARCHAR" property="transId" />
    <result column="trans_date" jdbcType="VARCHAR" property="transDate" />
    <result column="trans_amt" jdbcType="VARCHAR" property="transMoney" />
    <result column="fee_amt" jdbcType="VARCHAR" property="transFee" />
    <result column="trans_stat" jdbcType="VARCHAR" property="acctStat" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, create_time, update_time, trans_id, trans_date, huifu_id, settle_cycle, card_no, card_name, bank_code,
    trans_amt, fee_amt, fee_cust_id, trans_stat, settle_type, settle_abstract, settle_batch_no, reason
  </sql>
    <select id="listAll" resultMap = "BaseResultMap">
    select <include refid="Base_Column_List"></include>
    from trans_log_result_list
    <where>
      <if test="tenantId != null">
        and tenant_id = #{tenantId}
      </if>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="acctStat != null and acctStat != '' ">
        and trans_stat = #{acctStat}
      </if>
      <if test="startTimeStr != null and endTimeStr != null">
        and trans_date between #{startTimeStr} and #{endTimeStr}
      </if>
    </where>
    order by trans_date desc
  </select>

  <select id="exportListAll" resultMap = "BaseResultMap"  fetchSize="1000" >
    select <include refid="Base_Column_List"></include>
    from trans_log_result_list
    <where>
      <if test="tenantId != null">
        and tenant_id = #{tenantId}
      </if>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="acctStat != null and acctStat != '' ">
        and trans_stat = #{acctStat}
      </if>
      <if test="startTimeStr != null and endTimeStr != null">
        and trans_date between #{startTimeStr} and #{endTimeStr}
      </if>
    </where>
    order by trans_date desc
  </select>

  <select id="listAllCountMoney" resultType="java.math.BigDecimal">
    select sum(trans_amt)
    from trans_log_result_list
    <where>
      <if test="tenantId != null">
        and tenant_id = #{tenantId}
      </if>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="acctStat != null">
        and trans_stat = #{acctStat}
      </if>
      <if test="startTimeStr != null and endTimeStr != null">
        and trans_date between #{startTimeStr} and #{endTimeStr}
      </if>
    </where>
    order by id desc
  </select>
  <select id="listAllCountFeeAmt" resultType="java.math.BigDecimal">
    select sum(fee_amt)
    from trans_log_result_list
    <where>
      <if test="tenantId != null">
        and tenant_id = #{tenantId}
      </if>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="acctStat != null">
        and trans_stat = #{acctStat}
      </if>
      <if test="startTimeStr != null and endTimeStr != null">
        and trans_date between #{startTimeStr} and #{endTimeStr}
      </if>
    </where>
    order by id desc
  </select>
</mapper>