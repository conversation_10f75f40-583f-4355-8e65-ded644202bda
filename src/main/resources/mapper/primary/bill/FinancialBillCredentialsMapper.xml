<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.bill.mapper.FinancialBillCredentialsMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.bill.model.po.FinancialBillCredentials">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="bill_id" jdbcType="BIGINT" property="billId" />
    <result column="operator_id" jdbcType="BIGINT" property="operatorId" />
    <result column="credentials" jdbcType="VARCHAR" property="credentials" />
    <result column="credentials_time" jdbcType="TIMESTAMP" property="credentialsTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="operator_type" jdbcType="TINYINT" property="operatorType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, bill_id, operator_id, credentials, credentials_time, remark, operator_type, 
    create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from financial_bill_credentials
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from financial_bill_credentials
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.bill.model.po.FinancialBillCredentials" useGeneratedKeys="true">
    insert into financial_bill_credentials (tenant_id, bill_id, operator_id, 
      credentials, credentials_time, remark, 
      operator_type, create_time, update_time
      )
    values (#{tenantId,jdbcType=BIGINT}, #{billId,jdbcType=BIGINT}, #{operatorId,jdbcType=BIGINT}, 
      #{credentials,jdbcType=VARCHAR}, #{credentialsTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, 
      #{operatorType,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.bill.model.po.FinancialBillCredentials" useGeneratedKeys="true">
    insert into financial_bill_credentials
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="billId != null">
        bill_id,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="credentials != null">
        credentials,
      </if>
      <if test="credentialsTime != null">
        credentials_time,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="operatorType != null">
        operator_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="billId != null">
        #{billId,jdbcType=BIGINT},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=BIGINT},
      </if>
      <if test="credentials != null">
        #{credentials,jdbcType=VARCHAR},
      </if>
      <if test="credentialsTime != null">
        #{credentialsTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="operatorType != null">
        #{operatorType,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.bill.model.po.FinancialBillCredentials">
    update financial_bill_credentials
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="billId != null">
        bill_id = #{billId,jdbcType=BIGINT},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=BIGINT},
      </if>
      <if test="credentials != null">
        credentials = #{credentials,jdbcType=VARCHAR},
      </if>
      <if test="credentialsTime != null">
        credentials_time = #{credentialsTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="operatorType != null">
        operator_type = #{operatorType,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.bill.model.po.FinancialBillCredentials">
    update financial_bill_credentials
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      bill_id = #{billId,jdbcType=BIGINT},
      operator_id = #{operatorId,jdbcType=BIGINT},
      credentials = #{credentials,jdbcType=VARCHAR},
      credentials_time = #{credentialsTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      operator_type = #{operatorType,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByBillId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    financial_bill_credentials
    where
    tenant_id = #{tenantId}
    and bill_id = #{billId}
    and operator_type = #{operatorType}
  </select>
</mapper>