<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.bill.mapper.FinancialBillItemMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.bill.model.po.FinancialBillItem">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="bill_id" jdbcType="BIGINT" property="billId" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="business_type" jdbcType="TINYINT" property="businessType" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, bill_id, business_id, business_type, price, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from financial_bill_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from financial_bill_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.bill.model.po.FinancialBillItem" useGeneratedKeys="true">
    insert into financial_bill_item (tenant_id, bill_id, business_id, 
      business_type, price, create_time, 
      update_time)
    values (#{tenantId,jdbcType=BIGINT}, #{billId,jdbcType=BIGINT}, #{businessId,jdbcType=BIGINT}, 
      #{businessType,jdbcType=TINYINT}, #{price,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.bill.model.po.FinancialBillItem" useGeneratedKeys="true">
    insert into financial_bill_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="billId != null">
        bill_id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="billId != null">
        #{billId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=TINYINT},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.bill.model.po.FinancialBillItem">
    update financial_bill_item
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="billId != null">
        bill_id = #{billId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=TINYINT},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.bill.model.po.FinancialBillItem">
    update financial_bill_item
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      bill_id = #{billId,jdbcType=BIGINT},
      business_id = #{businessId,jdbcType=BIGINT},
      business_type = #{businessType,jdbcType=TINYINT},
      price = #{price,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByBillId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    financial_bill_item
    where tenant_id = #{tenantId}
    and bill_id = #{billId}
    and business_type = #{businessType}
  </select>

  <insert id="batchInsert" parameterType="list">
    insert into financial_bill_item (tenant_id, bill_id, business_id,
                                     business_type, price)
    values
    <foreach collection="financialBillItems" item="item" index="index" separator="," >
      (#{item.tenantId,jdbcType=BIGINT}, #{item.billId,jdbcType=BIGINT}, #{item.businessId,jdbcType=BIGINT},
      #{item.businessType,jdbcType=TINYINT}, #{item.price,jdbcType=DECIMAL})
    </foreach>
  </insert>

  <select id="queryCreatedBillItemInfo" resultType="long">
    select
    item.business_id
    from
    financial_bill_item item
    left join financial_bill bill on bill.id = item.bill_id
    where item.tenant_id = #{tenantId} and bill.type = 0
    and item.business_type = #{businessType}
    and item.business_id in
    <foreach collection="businessIds" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>
</mapper>