<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.bill.mapper.FinancialBillMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.bill.model.po.FinancialBill">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="bill_no" jdbcType="VARCHAR" property="billNo" />
    <result column="payee_id" jdbcType="BIGINT" property="payeeId" />
    <result column="payer_id" jdbcType="BIGINT" property="payerId" />
    <result column="bill_type" jdbcType="TINYINT" property="billType" />
    <result column="order_receivable_price" jdbcType="DECIMAL" property="orderReceivablePrice"/>
    <result column="order_after_sale_total_price" jdbcType="DECIMAL" property="orderAfterSaleTotalPrice"/>
    <result column="receivable_price" jdbcType="DECIMAL" property="receivablePrice" />
    <result column="received_price" jdbcType="DECIMAL" property="receivedPrice" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="file_path" jdbcType="VARCHAR" property="filePath" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, bill_no, payee_id, payer_id, bill_type, order_receivable_price, order_after_sale_total_price, receivable_price, received_price,
    start_time, end_time, `type`, `status`, audit_time, file_path, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from financial_bill
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from financial_bill
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.bill.model.po.FinancialBill" useGeneratedKeys="true">
    insert into financial_bill (tenant_id, bill_no, payee_id, 
      payer_id, bill_type,  order_receivable_price, order_after_sale_total_price, receivable_price,
      received_price, start_time, end_time, 
      `type`, `status`, audit_time, 
      file_path, create_time, update_time
      )
    values (#{tenantId,jdbcType=BIGINT}, #{billNo,jdbcType=VARCHAR}, #{payeeId,jdbcType=BIGINT}, 
      #{payerId,jdbcType=BIGINT}, #{billType,jdbcType=TINYINT}, #{orderReceivablePrice}, #{orderAfterSaleTotalPrice}, #{receivablePrice,jdbcType=DECIMAL},
      #{receivedPrice,jdbcType=DECIMAL}, #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, 
      #{type,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, #{auditTime,jdbcType=TIMESTAMP}, 
      #{filePath,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.bill.model.po.FinancialBill" useGeneratedKeys="true">
    insert into financial_bill
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="billNo != null">
        bill_no,
      </if>
      <if test="payeeId != null">
        payee_id,
      </if>
      <if test="payerId != null">
        payer_id,
      </if>
      <if test="billType != null">
        bill_type,
      </if>
      <if test="orderReceivablePrice != null">
        order_receivable_price,
      </if>
      <if test="orderAfterSaleTotalPrice != null">
        order_after_sale_total_price,
      </if>
      <if test="receivablePrice != null">
        receivable_price,
      </if>
      <if test="receivedPrice != null">
        received_price,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="auditTime != null">
        audit_time,
      </if>
      <if test="filePath != null">
        file_path,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="billNo != null">
        #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="payeeId != null">
        #{payeeId,jdbcType=BIGINT},
      </if>
      <if test="payerId != null">
        #{payerId,jdbcType=BIGINT},
      </if>
      <if test="billType != null">
        #{billType,jdbcType=TINYINT},
      </if>
      <if test="orderReceivablePrice != null">
        #{orderReceivablePrice},
      </if>
      <if test="orderAfterSaleTotalPrice != null">
        #{orderAfterSaleTotalPrice},
      </if>
      <if test="receivablePrice != null">
        #{receivablePrice,jdbcType=DECIMAL},
      </if>
      <if test="receivedPrice != null">
        #{receivedPrice,jdbcType=DECIMAL},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="auditTime != null">
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="filePath != null">
        #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.bill.model.po.FinancialBill">
    update financial_bill
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="billNo != null">
        bill_no = #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="payeeId != null">
        payee_id = #{payeeId,jdbcType=BIGINT},
      </if>
      <if test="payerId != null">
        payer_id = #{payerId,jdbcType=BIGINT},
      </if>
      <if test="billType != null">
        bill_type = #{billType,jdbcType=TINYINT},
      </if>
      <if test="orderReceivablePrice != null">
        order_receivable_price = #{orderReceivablePrice},
      </if>
      <if test="orderAfterSaleTotalPrice != null">
        order_after_sale_total_price = #{orderAfterSaleTotalPrice},
      </if>
      <if test="receivablePrice != null">
        receivable_price = #{receivablePrice,jdbcType=DECIMAL},
      </if>
      <if test="receivedPrice != null">
        received_price = #{receivedPrice,jdbcType=DECIMAL},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="filePath != null">
        file_path = #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.bill.model.po.FinancialBill">
    update financial_bill
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      bill_no = #{billNo,jdbcType=VARCHAR},
      payee_id = #{payeeId,jdbcType=BIGINT},
      payer_id = #{payerId,jdbcType=BIGINT},
      bill_type = #{billType,jdbcType=TINYINT},
      order_receivable_price = #{orderReceivablePrice},
      order_after_sale_total_price = #{orderAfterSaleTotalPrice},
      receivable_price = #{receivablePrice,jdbcType=DECIMAL},
      received_price = #{receivedPrice,jdbcType=DECIMAL},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      `type` = #{type,jdbcType=TINYINT},
      `status` = #{status,jdbcType=TINYINT},
      audit_time = #{auditTime,jdbcType=TIMESTAMP},
      file_path = #{filePath,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="list" parameterType="com.cosfo.manage.bill.model.dto.StoreBillQueryDTO" resultType = "com.cosfo.manage.bill.model.vo.FinancialStoreBillVO">
    select
        distinct bill.id billId, bill.create_time createTime, c.credentials_time storeCredentialsTime, bill.bill_no billNo,
        bill.start_time billStartTime, bill.end_time billEndTime, bill.payer_id storeId,
        bill.order_receivable_price receivableOrderPrice, bill.order_after_sale_total_price receivableOrderAfterSalePrice, bill.receivable_price receivablePrice,
        bill.file_path filePath, bill.audit_time auditTime
    from
      financial_bill bill
      left join financial_bill_credentials c on c.tenant_id = bill.tenant_id and bill.id = c.bill_id and c.operator_type = 1
      left join financial_bill_item item on item.tenant_id = bill.tenant_id and item.bill_id = bill.id
    <where>
      and bill.tenant_id = #{tenantId} and bill.type = 0
      <if test="createBillStartTime != null and createBillEndTime != null">
        and (bill.create_time between DATE_FORMAT(#{createBillStartTime},'%Y-%m-%d 00:00:00') and DATE_FORMAT(#{createBillEndTime},'%Y-%m-%d 23:59:59'))
      </if>
      <if test="storeUploadCredentialsStartTime != null and storeUploadCredentialsEndTime != null">
        and (c.credentials_time between DATE_FORMAT(#{storeUploadCredentialsStartTime},'%Y-%m-%d 00:00:00') and DATE_FORMAT(#{storeUploadCredentialsEndTime},'%Y-%m-%d 23:59:59'))
      </if>
      <if test="auditStartTime != null and auditEndTime != null">
        and (bill.audit_time between DATE_FORMAT(#{auditStartTime},'%Y-%m-%d 00:00:00')  and DATE_FORMAT(#{auditEndTime},'%Y-%m-%d 23:59:59'))
      </if>
      <if test="havingCredentials != null">
        <if test="havingCredentials == 0">
          and c.id is null
        </if>
        <if test="havingCredentials == 1">
          and c.id is not null
        </if>
      </if>
      <if test="billNo != null and billNo != ''">
        and bill.bill_no like concat('%',#{billNo},'%')
      </if>
      <if test="orderNo != null and orderIds != null">
        and item.business_type = 0 and item.business_id in
        <foreach collection="orderIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test="storeIds != null">
        and payer_id in
        <foreach collection="storeIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test="status != null">
        and status = #{status}
      </if>
    </where>
    <if test="storeUploadCredentialsTimeSort != null">
      order by c.create_time ${storeUploadCredentialsTimeSort}
    </if>
    <if test="auditTimeSort != null">
      order by bill.audit_time ${auditTimeSort}
    </if>
    <if test="storeUploadCredentialsTimeSort == null and auditTimeSort == null">
      order by bill.id ${createTimeSort}
    </if>
  </select>

  <select id="selectByBillNo" resultType="com.cosfo.manage.bill.model.vo.FinancialStoreBillVO">
    select
      bill.id billId, bill.create_time createTime, bill.bill_no billNo,
      bill.start_time billStartTime, bill.end_time billEndTime, bill.payer_id storeId,
      bill.order_receivable_price receivableOrderPrice, bill.order_after_sale_total_price receivableOrderAfterSalePrice, bill.receivable_price receivablePrice, bill.status,
      bill.audit_time auditTime, bill.file_path filePath
    from
      financial_bill bill
    where bill.tenant_id = #{tenantId} and bill.id = #{billId}
    and bill.type = #{billType}
  </select>

  <update id="updateStatus">
    update financial_bill
    set status = #{status},
        audit_time = now()
    where id = #{billId}
  </update>

  <select id="queryErrorHistoryBill" resultMap="BaseResultMap">
    select *
    from financial_bill
    where file_path in
    (select file_path from financial_bill group by file_path having count(1) > 1)
  </select>

  <update id="updateFilePath">
    update financial_bill
    set file_path = #{filePath}
    where id = #{billId}
  </update>
</mapper>