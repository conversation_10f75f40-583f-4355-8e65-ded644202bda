<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.tenant.mapper.TenantCommonConfigMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.tenant.model.po.TenantCommonConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="config_key" jdbcType="VARCHAR" property="configKey" />
    <result column="config_value" jdbcType="VARCHAR" property="configValue" />
    <result column="config_desc" jdbcType="VARCHAR" property="configDesc" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, config_key, config_value, config_desc, create_time, update_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tenant_common_config
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByTenantIdAndConfigKey" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tenant_common_config
    where tenant_id = #{tenantId}
    and config_key=#{configKey}
    limit 1
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tenant_common_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.tenant.model.po.TenantCommonConfig" useGeneratedKeys="true">
    insert into tenant_common_config (tenant_id, config_key, config_value, 
      config_desc, create_time, update_time
      )
    values (#{tenantId,jdbcType=BIGINT}, #{configKey,jdbcType=VARCHAR}, #{configValue,jdbcType=VARCHAR}, 
      #{configDesc,jdbcType=VARCHAR}, now(), now()
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.tenant.model.po.TenantCommonConfig" useGeneratedKeys="true">
    insert into tenant_common_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="configKey != null">
        config_key,
      </if>
      <if test="configValue != null">
        config_value,
      </if>
      <if test="configDesc != null">
        config_desc,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="configKey != null">
        #{configKey,jdbcType=VARCHAR},
      </if>
      <if test="configValue != null">
        #{configValue,jdbcType=VARCHAR},
      </if>
      <if test="configDesc != null">
        #{configDesc,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.tenant.model.po.TenantCommonConfig">
    update tenant_common_config
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="configKey != null">
        config_key = #{configKey,jdbcType=VARCHAR},
      </if>
      <if test="configValue != null">
        config_value = #{configValue,jdbcType=VARCHAR},
      </if>
      <if test="configDesc != null">
        config_desc = #{configDesc,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.tenant.model.po.TenantCommonConfig">
    update tenant_common_config
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      config_key = #{configKey,jdbcType=VARCHAR},
      config_value = #{configValue,jdbcType=VARCHAR},
      config_desc = #{configDesc,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateConfigValueById" parameterType="com.cosfo.manage.tenant.model.po.TenantCommonConfig">
    update tenant_common_config
    set config_value = #{configValue,jdbcType=VARCHAR},
      update_time = now()
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>