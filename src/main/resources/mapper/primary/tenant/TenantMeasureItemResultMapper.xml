<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.tenant.mapper.TenantMeasureItemResultMapper">
    <!-- 批量插入操作 -->
    <insert id="saveBatch" parameterType="java.util.List">
        INSERT INTO tenant_measure_item_result (tenant_id, report_id, item_id, item_result, item_type, item_title, item_route,
        status, first_module_name, second_module_name)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.tenantId}, #{item.reportId}, #{item.itemId}, #{item.itemResult}, #{item.itemType},#{item.itemTitle},
            #{item.itemRoute}, #{item.status}, #{item.firstModuleName}, #{item.secondModuleName})
        </foreach>
    </insert>

    <select id="listByCondition" resultType="com.cosfo.manage.tenant.model.po.TenantMeasureItemResult">
        SELECT id, tenant_id tenantId, report_id reportId, item_id itemId, item_result itemResult, item_type itemType,
        item_route itemRoute, status, item_title itemTitle, first_module_name firstModuleName, second_module_name
        secondModuleName, item_result_state itemResultState
        FROM tenant_measure_item_result
        <where>
            tenant_id = #{tenantId} AND report_id = #{reportId}
            <if test="itemType != null or itemResultState != null">
                AND (
                <if test="itemType != null">
                    item_type = #{itemType}
                </if>
                <if test="itemType != null and itemResultState != null">
                    OR
                </if>
                <if test="itemResultState != null">
                    (item_result_state = #{itemResultState} AND item_type = 2)
                </if>
                )
            </if>
        </where>
    </select>
</mapper>
