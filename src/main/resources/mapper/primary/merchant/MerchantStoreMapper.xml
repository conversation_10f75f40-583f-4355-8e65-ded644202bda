<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.merchant.mapper.MerchantStoreMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.merchant.model.po.MerchantStore">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="register_time" jdbcType="TIMESTAMP" property="registerTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="audit_remark" jdbcType="VARCHAR" property="auditRemark" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="bill_switch" property="billSwitch"/>
    <result column="online_payment" property="onlinePayment"/>
    <result column="store_no" property="storeNo"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, store_name, `type`, register_time, `status`, audit_remark,
    audit_time, create_time, update_time, remark, bill_switch, online_payment, store_no,
    balance_authority
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from merchant_store
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="listAll" resultType="com.cosfo.manage.merchant.model.dto.MerchantStoreDTO">
      select ms.id,
             ms.tenant_id tenantId,
             ms.store_name storeName,
             msa.phone,
             ms.type,
             ms.register_time registerTime,
             ms.status,
             ms.bill_switch billSwitch,
             ms.store_no storeNo,
             ms.audit_time auditTime,
             ms.remark,
             ms.online_payment onlinePayment
      from merchant_store ms
             left join merchant_store_account msa on msa.tenant_id = #{tenantId} and ms.id = msa.store_id and msa.type = 0
      <where>
        <if test="tenantId != null">
            and ms.tenant_id = #{tenantId}
        </if>
        <if test="id != null">
          and ms.id = #{id}
        </if>
        <if test="storeNo != null and storeNo != ''">
          and ms.store_no = #{storeNo}
        </if>
        <if test="storeName != null">
          and ms.store_name like concat('%',#{storeName},'%')
        </if>
        <if test="phone != null and phone != ''">
          and msa.phone like concat(#{phone},'%')
        </if>
        <if test="startTime != null and endTime != null">
          and ms.register_time <![CDATA[>=]]> #{startTime}
          and ms.register_time <![CDATA[<=]]> #{endTime}
        </if>
        <if test="status != null">
          and ms.status = #{status}
        </if>
        <if test="type != null">
          and ms.type = #{type}
        </if>
        <if test="billSwitch != null">
          and ms.bill_switch = #{billSwitch}
        </if>
        <if test="storeIds != null and storeIds.size() >0 ">
          and ms.id in
          <foreach collection="storeIds" open="(" close=")" separator="," item="item">
            #{item}
          </foreach>
        </if>
        <if test="noMatchingStoreIds != null and noMatchingStoreIds.size() >0 ">
          and ms.id not in
          <foreach collection="noMatchingStoreIds" open="(" close=")" separator="," item="item">
            #{item}
          </foreach>
        </if>
        <if test="supplyStoreIds != null and supplyStoreIds.size() >0 ">
          and ms.id
          <if test="supplyStatus == 0">
            in
          </if>
          <if test="supplyStatus == 1">
            not in
          </if>
          <foreach collection="supplyStoreIds" open="(" close=")" separator="," item="item">
            #{item}
          </foreach>
        </if>
      </where>
      order by ms.id desc
    </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from merchant_store
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.merchant.model.po.MerchantStore" useGeneratedKeys="true">
    insert into merchant_store (tenant_id, store_name,
      `type`, register_time, `status`,audit_remark, audit_time,
    <if test="billSwitch != null">
      bill_switch,
    </if>
    <if test="balanceAuthority != null">
      balance_authority,
    </if>
     online_payment, store_no, remark)
    values (#{tenantId,jdbcType=BIGINT}, #{storeName,jdbcType=VARCHAR},
      #{type,jdbcType=INTEGER}, #{registerTime,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER},
      #{auditRemark,jdbcType=VARCHAR}, #{auditTime,jdbcType=TIMESTAMP},
    <if test="billSwitch != null">
      #{billSwitch},
    </if>
    <if test="balanceAuthority != null">
      #{balanceAuthority},
    </if>
    #{onlinePayment}, #{storeNo}, #{remark})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.merchant.model.po.MerchantStore" useGeneratedKeys="true">
    insert into merchant_store
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="registerTime != null">
        register_time,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="auditRemark != null">
        audit_remark,
      </if>
      <if test="auditTime != null">
        audit_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="billSwitch != null">
        bill_switch,
      </if>
      <if test="onlinePayment != null">
        online_payment,
      </if>
      <if test="storeNo != null and storeNo != ''">
        store_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="registerTime != null">
        #{registerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="auditRemark != null">
        #{auditRemark,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billSwitch != null">
        #{billSwitch},
      </if>
      <if test="onlinePayment != null">
        #{onlinePayment},
      </if>
      <if test="storeNo != null and storeNo != ''">
        #{storeNo},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.merchant.model.po.MerchantStore">
    update merchant_store
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="registerTime != null">
        register_time = #{registerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="auditRemark != null">
        audit_remark = #{auditRemark,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR}
      </if>
      <if test="billSwitch != null">
        bill_switch = #{billSwitch},
      </if>
      <if test="onlinePayment != null">
        online_payment = #{onlinePayment},
      </if>
      <if test="storeNo != null and storeNo != ''">
        store_no = #{storeNo},
      </if>
      <if test="balanceAuthority != null">
        balance_authority = #{balanceAuthority},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.merchant.model.po.MerchantStore">
    update merchant_store
    set
      store_name = #{storeName,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=INTEGER},
      `status` = #{status,jdbcType=INTEGER},
      audit_remark = #{auditRemark,jdbcType=VARCHAR},
      audit_time = #{auditTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      <if test="billSwitch != null">
        bill_switch = #{billSwitch},
      </if>
      online_payment = #{onlinePayment},
      <if test="balanceAuthority != null">
        balance_authority = #{balanceAuthority},
      </if>
      store_no = #{storeNo}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryMerchantStore" resultMap="BaseResultMap">
    select
    s.id, s.tenant_id, s.store_name, s.`type`, s.register_time, s.`status`, s.audit_remark,
    s.audit_time, s.create_time, s.update_time, s.remark, s.bill_switch, s.online_payment
    from
    merchant_store s
    left join merchant_store_account a on a.tenant_id = s.tenant_id and a.store_id = s.id
    <where>
      <if test="storeType != null">
        and s.type = #{storeType}
      </if>
      <if test="storeName != null and storeName != ''">
        and s.store_name like #{storeName}
      </if>
      <if test="tenantId != null">
        and s.tenant_id = #{tenantId}
      </if>
      <if test="phone != null">
        and a.phone like concat('%',#{phone},'%')
      </if>
    </where>
  </select>

  <select id="queryWaitAuditStoreNum" resultType="integer">
    select
      count(id)
    from
      merchant_store
    where tenant_id = #{tenantId}
    and status = 0
  </select>

  <select id="batchQuery" resultType="com.cosfo.manage.merchant.model.dto.MerchantStoreDTO">
    select
    s.id, s.tenant_id tenantId,s.store_name storeName, s.type, a.phone accountPhone, s.store_no storeNo, s.status, s.type
    from
    merchant_store s
    left join merchant_store_account a on s.id = a.store_id and a.type = 0
    where
    s.tenant_id = #{tenantId}
    and s.id in
    <foreach collection="storeIds" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <select id="batchQueryByStoreIds" resultType="com.cosfo.manage.merchant.model.dto.MerchantStoreDTO">
    select
    s.id, s.tenant_id tenantId,s.store_name storeName, s.type, s.store_no storeNo, s.status
    from
    merchant_store s
    left join merchant_address ma on s.id = ma.store_id and s.tenant_id = ma.tenant_id
    where
    s.tenant_id = #{tenantId}
    and s.id in
    <foreach collection="storeIds" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <select id="countStoreNum" resultType="java.lang.Long">
    select ifnull(count(*), 0)
    from merchant_store
    where tenant_id = #{tenantId}
  </select>

  <select id="selectAllByTenantId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from merchant_store
    where tenant_id = #{tenantId}
  </select>
  <select id="selectByStoreName" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from merchant_store
    where store_name = #{storeName} and tenant_id = #{tenantId}
  </select>
  <select id="listStore" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from merchant_store s
    <where>
      <if test="id != null">
        and s.id = #{id}
      </if>
      <if test="storeNo != null and storeNo != ''">
        and s.store_no = #{storeNo}
      </if>
      <if test="type != null">
        and s.type = #{type}
      </if>
      <if test="storeName != null and storeName != ''">
        and s.store_name like #{storeName}
      </if>
      <if test="tenantId != null">
        and s.tenant_id = #{tenantId}
      </if>
      <if test="status != null">
        and s.status = #{status}
      </if>
      <if test="storeIds != null and storeIds.size() >0 ">
        and s.id in
        <foreach collection="storeIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
    </where>
  </select>
  <select id="selectMaxStoreNo" resultType="Long">
    select max(store_no)
    from merchant_store
    where tenant_id = #{tenantId}
  </select>

  <select id="selectIdListByParam" resultType="java.lang.Long">
      select ms.id
      from merchant_store ms
      left join merchant_store_group_mapping msgm on ms.tenant_id = msgm.tenant_id and ms.id = msgm.store_id
      <where>
        <if test="storeNo != null">
          and ms.store_no = #{storeNo}
        </if>
        <if test="storeName != null and storeName != ''">
          and ms.store_name like concat('%',#{storeName},'%')
        </if>
        <if test="tenantId != null">
          and ms.tenant_id = #{tenantId}
        </if>
        <if test="type != null">
          and ms.type = #{type}
        </if>
        <if test="groupId != null">
          and msgm.group_id = #{groupId}
        </if>
      </where>
  </select>
  <select id="selectList" resultType="com.cosfo.manage.merchant.model.dto.MerchantStoreDTO">
    select ms.id, ms.type, ms.store_name storeName, g.name groupName
    from merchant_store ms
    left join merchant_store_group_mapping m on ms.id = m.store_id and ms.tenant_id = m.tenant_id
    left join merchant_store_group g on g.id = m.group_id and g.tenant_id = m.tenant_id
    <where>
      ms.tenant_id = #{tenantId}
      <if test="storeIds != null and storeIds.size() >0 ">
        and ms.id in
        <foreach collection="storeIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
    </where>
  </select>
</mapper>
