<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.merchant.mapper.MerchantMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.merchant.model.po.Merchant">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="merchant_name" jdbcType="VARCHAR" property="merchantName" />
    <result column="logo_image" jdbcType="VARCHAR" property="logoImage" />
    <result column="background_image" jdbcType="VARCHAR" property="backgroundImage" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, merchant_name, logo_image, background_image, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from merchant
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from merchant
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.merchant.model.po.Merchant" useGeneratedKeys="true">
    insert into merchant (tenant_id, merchant_name, logo_image, 
      background_image, create_time, update_time
      )
    values (#{tenantId,jdbcType=BIGINT}, #{merchantName,jdbcType=VARCHAR}, #{logoImage,jdbcType=VARCHAR}, 
      #{backgroundImage,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.merchant.model.po.Merchant" useGeneratedKeys="true">
    insert into merchant
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="merchantName != null">
        merchant_name,
      </if>
      <if test="logoImage != null">
        logo_image,
      </if>
      <if test="backgroundImage != null">
        background_image,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="merchantName != null">
        #{merchantName,jdbcType=VARCHAR},
      </if>
      <if test="logoImage != null">
        #{logoImage,jdbcType=VARCHAR},
      </if>
      <if test="backgroundImage != null">
        #{backgroundImage,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.merchant.model.po.Merchant">
    update merchant
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="merchantName != null">
        merchant_name = #{merchantName,jdbcType=VARCHAR},
      </if>
      <if test="logoImage != null">
        logo_image = #{logoImage,jdbcType=VARCHAR},
      </if>
      <if test="backgroundImage != null">
        background_image = #{backgroundImage,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.merchant.model.po.Merchant">
    update merchant
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      merchant_name = #{merchantName,jdbcType=VARCHAR},
      logo_image = #{logoImage,jdbcType=VARCHAR},
      background_image = #{backgroundImage,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByTenantId" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List"></include>
    from
        merchant
    where tenant_id = #{tenantId}
  </select>

  <select id="selectByMId" resultType="com.cosfo.manage.merchant.model.vo.TenantDetailVO">
    select
        m.merchant_name merchantName,m.logo_image logoImage,
        m.background_image backgroundImage,c.company_name companyName,
        c.credit_code creditCode,c.business_license businessLicense,
        c.province, c.city, c.area, c.address, c.phone companyPhone
    from merchant m
    left join tenant t on m.tenant_id = t.id
    left join tenant_company c on c.tenant_id = m.tenant_id
    where
     m.tenant_id = #{param2}
  </select>
</mapper>