<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.merchant.mapper.MerchantDeliveryFeeRuleMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.merchant.model.po.MerchantDeliveryFeeRule">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="rule_type"  property="ruleType"/>
    <result column="price_type"  property="priceType"/>
    <result column="delivery_fee" jdbcType="DECIMAL" property="deliveryFee" />
    <result column="free_delivery_price" jdbcType="DECIMAL" property="freeDeliveryPrice" />
    <result column="relate_number" jdbcType="DECIMAL" property="relateNumber"/>
    <result column="default_type" jdbcType="INTEGER" property="defaultType"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="free_delivery_type" jdbcType="INTEGER" property="freeDeliveryType" />
    <result column="free_delivery_quantity" jdbcType="INTEGER" property="freeDeliveryQuantity" />
    <result column="priority" jdbcType="INTEGER" property="priority" />
    <result column="hit_item_ids" jdbcType="VARCHAR" property="hitItemIds" typeHandler="com.cosfo.manage.common.config.ListLongHandler"/>
    <result column="hit_areas" jdbcType="VARCHAR" property="hitAreas" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    <result column="include_new_flag" jdbcType="INTEGER" property="includeNewFlag"/>
    <result column="match_region_type" jdbcType="INTEGER" property="matchRegionType"/>
    <result column="hit_store_ids" jdbcType="VARCHAR" property="hitStoreIds" typeHandler="com.cosfo.manage.common.config.ListLongHandler"/>
    <result column="include_all_store_flag" jdbcType="INTEGER" property="includeAllStoreFlag"/>
    <result column="match_item_type" jdbcType="INTEGER" property="matchItemType"/>
    <result column="hit_goods_source" jdbcType="INTEGER" property="hitGoodsSource"/>
    <result column="fulfillment_type" jdbcType="INTEGER" property="fulfillmentType"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, `type`, rule_type, price_type, delivery_fee, free_delivery_price, relate_number, default_type, create_time, update_time, free_delivery_type,free_delivery_quantity,priority,hit_item_ids,hit_areas,include_new_flag,
    match_region_type, hit_store_ids, include_all_store_flag, match_item_type, hit_goods_source, fulfillment_type
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from merchant_delivery_fee_rule
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="listAll" resultMap="BaseResultMap">
      select <include refid="Base_Column_List"/>
      from merchant_delivery_fee_rule
      where tenant_id = #{tenantId}
    </select>

  <select id="listSpecialWithAddMarket" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from merchant_delivery_fee_rule
    where tenant_id = #{tenantId}
      and default_type = 0 and include_new_flag = 1
  </select>

  <select id="listSortRuleByType" resultMap="BaseResultMap" >
    select <include refid="Base_Column_List"/>
    from merchant_delivery_fee_rule
    where tenant_id = #{tenantId}
    and type = #{type}
    order by priority
  </select>

  <!-- 按租户查询规则，租户为空查询所有规则 -->
  <select id="listRuleByTenant" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_delivery_fee_rule
    where
      tenant_id is not null and type is not null
      <if test="tenantIds != null and tenantIds.size() >0 ">
        and tenant_id in
        <foreach collection="tenantIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
    order by tenant_id, type
  </select>

  <delete id="deleteSpecialRule">
    delete from merchant_delivery_fee_rule
    where tenant_id = #{tenantId}
    and type = #{type}
    and default_type = 0
    <if test="notDelIds != null and notDelIds.size > 0">
      and id not in (
          <foreach collection="notDelIds" item="rId" separator=",">
            #{rId}
          </foreach>
      )
    </if>
  </delete>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from merchant_delivery_fee_rule
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.merchant.model.po.MerchantDeliveryFeeRule" useGeneratedKeys="true">
    insert into merchant_delivery_fee_rule (tenant_id, `type`, rule_type, price_type, delivery_fee,
      free_delivery_price, relate_number, create_time, update_time,free_delivery_type,free_delivery_quantity
      )
    values (#{tenantId,jdbcType=BIGINT}, #{type,jdbcType=TINYINT}, #{ruleType}, #{priceType}, #{deliveryFee,jdbcType=DECIMAL},
      #{freeDeliveryPrice,jdbcType=DECIMAL}, #{relateNumber}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},#{freeDeliveryType}, #{freeDeliveryQuantity}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.merchant.model.po.MerchantDeliveryFeeRule" useGeneratedKeys="true">
    insert into merchant_delivery_fee_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="ruleType != null">
        rule_type,
      </if>
      <if test="priceType != null">
        price_type,
      </if>
      <if test="deliveryFee != null">
        delivery_fee,
      </if>
      <if test="freeDeliveryPrice != null">
        free_delivery_price,
      </if>
      <if test="relateNumber != null">
        relate_number,
      </if>
      <if test="defaultType != null">
        default_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="freeDeliveryType != null">
        free_delivery_type,
      </if>
      <if test="freeDeliveryQuantity != null">
        free_delivery_quantity,
      </if>
      <if test="priority != null">priority,</if>
      <if test="hitItemIds != null">hit_item_ids,</if>
      <if test="hitAreas != null">hit_areas,</if>
      <if test="includeNewFlag != null">include_new_flag,</if>
        <if test="matchRegionType != null">
            match_region_type,
        </if>
        <if test="hitStoreIds != null">
            hit_store_ids,
        </if>
        <if test="includeAllStoreFlag != null">
            include_all_store_flag,
        </if>
        <if test="matchItemType != null">
            match_item_type,
        </if>
        <if test="hitGoodsSource != null">
            hit_goods_source,
        </if>
        <if test="fulfillmentType != null">
            fulfillment_type,
        </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="ruleType != null">
        #{ruleType},
      </if>
      <if test="priceType != null">
        #{priceType},
      </if>
      <if test="deliveryFee != null">
        #{deliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="freeDeliveryPrice != null">
        #{freeDeliveryPrice,jdbcType=DECIMAL},
      </if>
      <if test="relateNumber != null">
        #{relateNumber},
      </if>
      <if test="defaultType != null">
        #{defaultType},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="freeDeliveryType != null">
        #{freeDeliveryType},
      </if>
      <if test="freeDeliveryQuantity != null">
        #{freeDeliveryQuantity},
      </if>
      <if test="priority != null">#{priority},</if>
      <if test="hitItemIds != null">#{hitItemIds,typeHandler=com.cosfo.manage.common.config.ListLongHandler},</if>
      <if test="hitAreas != null">#{hitAreas, typeHandler=com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler},</if>
      <if test="includeNewFlag != null">
          #{includeNewFlag},
      </if>
        <if test="matchRegionType != null">
            #{matchRegionType,jdbcType=INTEGER},
        </if>
        <if test="hitStoreIds != null">
            #{hitStoreIds,typeHandler=com.cosfo.manage.common.config.ListLongHandler},
        </if>
        <if test="includeAllStoreFlag != null">
            #{includeAllStoreFlag,jdbcType=INTEGER},
        </if>
        <if test="matchItemType != null">
            #{matchItemType,jdbcType=TINYINT},
        </if>
        <if test="hitGoodsSource != null">
            #{hitGoodsSource,jdbcType=TINYINT},
        </if>
        <if test="fulfillmentType != null">
            #{fulfillmentType,jdbcType=TINYINT},
        </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.merchant.model.po.MerchantDeliveryFeeRule">
    update merchant_delivery_fee_rule
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="ruleType != null">
        rule_type = #{ruleType},
      </if>
      <if test="priceType != null">
        price_type = #{priceType},
      </if>
      <if test="deliveryFee != null">
        delivery_fee = #{deliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="freeDeliveryPrice != null">
        free_delivery_price = #{freeDeliveryPrice,jdbcType=DECIMAL},
      </if>
      <if test="relateNumber != null">
        relate_number = #{relateNumber},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="freeDeliveryType != null">
        free_delivery_type = #{freeDeliveryType},
      </if>
      <if test="freeDeliveryQuantity != null">
        free_delivery_quantity = #{freeDeliveryQuantity},
      </if>
      <if test="priority != null">
        priority = #{priority,jdbcType=INTEGER},
      </if>
      <if test="hitItemIds != null">
        hit_item_ids = #{hitItemIds,typeHandler=com.cosfo.manage.common.config.ListLongHandler},
      </if>
      <if test="hitAreas != null">
        hit_areas = #{hitAreas,typeHandler=com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler},
      </if>
      <if test="includeNewFlag != null">
          include_new_flag = #{includeNewFlag,jdbcType=INTEGER},
      </if>
        <if test="matchRegionType != null">
            match_region_type = #{matchRegionType,jdbcType=INTEGER},
        </if>
        <if test="hitStoreIds != null">
            hit_store_ids = #{hitStoreIds,typeHandler=com.cosfo.manage.common.config.ListLongHandler},
        </if>
        <if test="includeAllStoreFlag != null">
            include_all_store_flag = #{includeAllStoreFlag,jdbcType=INTEGER},
        </if>
        <if test="matchItemType != null">
            match_item_type = #{matchItemType,jdbcType=TINYINT},
        </if>
        <if test="hitGoodsSource != null">
            hit_goods_source = #{hitGoodsSource,jdbcType=TINYINT},
        </if>
        <if test="fulfillmentType != null">
            fulfillment_type = #{fulfillmentType,jdbcType=TINYINT},
        </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.merchant.model.po.MerchantDeliveryFeeRule">
    update merchant_delivery_fee_rule
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      `type` = #{type,jdbcType=TINYINT},
      ruleType = #{ruleType},
      relate_number = #{relateNumber},
      delivery_fee = #{deliveryFee,jdbcType=DECIMAL},
      free_delivery_price = #{freeDeliveryPrice,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      free_delivery_type = #{freeDeliveryType},
      free_delivery_quantity = #{freeDeliveryQuantity}
        where id = #{id,jdbcType=BIGINT}
  </update>

  <delete id="deleteByIds">
    delete from merchant_delivery_fee_rule
    where tenant_id = #{tenantId,jdbcType=BIGINT}
    and id IN
    <foreach collection="ids" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </delete>

  <insert id="batchInsert">
    INSERT INTO (tenant_id, `type`, rule_type, price_type, delivery_fee,
    free_delivery_price, relate_number, default_type, create_time, update_time
    ) VALUES
    <foreach collection="list" separator="," item="item">
      (#{item.tenantId,jdbcType=BIGINT}, #{item.type,jdbcType=TINYINT}, #{item.ruleType}, #{item.priceType},
      #{item.deliveryFee,jdbcType=DECIMAL}, #{item.freeDeliveryPrice,jdbcType=DECIMAL}, #{item.relateNumber},
      #{item.defaultType}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

  <update id="updateWithItemAdd" parameterType="java.util.List">
    <foreach collection="updateRules" item="item" separator=";">
      UPDATE merchant_delivery_fee_rule
      <set>
        hit_item_ids = #{item.hitItemIds,typeHandler=com.cosfo.manage.common.config.ListLongHandler}
      </set>
      WHERE id = #{item.id}
    </foreach>
  </update>


</mapper>
