<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.merchant.mapper.MerchantDeliveryRuleWarehouseRelationMapper">
    <resultMap id="BaseResultMap" type="com.cosfo.manage.merchant.model.po.MerchantDeliveryRuleWarehouseRelation">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="rule_id" jdbcType="BIGINT" property="ruleId"/>
        <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
    id, tenant_id, `rule_id`, warehouse_no, create_time, update_time
  </sql>
    <delete id="deleteByRuleIds">
        delete from merchant_delivery_rule_warehouse_relation
        where tenant_id = #{tenantId,jdbcType=BIGINT}
        and rule_id IN
        <foreach collection="ruleIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteByWarehouseNoList">
        delete from merchant_delivery_rule_warehouse_relation
        where tenant_id = #{tenantId,jdbcType=BIGINT}
        and rule_id = #{ruleId}
        <if test="warehouseNoList != null and warehouseNoList.size() &gt; 0">
            and warehouse_no in
            <foreach collection="warehouseNoList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </delete>

</mapper>
