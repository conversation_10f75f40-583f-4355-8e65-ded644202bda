<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.merchant.mapper.MerchantDeliveryStepFeeMapper">

    <insert id="batchInsert">
        insert into merchant_delivery_step_fee
        (tenant_id, rule_id, fee_rule, step_threshold, delivery_fee, cal_type, deliveryfee_cal_rule)
        values
        <foreach collection="feeList" item="fee" separator=",">
            (
            #{fee.tenantId},
            #{fee.ruleId},
            #{fee.feeRule},
            #{fee.stepThreshold},
            #{fee.deliveryFee},
            <if test="fee.calType != null">#{fee.calType}</if>
            <if test="fee.calType == null">0</if>,
            #{fee.deliveryfeeCalRule, typeHandler=com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler}
            )
        </foreach>
    </insert>

</mapper>
