<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.merchant.mapper.MerchantStoreBalanceMapper">

    <resultMap id="BaseResultMap" type="com.cosfo.manage.merchant.model.po.MerchantStoreBalance">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="store_id" jdbcType="BIGINT" property="storeId"/>
        <result column="store_no" jdbcType="VARCHAR" property="storeNo"/>
        <result column="balance" jdbcType="DECIMAL" property="balance"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">id, tenant_id, store_id, store_no, balance, create_time, update_time</sql>

    <insert id="batchInsert">
        insert into merchant_store_balance (tenant_id, store_id, store_no, balance)
        values
        <foreach collection="balanceList" item="record" separator=",">
            (#{record.tenantId}, #{record.storeId}, #{record.storeNo}, #{record.balance})
        </foreach>
    </insert>

    <update id="updateBalanceByStoreId">
        update merchant_store_balance set balance = balance + #{changeBalance}
        where tenant_id = #{tenantId,jdbcType=BIGINT} AND store_id = #{storeId,jdbcType=BIGINT}
        AND balance + #{changeBalance} >=0
    </update>

    <select id="selectByStoreId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from merchant_store_balance
        where tenant_id = #{tenantId,jdbcType=BIGINT} AND store_id = #{storeId,jdbcType=BIGINT}
    </select>

    <select id="selectByStoreIdForUpdate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from merchant_store_balance
        where tenant_id = #{tenantId,jdbcType=BIGINT} AND store_id = #{storeId,jdbcType=BIGINT} for update
    </select>

    <select id="selectByStoreIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from merchant_store_balance
        where tenant_id = #{tenantId,jdbcType=BIGINT}
        AND store_id IN
        <foreach collection="storeIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>
</mapper>
