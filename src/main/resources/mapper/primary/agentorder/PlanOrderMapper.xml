<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.agentorder.mapper.PlanOrderMapper">

    <resultMap type="com.cosfo.manage.agentorder.model.po.PlanOrder" id="PlanOrderMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
        <result property="storeId" column="store_id" jdbcType="INTEGER"/>
        <result property="planOrderNo" column="plan_order_no" jdbcType="VARCHAR"/>
        <result property="agentOrderNo" column="agent_order_no" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="planType" column="plan_type" jdbcType="VARCHAR"/>
        <result property="itemInfoSnapshot" column="item_info_snapshot" jdbcType="VARCHAR"/>
        <result property="finishTime" column="finish_time" jdbcType="TIMESTAMP"/>
        <result property="successCreateOrderNo" column="success_create_order_no" jdbcType="VARCHAR"/>
        <result property="failReason" column="fail_reason" jdbcType="VARCHAR"/>
        <result property="cancelTime" column="cancel_time" jdbcType="TIMESTAMP"/>
        <result property="cancelRemark" column="cancel_remark" jdbcType="VARCHAR"/>
        <result property="cancelUserId" column="cancel_user_id" jdbcType="INTEGER"/>
        <result property="cancelType" column="cancel_type" jdbcType="VARCHAR"/>
        <result property="autoCancelTimeout" column="auto_cancel_timeout" jdbcType="INTEGER"/>
        <result property="autoCancelTime" column="auto_cancel_time" jdbcType="TIMESTAMP"/>
        <result property="planConfirmNotifyTime" column="plan_confirm_notify_time" jdbcType="TIMESTAMP"/>
        <result property="recommendReason" column="recommend_reason" jdbcType="VARCHAR"/>
        <result property="updateUserId" column="update_user_id" jdbcType="INTEGER"/>
        <result property="creatorUserId" column="creator_user_id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , tenant_id, store_id, plan_order_no, agent_order_no, status, plan_type, item_info_snapshot, finish_time, success_create_order_no, fail_reason, cancel_time, cancel_remark, cancel_user_id, cancel_type, auto_cancel_timeout, auto_cancel_time, plan_confirm_notify_time, recommend_reason, update_user_id, creator_user_id, create_time, update_time
    </sql>

    <select id="listNotifyPlanOrders" resultType="com.cosfo.manage.agentorder.model.po.PlanOrder">
        select
        <include refid="Base_Column_List"/>
        from `plan_order` WHERE `tenant_id` = #{tenantId}
        and `status` = #{planOrderStatus}
        <if test="planTypeList != null and planTypeList.size() > 0">
            AND `plan_type` IN
            <foreach collection="planTypeList" item="planType" open="(" close=")" separator=",">
                #{planType}
            </foreach>
        </if>
        and ( `plan_confirm_notify_time` is null or `plan_confirm_notify_time` &lt; #{today} )
    </select>

    <select id="queryPage" resultMap="PlanOrderMap">
        SELECT
        po.*
        FROM
        plan_order po
        <if test="queryParam.itemIds != null and queryParam.itemIds.size() > 0">
            LEFT JOIN
            agent_order_item aoi ON aoi.agent_order_no = po.agent_order_no
        </if>
        WHERE po.tenant_id = #{queryParam.tenantId}
        <if test="queryParam.planOrderStatus != null">
            AND po.status = #{queryParam.planOrderStatus}
        </if>
        <if test="queryParam.planOrderStatusList != null and queryParam.planOrderStatusList.size() > 0">
            AND po.status IN
            <foreach collection="queryParam.planOrderStatusList" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="queryParam.planOrderNo != null">
            AND po.plan_order_no = #{queryParam.planOrderNo}
        </if>
        <if test="queryParam.planType != null">
            AND po.plan_type = #{queryParam.planType}
        </if>
        <if test="queryParam.agentOrderNo != null">
            AND po.agent_order_no = #{queryParam.agentOrderNo}
        </if>
        <if test="queryParam.planCreateStartTime != null and queryParam.planCreateEndTime != null">
            AND po.create_time BETWEEN #{queryParam.planCreateStartTime} AND #{queryParam.planCreateEndTime}
        </if>
        <if test="queryParam.planCancelStartTime != null and queryParam.planCancelEndTime != null">
            AND po.cancel_time BETWEEN #{queryParam.planCancelStartTime} AND #{queryParam.planCancelEndTime}
        </if>
        <if test="queryParam.orderCreateStartTime != null and queryParam.orderCreateEndTime != null">
            AND po.finish_time BETWEEN #{queryParam.orderCreateStartTime} AND #{queryParam.orderCreateEndTime}
        </if>
        <if test="queryParam.storeIds != null and queryParam.storeIds.size() > 0">
            AND po.store_id IN
            <foreach collection="queryParam.storeIds" item="storeId" open="(" close=")" separator=",">
                #{storeId}
            </foreach>
        </if>
        <if test="queryParam.itemIds != null and queryParam.itemIds.size() > 0">
            AND aoi.item_id IN
            <foreach collection="queryParam.itemIds" item="itemId" open="(" close=")" separator=",">
                #{itemId}
            </foreach>
            group by po.id
        </if>
        <if test="queryParam.creatorUserIds != null and queryParam.creatorUserIds.size() > 0">
            AND po.creator_user_id in
            <foreach collection="queryParam.creatorUserIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <choose>
            <when test="queryParam.orderBy != null and queryParam.orderBy != ''">
                order by ${queryParam.orderBy}
            </when>
            <otherwise>
                ORDER BY po.id DESC
            </otherwise>
        </choose>
    </select>


    <select id="listWaitCreateOrders" resultType="com.cosfo.manage.agentorder.model.po.PlanOrder">
        select
        <include refid="Base_Column_List"/>
        from `plan_order`
        where `plan_type` = #{planType}
        and `status` = #{planOrderStatus}
        <if test="planOrderNos != null and planOrderNos.size() > 0">
            and plan_order_no in
            <foreach collection="planOrderNos" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by id limit 100
    </select>

</mapper>

