<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.order.mapper.OrderItemMapper">
    <resultMap id="BaseResultMap" type="com.cosfo.manage.order.model.po.OrderItem">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="item_id" jdbcType="BIGINT" property="itemId"/>
        <result column="amount" jdbcType="INTEGER" property="amount"/>
        <result column="payable_price" jdbcType="DECIMAL" property="payablePrice"/>
        <result column="total_price" jdbcType="DECIMAL" property="totalPrice"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="after_sale_expiry_time" property="afterSaleExpiryTime"/>
        <result column="delivery_quantity" property="deliveryQuantity"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , tenant_id, order_id, item_id, amount, payable_price, total_price, `status`, create_time,
    update_time, after_sale_expiry_time, delivery_quantity
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from order_item
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from order_item
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.order.model.po.OrderItem"
            useGeneratedKeys="true">
        insert into order_item (tenant_id, order_id, item_id,
                                amount, payable_price, total_price,
                                `status`, create_time, update_time)
        values (#{tenantId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{itemId,jdbcType=BIGINT},
                #{amount,jdbcType=INTEGER}, #{payablePrice,jdbcType=DECIMAL}, #{totalPrice,jdbcType=DECIMAL},
                #{status,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.cosfo.manage.order.model.po.OrderItem" useGeneratedKeys="true">
        insert into order_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="orderId != null">
                order_id,
            </if>
            <if test="itemId != null">
                item_id,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="payablePrice != null">
                payable_price,
            </if>
            <if test="totalPrice != null">
                total_price,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">
                #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="orderId != null">
                #{orderId,jdbcType=BIGINT},
            </if>
            <if test="itemId != null">
                #{itemId,jdbcType=BIGINT},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=INTEGER},
            </if>
            <if test="payablePrice != null">
                #{payablePrice,jdbcType=DECIMAL},
            </if>
            <if test="totalPrice != null">
                #{totalPrice,jdbcType=DECIMAL},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.order.model.po.OrderItem">
        update order_item
        <set>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=BIGINT},
            </if>
            <if test="itemId != null">
                item_id = #{itemId,jdbcType=BIGINT},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=INTEGER},
            </if>
            <if test="payablePrice != null">
                payable_price = #{payablePrice,jdbcType=DECIMAL},
            </if>
            <if test="totalPrice != null">
                total_price = #{totalPrice,jdbcType=DECIMAL},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.order.model.po.OrderItem">
        update order_item
        set tenant_id     = #{tenantId,jdbcType=BIGINT},
            order_id      = #{orderId,jdbcType=BIGINT},
            item_id       = #{itemId,jdbcType=BIGINT},
            amount        = #{amount,jdbcType=INTEGER},
            payable_price = #{payablePrice,jdbcType=DECIMAL},
            total_price   = #{totalPrice,jdbcType=DECIMAL},
            `status`      = #{status,jdbcType=TINYINT},
            create_time   = #{createTime,jdbcType=TIMESTAMP},
            update_time   = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="queryOrderItemVOByOrderId" resultType="com.cosfo.manage.order.model.vo.OrderItemVO">
        select i.id,
               i.item_id            itemId,
               i.amount,
               i.item_id             skuId,
               i.payable_price      price,
               i.total_price        totalPrice,
               s.supplier_tenant_id supplierTenantId,
               s.title,
               s.main_picture       mainPicture,
               s.supplier_sku_id       supplierSkuId,
               s.specification,
               s.warehouse_type warehouseType,
               s.goods_type goodsType,
               s.delivery_type deliveryType,
               i.order_id orderId,
               s.supplier_name supplierName,
               s.specification_unit specificationUnit,
               s.after_sale_unit afterSaleUnit,
               i.after_sale_expiry_time afterSaleExpiryTime,
               s.supply_price supplyPrice
        from order_item i
                 left join order_item_snapshot s on i.id = s.order_item_id
        where i.order_id = #{orderId}
    </select>

    <select id="selectByOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from order_item
        where
        tenant_id = #{tenantId}
        and order_id = #{orderId}
    </select>

    <select id="querySkuNum" resultType="integer">
        select
            count(distinct s.sku_id)
        from
            order_item i
        left join order_item_snapshot s on i.id = s.order_item_id
        where i.tenant_id = #{tenantId}
        and i.order_id in
        <foreach collection="orderIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="querySkuAmount" resultType="integer">
        select
            sum(i.amount)
        from
        order_item i
        where i.tenant_id = #{tenantId}
        and i.order_id in
        <foreach collection="orderIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>
    <select id="batchQueryByOrderId" resultType="com.cosfo.manage.order.model.vo.OrderItemVO">
        select oi.order_id orderId, oi.item_id itemId, ois.title, ois.specification, ois.specification_unit specificationUnit, ois.main_picture mainPicture,
               oi.amount amount, oi.payable_price price, oi.id id
        from order_item oi
        left join order_item_snapshot ois on oi.tenant_id = ois.tenant_id and oi.id = ois.order_item_id
        where
        oi.order_id in
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>

    </select>
    <select id="queryOrderItemDetailVOByOrderId" resultType="com.cosfo.manage.order.model.vo.OrderItemVO">
        select i.id,
               i.item_id            itemId,
               i.amount,
               i.item_id             skuId,
               i.payable_price      price,
               i.total_price        totalPrice,
               i.delivery_quantity        deliveryQuantity,
               s.supplier_tenant_id supplierTenantId,
               s.title,
               s.main_picture       mainPicture,
               s.specification,
               s.warehouse_type warehouseType,
               s.goods_type goodsType,
               s.delivery_type deliveryType,
               i.order_id orderId,
               s.supplier_name supplierName,
               s.specification_unit specificationUnit,
               s.after_sale_unit afterSaleUnit,
               i.item_id itemSkuId,
               i.after_sale_expiry_time afterSaleExpiryTime
        from order_item i
                 left join order_item_snapshot s on i.id = s.order_item_id
        where i.order_id = #{orderId}
    </select>
    <select id="batchQueryDetailVOList"
            resultType="com.cosfo.manage.order.model.vo.OrderItemVO">
        select
                oi.order_id orderId,
                oi.item_id itemId,
                ois.title,
                ois.specification,
                ois.specification_unit specificationUnit,
                ois.main_picture mainPicture,
                oi.amount amount,
                oi.delivery_quantity deliveryQuantity,
                oi.payable_price price,
                oi.item_id skuId,
                oi.id id,
                ois.supplier_tenant_id supplierTenantId
        from order_item oi
        left join order_item_snapshot ois on oi.tenant_id = ois.tenant_id and oi.id = ois.order_item_id
        <where>
            <if test="orderIds != null">
                and oi.order_id in
                <foreach collection="orderIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="supplierIds != null and supplierIds.size() > 0 ">
                and ois.supplier_tenant_id in
                <foreach collection="supplierIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <update id="updateSharingTime">
        update order_item
        set after_sale_expiry_time = #{afterSaleExpiryTime}
        where id = #{id}
    </update>

    <update id="updateDeliveryQuantity">
        update order_item
        set delivery_quantity = delivery_quantity + #{orderItemDeliveryDTO.quantity}
        where id = #{orderItemDeliveryDTO.orderItemId}
        and order_id = #{orderId}
        and delivery_quantity + #{orderItemDeliveryDTO.quantity} <![CDATA[ <= ]]> amount
    </update>
</mapper>
