<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.pos.mapper.PosBomLogMapper">
    <sql id="Base_Column_List">
        id,
        create_time,
        update_time,
        channel_type,
        tenant_id,
        out_store_code,
        out_menu_specification,
        out_menu_code,
        merchant_store_id,
        out_menu_name,
        price,
        available_date,
        opt_type,
        pos_bom_item
    </sql>
    <resultMap id="BaseResultMap" type="com.cosfo.manage.pos.model.po.PosBomLog">
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="channel_type" property="channelType"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="out_store_code" property="outStoreCode"/>
        <result column="out_menu_specification" property="outMenuSpecification"/>
        <result column="out_menu_code" property="outMenuCode"/>
        <result column="merchant_store_id" property="merchantStoreId"/>
        <result column="out_menu_name" property="outMenuName"/>
        <result column="price" property="price"/>
        <result column="available_date" property="availableDate"/>
        <result column="opt_type" property="optType"/>
        <result column="pos_bom_item" property="posBomItem"/>
    </resultMap>

<!--auto generated by MybatisCodeHelper on 2023-11-16-->
    <insert id="insertList">
        INSERT INTO pos_bom_log(
        channel_type,
        tenant_id,
        target_type,
        target_value,
        out_menu_specification,
        out_menu_code,
        merchant_store_id,
        out_menu_name,
        price,
        available_date,
        opt_type,
        pos_bom_item
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.channelType},
            #{element.tenantId},
            #{element.targetType},
            #{element.targetValue},
            #{element.outMenuSpecification},
            #{element.outMenuCode},
            #{element.merchantStoreId},
            #{element.outMenuName},
            #{element.price},
            #{element.availableDate},
            #{element.optType},
            #{element.posBomItem}
            )
        </foreach>
    </insert>
</mapper>
