<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.pos.mapper.PosBomItemMapper">
<insert id="batchInsert">
    insert into pos_bom_item
    (
    out_menu_code,
    channel_type,
    tenant_id,
    target_type,
    target_value,
    merchant_store_id,
    out_item_code,
    market_item_id,
    available_date,
    unit_cost_price,
    net_quantity,
    remarks,
    out_item_name,
    out_item_unit,
    composition_type,
    composition_id
    )
    values
    <foreach collection="list" item="item" index="index" separator=",">
        (
        #{item.outMenuCode},
        #{item.channelType},
        #{item.tenantId},
        #{item.targetType},
        #{item.targetValue},
        #{item.merchantStoreId},
        #{item.outItemCode},
        #{item.marketItemId},
        #{item.availableDate},
        #{item.unitCostPrice},
        #{item.netQuantity},
        #{item.remarks},
        #{item.outItemName},
        #{item.outItemUnit},
        #{item.compositionType},
        #{item.compositionId}
        )
    </foreach>
    ON DUPLICATE KEY UPDATE
    merchant_store_id = VALUES(merchant_store_id),
    market_item_id = VALUES(market_item_id),
    unit_cost_price = VALUES(unit_cost_price),
    net_quantity = VALUES(net_quantity),
    remarks = VALUES(remarks),
    out_item_name = VALUES(out_item_name),
    out_item_unit = VALUES(out_item_unit)
</insert>
</mapper>
