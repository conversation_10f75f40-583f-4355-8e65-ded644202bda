<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.wechat.mapper.WechatAuthorizerMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.wechat.model.po.WechatAuthorizer">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="platform_app_id" jdbcType="VARCHAR" property="platformAppId" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="app_type" jdbcType="INTEGER" property="appType" />
    <result column="access_token" jdbcType="VARCHAR" property="accessToken" />
    <result column="access_token_expiretime" jdbcType="TIMESTAMP" property="accessTokenExpiretime" />
    <result column="refresh_token" jdbcType="VARCHAR" property="refreshToken" />
    <result column="func_info" jdbcType="VARCHAR" property="funcInfo" />
    <result column="authorization_code" jdbcType="VARCHAR" property="authorizationCode" />
    <result column="authorization_code_expiretime" jdbcType="TIMESTAMP" property="authorizationCodeExpiretime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="authorizeurl" jdbcType="VARCHAR" property="authorizeurl" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id,platform_app_id , app_id, app_type, access_token, access_token_expiretime, refresh_token, func_info,
    authorization_code, authorization_code_expiretime, `status`, authorizeurl, create_time, 
    update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wechat_authorizer
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByAuthCode" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from wechat_authorizer
      where authorization_code = #{authCode,jdbcType=VARCHAR}
  </select>
  <select id="selectByRefreshToken" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from wechat_authorizer
    where     status = #{status,jdbcType=INTEGER}
    <if test="appId != null">
      AND   app_id = #{appId,jdbcType=VARCHAR}
    </if>
  </select>
  <select id="selectByTime" resultMap="BaseResultMap">
   select b.* from tenant_auth_connection b
   left join wechat_authorizer a
   on a.app_id = b.app_id
   where a.`status` = #{status,jdbcType=INTEGER}
   and b.create_time > #{createTime,jdbcType=TIMESTAMP}
  </select>

  <select id="selectByAppId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from wechat_authorizer
    where status = 1
    AND app_id = #{appId,jdbcType=VARCHAR}
  </select>

  <select id="getAuthTenants" resultMap="BaseResultMap">
    select
    id, app_id, access_token, `status`,  create_time
    from wechat_authorizer
    where status = 1
  </select>

  <select id="getAuthTenantsByPage" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from wechat_authorizer
    where status = 1
    and id > #{maxId}
    order by id
    limit #{size}
  </select>

  <select id="selectListAppIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from wechat_authorizer
    where status = 1
    <if test="appIds != null  and appIds.size()&gt;0">
      AND app_id IN
      <foreach close=")" collection="appIds" item="appId" open="(" separator=",">
        #{appId}
      </foreach>
    </if>
  </select>

  <select id="selectOneByAppId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from wechat_authorizer
    where app_id = #{appId,jdbcType=VARCHAR}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wechat_authorizer
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.wechat.model.po.WechatAuthorizer" useGeneratedKeys="true">
    insert into wechat_authorizer (platform_app_id, app_id, app_type, access_token,
      access_token_expiretime, refresh_token, 
      func_info, authorization_code, authorization_code_expiretime, 
      `status`, authorizeurl, create_time, 
      update_time)
    values (#{platformAppId,jdbcType=VARCHAR}, #{appId,jdbcType=VARCHAR}, #{appType,jdbcType=INTEGER}, #{accessToken,jdbcType=VARCHAR},
      #{accessTokenExpiretime,jdbcType=TIMESTAMP}, #{refreshToken,jdbcType=VARCHAR}, 
      #{funcInfo,jdbcType=VARCHAR}, #{authorizationCode,jdbcType=VARCHAR}, #{authorizationCodeExpiretime,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=INTEGER}, #{authorizeurl,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.wechat.model.po.WechatAuthorizer" useGeneratedKeys="true">
    insert into wechat_authorizer
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="platformAppId != null">
        platform_app_id,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="appType != null">
        app_type,
      </if>
      <if test="accessToken != null">
        access_token,
      </if>
      <if test="accessTokenExpiretime != null">
        access_token_expiretime,
      </if>
      <if test="refreshToken != null">
        refresh_token,
      </if>
      <if test="funcInfo != null">
        func_info,
      </if>
      <if test="authorizationCode != null">
        authorization_code,
      </if>
      <if test="authorizationCodeExpiretime != null">
        authorization_code_expiretime,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="authorizeurl != null">
        authorizeurl,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="platformAppId != null">
        #{platformAppId,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="appType != null">
        #{appType,jdbcType=INTEGER},
      </if>
      <if test="accessToken != null">
        #{accessToken,jdbcType=VARCHAR},
      </if>
      <if test="accessTokenExpiretime != null">
        #{accessTokenExpiretime,jdbcType=TIMESTAMP},
      </if>
      <if test="refreshToken != null">
        #{refreshToken,jdbcType=VARCHAR},
      </if>
      <if test="funcInfo != null">
        #{funcInfo,jdbcType=VARCHAR},
      </if>
      <if test="authorizationCode != null">
        #{authorizationCode,jdbcType=VARCHAR},
      </if>
      <if test="authorizationCodeExpiretime != null">
        #{authorizationCodeExpiretime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="authorizeurl != null">
        #{authorizeurl,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.wechat.model.po.WechatAuthorizer">
    update wechat_authorizer
    <set>
      <if test="platformAppId != null">
        platform_app_id = #{platformAppId,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="appType != null">
        app_type = #{appType,jdbcType=INTEGER},
      </if>
      <if test="accessToken != null">
        access_token = #{accessToken,jdbcType=VARCHAR},
      </if>
      <if test="accessTokenExpiretime != null">
        access_token_expiretime = #{accessTokenExpiretime,jdbcType=TIMESTAMP},
      </if>
      <if test="refreshToken != null">
        refresh_token = #{refreshToken,jdbcType=VARCHAR},
      </if>
      <if test="funcInfo != null">
        func_info = #{funcInfo,jdbcType=VARCHAR},
      </if>
      <if test="authorizationCode != null">
        authorization_code = #{authorizationCode,jdbcType=VARCHAR},
      </if>
      <if test="authorizationCodeExpiretime != null">
        authorization_code_expiretime = #{authorizationCodeExpiretime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="authorizeurl != null">
        authorizeurl = #{authorizeurl,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.wechat.model.po.WechatAuthorizer">
    update wechat_authorizer
    set  platform_app_id = #{platformAppId,jdbcType=VARCHAR},
      app_id = #{appId,jdbcType=VARCHAR},
      app_type = #{appType,jdbcType=INTEGER},
      access_token = #{accessToken,jdbcType=VARCHAR},
      access_token_expiretime = #{accessTokenExpiretime,jdbcType=TIMESTAMP},
      refresh_token = #{refreshToken,jdbcType=VARCHAR},
      func_info = #{funcInfo,jdbcType=VARCHAR},
      authorization_code = #{authorizationCode,jdbcType=VARCHAR},
      authorization_code_expiretime = #{authorizationCodeExpiretime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=INTEGER},
      authorizeurl = #{authorizeurl,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByAppId" parameterType="com.cosfo.manage.wechat.model.po.WechatAuthorizer">
    update wechat_authorizer
    <set>
      <if test="platformAppId != null">
        platform_app_id = #{platformAppId,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="appType != null">
        app_type = #{appType,jdbcType=INTEGER},
      </if>
      <if test="accessToken != null">
        access_token = #{accessToken,jdbcType=VARCHAR},
      </if>
      <if test="accessTokenExpiretime != null">
        access_token_expiretime = #{accessTokenExpiretime,jdbcType=TIMESTAMP},
      </if>
      <if test="refreshToken != null">
        refresh_token = #{refreshToken,jdbcType=VARCHAR},
      </if>
      <if test="funcInfo != null">
        func_info = #{funcInfo,jdbcType=VARCHAR},
      </if>
      <if test="authorizationCode != null">
        authorization_code = #{authorizationCode,jdbcType=VARCHAR},
      </if>
      <if test="authorizationCodeExpiretime != null">
        authorization_code_expiretime = #{authorizationCodeExpiretime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="authorizeurl != null">
        authorizeurl = #{authorizeurl,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where app_id = #{appId,jdbcType=VARCHAR}
  </update>
</mapper>