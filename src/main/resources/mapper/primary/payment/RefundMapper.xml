<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.order.mapper.payment.RefundMapper">
    <resultMap id="BaseResultMap" type="com.cosfo.manage.order.model.po.payment.Refund">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="after_sale_id" jdbcType="BIGINT" property="afterSaleId"/>
        <result column="refund_no" jdbcType="VARCHAR" property="refundNo"/>
        <result column="refund_status" jdbcType="INTEGER" property="refundStatus"/>
        <result column="sub_mchid" jdbcType="VARCHAR" property="subMchid"/>
        <result column="refund_price" jdbcType="DECIMAL" property="refundPrice"/>
        <result column="payment_price" jdbcType="DECIMAL" property="paymentPrice"/>
        <result column="refund_id" jdbcType="VARCHAR" property="refundId"/>
        <result column="channel" jdbcType="VARCHAR" property="channel"/>
        <result column="user_received_account" jdbcType="VARCHAR" property="userReceivedAccount"/>
        <result column="success_time" jdbcType="TIMESTAMP" property="successTime"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="retry_num" jdbcType="INTEGER" property="retryNum"/>
        <result column="req_seq_id" jdbcType="VARCHAR" property="reqSeqId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="confirm_refund_req_id" property="confirmRefundReqId"/>
        <result column="payment_id" property="paymentId"/>
    </resultMap>

    <sql id="Base_Column_List">
    id, tenant_id, after_sale_id, refund_no, refund_status, sub_mchid, refund_price, 
    payment_price, refund_id, channel, user_received_account, success_time, `status`, 
    update_time, create_time, retry_num, req_seq_id, confirm_refund_req_id, payment_id
  </sql>

    <select id="selectByAfterSaleId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from refund
        where after_sale_id = #{afterSaleId} and tenant_id = #{tenantId}
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from refund
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectSuccessByTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from refund
        <where>
            <if test="tenantId != null">
                tenant_id = #{tenantId}
                and
            </if>
            refund_status = 2
            and success_time <![CDATA[>=]]> #{startTime}
            AND success_time <![CDATA[<]]> #{endTime}
        </where>
    </select>

    <select id="selectSuccessPrice" resultType="java.math.BigDecimal">
        select ifnull(sum(refund_price), 0) from refund
        <where>
            <if test="tenantId!= null">
                tenant_id = #{tenantId} and
            </if>
            refund_status = 2 and success_time <![CDATA[>=]]> #{startTime} AND success_time <![CDATA[<]]> #{endTime}
        </where>
    </select>
    <select id="selectByRefundNo" resultType="com.cosfo.manage.order.model.po.payment.Refund">
        select
        <include refid="Base_Column_List"/>
        from refund
        where refund_no = #{refundNo}
    </select>

</mapper>