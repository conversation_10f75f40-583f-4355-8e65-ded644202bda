<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.report.mapper.MerchantStoreDetailPurchaseMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.merchant.model.po.MerchantStoreDetailPurchase">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="time_tag" jdbcType="VARCHAR" property="timeTag" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="purchase_num" jdbcType="INTEGER" property="purchaseNum" />
    <result column="purchase_price" jdbcType="DECIMAL" property="purchasePrice" />
    <result column="refund_num" jdbcType="INTEGER" property="refundNum" />
    <result column="refund_price" jdbcType="DECIMAL" property="refundPrice" />
    <result column="contact" jdbcType="VARCHAR" property="contact" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="bill_switch" jdbcType="TINYINT" property="billSwitch" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
  </resultMap>

  <resultMap id="DTOResultMap" type="com.cosfo.manage.merchant.model.dto.MerchantStoreDetailPurchaseDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="time_tag" jdbcType="VARCHAR" property="timeTag" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="purchase_num" jdbcType="INTEGER" property="purchaseNum" />
    <result column="purchase_price" jdbcType="DECIMAL" property="purchasePrice" />
    <result column="refund_num" jdbcType="INTEGER" property="refundNum" />
    <result column="refund_price" jdbcType="DECIMAL" property="refundPrice" />
    <result column="contact" jdbcType="VARCHAR" property="contact" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="bill_switch" jdbcType="TINYINT" property="billSwitch" />
    <result column="store_type_desc" jdbcType="VARCHAR" property="storeTypeDesc" />
    <result column="bill_switch_desc" jdbcType="VARCHAR" property="billSwitchDesc" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
  </resultMap>

  <sql id="Base_Column_List">
    id, tenant_id, time_tag, store_id, store_name, `type`, purchase_num, purchase_price,
    refund_num, refund_price, contact, phone, bill_switch, create_time, update_time,store_code
  </sql>
  <select id="queryAll" resultMap="DTOResultMap" fetchSize="1000" >
    select id,
           tenant_id,
           time_tag,
           store_id,
           store_name,
           `type`,
           purchase_num,
           purchase_price,
           refund_num,
           refund_price,
           contact,
           phone,
           bill_switch,
           if(type = 0, '直营店', if(type = 1, '加盟店', '托管店')) as store_type_desc,
           if(bill_switch = 0, '关闭', '开启') as bill_switch_desc,
           group_name,
           store_code
    from merchant_store_detail_purchase
    where tenant_id = #{tenantId}
      and time_tag &gt;= #{startTime}
      and time_tag &lt;= #{endTime}
  </select>

</mapper>
