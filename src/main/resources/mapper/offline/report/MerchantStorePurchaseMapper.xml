<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.report.mapper.MerchantStorePurchaseMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.merchant.model.po.MerchantStorePurchase">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="time_tag" jdbcType="VARCHAR" property="timeTag" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="pay_type" jdbcType="TINYINT" property="payType" />
    <result column="store_in_operation_num" jdbcType="INTEGER" property="storeInOperationNum" />
    <result column="direct_store_in_operation_num" jdbcType="INTEGER" property="directStoreInOperationNum" />
    <result column="join_store_in_operation_num" jdbcType="INTEGER" property="joinStoreInOperationNum" />
    <result column="managed_store_in_operation_num" jdbcType="INTEGER" property="managedStoreInOperationNum" />
    <result column="purchased_store_num" jdbcType="INTEGER" property="purchasedStoreNum" />
    <result column="last_purchased_store_num" jdbcType="INTEGER" property="lastPurchasedStoreNum" />
    <result column="purchased_store_chain" jdbcType="VARCHAR" property="purchasedStoreChain" />
    <result column="purchased_direct_store_num" jdbcType="INTEGER" property="purchasedDirectStoreNum" />
    <result column="last_purchased_direct_store_num" jdbcType="INTEGER" property="lastPurchasedDirectStoreNum" />
    <result column="purchased_direct_store_chain" jdbcType="VARCHAR" property="purchasedDirectStoreChain" />
    <result column="purchased_join_store_num" jdbcType="INTEGER" property="purchasedJoinStoreNum" />
    <result column="last_purchased_join_store_num" jdbcType="INTEGER" property="lastPurchasedJoinStoreNum" />
    <result column="purchased_join_store_chain" jdbcType="VARCHAR" property="purchasedJoinStoreChain" />
    <result column="purchased_managed_store_num" jdbcType="INTEGER" property="purchasedManagedStoreNum" />
    <result column="last_purchased_managed_store_num" jdbcType="INTEGER" property="lastPurchasedManagedStoreNum" />
    <result column="purchased_managed_store_chain" jdbcType="VARCHAR" property="purchasedManagedStoreChain" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, time_tag, `type`, pay_type, store_in_operation_num, direct_store_in_operation_num,
    join_store_in_operation_num, managed_store_in_operation_num, purchased_store_num,
    last_purchased_store_num, purchased_store_chain, purchased_direct_store_num, last_purchased_direct_store_num,
    purchased_direct_store_chain, purchased_join_store_num, last_purchased_join_store_num,
    purchased_join_store_chain, purchased_managed_store_num, last_purchased_managed_store_num,
    purchased_managed_store_chain, create_time, update_time
  </sql>
  <select id="querySummary" resultMap="BaseResultMap">
    select ifnull(store_in_operation_num, 0) store_in_operation_num,
           ifnull(direct_store_in_operation_num, 0) direct_store_in_operation_num,
           ifnull(join_store_in_operation_num, 0) join_store_in_operation_num,
           ifnull(managed_store_in_operation_num, 0) managed_store_in_operation_num,
           ifnull(purchased_store_num, 0) purchased_store_num,
           ifnull(last_purchased_store_num, 0) last_purchased_store_num,
           ifnull(purchased_store_chain, '-') purchased_store_chain,
           ifnull(purchased_direct_store_num, 0) purchased_direct_store_num,
           ifnull(last_purchased_direct_store_num, 0) last_purchased_direct_store_num,
           ifnull(purchased_direct_store_chain, '-') purchased_direct_store_chain,
           ifnull(purchased_join_store_num, 0) purchased_join_store_num,
           ifnull(last_purchased_join_store_num, 0) last_purchased_join_store_num,
           ifnull(purchased_join_store_chain, '-') purchased_join_store_chain,
           ifnull(purchased_managed_store_num, 0) purchased_managed_store_num,
           ifnull(last_purchased_managed_store_num, 0) last_purchased_managed_store_num,
           ifnull(purchased_managed_store_chain, '-') purchased_managed_store_chain
    from merchant_store_purchase
    where tenant_id = #{tenantId} and time_tag = #{timeTag} and type = #{type} and pay_type = #{payType}
  </select>

  <select id="batchQuerySummary" resultMap="BaseResultMap">
    select
    time_tag,
    ifnull(store_in_operation_num, 0) store_in_operation_num,
    ifnull(direct_store_in_operation_num, 0) direct_store_in_operation_num,
    ifnull(join_store_in_operation_num, 0) join_store_in_operation_num,
    ifnull(managed_store_in_operation_num, 0) managed_store_in_operation_num,
    ifnull(purchased_store_num, 0) purchased_store_num,
    ifnull(last_purchased_store_num, 0) last_purchased_store_num,
    ifnull(purchased_store_chain, '-') purchased_store_chain,
    ifnull(purchased_direct_store_num, 0) purchased_direct_store_num,
    ifnull(last_purchased_direct_store_num, 0) last_purchased_direct_store_num,
    ifnull(purchased_direct_store_chain, '-') purchased_direct_store_chain,
    ifnull(purchased_join_store_num, 0) purchased_join_store_num,
    ifnull(last_purchased_join_store_num, 0) last_purchased_join_store_num,
    ifnull(purchased_join_store_chain, '-') purchased_join_store_chain,
    ifnull(purchased_managed_store_num, 0) purchased_managed_store_num,
    ifnull(last_purchased_managed_store_num, 0) last_purchased_managed_store_num,
    ifnull(purchased_managed_store_chain, '-') purchased_managed_store_chain
    from merchant_store_purchase
    where tenant_id = #{tenantId} and type = #{type} and pay_type = #{payType} and time_tag in
    <foreach collection="timeTags" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
    group by tenant_id, time_tag
  </select>


</mapper>
