<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.report.mapper.BillAgentWarehouseSummaryMapper">
    <select id="querySummary" resultType="com.cosfo.manage.bill.model.po.BillAgentWarehouseSummary">
        select
            ifnull(sum(total_actual_warehouse_expenses), 0) totalActualWarehouseExpenses,
            ifnull(sum(total_warehouse_expenses), 0) totalWarehouseExpenses,
            ifnull(sum(total_after_sale_warehouse_expenses), 0) totalAfterSaleWarehouseExpenses,
            ifnull(sum(total_sales_amount), 0) totalSalesAmount,
            ifnull(sum(total_sales_amount_wechat_pay), 0) totalSalesAmountWechatPay,
            ifnull(sum(total_sales_amount_bill_balance_pay), 0) totalSalesAmountBillBalancePay,
            ifnull(sum(after_sale_amount_wechat_pay), 0) afterSaleAmountWechatPay,
            ifnull(sum(after_sale_amount_bill_balance_pay), 0) afterSaleAmountBillBalancePay,
            ifnull(sum(deduct_after_sales_amount_wechat_pay), 0) deductAfterSalesAmountWechatPay,
            ifnull(sum(deduct_after_sales_amount_bill_balance_pay), 0) deductAfterSalesAmountBillBalancePay,
            ifnull(sum(delivery_fee_deduct_after_sales_amount), 0) deliveryFeeDeductAfterSalesAmount
        from bill_agent_warehouse_summary
        where tenant_id = #{tenantId}
          and time_tag between #{startTime} and #{endTime}
    </select>
</mapper>
