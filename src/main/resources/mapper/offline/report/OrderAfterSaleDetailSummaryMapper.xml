<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.report.mapper.OrderAfterSaleDetailSummaryMapper">

    <select id="queryByConditionSum" resultType="com.cosfo.manage.report.model.po.OrderAfterSaleDetailSummary">
        select ifnull(sum(item_refund_price), 0) itemRefundPrice, sum(delivery_refund_fee) deliveryRefundFee, delivery_refund_fee_flag deliveryRefundFeeFlag, after_sale_type afterSaleType
        from order_after_sale_detail_summary
        <where>
            tenant_id = #{tenantId} and order_no = #{orderNo} and item_id = #{itemId}
            <if test="!specialFlag">
                and time_tag between #{startTime} and #{endTime}
            </if>
            <if test="specialFlag">
                and special_time_tag between #{startTime} and #{endTime}
            </if>
        </where>
<!--        where tenant_id = #{tenantId} and order_no = #{orderNo} and item_id = #{itemId} and time_tag between #{startTime} and #{endTime}-->
    </select>
</mapper>
