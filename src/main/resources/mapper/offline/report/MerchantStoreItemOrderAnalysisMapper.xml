<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.report.mapper.MerchantStoreItemOrderAnalysisMapper">

    <select id="queryStoreItemOrderSum"
            resultType="com.cosfo.manage.merchant.model.dto.MerchantStoreItemOrderOverviewDTO">
        select
        ifnull(sum(average_order_period), 0) averageOrderPeriodSum,
        ifnull(sum(order_amount), 0) orderAmount,
        ifnull(sum(order_price), 0) orderPrice,
        ifnull(count(id), 0) recordCounts
        from merchant_store_item_order_analysis
        <where>
            tenant_id = #{tenantId} and time_tag = #{timeTag} and type = #{type}
            <if test="title != null and title != ''">
                and title like concat('%', #{title}, '%')
            </if>
            <if test="storeIds != null">
                and store_id in
                <foreach collection="storeIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="listByCondition" resultType="com.cosfo.manage.merchant.model.po.MerchantStoreItemOrderAnalysis">
        select
            id,
            tenant_id tenantId,
            time_tag timeTag,
            type, item_id itemId,
            store_id storeId,
            title,
            average_order_period averageOrderPeriod,
            average_order_period_upper_period averageOrderPeriodUpperPeriod,
            order_amount orderAmount,
            order_amount_upper_period orderAmountUpperPeriod,
            order_price orderPrice,
            order_price_upper_period orderPriceUpperPeriod,
            last_order_time lastOrderTime,
            last_order_amount last_orderAmount,
            last_order_price lastOrderPrice,
            average_order_period_last_period averageOrderPeriodLastPeriod,
            order_amount_last_period orderAmountLastPeriod,
            order_price_last_period orderPriceLastPeriod
        from merchant_store_item_order_analysis
        <where>
            tenant_id = #{tenantId} and time_tag = #{timeTag} and type = #{type}
            <if test="title != null and title != ''">
                and title like concat('%', #{title}, '%')
            </if>
            <if test="storeIds != null">
                and store_id in
                <foreach collection="storeIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="averageOrderPeriodSort != null and averageOrderPeriodSort != ''">
                order by average_order_period ${averageOrderPeriodSort}, id asc
            </if>
            <if test="orderAmountSort != null and orderAmountSort != ''">
                order by order_amount ${orderAmountSort}, id asc
            </if>
            <if test="orderPriceSort != null and orderPriceSort != ''">
                order by order_price ${orderPriceSort}, id asc
            </if>
            <if test="lastOrderTimeSort != null and lastOrderTimeSort != ''">
                order by last_order_time ${lastOrderTimeSort}, id asc
            </if>
            <if test="lastOrderAmountSort != null and lastOrderAmountSort != ''">
                order by last_order_amount ${lastOrderAmountSort}, id asc
            </if>
            <if test="lastOrderPriceSort != null and lastOrderPriceSort != ''">
                order by last_order_price ${lastOrderPriceSort}, id asc
            </if>
        </where>
    </select>

    <select id="exportByCondition" resultType="com.cosfo.manage.merchant.model.po.MerchantStoreItemOrderAnalysis" fetchSize="1000">
        select
        id,
        tenant_id tenantId,
        time_tag timeTag,
        type, item_id itemId,
        store_id storeId,
        title,
        average_order_period averageOrderPeriod,
        average_order_period_upper_period averageOrderPeriodUpperPeriod,
        order_amount orderAmount,
        order_amount_upper_period orderAmountUpperPeriod,
        order_price orderPrice,
        order_price_upper_period orderPriceUpperPeriod,
        last_order_time lastOrderTime,
        last_order_amount last_orderAmount,
        last_order_price lastOrderPrice,
        average_order_period_last_period averageOrderPeriodLastPeriod,
        order_amount_last_period orderAmountLastPeriod,
        order_price_last_period orderPriceLastPeriod
        from merchant_store_item_order_analysis
        <where>
            tenant_id = #{tenantId} and time_tag = #{timeTag} and type = #{type}
            <if test="title != null and title != ''">
                and title like concat('%', #{title}, '%')
            </if>
            <if test="storeIds != null">
                and store_id in
                <foreach collection="storeIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
        <if test="averageOrderPeriodSort != null and averageOrderPeriodSort != ''">
            order by average_order_period ${averageOrderPeriodSort}, id asc
        </if>
    </select>
</mapper>
