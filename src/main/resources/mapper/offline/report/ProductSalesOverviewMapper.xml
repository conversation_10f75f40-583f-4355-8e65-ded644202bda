<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.report.mapper.ProductSalesOverviewMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.product.model.po.ProductSalesOverview">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="time_tag" jdbcType="VARCHAR" property="timeTag" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="pay_success_num" jdbcType="INTEGER" property="paySuccessNum" />
    <result column="last_success_num" jdbcType="INTEGER" property="lastSuccessNum" />
    <result column="pay_success_price" jdbcType="DECIMAL" property="paySuccessPrice" />
    <result column="last_pay_success_price" jdbcType="DECIMAL" property="lastPaySuccessPrice" />
    <result column="refund_num" jdbcType="INTEGER" property="refundNum" />
    <result column="last_refund_num" jdbcType="INTEGER" property="lastRefundNum" />
    <result column="refund_price" jdbcType="DECIMAL" property="refundPrice" />
    <result column="last_refund_price" jdbcType="DECIMAL" property="lastRefundPrice" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="store_type" jdbcType="TINYINT" property="storeType" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="delivery_type" property="deliveryType"/>
    <result column="warehouse_type" property="warehouseType"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, time_tag, `type`, pay_success_num, last_success_num, pay_success_price,
    last_pay_success_price, refund_num, last_refund_num, refund_price, last_refund_price,
    category_id, store_type, store_name, province, city, create_time, update_time, warehouse_type, delivery_type
  </sql>
  <select id="queryAll" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from product_sales_overview
    <where>
      <if test="tenantId != null">
        and tenant_id = #{tenantId}
      </if>
      <if test="timeTag != null">
        and time_tag = #{timeTag}
      </if>
      <if test="timeTags != null and timeTags.size() > 0">
        and time_tag in
        <foreach collection="timeTags" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test="type != null">
        and type = #{type}
      </if>
      <if test="categoryIds != null and categoryIds.size() > 0">
        and category_id in
        <foreach collection="categoryIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test="storeTypes != null and storeTypes.size() > 0">
        and store_type in
        <foreach collection="storeTypes" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test="storeName != null">
        and store_name like concat('', #{storeName}, '%')
      </if>
    </where>
  </select>

  <select id="querySummary" resultMap="BaseResultMap">
    select sum(pay_success_num) pay_success_num,
           sum(last_success_num) last_success_num,
           sum(pay_success_price) pay_success_price,
           sum(last_pay_success_price) last_pay_success_price,
           sum(refund_num) refund_num,
           sum(last_refund_num) last_refund_num,
           sum(refund_price) refund_price,
           sum(last_refund_price) last_refund_price
    from product_sales_overview
    <where>
      <if test="tenantId != null">
        and tenant_id = #{tenantId}
      </if>
      <if test="timeTag != null">
        and time_tag = #{timeTag}
      </if>
      <if test="type != null">
        and type = #{type}
      </if>
      <if test="categoryIds != null and categoryIds.size() > 0">
        and category_id in
        <foreach collection="categoryIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test="storeTypes != null and storeTypes.size() > 0">
        and store_type in
        <foreach collection="storeTypes" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test="addressList != null and addressList.size() > 0">
        and address in
        <foreach collection="addressList" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test="storeName != null">
        and store_name like concat('%', #{storeName}, '%')
      </if>
      <if test="goodsType != null">
        and goods_type = #{goodsType}
      </if>
      <if test="title != null and title != ''">
        and title like concat('%', #{title}, '%')
      </if>
      <if test="itemId != null">
        and item_id = #{itemId}
      </if>
    </where>
  </select>

  <select id="batchQuerySummary" resultMap="BaseResultMap">
    select
    time_tag,
    sum(pay_success_num) pay_success_num,
    sum(last_success_num) last_success_num,
    sum(pay_success_price) pay_success_price,
    sum(last_pay_success_price) last_pay_success_price,
    sum(refund_num) refund_num,
    sum(last_refund_num) last_refund_num,
    sum(refund_price) refund_price,
    sum(last_refund_price) last_refund_price
    from product_sales_overview
    <where>
      <if test="tenantId != null">
        and tenant_id = #{tenantId}
      </if>
      <if test="timeTag != null">
        and time_tag = #{timeTag}
      </if>
      <if test="timeTags != null and timeTags.size() > 0">
        and time_tag in
        <foreach collection="timeTags" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test="type != null">
        and type = #{type}
      </if>
      <if test="categoryIds != null and categoryIds.size() > 0">
        and category_id in
        <foreach collection="categoryIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test="storeTypes != null and storeTypes.size() > 0">
        and store_type in
        <foreach collection="storeTypes" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test="addressList != null and addressList.size() > 0">
        and address in
        <foreach collection="addressList" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test="storeName != null">
        and store_name like concat('%', #{storeName}, '%')
      </if>
      <if test="warehouseType != null">
        and warehouse_type = #{warehouseType}
      </if>
      <if test="deliveryType != null">
        and delivery_type = #{deliveryType}
      </if>
      <if test="title != null">
        and title = #{title}
      </if>
      <if test="itemId != null">
        and item_id = #{itemId}
      </if>
      <if test="goodsType != null">
        and goods_type = #{goodsType}
      </if>
    </where>
    group by tenant_id, time_tag
  </select>
</mapper>
