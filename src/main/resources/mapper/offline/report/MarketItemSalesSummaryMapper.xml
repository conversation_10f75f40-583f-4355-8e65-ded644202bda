<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.report.mapper.MarketItemSalesSummaryMapper">

    <select id="queryByConditionWithHandler" resultType="com.cosfo.manage.report.model.po.MarketItemSalesSummary" fetchSize="1000">
        select id,
        tenant_id tenantId,
        time_tag timeTag,
        item_id itemId,
        item_code itemCode,
        item_title itemTitle,
        item_specification itemSpecification,
        ifnull(sum(total_amount), 0) totalAmount,
        ifnull(sum(total_price), 0) totalPrice,
        ifnull(sum(total_refund_price), 0) totalRefundPrice,
        ifnull(sum(total_price_deducted_refund), 0) totalPriceDeductedRefund,
        ifnull(sum(goods_total_supply_price), 0) goodsTotalSupplyPrice,
        ifnull(sum(sales_and_supply_difference_deducted_price), 0) salesAndSupplyDifferenceDeductedPrice,
        ifnull(sum(goods_refund_price), 0) goodsRefundPrice,
        ifnull(sum(goods_price_deducted_refund), 0) goodsPriceDeductedRefund,
        pay_type
        from market_item_sales_summary
        <where>
            tenant_id = #{tenantId} and time_tag between #{startTime} and #{endTime} and supplier_id = #{supplierId}
            <if test="payTypes != null and payTypes.size() >0 ">
                and pay_type in
                <foreach collection="payTypes" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
        group by tenant_id, item_id
    </select>
</mapper>
