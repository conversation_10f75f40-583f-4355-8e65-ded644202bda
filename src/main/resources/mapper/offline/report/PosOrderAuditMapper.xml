<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.report.mapper.PosOrderAuditMapper">
    <select id="listPage" resultType="com.cosfo.manage.report.model.vo.PosOrderAuditVO">
        select
        out_store_name,
        out_store_code,
        out_item_name,
        out_item_code,
        specification,
        specification_unit,
        sum(use_count) AS use_count,
        sum(need_buy_count) AS need_buy_count,
        sum(real_buy_count) AS real_buy_count,
        IF(sum(need_buy_count) - sum(real_buy_count) <![CDATA[ <= ]]> 0,0,sum(need_buy_count) - sum(real_buy_count)) AS diffCount ,
        IF(sum(need_buy_count) - sum(real_buy_count) <![CDATA[ <= ]]> 0 or sum(need_buy_count) = 0,0,round((sum(need_buy_count) - sum(real_buy_count))/sum(need_buy_count),2) * 100) AS privateProcurement,
        IF(sum(need_buy_count) - sum(real_buy_count) <![CDATA[ <= ]]> 0 or sum(need_buy_count) = 0,0,if(round((sum(need_buy_count) - sum(real_buy_count))/sum(need_buy_count),2)* 100 <![CDATA[ < ]]> #{query.privateProcurement},0,1)) as status
        FROM pos_order_audit
        <where>
            tenant_id = #{query.tenantId} and channel_type = #{query.channelType}
            <if test="query.outStoreName != null and query.outStoreName != ''">
                AND out_store_name LIKE CONCAT('%',#{query.outStoreName}, '%')
            </if>
            <if test="query.outItemName != null and query.outItemName != ''">
                AND out_item_name LIKE CONCAT('%',#{query.outItemName}, '%')
            </if>
            <if test="query.beginWeek != null and query.endWeek != null ">
                And report_week <![CDATA[>=]]> #{query.beginWeek} AND report_week <![CDATA[<=]]> #{query.endWeek}
            </if>
        </where>
            group by channel_type,tenant_id,out_store_code,out_item_code
        <if test="query.status != null">
            having `status` = #{query.status}
        </if>
    </select>
</mapper>
