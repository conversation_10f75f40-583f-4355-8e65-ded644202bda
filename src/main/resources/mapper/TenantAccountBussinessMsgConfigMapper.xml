<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.tenant.mapper.TenantAccountBussinessMsgConfigMapper">

    <resultMap id="BaseResultMap" type="com.cosfo.manage.tenant.model.po.TenantAccountBussinessMsgConfig">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="bussinessType" column="bussiness_type" jdbcType="TINYINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="tenantAccountId" column="tenant_account_id" jdbcType="BIGINT"/>
            <result property="availableStatus" column="available_status" jdbcType="TINYINT"/>
            <result property="pushHour" column="push_hour" jdbcType="TINYINT"/>
    </resultMap>

    <resultMap id="bussinessConfigMap" type="com.cosfo.manage.merchant.model.dto.TenantAccountMsgConfigDTO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="bussinessType" column="bussiness_type" jdbcType="TINYINT"/>
        <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
        <result property="tenantAccountId" column="tenant_account_id" jdbcType="BIGINT"/>
        <result property="availableStatus" column="available_status" jdbcType="TINYINT"/>
        <result property="pushHour" column="push_hour" jdbcType="TINYINT"/>
        <result property="supplierId" column="supplier_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_time,update_time,
        bussiness_type,tenant_id,tenant_account_id,
        available_status,push_hour
    </sql>

<!--    <select id="listWithReceive" resultMap="bussinessConfigMap">-->
<!--        SELECT-->
<!--        tabmc.*,tasm.supplier_id-->
<!--        FROM-->
<!--        tenant_account_bussiness_msg_config tabmc-->
<!--        INNER JOIN tenant_account_receive_msg_switch tarms ON tabmc.tenant_id = tarms.tenant_id-->
<!--        AND tabmc.tenant_account_id = tarms.tenant_account_id-->
<!--        INNER JOIN tenant_account_supplier_mapping tasm ON tabmc.tenant_id = tasm.tenant_id-->
<!--        AND tabmc.tenant_account_id = tasm.account_id-->
<!--        <where>-->
<!--            <if test="bussinessType != null">-->
<!--                and tabmc.bussiness_type = #{bussinessType}-->
<!--            </if>-->
<!--            <if test="availableStatus != null">-->
<!--                and tabmc.available_status = #{availableStatus}-->
<!--                and tarms.available_status = #{availableStatus}-->
<!--            </if>-->
<!--            <if test="pushHour != null">-->
<!--                AND tabmc.push_hour = #{pushHour}-->
<!--            </if>-->
<!--            <if test="channelCode != null">-->
<!--                AND tarms.channel_code = #{channelCode}-->
<!--            </if>-->
<!--            <if test="channelType != null">-->
<!--                tarms.channel_type = #{channelType}-->
<!--            </if>-->
<!--        </where>-->
<!--        order by tasm.supplier_id asc,tabmc.id asc-->
<!--    </select>-->
</mapper>
