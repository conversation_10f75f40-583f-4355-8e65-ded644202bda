<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.file.mapper.CommonResourceMapper">

    <resultMap type="com.cosfo.manage.file.model.po.CommonResource" id="CommonResourceMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
        <result property="resourceType" column="resource_type" jdbcType="INTEGER"/>
        <result property="resourceName" column="resource_name" jdbcType="VARCHAR"/>
        <result property="objectOssKey" column="object_oss_key" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="CommonResourceMap">
        select
          id, tenant_id, resource_type, resource_name, object_oss_key, create_time, update_time
        from common_resource
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="CommonResourceMap">
        select
          id, tenant_id, resource_type, resource_name, object_oss_key, create_time, update_time
        from common_resource
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="tenantId != null">
                and tenant_id = #{tenantId}
            </if>
            <if test="resourceType != null">
                and resource_type = #{resourceType}
            </if>
            <if test="resourceName != null and resourceName != ''">
                and resource_name = #{resourceName}
            </if>
            <if test="objectOssKey != null and objectOssKey != ''">
                and object_oss_key = #{objectOssKey}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from common_resource
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="tenantId != null">
                and tenant_id = #{tenantId}
            </if>
            <if test="resourceType != null">
                and resource_type = #{resourceType}
            </if>
            <if test="resourceName != null and resourceName != ''">
                and resource_name = #{resourceName}
            </if>
            <if test="objectOssKey != null and objectOssKey != ''">
                and object_oss_key = #{objectOssKey}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into common_resource(tenant_id, resource_type, resource_name, object_oss_key, create_time, update_time)
        values (#{tenantId}, #{resourceType}, #{resourceName}, #{objectOssKey}, #{createTime}, #{updateTime})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into common_resource(tenant_id, resource_type, resource_name, object_oss_key, create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.tenantId}, #{entity.resourceType}, #{entity.resourceName}, #{entity.objectOssKey}, #{entity.createTime}, #{entity.updateTime})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into common_resource(tenant_id, resource_type, resource_name, object_oss_key, create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.tenantId}, #{entity.resourceType}, #{entity.resourceName}, #{entity.objectOssKey}, #{entity.createTime}, #{entity.updateTime})
        </foreach>
        on duplicate key update
        tenant_id = values(tenant_id),
        resource_type = values(resource_type),
        resource_name = values(resource_name),
        object_oss_key = values(object_oss_key),
        create_time = values(create_time),
        update_time = values(update_time)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update common_resource
        <set>
            <if test="tenantId != null">
                tenant_id = #{tenantId},
            </if>
            <if test="resourceType != null">
                resource_type = #{resourceType},
            </if>
            <if test="resourceName != null and resourceName != ''">
                resource_name = #{resourceName},
            </if>
            <if test="objectOssKey != null and objectOssKey != ''">
                object_oss_key = #{objectOssKey},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from common_resource where id = #{id}
    </delete>

</mapper>

