logging:
  level:
    root: info
    org.springframework: INFO
    org.mybatis: INFO
    com.cosfo.manage: INFO
  pattern:
    console: "%d - %msg%n"
server:
  port: 8081
# 日志文件路径
log-path: ${APP_LOG_DIR:./log}
#pagehelper分页插件配置
pagehelper:
  helperDialect: mysql
  reasonable: true  # 禁用合理化时，如果pageNum<1或pageNum>pages会返回空数据
  supportMethodsArguments: true
  params: count=countSql

# 七牛云配置
qiniu:
  ACCESS_KEY: D7qgaXDWTIv0ormcO8IozO1RyT7P1YLP-y3tObIJ
  SECRET_KEY: ZLVTkrc7cwZlyNhLVieyeBwJ-UD_0p4Co9ZdSu3T
  bucketname: cosfo
  XM_BUCKETNAME: suyuan

# 数据库配置
spring:
  application:
    name: cosfo-manage
  datasource:
    hikari:
      minimum-idle: 3
      maximum-pool-size: 10
      max-lifetime: 30000   #不能小于30秒，否则默认回到1800秒
      connection-test-query: SELECT 1
    username: dev4
    password: xianmu619
    #?serverTimezone=UTC解决时区的报错
    url: *******************************************************************************************************************************************************
    # mysql5 的驱动是 com.mysql.jdbc.Driver, mysql8的驱动是 com.mysql.cj.jdbc.Driver
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 自定义数据源
    type: com.alibaba.druid.pool.DruidDataSource
    #druid 数据源专有配置
    initialSize: 5
    minIdle: 5
    maxActive: 80
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 300000
    offline:
      username: dev
      password: xianmu619
      url: *****************************************************************************************************************************************
  schedulerx2:
    endpoint: acm.aliyun.com
    namespace: a0140545-ea7f-40c0-b496-09e56fa38e54
    groupId: saas-manage
    appKey: h/ELh8jn5jQBaDs9+j35uA==
  # redis配置
  redis:
    host: test-redis.summerfarm.net
    password: xianmu619
    port: 6379
    timeout: 6000
    database: 0
    # auth服务依赖
  authRedis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 4
    jedis:
      pool:
        max-active: 5
        max-idle: 5
        min-idle: 5
        max-wait: 5
saasmall:
  api-host: http://cosfo-mall-svc

summerfarm:
  mall:
    api-host: http://mall-svc/
  api-host: http://manage-svc/

redisson:
  address: test-redis.summerfarm.net:6379
  password: xianmu619
  type: STANDALONE
  enabled: true
  database: 4

rocketmq:
  name-server: test-mq-nameserver.summerfarm.net:9876
  producer:
    enable-msg-trace: off
    group: GID_saas-manage
    send-message-timeout: 10000
    access-key: RocketmqAdmin
    secret-key: RocketmqAdmin
  consumer:
    access-key: RocketmqAdmin
    secret-key: RocketmqAdmin

#mybatis-plus:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

tenant:
  # 是否开启租户模式
  enable: true
  # 需要排除的多租户的表
  exclusionTable:
    - "system_parameters"
    - "area_city_group"
    - "area_city_group_mapping"
    - "brand_category_mapping"
    - "category"
    - "common_location_city"
    - "file_download_record"
    - "location_province"
    - "order_self_lifting"
    - "product_pricing_supply_city_mapping"
    - "sms_scene"
    - "supplier_delivery_info"
    - "system_admin"
    - "system_admin_role"
    - "system_menu"
    - "system_parameters"
    - "system_permission"
    - "system_role"
    - "system_role_menu"
    - "system_role_permission"
    - "tenant"
    - "tenant_agreement"
    - "user"
    - "wechat_authorizer"
    - "wechat_lite_config"
    - "wechat_template_package"
    - "brand"
    - "administrative_division"
    - "common_location"
    - "market_detail"
    - "market_item_detail"
    - "msg_scene"
    - "system_wechant_tp_info"
    - "product_city_stock"
    - "product_sku_order_summary"
    - "tenant_account_supplier_mapping"
    - "tenant_measure_item"
    - "trans_log_result_list"
    - "xm_contact_mapping"
    - "hui_fu_mock_refund"
    - "hui_fu_mock_transaction_summary"
    - "hui_fu_mock_account"
    - "hui_fu_mock_payment"
    - "tenant_function_set"
    - "tenant_guide_info"
  # 租户字段名称
  column: tenant_id

dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    address: nacos://test-nacos.summerfarm.net:11000
    parameters:
      namespace: cfccb911-1306-4ea7-8a9f-18436f9dd245
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: online
    timeout: 5000
    retries: 0
    telnet: ls,ps,cd,pwd,trace,count,invoke,select,status,log,help,clear,exit,shutdown
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    timeout: 10000
    check: false
tp:
  appId: wxd933d2dc3d3546cc

# 汇付配置
huifu:
  sys_id: ****************
  product_id: PAYUN
  channel_no: ********
  private_key: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAIkLjvPSkZhis7K3oFStvyrs0UXEADzLeDD5rb5j7VAVzeRA3Fx0kMBqtD+WEnqxGi0H3tM+mGRSNuc/ImF+3Iz9xAuwJCOS5g9ZDwQvwmvG9YrzDvcK1u957U53ukT3BHYt+WXZ46/Iyqsq7pSgLo/EzbMf8IfuqZqIKWHsx1YFAgMBAAECgYAMD8BdJUM7LjSynga2bTROCtnAUifTMfU6Gj94akMQsVqVpD/Aw2GaDcofbo3hzoSHQhISdYfkDHhYke3stsWiYgoDK2Cqow3BtocGSGePwFprJXWQJfKBO1ADb4zEka6q3zo9lcxsCqa+fx1G3uLIJNin3QWqOLXquo0GXOgEAQJBAMd/WNIeqKi/MNv5MtpiyIlGxOmdH7NPn7oW0exEzFeWsPLsl+******************************+XXhKmUCQQCv3BRFLKAH9aivHcVJZaqzd5VmFaGSWeZkiBLBa1i8ZneQv4rc1/p8M5Rvg2WHY+JTx7KxqygyahN3teqvx/MhAkEAruCSErbvb+URRnL/QfLACZ4wtPyYMk4FHVItuKhiXBFrkbcWOE/P0ashKEWsmZp45ufvviglR08LGbEJe2zjBQJALyQvyttLitavgUHZwPMf7zv/MH5b8X9n40sWvAKqptZQ9txhvRGoc+Lfx4TRkpmT8iF2JWpcPCdzUIPThYt0AQJAVIJZBYLsGAcmdhs22OCL0J62FVoxcYFBAKXA2en33goOpF/idpWUo3w++eZGAFqdLh5TKyJCj5xOgkVuYFuUAA==

xm:
  log:
    enable: true
    resp: true
    biz:
      enable: true
  oss:
    persistent-storage:
      bucketName: test-app-perm
      endpoint: oss-cn-hangzhou.aliyuncs.com
      innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
      domain: devossperm.summerfarm.net
    temporary-storage:
      bucketName: test-app-temp
      endpoint: oss-cn-hangzhou.aliyuncs.com
      innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
      domain: devosstemp.summerfarm.net

  downloadcenter:
    # 消费者组前缀为GID_download_center_consumergroup_
    consumer_group_suffix: cosfo-manage
    # tag前缀为tag_
    consumer_tag_suffix: cosfo-manage

H5:
  mall:
    url: https://dev4mall.cosfo.cn/index.html#/pages/loading/index?token=

nacos:
  config:
    server-addr: test-nacos.summerfarm.net:11000
    namespace: b4bafec8-bee6-4ec0-a276-1cda3ad832a8