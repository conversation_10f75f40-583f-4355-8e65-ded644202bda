server:
  port: 8081
# 数据库配置
spring:
  datasource:
    hikari:
      minimum-idle: 3
      maximum-pool-size: 10
      max-lifetime: 30000   #不能小于30秒，否则默认回到1800秒
      connection-test-query: SELECT 1
    username: test
    password: xianmu619
    #?serverTimezone=UTC解决时区的报错
    url: ********************************************************************************************************************************
    # mysql5 的驱动是 com.mysql.jdbc.Driver, mysql8的驱动是 com.mysql.cj.jdbc.Driver
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 自定义数据源
    type: com.alibaba.druid.pool.DruidDataSource
    #druid 数据源专有配置
    initialSize: 5
    minIdle: 5
    maxActive: 20
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 300000
    offline:
      username: test
      password: xianmu619
      url: *****************************************************************************************************************************************
  schedulerx2:
    endpoint: acm.aliyun.com
    namespace: 39ca7826-3009-4217-a25c-d15c6f1e7282
    groupId: saas-dev
    appKey: dUCNLzRFjU+rmBFF7QhFvA==
  # redis配置
  redis:
    host: test-redis.summerfarm.net
    password: xianmu619
    port: 6379
    timeout: 6000
    database: 0
  # auth服务依赖
  authRedis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 0
saasmall:
  api-host: https://devmall.cosfo.cn

redisson:
  address: test-redis.summerfarm.net:6379
  password: xianmu619
  type: STANDALONE
  enabled: true
  database: 0

summerfarm:
  mall:
    api-host: http://mall-svc/
  api-host: https://devadmin.summerfarm.net/
  #api-host: http://localhost:9000
rocketmq:
  #name-server: **************:9876
  #name-server: **************:9876
  name-server: dev-mq-nameserver.summerfarm.net:9876
  producer:
    enable-msg-trace: off
    group: GID_saas-mall
    send-message-timeout: 10000
    access-key: Rocketmq
    secret-key: Rocketmq
  consumer:
    access-key: Rocketmq
    secret-key: Rocketmq
#mybatis-plus:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

tenant:
  # 是否开启租户模式
  enable: true
  # 需要排除的多租户的表
  exclusionTable:
    - "system_parameters"
    - "area_city_group"
    - "area_city_group_mapping"
    - "brand_category_mapping"
    - "category"
    - "common_location_city"
    - "file_download_record"
    - "location_province"
    - "order_self_lifting"
    - "product_pricing_supply_city_mapping"
    - "sms_scene"
    - "supplier_delivery_info"
    - "system_admin"
    - "system_admin_role"
    - "system_menu"
    - "system_parameters"
    - "system_permission"
    - "system_role"
    - "system_role_menu"
    - "system_role_permission"
    - "tenant"
    - "tenant_agreement"
    - "user"
    - "wechat_authorizer"
    - "wechat_lite_config"
    - "wechat_template_package"
    - "brand"
    - "tenant_guide_info"
  # 租户字段名称
  column: tenant_id

dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    address: nacos://test-nacos.summerfarm.net:11000
    # address: nacos://127.0.0.1:11000
    #    address: nacos://*********:11000
    parameters:
      namespace: fac8164c-1da8-43d2-bf49-e187bda7fcb4
  protocol:
    id: dubbo
    name: dubbo
    port: 20881
  provider:
    version: 1.0.0
    group: george
    timeout: 5000
    retries: 0
  consumer:
    version: 1.0.0
    group: george
    retries: 0
    timeout: 50000
    check: false

tp:
  appId: wx85a9fed1e711916e

# 汇付配置
huifu:
  sys_id: 6666000124683186
  product_id: PAYUN
  channel_no: 00005016
  private_key: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAIkLjvPSkZhis7K3oFStvyrs0UXEADzLeDD5rb5j7VAVzeRA3Fx0kMBqtD+WEnqxGi0H3tM+mGRSNuc/ImF+3Iz9xAuwJCOS5g9ZDwQvwmvG9YrzDvcK1u957U53ukT3BHYt+WXZ46/Iyqsq7pSgLo/EzbMf8IfuqZqIKWHsx1YFAgMBAAECgYAMD8BdJUM7LjSynga2bTROCtnAUifTMfU6Gj94akMQsVqVpD/Aw2GaDcofbo3hzoSHQhISdYfkDHhYke3stsWiYgoDK2Cqow3BtocGSGePwFprJXWQJfKBO1ADb4zEka6q3zo9lcxsCqa+fx1G3uLIJNin3QWqOLXquo0GXOgEAQJBAMd/WNIeqKi/MNv5MtpiyIlGxOmdH7NPn7oW0exEzFeWsPLsl+******************************+XXhKmUCQQCv3BRFLKAH9aivHcVJZaqzd5VmFaGSWeZkiBLBa1i8ZneQv4rc1/p8M5Rvg2WHY+JTx7KxqygyahN3teqvx/MhAkEAruCSErbvb+URRnL/QfLACZ4wtPyYMk4FHVItuKhiXBFrkbcWOE/P0ashKEWsmZp45ufvviglR08LGbEJe2zjBQJALyQvyttLitavgUHZwPMf7zv/MH5b8X9n40sWvAKqptZQ9txhvRGoc+Lfx4TRkpmT8iF2JWpcPCdzUIPThYt0AQJAVIJZBYLsGAcmdhs22OCL0J62FVoxcYFBAKXA2en33goOpF/idpWUo3w++eZGAFqdLh5TKyJCj5xOgkVuYFuUAA==

#xm:
#  oss:
#    persistent-storage:
#      bucketName: test-app-perm
#      endpoint: oss-cn-hangzhou.aliyuncs.com
#      innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
#      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
#      accessKeySecret: ******************************
#      domain: devossperm.summerfarm.net
#    temporary-storage:
#      bucketName: test-app-temp
#      endpoint: oss-cn-hangzhou.aliyuncs.com
#      innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
#      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
#      accessKeySecret: ******************************
#      domain: devosstemp.summerfarm.net

H5:
  mall:
    url: https://devmall.confo.cn#/pages/loading/index?token=

nacos:
  config:
    server-addr: test-nacos.summerfarm.net:11000
    namespace: 19b82444-16f9-4d22-a522-b7ac6495c954