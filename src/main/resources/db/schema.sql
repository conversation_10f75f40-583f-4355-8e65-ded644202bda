drop table if exists `merchant_delivery_fee_rule`;


CREATE TABLE `merchant_delivery_fee_rule` (
                                              `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'primary key',
                                              `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                              `type` tinyint DEFAULT NULL COMMENT '0、无仓 1、三方仓 2、自营仓',
                                              `delivery_fee` decimal(10, 2) DEFAULT NULL COMMENT '运费/ 加价运费 / 实时上浮百分比',
                                              `free_delivery_price` decimal(10, 2) DEFAULT NULL COMMENT '免运费金额',
                                              `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
                                              `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT 'update time',
                                              `rule_type` tinyint DEFAULT NULL COMMENT '0,每单1,每日2,基于仓运费报价',
                                              `price_type` tinyint DEFAULT NULL COMMENT '0,固定 1实时加价 2实时上浮',
                                              `relate_number` decimal(10, 2) DEFAULT NULL COMMENT '实时加价运费，实时上浮百分比',
                                              `default_type` tinyint NOT NULL DEFAULT '1' COMMENT '是否是仓库的默认数据0:非默认类型;1:默认类型',
                                              `free_delivery_type` int NOT NULL DEFAULT '0' COMMENT '免运费规则，0金额，1数量',
                                              `free_delivery_quantity` int DEFAULT NULL COMMENT '免运费数量',
                                              `priority` int DEFAULT NULL COMMENT '优先级',
                                              `hit_item_ids` json DEFAULT NULL COMMENT '选中商品ID列表',
                                              `hit_areas` json DEFAULT NULL COMMENT '选中配送区域名称列表',
                                              PRIMARY KEY (`id`),
                                              KEY `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB;