#************H2  Begin****************
spring:
  datasource:
    hikari:
      minimum-idle: 3
      maximum-pool-size: 10
      max-lifetime: 30000   #不能小于30秒，否则默认回到1800秒
      connection-test-query: SELECT 1
    schema: classpath:db/schema.sql
    data: classpath:db/data.sql
    url: jdbc:h2:file:~/test;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=FALSE;MODE=MYSQL
    username: san
    password:
    driver-class-name: org.h2.Driver
    type: com.alibaba.druid.pool.DruidDataSource
  h2:
    console:
      enabled: true
      settings:
        web-allow-others: true
        trace: true
      path: /h2-console
mybatis:
  mapper-locations: classpath:mapper/primary/*/*xml
  type-aliases-package: com.cosfo.manage.merchant.model.po

xm:
  mybatis:
    interceptor:
      enabled: true