package com.cosfo.manage.tenant.mapper;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootTest
class TenantCompanyMapperTest {

    @Resource
    private TenantCompanyMapper tenantCompanyMapper;
    @Test
    void selectByCompanyName() {

//        List<Long> tenantIds = tenantCompanyMapper.selectByCompanyName("鲜沐");
//        System.out.println(tenantIds);
    }
}