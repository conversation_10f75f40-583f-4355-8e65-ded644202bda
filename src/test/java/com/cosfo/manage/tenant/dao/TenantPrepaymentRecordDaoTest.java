package com.cosfo.manage.tenant.dao;

import com.cosfo.manage.bill.model.dto.PrepaymentRecordQueryDTO;
import com.cosfo.manage.bill.model.vo.PrepaymentRecordTotalVO;
import com.cosfo.manage.tenant.model.po.TenantPrepaymentRecord;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class TenantPrepaymentRecordDaoTest {

    @Resource
    private TenantPrepaymentRecordDao tenantPrepaymentRecordDao;
    @Test
    void queryPrepaymentTotal() {
        PrepaymentRecordQueryDTO dto = new PrepaymentRecordQueryDTO();
        dto.setTenantId(2L);
        PrepaymentRecordTotalVO prepaymentRecordTotalVO = tenantPrepaymentRecordDao.queryPrepaymentTotal(dto);
        System.out.println(prepaymentRecordTotalVO);
    }
}