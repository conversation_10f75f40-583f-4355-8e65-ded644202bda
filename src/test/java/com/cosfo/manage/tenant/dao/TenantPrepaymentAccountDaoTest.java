package com.cosfo.manage.tenant.dao;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class TenantPrepaymentAccountDaoTest {

    @Resource
    private TenantPrepaymentAccountDao tenantPrepaymentAccountDao;

//    @Test
//    void getAllTenantId() {
//        List<Long> tenantIds = tenantPrepaymentAccountDao.getAllTenantId();
//        System.out.println(tenantIds);
//    }
}