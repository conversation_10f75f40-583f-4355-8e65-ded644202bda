package com.cosfo.manage.tenant.dao;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.manage.bill.model.dto.PrepaymentTransactionQueryDTO;
import com.cosfo.manage.bill.model.vo.PrepaymentTransactionTotalVO;
import com.cosfo.manage.tenant.model.po.TenantPrepaymentTransaction;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.time.LocalDate;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class TenantPrepaymentTransactionDaoTest {

    @Resource
    private TenantPrepaymentTransactionDao tenantPrepaymentTransactionDao;

    @Test
    void queryPrepaymentTransactionPage() {
        PrepaymentTransactionQueryDTO queryDTO = new PrepaymentTransactionQueryDTO();
        queryDTO.setStartTime(LocalDate.now());
        queryDTO.setEndTime(LocalDate.now());
        queryDTO.setTenantId(2L);
        queryDTO.setPageIndex(1);
        queryDTO.setPageSize(10);

        Page<TenantPrepaymentTransaction> tenantPrepaymentTransactionPage = tenantPrepaymentTransactionDao.queryPrepaymentTransactionPage(queryDTO);
        System.out.println(JSON.toJSONString(tenantPrepaymentTransactionPage));
    }

    @Test
    void queryPrepaymentTransactionList() {

        PrepaymentTransactionQueryDTO queryDTO = new PrepaymentTransactionQueryDTO();
        queryDTO.setStartTime(LocalDate.now());
        queryDTO.setEndTime(LocalDate.now());
        queryDTO.setTenantId(2L);

        List<TenantPrepaymentTransaction> tenantPrepaymentTransactionList = tenantPrepaymentTransactionDao.queryPrepaymentTransactionList(queryDTO);
        System.out.println(JSON.toJSONString(tenantPrepaymentTransactionList));
    }

    @Test
    void queryPrepaymentTransactionTotal() {

        PrepaymentTransactionQueryDTO queryDTO = new PrepaymentTransactionQueryDTO();
        queryDTO.setStartTime(LocalDate.now());
        queryDTO.setEndTime(LocalDate.now());
        queryDTO.setTenantId(24457L);

        PrepaymentTransactionTotalVO prepaymentTransactionTotalVO = tenantPrepaymentTransactionDao.queryPrepaymentTransactionTotal(queryDTO);
        System.out.println(JSON.toJSONString(prepaymentTransactionTotalVO));
    }
}