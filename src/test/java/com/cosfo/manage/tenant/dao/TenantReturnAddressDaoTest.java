package com.cosfo.manage.tenant.dao;

import com.cosfo.manage.order.model.vo.ReturnAddressVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 *
 * @author: xiaowk
 * @time: 2023/5/29 下午5:15
 */
@SpringBootTest
public class TenantReturnAddressDaoTest {

    @Resource
    private TenantReturnAddressDao tenantReturnAddressDao;

    @Test
    void testGet(){
        ReturnAddressVO vo = tenantReturnAddressDao.getByPrimaryKey(1L);
        System.err.println(vo);
    }

}
