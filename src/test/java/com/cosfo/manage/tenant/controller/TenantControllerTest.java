package com.cosfo.manage.tenant.controller;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 */
@SpringBootTest
@AutoConfigureMockMvc
//@WebMvcTest(TenantController.class)
class TenantControllerTest {

    @Autowired
    private MockMvc mockMvc;

//    @MockBean
//    private TenantService tenantService;

    @Test
    void querySupplierList() throws Exception {
//        when(tenantService.querySupplierList()).thenReturn(Lists.newArrayList());
        ResultActions actions = this.mockMvc.perform(post("/tenant/query/supplier").header("token", "cosfo-manage_062bb0dcfd7fedfa73c68aef55b7e332")).andExpect(status().isOk());
        actions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        actions.andDo(print());
    }
}