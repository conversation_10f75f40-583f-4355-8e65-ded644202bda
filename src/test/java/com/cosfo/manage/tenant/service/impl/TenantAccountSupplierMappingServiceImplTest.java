package com.cosfo.manage.tenant.service.impl;

import com.cosfo.manage.tenant.service.TenantAccountSupplierMappingService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class TenantAccountSupplierMappingServiceImplTest {

    @Resource
    private TenantAccountSupplierMappingService tenantAccountSupplierMappingService;
    @Test
    void queryByAuthUserId() {
        List<Long> supplierIds = tenantAccountSupplierMappingService.queryByAuthUserId(10335L);
        System.out.println(supplierIds);
    }
}