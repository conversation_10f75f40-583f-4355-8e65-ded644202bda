package com.cosfo.manage.tenant.service.impl;

import com.cosfo.manage.market.model.po.MarketItemCompositeMarket;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@RunWith(SpringRunner.class)
class MarketItemCompositeMarketServiceImplTest {

    @Resource
    private MarketItemCompositeMarketServiceImpl marketItemCompositeMarketService;

    @Test
    void queryByMarketItemIds() {
        Long tenantId = 2L;
        List<Long> marketItemIds = Arrays.asList(27285L, 27257L, 27191L);
        List<MarketItemCompositeMarket> marketItemCompositeMarkets = marketItemCompositeMarketService.queryByMarketItemIds(tenantId, marketItemIds);
        System.out.println(marketItemCompositeMarkets);
    }
}