package com.cosfo.manage.tenant.service;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.manage.tenant.model.po.TenantAuthConnection;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
@Slf4j
class TenantAuthConnectionServiceTest {

    @Resource
    private TenantAuthConnectionService tenantAuthConnectionService;

    @Test
    public void testTenantAuthConnectionService(){
        TenantAuthConnection tenantAuthConnection = tenantAuthConnectionService.selectAuthorizerByTenantId(2L);
        log.info("tenantAuthConnection:{}", tenantAuthConnection);
    }

    @Test
    void selectAuthorizerByTenantId() {

        TenantAuthConnection tenantAuthConnection = tenantAuthConnectionService.selectAuthorizerByTenantId(2L);

        TenantAuthConnection result = JSONObject.parseObject("  {\n" +
                "    \"id\": 4,\n" +
                "    \"app_id\": \"wx0234b1d4eb212e12\",\n" +
                "    \"tenant_id\": 2,\n" +
                "    \"status\": 1,\n" +
                "    \"account_type\": \"MERCHANT_ID\",\n" +
                "    \"pay_mchid\": \"**********\",\n" +
                "    \"pay_secret\": \"qwer1poiu8lkjh7nbv6vgfd5tg8kn9as\",\n" +
                "    \"pay_cert_path\": \"/usr/local/pay-cert/**********_20220929_cert\",\n" +
                "    \"create_time\": \"2022-05-23 18:41:50\",\n" +
                "    \"update_time\": \"2023-09-22 16:10:48\",\n" +
                "    \"huifu_id\": \"****************\",\n" +
                "    \"secret_key\": \"MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAKy4K3Xc84UOOCUZP3rXxgwui2aiaFtZ1sMfb7JiQYn7YmUug2ctYcXwiB6V0wWBRHgUo6SYD+iNAIHwNUT39OjowP/huovPMzuSqp6JDN5QEmhGHdv9V0DQHIjQt/kaHZReSKei8NxzM5xtmWu5GgWNVJi7UqfpWjImb7/W+Mo7AgMBAAECgYAZStrqZpGukVd9b0YRehmBXSuCuxOnFO/TIP5dU/AfAZX2FSqe6FFiCAgW2n/NVZGuN++CwdXKiyNg48kZMWpGl1Bn9fVfzSpwKKv1hkEJc58nxQ4Dn6HASP188z4DytPzq8T3R5ceSHLpTT0EPenIiRkeKklDb9YkAgDFcZ0WiQJBAON5dvBQE3ewevRsu4yszx4mdGt3TVBNZRwZGrzk4bEH6KlVeBioz7uN4Ii8Bhp8oRt99OUrl/rmZEHwjSVA4lcCQQDCYOwVlAlAsn30UZ71emU7IpKQlHQd+HEQPCSD1XbwF/pn0MYNZ7decOW+Ox6++I44jL5CvC7RhXDLAF+GTdC9AkEAlwmgkqHouzEgAslrolVf1Jod9PkrCaXJ++UjXsbuoDgrILxSWLVF8TecHc4Sk2WrJ3DzuXK/n+V4LlxFq7WwUwJAUKHIDUN1eyMP4LOjDw2QxLEYv2T1riELNcLdGtsIFZy8wSf3oEPv6vtGMl1v6aRNyuOHYUOS4FNMcMlc1uecuQJBAK4p2AQ0fMm9G1ljaTh81UcpGDw+EZsjitwi4GnGFwzzsjp+K/5BjcqiqdoJdFhHZbBS31nDGOSAp3y7vkNkskQ=\",\n" +
                "    \"public_key\": \"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCsuCt13POFDjglGT9618YMLotmomhbWdbDH2+yYkGJ+2JlLoNnLWHF8IgeldMFgUR4FKOkmA/ojQCB8DVE9/To6MD/4bqLzzM7kqqeiQzeUBJoRh3b/VdA0ByI0Lf5Gh2UXkinovDcczOcbZlruRoFjVSYu1Kn6VoyJm+/1vjKOwIDAQAB\",\n" +
                "    \"huifu_public_key\": \"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAn+dpLytQRBfIyEj1cVLTx5mt5+mhts/+Ri6p148Q9ru/k1rllr6/H5Mqjb6ITO7Fl6tBqynkJ96WWcqlBsMb0AhtWUTeAfpy3fNbFC07FbBAR3mZyyaG1QptgNlFlRMEGp16A4Fq+pE3ZVrXu3Y0BPhAoajvSQwQsM+IRn1/3yPpIioAPdL1DjwknTjAnjUudW5dFyp7z/fPgnPU+hu2HLr/OEA3F5UnNzAXX8/WULpCYJ+OkgnL8nyglNFfBNtkg31VoYTCbgyMfZUi5Le0JwE9H9Sti0sSZ9s8xUzrsXLferTBf6wST61vqv+QUOaJXXSvFwvqZpzjHG5R/NK7JwIDAQAB\",\n" +
                "    \"sync_time\": \"2023-05-24 12:00:01\",\n" +
                "    \"oa_app_id\": \"wxda8819c87e1e69c8\",\n" +
                "    \"oa_app_secret\": \"f4effe0655d1fbf8b0d45b90234c5ee9\",\n" +
                "    \"create_template_status\": 2,\n" +
                "    \"wechat_direct_switch\": 1,\n" +
                "    \"wechat_indirect_switch\": 1,\n" +
                "    \"wechat_indirect_sharing_switch\": 1,\n" +
                "    \"ali_indirect_switch\": 1,\n" +
                "    \"ali_indirect_sharing_switch\": 1\n" +
                "  }", TenantAuthConnection.class);
        assertEquals(result, tenantAuthConnection);
    }
}