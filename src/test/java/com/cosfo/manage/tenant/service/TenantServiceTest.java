package com.cosfo.manage.tenant.service;

import com.cosfo.manage.tenant.model.dto.TenantAccountDTO;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.product.model.dto.ProductPricingSupplyDTO;
import com.cosfo.manage.product.service.ProductPricingSupplyService;
import com.cosfo.manage.tenant.model.dto.RegistryDTO;
import com.cosfo.manage.tenant.model.vo.SupplierTenantVO;
import com.cosfo.manage.tenant.service.impl.TenantAccountServiceImpl;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.Arrays;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class TenantServiceTest {

    @Resource
    private ProductPricingSupplyService productPricingSupplyService;
    @Resource
    private TenantService tenantService;
    @Resource
    private TenantAccountServiceImpl tenantAccountServiceImpl;

    @Test
    void querySupplierMapWithEmptyParam() {

        Map<Long, SupplierTenantVO> longSupplierTenantVOMap = tenantService.querySupplierMap(Sets.newHashSet());
        System.out.println(longSupplierTenantVOMap);
    }

    @Test
    void queryTenantCompanyDetail() {
        tenantService.queryTenantCompanyDetail(2L);
    }

    @Test
    void modifyRegistry() {
        RegistryDTO registryDTO = new RegistryDTO();
        registryDTO.setRegistrySwitch(1);
        registryDTO.setTenantId(2L);
//        Boolean result = tenantService.modifyRegistry(registryDTO);
//        System.err.println(result);
    }


    @Resource
    private TenantAccountService tenantAccountService;

    @Test
    public void testAdd() {
        TenantAccountDTO tenantAccount = new TenantAccountDTO();
        tenantAccount.setPhone("***********");
        tenantAccount.setNickname("乔治测试");
        tenantAccount.setRoleIds(Arrays.asList(10320L, 10321L));
        tenantAccount.setTenantId(2L);
        tenantAccount.setOperatorPhone("***********");
        tenantAccount.setSupplierIds(Arrays.asList(3125L, 3100L));
        tenantAccountService.create(tenantAccount);
    }

    @Test
    public void checkHavingSuperAdmin() {
        /*Boolean aBoolean = tenantAccountServiceImpl.checkHavingSuperAdmin(99999L);
        System.out.println(aBoolean);*/
    }
}