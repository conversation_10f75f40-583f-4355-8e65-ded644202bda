package com.cosfo.manage.tenant.service.impl;

import com.cosfo.manage.bill.mapper.PaymentMapper;
import com.cosfo.manage.bill.model.po.Payment;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@RunWith(SpringRunner.class)
class MarketItemOrderSummaryServiceImplTest {

    @Resource
    private MarketItemOrderSummaryServiceImpl marketItemOrderSummaryService;
    @Resource
    private PaymentMapper paymentMapper;

    @Test
    void generateMarketItemOrderSummaryByPayment() {
        Long paymentId = 71008L;
        Payment payment = paymentMapper.selectByPrimaryKey(paymentId);
        marketItemOrderSummaryService.generateMarketItemOrderSummaryByPayment(payment);
    }

    @Test
    void initMarketItemOrderSummary() {
//        marketItemOrderSummaryService.initMarketItemOrderSummary(1);
    }
}