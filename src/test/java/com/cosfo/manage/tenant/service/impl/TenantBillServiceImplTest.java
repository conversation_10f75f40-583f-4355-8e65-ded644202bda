package com.cosfo.manage.tenant.service.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.tenant.model.input.TenantBillInput;
import com.cosfo.manage.tenant.model.po.TenantBill;
import com.cosfo.manage.tenant.service.TenantBillService;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/4/3
 */
@SpringBootTest
class TenantBillServiceImplTest {
    @Resource
    private TenantBillService tenantBillService;

    @Test
    public void testBillList() {
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(2L);
        TenantBillInput query = new TenantBillInput();
        query.setPageNum(1);
        query.setPageSize(10);
        query.setStartTime("2022-01-01 00:00:00");
        query.setEndTime("2023-04-01 00:00:00");
        tenantBillService.listPage(loginContextInfoDTO, query);
    }

    @Test
    public void testBillListDateTime() {
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(1003L);
        TenantBillInput query = new TenantBillInput();
        query.setPageNum(1);
        query.setPageSize(10);
        query.setStartTime("2023-09-14 00:00:00");
        query.setEndTime("2023-09-14 23:59:59");
        System.out.println(JSON.toJSONString(tenantBillService.listPage(loginContextInfoDTO, query)));

    }

    @Test
    public void testBillListDate() {
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(1003L);
        TenantBillInput query = new TenantBillInput();
        query.setPageNum(1);
        query.setPageSize(10);
        query.setStartTime("2023-09-14");
        query.setEndTime("2023-09-14");
        System.out.println(JSON.toJSONString(tenantBillService.listPage(loginContextInfoDTO, query)));
    }

    @Test
    public void exportTest() {
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(1003L);
        TenantBillInput query = new TenantBillInput();
        query.setStartTime("2023-09-14 00:00:00");
        query.setEndTime("2023-09-14 23:59:59");
        tenantBillService.export(loginContextInfoDTO, query);
    }


    @Test
    public void exportWithDateTest() {
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(1003L);
        TenantBillInput query = new TenantBillInput();
        query.setStartTime("2023-09-14");
        query.setEndTime("2023-09-14");
        tenantBillService.export(loginContextInfoDTO, query);
    }
}