package com.cosfo.manage.mybatis;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.exceptions.MybatisPlusException;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.mysql.cj.jdbc.MysqlDataSource;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Scanner;

/**
 * 代码生成器
 *
 * @author: Cathy
 */
public class CodeGenerator {

    private final static String templatePath = "/templates/mapper.xml.vm";
    private final static String hostIpAddress = "************";

    /**
     * <p>
     * 读取控制台内容
     * </p>
     */
    public static String scanner(String tip) {
        Scanner scanner = new Scanner(System.in);
        StringBuilder help = new StringBuilder();
        help.append("请输入" + tip + "：");
        System.out.println(help.toString());
        if (scanner.hasNext()) {
            String ipt = scanner.next();
            if (ObjectUtil.isNotNull(ipt)) {
                return ipt;
            }
        }
        throw new MybatisPlusException("请输入正确的" + tip + "！");
    }

    public static void main(String[] args) {
        String projectPath = System.getProperty("user.dir");
        MysqlDataSource mysqlDataSource = new MysqlDataSource();
        mysqlDataSource.setUrl("******************************************************************************");
        mysqlDataSource.setUser("test");
        mysqlDataSource.setPassword("xianmu619");
        //要生成的表
        List<String> list = new ArrayList<>();
        list.add("tenant_return_address");

        FastAutoGenerator.create(new DataSourceConfig.Builder(mysqlDataSource))
                .globalConfig(builder -> {
                    builder.author("xiaowk") // 设置作者
//                            .enableSwagger() // 开启 swagger 模式
                            .disableOpenDir()
                            .fileOverride() // 覆盖已生成文件
                            .outputDir(projectPath + "/" + "/src/main/java"); // 指定输出目录
                })
                .packageConfig(builder -> {
                    builder.parent("com.cosfo.manage") // 设置父包名
                            .moduleName("tenant") // 设置父包模块名
                            .entity("model.po")
                            .service("dao")
                            .serviceImpl("dao.impl")
                            .pathInfo(Collections.singletonMap(OutputFile.mapperXml, projectPath + "/" + "/src/main/resources/mapper/primary" + "/tenant")); // 设置mapperXml生成路径
                })
                .strategyConfig(builder -> {
                    builder.addInclude("tenant_return_address") // 设置需要生成的表名
                            .entityBuilder()
                            .enableLombok()
                            .serviceBuilder()
                            .formatServiceFileName("%sDao")
                            .formatServiceImplFileName("%sDaoImpl")
                    ; // 设置过滤表前缀
                })
                .templateConfig(builder -> builder.controller(""))
//                .templateEngine(new FreemarkerTemplateEngine()) // 使用Freemarker引擎模板，默认的是Velocity引擎模板
                .execute();
    }

}

