package com.cosfo.manage.financial.service.impl;

import com.cosfo.manage.bill.model.dto.TenantBillQueryDTO;
import com.cosfo.manage.bill.service.impl.FinancialTenantBillServiceImpl;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.report.model.po.OrderAfterSaleDetailSummary;
import com.cosfo.manage.report.repository.OrderAfterSaleDetailSummaryRepository;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/4/25 17:24
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class FinancialTenantBillServiceImplTest {

    @Resource
    private FinancialTenantBillServiceImpl financialTenantBillService;

    @Resource
    private OrderAfterSaleDetailSummaryRepository orderAfterSaleDetailSummaryRepository;

    @Test
    public void testExportStatement() {
        LocalDateTime startTime = LocalDateTime.of(2023, 10, 01,0,0);
        LocalDateTime endTime = LocalDateTime.of(2023, 10, 10,0,0);
        TenantBillQueryDTO tenantBillQueryDTO = new TenantBillQueryDTO();
        tenantBillQueryDTO.setTenantId(2L);
        tenantBillQueryDTO.setStartTime(startTime);
        tenantBillQueryDTO.setEndTime(endTime);
        tenantBillQueryDTO.setExportContentType(Arrays.asList(1));
        tenantBillQueryDTO.setType(1);
        tenantBillQueryDTO.setSupplierId(0L);

        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(2L);
        financialTenantBillService.export(tenantBillQueryDTO, loginContextInfoDTO);
    }

    @Test
    public void testOrderItemDetailSummarySaveBatch() {
        String data = "\"2\",\"AS1710923087879675904\",\"OR169675058020807\",\"100\",\"0\",\"2\",\"22.00\"\n" +
                "\"2\",\"AS1710922739609837568\",\"OR169675058020807\",\"10\",\"0\",\"2\",\"10.00\"";
        OrderAfterSaleDetailSummary base = new OrderAfterSaleDetailSummary();
        base.setTenantId(2L);
        base.setTimeTag("20231001");
        base.setAfterSaleOrderNo("1");
        base.setOrderNo("1");
        base.setOrderTime(LocalDateTime.now());
        base.setFinishedTime(LocalDateTime.now());
        base.setItemTitle("商品标题");
        base.setItemId(1L);
        base.setItemCode("code");
        base.setItemSpecification("商品规格");
        base.setItemAmount(10);
        base.setAfterSaleAmount(1);
        base.setAfterSaleType(0);
        base.setServiceType(1);
        base.setResponsibilityType(1);
        base.setReason("售后原因");
        base.setTotalRefundPrice(new BigDecimal(1.0));
        base.setItemRefundPrice(new BigDecimal(1.0));
        base.setDeliveryRefundFee(new BigDecimal(1.0));
        base.setGoodsType(1);
        base.setGoodsType(1);
        base.setGoodsAgentFee(new BigDecimal(1.0));
        base.setGoodsRefundPrice(new BigDecimal(1.0));
        base.setCreateTime(LocalDateTime.now());
        base.setStoreName("假数据");
        base.setPayType(1);

        base.setItemAmount(1);
        List<OrderAfterSaleDetailSummary> list = Lists.newArrayList();
        String[] dataArr = data.split("\n");
        for (String afterSale : dataArr) {
            OrderAfterSaleDetailSummary orderAfterSaleDetailSummary = new OrderAfterSaleDetailSummary();
            BeanUtils.copyProperties(base, orderAfterSaleDetailSummary);
            String[] split = afterSale.split(",");
            orderAfterSaleDetailSummary.setTenantId(Long.valueOf(split[0].replaceAll("\"", "")));
            orderAfterSaleDetailSummary.setAfterSaleOrderNo(split[1].replaceAll("\"", ""));
            orderAfterSaleDetailSummary.setOrderNo(split[2].replaceAll("\"", ""));
            orderAfterSaleDetailSummary.setAfterSaleAmount(Integer.valueOf(split[3].replaceAll("\"", "")));
            orderAfterSaleDetailSummary.setAfterSaleType(Integer.valueOf(split[4].replaceAll("\"", "")));
            orderAfterSaleDetailSummary.setServiceType(Integer.valueOf(split[5].replaceAll("\"", "")));
            orderAfterSaleDetailSummary.setTotalRefundPrice(new BigDecimal(split[6].replaceAll("\"", "")));
            list.add(orderAfterSaleDetailSummary);
        }

        orderAfterSaleDetailSummaryRepository.saveBatch(list);
    }
}
