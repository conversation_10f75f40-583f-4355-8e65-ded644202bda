package com.cosfo.manage.order.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.erp.client.aftersale.provider.OrderAfterSaleCommandProvider;
import com.cosfo.erp.client.aftersale.req.OrderCloseReq;
import com.cosfo.manage.TestApplication;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.facade.OfcAfterSaleFacade;
import com.cosfo.manage.facade.dto.FulfillmentDeliveryInfoDTO;
import com.cosfo.manage.order.model.dto.OrderAfterSaleDeliveryDTO;
import com.cosfo.manage.order.model.dto.OrderDTO;
import com.cosfo.manage.order.model.dto.aftersale.OrderAfterSaleQueryDTO;
import com.cosfo.manage.order.model.vo.aftersale.OrderAfterSaleInfoVO;
import com.cosfo.manage.order.model.vo.aftersale.OrderAfterSaleProductVO;
import com.cosfo.manage.order.service.OrderAfterSaleService;
import com.cosfo.manage.order.service.OrderBusinessService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import net.summerfarm.wnc.client.resp.WarehouseStorageResp;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;


/**
 * 描述: 单元测试
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/31
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {TestApplication.class})
class OrderAfterSaleServiceImplTest {

    @Resource
    private OrderAfterSaleService orderAfterSaleService;
    @Resource
    private OrderBusinessService orderBusinessService;
    @Resource
    private OfcAfterSaleFacade ofcAfterSaleFacade;

    @Resource
    private RedissonClient redissonClient;
    @DubboReference
    private OrderAfterSaleCommandProvider orderAfterSaleCommandProvider;

    @Test
    void testRedis() throws InterruptedException {

        for (int i=0; i<10; i++) {
            Runnable runnable = new Runnable() {
                public void run() {
                    RLock lock = redissonClient.getLock("test_order_id_aaa");
                    System.err.println("锁对象：lock=" + lock);
                    try {
                        boolean lockFlag = lock.tryLock();

                        if (!lockFlag) {
                            System.err.println("锁定失败" + Thread.currentThread().getName());
                            return;
                        }

                        System.err.println("锁定成功" + Thread.currentThread().getName());
                        try {
                            Thread.sleep(100L);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }catch (Exception e){
                        System.err.println("异常 e=" + e.getMessage());
                    } finally {
//                        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                        if (lock.isLocked()) {
                            System.err.println("解锁:" + Thread.currentThread().getName());
                            lock.unlock();
                        }
                    }
                }
            };
            new Thread(runnable).start();
        }

        Thread.sleep(1000000000L);
    }

    @Test
    void testGetReturnAddressId(){
//        Long id = orderAfterSaleMapper.getRecentlyUsedReturnAddressId(2222L);
//        System.err.println(id);
    }

    @Test
    void testCloseOrder(){
        OrderCloseReq orderCloseReq = new OrderCloseReq();
        orderCloseReq.setOrderNo("*****************");
        orderCloseReq.setTenantId(2L);
        orderCloseReq.setAccountId(-1L);
        DubboResponse dubboResponse = orderAfterSaleCommandProvider.closeOrder(orderCloseReq);
        System.err.println(JSONObject.toJSONString(dubboResponse));
        if (!dubboResponse.isSuccess()) {
            System.err.println(dubboResponse.getMsg());
        }
    }

    @Test
    void validationIsNeedRefundDeliveryFee() {

//        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
//        loginContextInfoDTO.setTenantId(1003L);
//        orderService.closeOrder(7361L, loginContextInfoDTO);
        OrderDTO orderDTO = new OrderDTO();
        orderDTO.setId(6996L);
        orderDTO.setTenantId(1003L);
        orderDTO.setOrderNo("*****************");
        orderBusinessService.selfLifting(orderDTO, true);
    }

    @Test
    void testGet1() {
        String afterSaleNo = "*****************";
        List<FulfillmentDeliveryInfoDTO> list = ofcAfterSaleFacade.queryDelivery(Lists.newArrayList(afterSaleNo));
        System.err.println(JSONObject.toJSONString(list));
    }

    @Test
    void testInsertdelivery() {
        OrderAfterSaleDeliveryDTO orderAfterSaleDeliveryDTO = new OrderAfterSaleDeliveryDTO();
        orderAfterSaleDeliveryDTO.setAfterSaleOrderNo("");
        orderAfterSaleDeliveryDTO.setDeliveryType(1);
        orderAfterSaleDeliveryDTO.setDeliveryCompany("");
        orderAfterSaleDeliveryDTO.setDeliveryNo("");
        orderAfterSaleDeliveryDTO.setRemark(null);

        ofcAfterSaleFacade.insertAfterSaleLogistics(orderAfterSaleDeliveryDTO, "aaaaa");
    }

    @Test
    void testQuerydelivery() {
        List<FulfillmentDeliveryInfoDTO> list = ofcAfterSaleFacade.queryDelivery(Arrays.asList("AS168197288914937"));
        System.err.println(JSONObject.toJSONString(list));
        OrderAfterSaleDeliveryDTO dto = new OrderAfterSaleDeliveryDTO();
        dto.setAfterSaleOrderNo("AS168197288914937");
        dto.setDeliveryType(1);
        dto.setDeliveryCompany("顺丰");
        dto.setDeliveryNo("11111111111111111111");
        ofcAfterSaleFacade.updateDeliveryInfo(dto, "xkw111");
        List<FulfillmentDeliveryInfoDTO> list2 = ofcAfterSaleFacade.queryDelivery(Arrays.asList("AS168197288914937"));
        System.err.println(JSONObject.toJSONString(list2));

    }

    @Test
    void testqueryinbound() {
        System.err.println(ofcAfterSaleFacade.queryInboundInfo("AS168267194451642"));
    }

    @Test
    void testqueryw() {
        WarehouseStorageResp warehouseStorageResp = ofcAfterSaleFacade.queryWarehouse(197);
        System.err.println(JSONObject.toJSONString(warehouseStorageResp));

    }

    @Test
    void testUpdatestatus() {
//        OrderAfterSale update = new OrderAfterSale();
//        update.setHandleRemark("1111");
//        update.setStatus(5);
//        update.setSecondHandleRemark("hhhhhhxxxxx");
//        System.err.println(afterSaleService.updateRecordByIdAndSourceStatus(297L, 6, update));
    }

    @Test
    void testUpdatedelivery() {
        OrderAfterSaleDeliveryDTO dto = new OrderAfterSaleDeliveryDTO();
        dto.setAfterSaleOrderNo("");
        dto.setDeliveryType(1);
        dto.setDeliveryCompany("");
        dto.setDeliveryNo("");
        dto.setRemark(null);

        ofcAfterSaleFacade.updateDeliveryInfo(dto, "bbbb");
    }

    @Test
    void testList() {
        OrderAfterSaleQueryDTO orderAfterSaleQueryDTO = new OrderAfterSaleQueryDTO();
        orderAfterSaleQueryDTO.setPageIndex(1);
        orderAfterSaleQueryDTO.setPageSize(10);
//        orderAfterSaleQueryDTO.setAfterSaleOrderNo("AS16584270335712378");
        //orderAfterSaleQueryDTO.setSupplierIds(Arrays.asList(2189L));
        orderAfterSaleQueryDTO.setStartTime(DateUtil.parse("2023-10-03 00:00:00"));
        orderAfterSaleQueryDTO.setEndTime(DateUtil.parse("2023-10-03 23:59:59"));
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(2L);
        loginContextInfoDTO.setAuthUserId(44L);
        CommonResult<PageInfo<OrderAfterSaleInfoVO>> orderListAfterSale = orderAfterSaleService.getOrderListAfterSale(orderAfterSaleQueryDTO, loginContextInfoDTO);
        System.out.println(JSON.toJSONString(orderListAfterSale));
    }

    @Test
    public void getOrderAfterSaleCommodityTest() {

        CommonResult<List<OrderAfterSaleProductVO>> orderAfterSaleCommodity = orderAfterSaleService.getOrderAfterSaleCommodity(95440L);
        System.out.println(JSON.toJSONString(orderAfterSaleCommodity));
    }
}
