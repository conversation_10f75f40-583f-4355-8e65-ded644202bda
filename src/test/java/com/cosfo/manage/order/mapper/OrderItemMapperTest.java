package com.cosfo.manage.order.mapper;

import com.cosfo.manage.order.model.vo.OrderItemVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class OrderItemMapperTest {

    @Resource
    private OrderItemMapper orderItemMapper;

    @Test
    void queryOrderItemVOByOrderId() {
        List<OrderItemVO> orderItemVOS = orderItemMapper.queryOrderItemVOByOrderId(6L);
        System.out.println(orderItemVOS);
    }
}