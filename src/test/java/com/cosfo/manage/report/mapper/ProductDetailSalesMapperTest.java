package com.cosfo.manage.report.mapper;

import com.cosfo.manage.product.model.dto.ProductDetailSalesDTO;
import com.cosfo.manage.product.model.dto.ProductDetailSalesQueryDTO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class ProductDetailSalesMapperTest {

    @Resource
    private ProductDetailSalesMapper productDetailSalesMapper;

    @Test
    void queryAll() {
        ProductDetailSalesQueryDTO queryDTO = new ProductDetailSalesQueryDTO();
        queryDTO.setTenantId(3L);
        queryDTO.setStartTime("20230130");
        queryDTO.setEndTime("20230201");
        List<ProductDetailSalesDTO> productDetailSalesDTOS = productDetailSalesMapper.queryAll(queryDTO);
        assertFalse(CollectionUtils.isEmpty(productDetailSalesDTOS));
    }
}