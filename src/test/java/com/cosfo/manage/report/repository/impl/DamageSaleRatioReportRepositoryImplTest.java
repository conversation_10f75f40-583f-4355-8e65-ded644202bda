package com.cosfo.manage.report.repository.impl;

import com.cosfo.manage.report.model.dto.ReportCommonQueryDTO;
import com.cosfo.manage.report.model.vo.PurchaseDetailAggVO;
import com.cosfo.manage.report.repository.DamageSaleRatioReportRepository;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class DamageSaleRatioReportRepositoryImplTest {

    @Resource
    private DamageSaleRatioReportRepository damageSaleRatioReportRepository;

    @Test
    public void damageTest() throws Exception {
        ReportCommonQueryDTO queryDTO = new ReportCommonQueryDTO();
        queryDTO.setTenantId(2L);
        PurchaseDetailAggVO damageSaleRatioDetailAgg = damageSaleRatioReportRepository.getDamageSaleRatioDetailAgg(queryDTO);
        System.out.println(damageSaleRatioDetailAgg);
    }
}