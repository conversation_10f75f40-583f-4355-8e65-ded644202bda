package com.cosfo.manage.report.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cosfo.manage.product.model.dto.ProductDetailSalesQueryDTO;
import com.cosfo.manage.product.model.po.ProductSku;
import com.cosfo.manage.product.model.po.ProductSpu;
import com.cosfo.manage.product.repository.ProductSkuRepository;
import com.cosfo.manage.product.repository.ProductSpuRepository;
import com.cosfo.manage.report.model.dto.ReportCommonQueryDTO;
import com.cosfo.manage.report.model.po.DamageDetailReport;
import com.cosfo.manage.report.model.po.DamageSaleRatioReport;
import com.cosfo.manage.report.model.po.PurchaseDetailReport;
import com.cosfo.manage.report.model.po.PurchasesBackDetailReport;
import com.cosfo.manage.report.model.vo.DamageDetailReportVO;
import com.cosfo.manage.report.model.vo.PurchaseDetailAggVO;
import com.cosfo.manage.report.repository.DamageDetailReportRepository;
import com.cosfo.manage.report.repository.DamageSaleRatioReportRepository;
import com.cosfo.manage.report.repository.PurchaseDetailReportRepository;
import com.cosfo.manage.report.repository.PurchasesBackDetailReportRepository;
import com.cosfo.manage.report.service.ReportService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import net.xianmu.common.result.CommonResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/10/13 17:44
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class ReportServiceImpl {

    @Resource
    private ReportService reportService;
    @Resource
    private DamageDetailReportRepository damageDetailReportRepository;
    @Resource
    private DamageSaleRatioReportRepository damageSaleRatioReportRepository;
    @Resource
    private PurchaseDetailReportRepository purchaseDetailReportRepository;
    @Resource
    private PurchasesBackDetailReportRepository purchasesBackDetailReportRepository;
    @Resource
    private ProductSpuRepository productSpuRepository;

    @Resource
    private ProductSkuRepository productSkuRepository;

    @Test
    public void generateProductDetailSalesExcel() {
        ProductDetailSalesQueryDTO queryDTO = new ProductDetailSalesQueryDTO();
        queryDTO.setTenantId(2L);
        queryDTO.setStartTime("20220808");
        queryDTO.setEndTime("20220830");
        reportService.generateProductDetailSalesExcel(queryDTO, 130L);
    }

    @Test
    public void queryDamageDetailTest() {
        ReportCommonQueryDTO queryDTO = new ReportCommonQueryDTO();
        queryDTO.setTenantId(1L);
        queryDTO.setPageSize(10);
        CommonResult<PageInfo<DamageDetailReportVO>> pageInfoCommonResult = reportService.queryDamageDetail(queryDTO);
        System.out.println(pageInfoCommonResult);
    }

    @Test
    public void aggTest() {
        ReportCommonQueryDTO queryDTO = new ReportCommonQueryDTO();
        queryDTO.setTenantId(2L);
        CommonResult<PurchaseDetailAggVO> purchaseDetailAggVOCommonResult = reportService.queryPurchaseDetailAgg(queryDTO);
        System.out.println(purchaseDetailAggVOCommonResult.getData());
    }

    @Test
    public void generatorPurchaseDetailReportDataTest() {
        List<ProductSku> productSkus = productSkuRepository.list(new QueryWrapper<ProductSku>().eq("tenant_id", 2).last("limit 100"));
        List<ProductSpu> productSpus = productSpuRepository.list(new QueryWrapper<ProductSpu>().eq("tenant_id", 2).last("limit 100"));
        List<String> productNames = productSpus.stream().map(ProductSpu::getTitle).collect(Collectors.toList());
        List<PurchaseDetailReport> mockData = new ArrayList<>(1000);
        List<String> purchaseNos = Lists.newArrayList("20230116459992049", "20230116459962017", "20230111459984015", "20230111459926004", "20230110459919043", "20230110459919043", "20230110459919043", "20230110459983042", "20230110459912034", "20230110459914007", "20230110459907006", "20230110459964004", "20230110459993003", "20230110459911002", "20230110459918001", "20230109459956047", "20230109459943043", "20230109459930007", "20230106459952094", "20230106459932068", "20230106459982065", "20230106459982065", "20230106459953064", "20230106459953064", "20230106459902061", "20230106459976060", "20230106459952059", "20230106459976056", "20230106459910054", "20230106459939044", "20230106459926043", "20230106459926043", "20230106459923041", "20230106459903031", "20230106459908030", "20230106459966029", "20230106459966029", "20230106459909022", "20230106459969017", "20230106459969017", "20230105459927045", "20230105459927045", "20230105459966039", "20230105459913037", "20230105459942034", "20230105459942034", "20230105459955033", "20230105459955033", "20230105459997011", "20230105459908006", "20230105459977005", "20230104459917055", "20230104459953040", "20230104459947039", "20230104459921038", "20230104459970001", "20230103459907047", "20230103459938045", "20230103459900043", "20230103459983042", "20230103459940041", "20230103459945036", "20230103459959035", "20230103459963034", "20230103459998032", "20230103459932021", "20221229462712006");
        LocalDate localDate = LocalDate.now();
        LocalDateTime localDateTime = LocalDateTime.now();
        JSONArray warehouse = JSON.parseArray("[{\"warehouseId\":1,\"warehouseName\":\"杭州总仓\"},{\"warehouseId\":2,\"warehouseName\":\"上海总仓\"},{\"warehouseId\":4,\"warehouseName\":\"河北总仓\"},{\"warehouseId\":5,\"warehouseName\":\"广东总仓\"},{\"warehouseId\":184,\"warehouseName\":\"大鹏一\"},{\"warehouseId\":8,\"warehouseName\":\"宁波总仓\"}]");
        List<String> status = Lists.newArrayList("全未入库", "部分入库", "全部入库");
        JSONArray suppliers = JSON.parseArray("[{\"supplierId\":\"2189\",\"supplierName\":\"供应商A\"},{\"supplierId\":\"2190\",\"supplierName\":\"个人供应商A\"},{\"supplierId\":\"2202\",\"supplierName\":\"中安12\"},{\"supplierId\":\"2205\",\"supplierName\":\"大安12\"},{\"supplierId\":\"2207\",\"supplierName\":\"测试供应商2\"},{\"supplierId\":\"2208\",\"supplierName\":\"测试供应商1\"},{\"supplierId\":\"2209\",\"supplierName\":\"测试供应商3\"},{\"supplierId\":\"2210\",\"supplierName\":\"李晶晶测试修改1\"}]");
        for (int i = 0; i < 1000; i++) {
            PurchaseDetailReport report = new PurchaseDetailReport();
            report.setPurchaseDate(localDate.minusDays(rd(100)));
            report.setPurchaseNo(purchaseNos.get(rd(purchaseNos.size())));
            report.setPurchaser(productNames.get(rd(productNames.size())));
            int warehouseRandom = rd(warehouse.size());
            report.setWarehouse(warehouse.getJSONObject(warehouseRandom).getString("warehouseName"));
            report.setWarehouseId(warehouse.getJSONObject(warehouseRandom).getLong("warehouseId"));
            report.setPurchaseStatus(status.get(rd(status.size())));
            int sku = rd(productSkus.size());
            report.setSkuId(productSkus.get(sku).getId());
            int spu = rd(productSpus.size());
            report.setSpuId(productSpus.get(spu).getId());
            report.setName(productSpus.get(spu).getTitle());
            int supplier = rd(suppliers.size());
            report.setSupplier(suppliers.getJSONObject(supplier).getString("supplierName"));
            report.setSupplierId(suppliers.getJSONObject(supplier).getIntValue("supplierId"));
            report.setSpecification(productSkus.get(sku).getSpecification());
            report.setUnit(productSkus.get(sku).getSpecificationUnit());
            report.setInboundStatus(status.get(rd(status.size())));
            report.setInboundDate(localDateTime.minusDays(rd(100)+1));
            report.setPurchaseAmount(BigDecimal.valueOf(rd(100)+1));
            report.setInboundAmount(BigDecimal.valueOf(rd(100)+1));
            report.setPurchaseQuantity(rd(100)+1);
            report.setInboundQuantity(rd(100)+1);
            report.setTenantId(2L);
            report.setCategoryId(productSpus.get(spu).getCategoryId().intValue());
            mockData.add(report);
        }
        purchaseDetailReportRepository.saveBatch(mockData,200);
    }

    @Test
    public void generatorPurchaseBackDetailReportDataTest() {
        JSONArray purchaseReturnList = JSON.parseArray("[{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-11 21:53:22\",\"auditor\":\"13732211112\",\"expectTime\":\"2023-01-11 00:00:00\",\"id\":3337,\"purchasesBackNo\":\"0202301116743403\",\"status\":2,\"stockState\":0,\"storeNo\":1,\"supplier\":\"供应商A\",\"tenantName\":\"13732211112\",\"type\":1,\"updatetime\":\"2023-01-11 21:53:22\",\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-10 18:46:08\",\"auditor\":\"系统默认\",\"id\":3300,\"purchasesBackNo\":\"0202301104436886\",\"status\":2,\"storeNo\":1,\"supplier\":\"供应商A\",\"type\":0,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-10 17:07:22\",\"auditor\":\"系统默认\",\"id\":3285,\"purchasesBackNo\":\"0202301102544526\",\"status\":2,\"storeNo\":1,\"supplier\":\"测试供应商1\",\"type\":0,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-10 17:02:46\",\"auditor\":\"系统默认\",\"id\":3284,\"purchasesBackNo\":\"0202301100004120\",\"status\":2,\"storeNo\":1,\"supplier\":\"测试供应商2\",\"type\":0,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-10 17:01:40\",\"auditor\":\"系统默认\",\"id\":3283,\"purchasesBackNo\":\"0202301108533587\",\"status\":2,\"storeNo\":1,\"supplier\":\"个人供应商A\",\"type\":0,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-06 18:50:00\",\"auditor\":\"系统默认\",\"id\":3240,\"purchasesBackNo\":\"0202301062333367\",\"status\":2,\"storeNo\":1,\"supplier\":\"舒凡专用\",\"type\":0,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-06 16:46:31\",\"auditor\":\"系统默认\",\"expectTime\":\"2023-01-06 00:00:00\",\"id\":3227,\"purchasesBackNo\":\"0202301064552616\",\"status\":2,\"stockState\":2,\"storeNo\":1,\"supplier\":\"测试供应商2\",\"type\":1,\"updatetime\":\"2023-01-06 16:49:09\",\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-06 16:42:59\",\"auditor\":\"系统默认\",\"id\":3226,\"purchasesBackNo\":\"0202301062575502\",\"status\":2,\"storeNo\":1,\"supplier\":\"测试供应商2\",\"type\":0,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-06 16:34:18\",\"auditor\":\"系统默认\",\"id\":3223,\"purchasesBackNo\":\"0202301068420058\",\"status\":2,\"storeNo\":1,\"supplier\":\"舒凡专用\",\"type\":0,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-06 16:30:37\",\"auditor\":\"朱永林测试商城\",\"id\":3222,\"purchasesBackNo\":\"0202301064216473\",\"status\":2,\"storeNo\":1,\"supplier\":\"舒凡专用\",\"type\":0,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-06 16:28:40\",\"auditor\":\"朱永林测试商城\",\"id\":3221,\"purchasesBackNo\":\"0202301063077122\",\"status\":2,\"storeNo\":1,\"supplier\":\"舒凡专用\",\"type\":0,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-06 16:28:19\",\"auditor\":\"朱永林测试商城\",\"id\":3220,\"purchasesBackNo\":\"0202301064043660\",\"status\":2,\"storeNo\":1,\"supplier\":\"舒凡专用\",\"type\":0,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-06 16:27:54\",\"auditor\":\"朱永林测试商城\",\"id\":3219,\"purchasesBackNo\":\"0202301067231405\",\"status\":2,\"storeNo\":1,\"supplier\":\"舒凡专用\",\"type\":0,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-06 16:26:04\",\"auditor\":\"朱永林测试商城\",\"id\":3218,\"purchasesBackNo\":\"0202301064714364\",\"status\":2,\"storeNo\":1,\"supplier\":\"舒凡专用\",\"type\":0,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-06 16:18:16\",\"auditor\":\"朱永林测试商城\",\"expectTime\":\"2023-01-06 00:00:00\",\"id\":3216,\"purchasesBackNo\":\"0202301062064418\",\"status\":2,\"storeNo\":1,\"supplier\":\"舒凡专用\",\"type\":1,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-06 16:15:39\",\"auditor\":\"朱永林测试商城\",\"expectTime\":\"2023-01-14 00:00:00\",\"id\":3213,\"purchasesBackNo\":\"0202301060541771\",\"status\":2,\"storeNo\":1,\"supplier\":\"舒凡专用\",\"type\":1,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-06 16:05:35\",\"auditor\":\"朱永林测试商城\",\"id\":3211,\"purchasesBackNo\":\"0202301060700641\",\"status\":2,\"storeNo\":1,\"supplier\":\"舒凡专用\",\"type\":0,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-06 15:35:31\",\"auditor\":\"朱永林测试商城\",\"expectTime\":\"2023-01-06 00:00:00\",\"id\":3206,\"purchasesBackNo\":\"0202301067634605\",\"status\":2,\"storeNo\":1,\"supplier\":\"大安12\",\"type\":1,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-06 15:19:33\",\"auditor\":\"朱永林测试商城\",\"id\":3204,\"purchasesBackNo\":\"0202301063067642\",\"status\":2,\"storeNo\":1,\"supplier\":\"舒凡专用\",\"type\":0,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-06 14:32:00\",\"auditor\":\"朱永林测试商城\",\"expectTime\":\"2023-01-06 00:00:00\",\"id\":3201,\"purchasesBackNo\":\"0202301062138727\",\"status\":2,\"storeNo\":1,\"supplier\":\"个人供应商A\",\"type\":1,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-06 14:30:31\",\"auditor\":\"朱永林测试商城\",\"id\":3200,\"purchasesBackNo\":\"0202301061852401\",\"status\":2,\"storeNo\":1,\"supplier\":\"舒凡专用\",\"type\":0,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-06 11:19:34\",\"auditor\":\"朱永林测试商城\",\"id\":3197,\"purchasesBackNo\":\"0202301062656825\",\"status\":2,\"storeNo\":1,\"supplier\":\"舒凡专用\",\"type\":0,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-06 11:09:20\",\"auditor\":\"朱永林测试商城\",\"id\":3194,\"purchasesBackNo\":\"0202301064661246\",\"status\":2,\"storeNo\":1,\"supplier\":\"舒凡专用\",\"type\":0,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-05 18:32:30\",\"auditor\":\"朱永林测试商城\",\"id\":3185,\"purchasesBackNo\":\"0202301058861177\",\"status\":2,\"storeNo\":1,\"supplier\":\"供应商3\",\"type\":0,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-05 13:51:43\",\"auditor\":\"朱永林测试商城\",\"id\":3179,\"purchasesBackNo\":\"0202301050030635\",\"status\":2,\"storeNo\":5,\"supplier\":\"供应商A\",\"type\":0,\"warehouseName\":\"广东总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-05 11:58:47\",\"auditor\":\"朱永林测试商城\",\"id\":3178,\"purchasesBackNo\":\"0202301050663816\",\"status\":2,\"storeNo\":5,\"supplier\":\"供应商A\",\"type\":0,\"warehouseName\":\"广东总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-05 11:51:10\",\"auditor\":\"朱永林测试商城\",\"id\":3174,\"purchasesBackNo\":\"0202301050212475\",\"status\":2,\"storeNo\":5,\"supplier\":\"供应商A\",\"type\":0,\"warehouseName\":\"广东总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-05 11:02:18\",\"auditor\":\"朱永林测试商城\",\"id\":3171,\"purchasesBackNo\":\"0202301056370115\",\"status\":2,\"storeNo\":1,\"supplier\":\"供应商A\",\"type\":0,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-04 16:50:56\",\"auditor\":\"朱永林测试商城\",\"expectTime\":\"2022-12-28 00:00:00\",\"id\":3158,\"purchasesBackNo\":\"0202301046018113\",\"status\":2,\"storeNo\":1,\"supplier\":\"供应商A\",\"type\":1,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-04 16:49:11\",\"auditor\":\"朱永林测试商城\",\"id\":3157,\"purchasesBackNo\":\"0202301045823523\",\"status\":2,\"storeNo\":1,\"supplier\":\"供应商A\",\"type\":1,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-04 16:48:46\",\"auditor\":\"朱永林测试商城\",\"expectTime\":\"2023-01-04 00:00:00\",\"id\":3156,\"purchasesBackNo\":\"0202301046710252\",\"status\":2,\"storeNo\":1,\"supplier\":\"供应商A\",\"type\":1,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-04 16:39:30\",\"auditor\":\"朱永林测试商城\",\"id\":3155,\"purchasesBackNo\":\"0202301041820184\",\"status\":2,\"storeNo\":1,\"supplier\":\"供应商A\",\"type\":1,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-04 16:10:27\",\"auditor\":\"朱永林测试商城\",\"id\":3154,\"purchasesBackNo\":\"0202301044000521\",\"status\":2,\"storeNo\":1,\"supplier\":\"供应商A\",\"type\":0,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-04 16:09:22\",\"auditor\":\"朱永林测试商城\",\"id\":3153,\"purchasesBackNo\":\"0202301042552750\",\"status\":2,\"storeNo\":1,\"supplier\":\"供应商A\",\"type\":0,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-04 16:05:52\",\"auditor\":\"朱永林测试商城\",\"id\":3151,\"purchasesBackNo\":\"0202301044618601\",\"status\":2,\"storeNo\":1,\"supplier\":\"供应商A\",\"type\":0,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-04 15:59:39\",\"auditor\":\"朱永林测试商城\",\"id\":3149,\"purchasesBackNo\":\"0202301044371045\",\"status\":2,\"storeNo\":1,\"supplier\":\"供应商A\",\"type\":0,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-04 15:58:42\",\"auditor\":\"朱永林测试商城\",\"id\":3148,\"purchasesBackNo\":\"0202301040344850\",\"status\":2,\"storeNo\":1,\"supplier\":\"供应商A\",\"type\":0,\"warehouseName\":\"杭州总仓\"},{\"adminName\":\"saas价格同步品牌方\",\"auditTime\":\"2023-01-04 15:44:02\",\"auditor\":\"朱永林测试商城\",\"id\":3144,\"purchasesBackNo\":\"0202301047547282\",\"status\":2,\"storeNo\":1,\"supplier\":\"个人供应商A\",\"type\":0,\"warehouseName\":\"杭州总仓\"}]");

        List<ProductSku> productSkus = productSkuRepository.list(new QueryWrapper<ProductSku>().eq("tenant_id", 2).last("limit 100"));
        List<ProductSpu> productSpus = productSpuRepository.list(new QueryWrapper<ProductSpu>().eq("tenant_id", 2).last("limit 100"));
        List<PurchasesBackDetailReport> mockData = new ArrayList<>(1000);
        LocalDate localDate = LocalDate.now();
        LocalDateTime localDateTime = LocalDateTime.now();
        JSONArray warehouse = JSON.parseArray("[{\"warehouseId\":1,\"warehouseName\":\"杭州总仓\"},{\"warehouseId\":2,\"warehouseName\":\"上海总仓\"},{\"warehouseId\":4,\"warehouseName\":\"河北总仓\"},{\"warehouseId\":5,\"warehouseName\":\"广东总仓\"},{\"warehouseId\":184,\"warehouseName\":\"大鹏一\"},{\"warehouseId\":8,\"warehouseName\":\"宁波总仓\"}]");
        List<String> status = Lists.newArrayList("全未入库", "部分入库", "全部入库");
        JSONArray suppliers = JSON.parseArray("[{\"supplierId\":\"2189\",\"supplierName\":\"供应商A\"},{\"supplierId\":\"2190\",\"supplierName\":\"个人供应商A\"},{\"supplierId\":\"2202\",\"supplierName\":\"中安12\"},{\"supplierId\":\"2205\",\"supplierName\":\"大安12\"},{\"supplierId\":\"2207\",\"supplierName\":\"测试供应商2\"},{\"supplierId\":\"2208\",\"supplierName\":\"测试供应商1\"},{\"supplierId\":\"2209\",\"supplierName\":\"测试供应商3\"},{\"supplierId\":\"2210\",\"supplierName\":\"李晶晶测试修改1\"}]");
        for (int i = 0; i < 1000; i++) {
            PurchasesBackDetailReport report = new PurchasesBackDetailReport();
            JSONObject purchaseReturn = purchaseReturnList.getJSONObject(rd(purchaseReturnList.size()));
            report.setBackDate(localDate.minusDays(rd(100)));
            report.setBackNo(purchaseReturn.getString("purchasesBackNo"));
            report.setBackType(purchaseReturn.getIntValue("type"));
            report.setOperator(purchaseReturn.getString("adminName"));
            report.setPurchaser(purchaseReturn.getString("adminName"));
            int sku = rd(productSkus.size());
            report.setSkuId(productSkus.get(sku).getId());
            int spu = rd(productSpus.size());
            report.setSpuId(productSpus.get(spu).getId());
            report.setName(productSpus.get(spu).getTitle());
            report.setSpecification(productSkus.get(sku).getSpecification());
            report.setUnit(productSkus.get(sku).getSpecificationUnit());
            report.setQualityDate(localDate.minusDays(rd(100)));
            report.setProductionDate(localDate.minusDays(rd(100)));
            int warehouseRandom = rd(warehouse.size());
            report.setBackWarehouse(warehouse.getJSONObject(warehouseRandom).getString("warehouseName"));
            report.setBackWarehouseId(warehouse.getJSONObject(warehouseRandom).getLong("warehouseId"));
            report.setOutboundStatus(status.get(rd(status.size())));
            report.setBackAmount(BigDecimal.valueOf(rd(100)+1));
            report.setBackQuantity(rd(100)+1);
            report.setTenantId(2L);
            report.setCategoryId(productSpus.get(spu).getCategoryId().intValue());
            mockData.add(report);
        }
        purchasesBackDetailReportRepository.saveBatch(mockData,500);

    }

    @Test
    public void generatorDamageDetailReportDataTest() {
        List<ProductSku> productSkus = productSkuRepository.list(new QueryWrapper<ProductSku>().eq("tenant_id", 2).last("limit 100"));
        List<ProductSpu> productSpus = productSpuRepository.list(new QueryWrapper<ProductSpu>().eq("tenant_id", 2).last("limit 100"));
        List<String> productNames = productSpus.stream().map(ProductSpu::getTitle).collect(Collectors.toList());
        List<DamageDetailReport> mockData = new ArrayList<>(1000);
        List<String> purchaseNos = Lists.newArrayList("20230116459992049", "20230116459962017", "20230111459984015", "20230111459926004", "20230110459919043", "20230110459919043", "20230110459919043", "20230110459983042", "20230110459912034", "20230110459914007", "20230110459907006", "20230110459964004", "20230110459993003", "20230110459911002", "20230110459918001", "20230109459956047", "20230109459943043", "20230109459930007", "20230106459952094", "20230106459932068", "20230106459982065", "20230106459982065", "20230106459953064", "20230106459953064", "20230106459902061", "20230106459976060", "20230106459952059", "20230106459976056", "20230106459910054", "20230106459939044", "20230106459926043", "20230106459926043", "20230106459923041", "20230106459903031", "20230106459908030", "20230106459966029", "20230106459966029", "20230106459909022", "20230106459969017", "20230106459969017", "20230105459927045", "20230105459927045", "20230105459966039", "20230105459913037", "20230105459942034", "20230105459942034", "20230105459955033", "20230105459955033", "20230105459997011", "20230105459908006", "20230105459977005", "20230104459917055", "20230104459953040", "20230104459947039", "20230104459921038", "20230104459970001", "20230103459907047", "20230103459938045", "20230103459900043", "20230103459983042", "20230103459940041", "20230103459945036", "20230103459959035", "20230103459963034", "20230103459998032", "20230103459932021", "20221229462712006");
        LocalDate localDate = LocalDate.now();
        JSONArray warehouse = JSON.parseArray("[{\"warehouseId\":1,\"warehouseName\":\"杭州总仓\"},{\"warehouseId\":2,\"warehouseName\":\"上海总仓\"},{\"warehouseId\":4,\"warehouseName\":\"河北总仓\"},{\"warehouseId\":5,\"warehouseName\":\"广东总仓\"},{\"warehouseId\":184,\"warehouseName\":\"大鹏一\"},{\"warehouseId\":8,\"warehouseName\":\"宁波总仓\"}]");
        List<String> status = Lists.newArrayList("采购部-品质问题", "配-破损", "配-短少", "配-失温", "配-其它", "退货货损", "运营部-滞销过期", "运营部-商品下架", "过期货损", "货检货损", "调拨货损", "规格货损", "干-破损", "干-短少", "干-失温", "干-其它", "商品下架货损", "司机买赔", "变质货损", "分包货损", "其他", "仓-过期", "仓-质检", "仓-破损", "仓-物流加工", "仓-品质问题", "仓-其它", "不可抗力-自然灾害", "不可抗力-疫情", "不可抗力-客户原因", "不可抗力-其它");
        for (int i = 0; i < 1000; i++) {
            DamageDetailReport report = new DamageDetailReport();
            report.setOutboundDate(localDate.minusDays(rd(100)));
            report.setDamageNo(purchaseNos.get(rd(purchaseNos.size())));
            int warehouseRandom = rd(warehouse.size());
            report.setWarehouse(warehouse.getJSONObject(warehouseRandom).getString("warehouseName"));
            report.setWarehouseId(warehouse.getJSONObject(warehouseRandom).getLong("warehouseId"));
            int sku = rd(productSkus.size());
            report.setSkuId(productSkus.get(sku).getId());
            int spu = rd(productSpus.size());
            report.setSpuId(productSpus.get(spu).getId());
            report.setName(productSpus.get(spu).getTitle());
            report.setSpecification(productSkus.get(sku).getSpecification());
            report.setUnit(productSkus.get(sku).getSpecificationUnit());
            report.setPurchaser(productNames.get(rd(productNames.size())));
            report.setDamageAmount(BigDecimal.valueOf(rd(100)+1));
            report.setDamageQuantity(rd(100)+1);
            report.setDamageType(status.get(rd(status.size())));
            report.setCredentials("reasonImages/816f1owxe8ohqxh1r.jpg");
            report.setTenantId(2L);
            report.setCategoryId(productSpus.get(spu).getCategoryId().intValue());
            mockData.add(report);
        }
        damageDetailReportRepository.saveBatch(mockData,500);
    }

    @Test
    public void generatorDamageRaitoDetailReportDataTest() {
        List<ProductSku> productSkus = productSkuRepository.list(new QueryWrapper<ProductSku>().eq("tenant_id", 2).last("limit 100"));
        List<ProductSpu> productSpus = productSpuRepository.list(new QueryWrapper<ProductSpu>().eq("tenant_id", 2).last("limit 100"));
        List<String> productNames = productSpus.stream().map(ProductSpu::getTitle).collect(Collectors.toList());
        List<DamageSaleRatioReport> mockData = new ArrayList<>(1000);
        LocalDate localDate = LocalDate.now();
        JSONArray warehouse = JSON.parseArray("[{\"warehouseId\":1,\"warehouseName\":\"杭州总仓\"},{\"warehouseId\":2,\"warehouseName\":\"上海总仓\"},{\"warehouseId\":4,\"warehouseName\":\"河北总仓\"},{\"warehouseId\":5,\"warehouseName\":\"广东总仓\"},{\"warehouseId\":184,\"warehouseName\":\"大鹏一\"},{\"warehouseId\":8,\"warehouseName\":\"宁波总仓\"}]");
        for (int i = 0; i < 1000; i++) {
            DamageSaleRatioReport report = new DamageSaleRatioReport();
            report.setDamageDate(localDate.minusDays(rd(100)));
            int sku = rd(productSkus.size());
            report.setSkuId(productSkus.get(sku).getId());
            int spu = rd(productSpus.size());
            report.setSpuId(productSpus.get(spu).getId());
            report.setName(productSpus.get(spu).getTitle());
            report.setSpecification(productSkus.get(sku).getSpecification());
            report.setUnit(productSkus.get(sku).getSpecificationUnit());
            int warehouseRandom = rd(warehouse.size());
            report.setWarehouse(warehouse.getJSONObject(warehouseRandom).getString("warehouseName"));
            report.setWarehouseId(warehouse.getJSONObject(warehouseRandom).getLong("warehouseId"));
            report.setPurchaser(productNames.get(rd(productNames.size())));
            report.setDamageAmount(BigDecimal.valueOf(rd(10)+1));
            report.setDamageQuantity(rd(100)+1);
            report.setOutboundAmount(BigDecimal.valueOf(rd(1000)+1));
            report.setOutboundQuantity(rd(100)+1);
            report.setBackAmount(BigDecimal.valueOf(rd(10)+1));
            report.setBackQuantity(rd(100)+1);
            report.setDamageSaleRatio(report.getDamageAmount().add(report.getBackAmount()).multiply(BigDecimal.valueOf(100)).divide(report.getOutboundAmount(), 2, RoundingMode.HALF_UP));
            System.out.println(report.getDamageSaleRatio());
            report.setTenantId(2L);
            report.setCategoryId(productSpus.get(spu).getCategoryId().intValue());
            mockData.add(report);
        }
        damageSaleRatioReportRepository.saveBatch(mockData,500);
    }

    private int rd(int max) {
        return (int)(Math.random() * max);
    }

}
