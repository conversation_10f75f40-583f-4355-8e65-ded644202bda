package com.cosfo.manage.report.service.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.report.model.dto.PurchaseSummarySkuQueryDTO;
import com.cosfo.manage.report.model.dto.PurchaseSummarySupplierQueryDTO;
import com.cosfo.manage.report.model.vo.PurchaseSummarySkuAggVO;
import com.cosfo.manage.report.model.vo.PurchaseSummarySkuVO;
import com.cosfo.manage.report.model.vo.PurchaseSummarySupplierAggVO;
import com.cosfo.manage.report.model.vo.PurchaseSummarySupplierVO;
import com.cosfo.manage.report.service.PurchaseSummaryReportService;
import com.github.pagehelper.PageInfo;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
class PurchaseSummaryReportServiceImplTest {

    @Resource
    private PurchaseSummaryReportService purchaseSummaryReportService;

    @Test
    void queryPurchaseSummaryAggBySku() {
        PurchaseSummarySkuQueryDTO queryDTO = new PurchaseSummarySkuQueryDTO();
        queryDTO.setTenantId(2L);
        PurchaseSummarySkuAggVO purchaseSummarySkuAggVO = purchaseSummaryReportService.queryPurchaseSummaryAggBySku(queryDTO);
        System.err.println(JSON.toJSONString(purchaseSummarySkuAggVO));
    }

    @Test
    void queryPurchaseSummaryListBySku() {
        PurchaseSummarySkuQueryDTO queryDTO = new PurchaseSummarySkuQueryDTO();
        queryDTO.setPageIndex(1);
        queryDTO.setPageSize(10);

        queryDTO.setTenantId(2L);
        PageInfo<PurchaseSummarySkuVO> page = purchaseSummaryReportService.queryPurchaseSummaryListBySku(queryDTO);
        System.err.println(JSON.toJSONString(page));
    }

    @Test
    void queryPurchaseSummaryAggBySupplier() {
        PurchaseSummarySupplierQueryDTO queryDTO = new PurchaseSummarySupplierQueryDTO();
        queryDTO.setTenantId(2L);
        PurchaseSummarySupplierAggVO vo = purchaseSummaryReportService.queryPurchaseSummaryAggBySupplier(queryDTO);
        System.err.println(JSON.toJSONString(vo));
    }

    @Test
    void queryPurchaseSummaryListBySupplier() {
        PurchaseSummarySupplierQueryDTO queryDTO = new PurchaseSummarySupplierQueryDTO();
        queryDTO.setPageIndex(1);
        queryDTO.setPageSize(10);

        queryDTO.setTenantId(2L);
        PageInfo<PurchaseSummarySupplierVO> page = purchaseSummaryReportService.queryPurchaseSummaryListBySupplier(queryDTO);
        System.err.println(JSON.toJSONString(page));
    }
}