package com.cosfo.manage.report.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.manage.report.model.dto.PosOrderAuditQueryDTO;
import com.cosfo.manage.report.model.vo.PosOrderAuditVO;
import com.cosfo.manage.report.repository.PosOrderAuditRepository;
import com.github.pagehelper.PageInfo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
public class PosOrderAuditServiceTest {
    @Resource
    private PosOrderAuditService posOrderAuditService;
    @Autowired
    private PosOrderAuditRepository posOrderAuditRepository;

    @Test
    void listPage(){
        PosOrderAuditQueryDTO dto = new PosOrderAuditQueryDTO ();
        dto.setTenantId(2L);
        dto.setOutStoreName("a");
        dto.setChannelType(1);
//        dto.setBeginWeek();
//        dto.setEndWeek();
        dto.setOutItemName("a");
        dto.setStatus(0);
//        dto.setPrivateProcurement();
        dto.setPageIndex(1);
        dto.setPageSize(10);
        PageInfo<PosOrderAuditVO> posOrderAuditVOPageInfo = posOrderAuditService.listPage (dto);
        System.out.println (posOrderAuditVOPageInfo);
    }

    @Test
    void listPagedao(){
        PosOrderAuditQueryDTO dto = new PosOrderAuditQueryDTO ();
        dto.setTenantId(2L);
        dto.setChannelType(2);
        dto.setPageIndex(1);
        dto.setPageSize(1);
        Page<PosOrderAuditVO> posOrderAuditVOIPage = posOrderAuditRepository.listPage (dto);
        System.out.println (JSON.toJSONString(posOrderAuditVOIPage.getRecords()));
    }

}
