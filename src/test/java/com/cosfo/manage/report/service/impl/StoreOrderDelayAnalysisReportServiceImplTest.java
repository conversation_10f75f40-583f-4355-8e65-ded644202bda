package com.cosfo.manage.report.service.impl;

import com.cosfo.manage.report.model.dto.StoreOrderDelayAnalysisInput;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
class StoreOrderDelayAnalysisReportServiceImplTest {

    @Resource
    private StoreOrderDelayAnalysisReportServiceImpl storeOrderDelayAnalysisReportService;

    @Test
    void queryList() {
        StoreOrderDelayAnalysisInput input = new StoreOrderDelayAnalysisInput();
        input.setPageIndex(1);
        input.setPageSize(10);
        input.setTenantId(2L);
        storeOrderDelayAnalysisReportService.queryList(input);
    }

    @Test
    void generateStoreOrderDelayAnalysisReport(){
        StoreOrderDelayAnalysisInput input = new StoreOrderDelayAnalysisInput();
        input.setTenantId(2L);
        storeOrderDelayAnalysisReportService.generateStoreOrderDelayAnalysisReport(input);
    }
}