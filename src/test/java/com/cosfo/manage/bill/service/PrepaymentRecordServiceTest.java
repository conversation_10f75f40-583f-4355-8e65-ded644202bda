package com.cosfo.manage.bill.service;

import com.cosfo.manage.bill.model.dto.PrepaymentRecordDTO;
import com.cosfo.manage.bill.model.dto.PrepaymentRecordQueryDTO;
import com.cosfo.manage.bill.model.vo.PrepaymentRecordTotalVO;
import com.cosfo.manage.bill.model.vo.PrepaymentRecordVO;
import com.github.pagehelper.PageInfo;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class PrepaymentRecordServiceTest {

    @Resource
    private PrepaymentRecordService prepaymentRecordService;

    @Test
    void addPrepaymentRecord() {
        PrepaymentRecordDTO recordDTO = new PrepaymentRecordDTO();
        recordDTO.setTenantId(2L);
        recordDTO.setSupplierTenantId(1L);
        recordDTO.setType(2);
        recordDTO.setAmount(BigDecimal.valueOf(100));
        recordDTO.setPayTime(LocalDateTime.now());
        recordDTO.setProof("/test.png");
        recordDTO.setRemark("test");
        prepaymentRecordService.addPrepaymentRecord(recordDTO);
    }

    @Test
    void getPrepaymentRecord() {
        PrepaymentRecordVO prepaymentRecord = prepaymentRecordService.getPrepaymentRecord(1L);
        System.out.println(prepaymentRecord);
        assertNotNull(prepaymentRecord);
    }

    @Test
    void queryPage() {
        PrepaymentRecordQueryDTO queryDTO = new PrepaymentRecordQueryDTO();
        queryDTO.setPageSize(10);
        queryDTO.setPageIndex(1);
        queryDTO.setDateStart(LocalDate.now());
        queryDTO.setDateEnd(LocalDate.now());
        queryDTO.setTenantId(2L);
        PageInfo<PrepaymentRecordVO> prepaymentRecordVOPageInfo = prepaymentRecordService.queryPrepaymentRecordPage(queryDTO);
        System.out.println(prepaymentRecordVOPageInfo);
        assertNotNull(prepaymentRecordVOPageInfo);
        assertEquals(prepaymentRecordVOPageInfo.getTotal(), 1);
    }

    @Test
    void queryTotal() {
        PrepaymentRecordQueryDTO queryDTO = new PrepaymentRecordQueryDTO();
        PrepaymentRecordTotalVO prepaymentRecordTotalVO = prepaymentRecordService.queryPrepaymentTotal(queryDTO);
        System.out.println(prepaymentRecordTotalVO);
        assertNotNull(prepaymentRecordTotalVO);
    }
}