package com.cosfo.manage.bill.service.impl;

import com.cosfo.manage.bill.service.FinancialBillRuleService;
import com.cosfo.manage.bill.service.FinancialBillService;
import com.cosfo.manage.order.mapper.OrderAddressMapper;
import com.cosfo.manage.order.model.vo.OrderAddressVO;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/9
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class FinancialBillRuleServiceImplTest {
    @Resource
    private FinancialBillRuleService financialBillRuleService;
    @Resource
    private FinancialBillService financialBillService;

    @Test
    void getNextBillTime() {
        FinancialBillRuleServiceImpl financialBillRuleService = new FinancialBillRuleServiceImpl();
        financialBillRuleService.getNextBillTime(1,8);
    }


    @Test
    public void billTaskTest(){
        financialBillService.billTask();
    }
    @Test
    public void testOrder(){
//        OrderAddressVO orderAddressVO = orderAddressMapper.selectByOrderId(111L);
//        System.err.println(orderAddressVO.toString());
    }
}