package com.cosfo.manage.balance;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.manage.merchant.model.dto.balance.BalanceRecordDTO;
import com.cosfo.manage.merchant.model.dto.balance.MerchantStoreBalanceDTO;
import com.cosfo.manage.merchant.service.MerchantStoreBalanceService;
import com.cosfo.manage.merchant.service.MerchantStoreService;
import com.cosfo.manage.product.service.impl.ProductPricingSupplyServiceImpl;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.CommonResult;
import org.apache.commons.lang3.RandomUtils;
import org.apache.dubbo.common.utils.NamedThreadFactory;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.concurrent.BrokenBarrierException;
import java.util.concurrent.CyclicBarrier;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @Author: fansongsong
 * @date: 2023-03-16  15:40
 * @Description: 自测类
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class StoreBalanceServiceTest {

    private static LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();

    static {
        loginContextInfoDTO.setTenantId(1024L);
    }

    @Resource
    private MerchantStoreService merchantStoreService;

    @Resource
    private MerchantStoreBalanceService merchantStoreBalanceService;
    @Resource
    private ProductPricingSupplyServiceImpl productPricingSupplyService;

    /**
     * 门店详情自测
     */
    @Test
    public void selectDetail() {
//        LocalDateTime now = LocalDateTime.now();
//        ProductPricingSupplyDTO productPricingSupplyDTO = new ProductPricingSupplyDTO();
//        productPricingSupplyDTO.setStartTime(now.minusDays(10));
//        productPricingSupplyDTO.setEndTime(now.minusDays(5));
//        ProductPricingSupplyDTO productPricingSupplyDTO1 = new ProductPricingSupplyDTO();
//        productPricingSupplyDTO1.setStartTime(now.minusDays(6));
//        productPricingSupplyDTO1.setEndTime(now.minusDays(2));
//        ProductPricingSupplyDTO productPricingSupplyDTO2 = new ProductPricingSupplyDTO();
//        productPricingSupplyDTO2.setStartTime(now.minusDays(3));
//        productPricingSupplyDTO2.setEndTime(now.plusDays(5));
//
//        ProductPricingSupplyDTO productPricingSupplyDTO3 = new ProductPricingSupplyDTO();
//        productPricingSupplyDTO3.setStartTime(now.plusDays(3));
//        productPricingSupplyDTO3.setEndTime(now.plusDays(10));
//        ProductPricingSupplyDTO productPricingSupplyDTO4 = new ProductPricingSupplyDTO();
//        productPricingSupplyDTO4.setStartTime(now.plusDays(15));
//        productPricingSupplyDTO4.setEndTime(now.plusDays(20));
//        List<ProductPricingSupplyDTO> supplyTimeList = Lists.newArrayList(productPricingSupplyDTO, productPricingSupplyDTO1, productPricingSupplyDTO2, productPricingSupplyDTO3, productPricingSupplyDTO4);
//        Pair<LocalDateTime, LocalDateTime> aggregationTime = productPricingSupplyService.getAggregationTime(supplyTimeList, now);
//        System.err.println(JSON.toJSONString(aggregationTime));
        MerchantStoreDTO merchantStoreDTOResultDTO = merchantStoreService.selectDetail(1L);
        log.info(JSON.toJSONString(merchantStoreDTOResultDTO));
    }

//    /**
//     * 新增门店
//     */
//    @Test
//    public void saveStore() {
//        MerchantStoreDTO merchantStoreDTO = new MerchantStoreDTO();
//        merchantStoreDTO.setStoreNo(4413L);
//        merchantStoreDTO.setStoreName("松松自测");
//        merchantStoreDTO.setBillSwitch(BillSwitchEnum.SHUTDOWN.getCode());
//        merchantStoreDTO.setType(0);
//        merchantStoreDTO.setBalanceAuthority(BalancePermissionTypeEnum.OPEN_BALANCE_AUTH.getType());
//
//        List<MerchantStoreAccountDTO> accountList = Lists.newArrayList();
//        MerchantStoreAccountDTO merchantStoreAccountDTO = new MerchantStoreAccountDTO();
//        merchantStoreAccountDTO.setAccountName("小风");
//        merchantStoreAccountDTO.setPhone("***********");
//        accountList.add(merchantStoreAccountDTO);
//        merchantStoreDTO.setAccountList(accountList);
//
//        merchantStoreDTO.setProvince("浙江省");
//        merchantStoreDTO.setCity("杭州市");
//        merchantStoreDTO.setArea("西湖区");
//        merchantStoreDTO.setAddress("文一西路98号");
//        merchantStoreDTO.setHouseNumber("98号");
//
//        List<MerchantContactDTO> contactList = Lists.newArrayList();
//        MerchantContactDTO merchantContactDTO = new MerchantContactDTO();
//        merchantContactDTO.setContactName("大大");
//        merchantContactDTO.setPhone("***********");
//        merchantContactDTO.setDefaultFlag(1);
//        contactList.add(merchantContactDTO);
//        merchantStoreDTO.setContactList(contactList);
//
//        ResultDTO resultDTO = merchantStoreService.saveStore(merchantStoreDTO, loginContextInfoDTO);
//        log.info(JSON.toJSONString(resultDTO));
//    }
//
//    @Test
//    public void updateStore() {
//        MerchantStoreDTO merchantStoreDTO = new MerchantStoreDTO();
//        merchantStoreDTO.setStoreNo(20230316L);
//        merchantStoreDTO.setStoreName("316门店");
//        merchantStoreDTO.setBillSwitch(BillSwitchEnum.SHUTDOWN.getCode());
//        merchantStoreDTO.setType(0);
//        merchantStoreDTO.setBalanceAuthority(BalancePermissionTypeEnum.CLOSE_BALANCE_AUTH.getType());
//
//        List<MerchantStoreAccountDTO> accountList = Lists.newArrayList();
//        MerchantStoreAccountDTO merchantStoreAccountDTO = new MerchantStoreAccountDTO();
//        merchantStoreAccountDTO.setAccountName("小风");
//        merchantStoreAccountDTO.setPhone("***********");
//        accountList.add(merchantStoreAccountDTO);
//        merchantStoreDTO.setAccountList(accountList);
//
//        merchantStoreDTO.setProvince("浙江省");
//        merchantStoreDTO.setCity("杭州市");
//        merchantStoreDTO.setArea("西湖区");
//        merchantStoreDTO.setAddress("天目山路518号");
//        merchantStoreDTO.setHouseNumber("518号");
//
//        merchantStoreDTO.setId(4086L);
//
//        List<MerchantContactDTO> contactList = Lists.newArrayList();
//        MerchantContactDTO merchantContactDTO = new MerchantContactDTO();
//        merchantContactDTO.setContactName("大大");
//        merchantContactDTO.setPhone("***********");
//        merchantContactDTO.setDefaultFlag(1);
//        contactList.add(merchantContactDTO);
//        merchantStoreDTO.setContactList(contactList);
//
//        ResultDTO resultDTO = merchantStoreService.updateStore(merchantStoreDTO, loginContextInfoDTO);
//        log.info(JSON.toJSONString(resultDTO));
//    }

    private static final Integer DEFAULT_NUM = 50;
    ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(DEFAULT_NUM, DEFAULT_NUM, 10, TimeUnit.MINUTES, new LinkedBlockingQueue(100), new NamedThreadFactory("StoreBalance-thread"));

    @Test
    public void adjustBalance() throws InterruptedException {
        CyclicBarrier cyclicBarrier = new CyclicBarrier(DEFAULT_NUM);

        for (int i = 0; i < DEFAULT_NUM; i++) {
            threadPoolExecutor.execute(() -> {
                        try {
                            double randomValue = RandomUtils.nextDouble(1, 1000);
                            MerchantStoreBalanceDTO merchantStoreBalanceDTO = new MerchantStoreBalanceDTO();
                            merchantStoreBalanceDTO.setChangeBalance(new BigDecimal(randomValue).setScale(2, RoundingMode.HALF_UP));
                            merchantStoreBalanceDTO.setStoreId(4086L);
                            merchantStoreBalanceDTO.setProof("www.baidu.com,www.123.com");
                            cyclicBarrier.await();
                            CommonResult commonResult = merchantStoreBalanceService.adjustBalance(merchantStoreBalanceDTO, loginContextInfoDTO);
                            log.info("NAME:{},merchantStoreBalanceDTO:{},RESULT:{}", Thread.currentThread().getName(), JSON.toJSONString(merchantStoreBalanceDTO), JSON.toJSONString(commonResult));
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        } catch (BrokenBarrierException e) {
                            e.printStackTrace();
                        }

                    }

            );
        }
        Thread.sleep(15000);
    }

    @Test
    public void adjustBalance1() throws InterruptedException {
        CyclicBarrier cyclicBarrier = new CyclicBarrier(DEFAULT_NUM);

        for (int i = 0; i < DEFAULT_NUM; i++) {
            threadPoolExecutor.execute(() -> {
                        try {
                            double randomValue = -6000;
                            MerchantStoreBalanceDTO merchantStoreBalanceDTO = new MerchantStoreBalanceDTO();
                            merchantStoreBalanceDTO.setChangeBalance(new BigDecimal(randomValue).setScale(2, RoundingMode.HALF_UP));
                            merchantStoreBalanceDTO.setStoreId(4086L);
                            merchantStoreBalanceDTO.setProof("www.baidu.com,www.123.com");
                            cyclicBarrier.await();
                            CommonResult commonResult = merchantStoreBalanceService.adjustBalance(merchantStoreBalanceDTO, loginContextInfoDTO);
                            log.info("NAME:{},merchantStoreBalanceDTO:{},RESULT:{}", Thread.currentThread().getName(), JSON.toJSONString(merchantStoreBalanceDTO), JSON.toJSONString(commonResult));
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        } catch (BrokenBarrierException e) {
                            e.printStackTrace();
                        }

                    }

            );
        }
        Thread.sleep(15000);
    }

    @Test
    public void overview() {
        log.info(JSON.toJSONString(merchantStoreBalanceService.balanceOverview(loginContextInfoDTO)));
    }

    @Test
    public void balanceChangeRecord() {
    }

    @Test
    public void exportBalanceChangeRecord() {
        BalanceRecordDTO balanceRecordDTO = new BalanceRecordDTO();
        LocalDateTime startTime = LocalDateTime.now();
        LocalDateTime endTime = startTime.plusYears(1);
        balanceRecordDTO.setStartTime(startTime);
        balanceRecordDTO.setEndTime(endTime);
        log.info(JSON.toJSONString(merchantStoreBalanceService.exportBalanceChangeRecord(loginContextInfoDTO, balanceRecordDTO)));
    }
}