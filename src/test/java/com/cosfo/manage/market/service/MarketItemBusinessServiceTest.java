package com.cosfo.manage.market.service;

import com.cofso.item.client.resp.MarketItemOnSaleSimple4StoreResp;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class MarketItemBusinessServiceTest {

    @Resource
    private MarketItemBusinessService marketItemBusinessService;
    @Test
    void selectSaleStatusBySkuIds() {
        List<MarketItemOnSaleSimple4StoreResp> marketItemOnSaleSimple4StoreResps = marketItemBusinessService.selectSaleStatusBySkuIds(2L, Lists.newArrayList(10193L));
        System.out.println(marketItemOnSaleSimple4StoreResps);
    }
}