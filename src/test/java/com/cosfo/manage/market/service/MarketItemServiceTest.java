package com.cosfo.manage.market.service;

import com.alibaba.fastjson.JSON;
import com.cofso.item.client.req.MarketItemCommonQueryReq;
import com.cosfo.manage.common.context.DeleteFlagEnum;
import com.cosfo.manage.facade.MarketItemFacade;
import com.cosfo.manage.market.model.dto.MarketItemInfoDTO;
import com.cosfo.manage.market.model.vo.MarketItemInfoPageVO;
import com.github.pagehelper.PageInfo;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * <AUTHOR>
 */
@ActiveProfiles("dev2")
@SpringBootTest
class MarketItemServiceTest {

    @Resource
    private MarketItemService marketItemService;
    @Resource
    private MarketItemFacade marketItemFacade;

    @Test
    void getMapByItemIds() {
        Map<Long, MarketItemInfoDTO> mapByItemIds = marketItemService.getMapByItemIds(Lists.newArrayList(30024L), 2L);
        System.out.println(JSON.toJSONString(mapByItemIds));
        assertEquals(1, mapByItemIds.size());
        assertEquals(30024L, mapByItemIds.get(30024L).getItemId());
    }

    @Test
    void getMapByItemIdsWithNotExistId() {
        Map<Long, MarketItemInfoDTO> mapByItemIds = marketItemService.getMapByItemIds(Lists.newArrayList(30024111L), 2L);
        System.out.println(JSON.toJSONString(mapByItemIds));
        assertEquals(0, mapByItemIds.size());
    }

    @Test
    void testquery() {
        MarketItemCommonQueryReq req = new MarketItemCommonQueryReq();
        req.setTenantId(2L);
        req.setPageNum(1);
        req.setPageSize(100);
        req.setCombineFlag(Boolean.FALSE);
        req.setDeleteFlag(DeleteFlagEnum.NORMAL.getFlag());

//        MarketItemInfoQueryFlagReq marketItemInfoQueryFlagReq = new MarketItemInfoQueryFlagReq();
//        marketItemInfoQueryFlagReq.setClassificationIdFlag(true);

        Set<Long> allItemIds = new HashSet<>();
        while (true) {
            PageInfo<MarketItemInfoPageVO> page = marketItemFacade.pageMarketItem (req);
            if (CollectionUtils.isEmpty(page.getList())) {
                break;
            }
            allItemIds.addAll(page.getList().stream().map(MarketItemInfoPageVO::getId).collect(Collectors.toList()));
            req.setPageNum(page.getPageNum() + 1);
        }
        System.err.println(allItemIds.size());
    }
}