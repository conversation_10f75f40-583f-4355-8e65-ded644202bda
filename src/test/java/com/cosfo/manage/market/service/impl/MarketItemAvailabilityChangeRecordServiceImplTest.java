package com.cosfo.manage.market.service.impl;

import com.cofso.item.client.enums.OnSaleTypeEnum;
import com.cosfo.manage.market.model.po.MarketItemAvailabilityChangeRecord;
import com.cosfo.manage.market.service.MarketItemAvailabilityChangeRecordService;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.List;


@RunWith(SpringRunner.class)
@SpringBootTest
class MarketItemAvailabilityChangeRecordServiceImplTest {

    @Resource
    private MarketItemAvailabilityChangeRecordService marketItemAvailabilityChangeRecordService;

    @Test
    public void testCreateSkuRecord() {
        Long tenantId = 2L;
        Long skuId = 10193L;
        LocalDateTime now = LocalDateTime.now();
        List<MarketItemAvailabilityChangeRecord> records = marketItemAvailabilityChangeRecordService.createHasGoodItemStockChange(tenantId, skuId, now);

        marketItemAvailabilityChangeRecordService.batchSaveRecords(records);
    }

    @Test
    public void testOnSaleRecord() {
        Long tenantId = 2L;
        long itemId = 28445L;
        LocalDateTime now = LocalDateTime.now();
        MarketItemAvailabilityChangeRecord record = marketItemAvailabilityChangeRecordService.createItemOnSaleChange(tenantId, itemId, OnSaleTypeEnum.SOLD_OUT, now);

        marketItemAvailabilityChangeRecordService.saveRecord(record);
    }

}