package com.cosfo.manage.market.service.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.model.dto.ExcelImportResDTO;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.market.model.dto.MarketQueryInput;
import com.cosfo.manage.market.model.vo.MarketSpuVO;
import com.cosfo.manage.market.service.MarketItemService;
import com.github.pagehelper.PageInfo;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.internal.matchers.Any;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;

import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("dev4")
public class MarketServiceImplIntegrationTest {

    @Autowired
    private MarketServiceImpl marketService;

    @Test
    void testImportThirdMap() throws IOException {

        // Given
        String originalFilename = "testFile.xlsx";
        String contentType = "text/xlsx";
        String filePath = "/Users/<USER>/Downloads/pos_item_demo.xlsx"; // replace with your file path
        FileInputStream input = new FileInputStream(Paths.get(filePath).toFile());
        MockMultipartFile multipartFile = new MockMultipartFile("file",
                originalFilename, contentType, input);
        Long tenantId = 2L; // replace with your tenantId

        // When
        ExcelImportResDTO result = marketService.importThirdMap(multipartFile, tenantId);
        System.out.println(JSON.toJSONString(result));
        // Then
        assertNotNull(result);
        // Add more assertions based on your business requirements
    }

    @Test
    void listWithThirdMapTest() {
        MarketQueryInput marketQueryInput = new MarketQueryInput();
        marketQueryInput.setId(2016415L);
        marketQueryInput.setOnSale(1);
        marketQueryInput.setPageIndex(1);
        marketQueryInput.setPageSize(10);
        LoginContextInfoDTO contextInfoDTO = new LoginContextInfoDTO();
        contextInfoDTO.setTenantId(2L);
        PageInfo<MarketSpuVO> list = marketService.list(marketQueryInput, contextInfoDTO);
        System.out.println(JSON.toJSON(list.getList()));
    }
}