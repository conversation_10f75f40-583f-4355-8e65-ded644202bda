package com.cosfo.manage.product.service.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.TestApplication;
import com.cosfo.manage.common.easy.excel.helper.ChainDropDown;
import com.cosfo.manage.good.service.SyncProductService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * @author: xiaowk
 * @time: 2023/7/1 下午10:38
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {TestApplication.class})
@Slf4j
public class SyncProductServiceTest {

    @Resource
    private SyncProductService syncProductService;

    @Test
    public void testSyncAgentProduct(){
        List<Long> skuIdList = Lists.newArrayList(9150L);

        syncProductService.syncAgentProductFromXianmu(24529L, skuIdList);
    }


    @Test
    public void testMapPut(){
        Map<Integer, ChainDropDown> map = new HashMap<>(16);
        Integer oriIdx = 3;
        Integer newIdx = 4;

        if(!map.containsKey(oriIdx)){
            ChainDropDown firstChainDropDown = new ChainDropDown();
            firstChainDropDown.setRootFlag(true);
            firstChainDropDown.setTypeName("类目");
            firstChainDropDown.setRowIndex(0);
            map.put(oriIdx, firstChainDropDown);
        }
        log.info(">>>>>> 原来的写法，putMap结果：{}", JSON.toJSONString(map));
        map.computeIfAbsent(newIdx,c -> {
            ChainDropDown firstChainDropDown = new ChainDropDown();
            firstChainDropDown.setRootFlag(true);
            firstChainDropDown.setTypeName("类目");
            firstChainDropDown.setRowIndex(0);
            return firstChainDropDown;
        });
        log.info(">>>>>> computeIfAbsent的写法，putMap结果：{}", JSON.toJSONString(map));

        map.computeIfAbsent(newIdx,c -> {
            ChainDropDown firstChainDropDown = new ChainDropDown();
            firstChainDropDown.setRootFlag(true);
            firstChainDropDown.setTypeName("类目");
            firstChainDropDown.setRowIndex(0);
            return firstChainDropDown;
        });
        log.info(">>>>>> computeIfAbsent重复put，putMap结果：{}", JSON.toJSONString(map));

    }
}
