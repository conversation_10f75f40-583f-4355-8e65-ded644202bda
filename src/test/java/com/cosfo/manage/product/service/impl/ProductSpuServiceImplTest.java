package com.cosfo.manage.product.service.impl;

import com.cosfo.manage.good.service.ProductService;
import com.cosfo.manage.product.model.po.ProductSpu;
import com.cosfo.manage.product.service.ProductSkuService;
import com.cosfo.manage.product.service.ProductSpuService;
import com.cosfo.summerfarm.model.dto.SummerFarmSkuMsgDTO;
import com.cosfo.summerfarm.model.dto.product.SummerfarmProductAuditResultDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/26
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class ProductSpuServiceImplTest {
    @Resource
    private ProductSpuService productSpuService;
    @Resource
    private ProductSkuService productSkuService;
    @Resource
    private ProductService productService;

    @Test
    public void testReceiveProductInfo(){
        SummerFarmSkuMsgDTO summerFarmSkuMsgDTO = new SummerFarmSkuMsgDTO();
        summerFarmSkuMsgDTO.setSkuId(16073L);
        summerFarmSkuMsgDTO.setVolume("5*5*5");
        summerFarmSkuMsgDTO.setWeightNum(new BigDecimal(200));
        productSkuService.receiveSummerfarmProductInfo(summerFarmSkuMsgDTO);
    }

    @Test
    public void testReceiveSummerfarmProductAuditResult(){
        // {"auditTime":"2023-12-28T16:25:02","auditResult":0,"refuseReason":"保质期有误,体积有误","skuId":27575}
        SummerfarmProductAuditResultDTO summerfarmProductAuditResultDTO = new SummerfarmProductAuditResultDTO();
        summerfarmProductAuditResultDTO.setSkuId(27575L);
        summerfarmProductAuditResultDTO.setRefuseReason("保质期有误,体积有误");
        summerfarmProductAuditResultDTO.setAuditResult(0);
        productService.receiveSummerfarmProductAuditResult(summerfarmProductAuditResultDTO);
    }
}
