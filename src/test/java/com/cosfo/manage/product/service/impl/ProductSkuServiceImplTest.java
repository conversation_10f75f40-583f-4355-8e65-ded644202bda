package com.cosfo.manage.product.service.impl;

import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/5/23
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class ProductSkuServiceImplTest {

    @Resource
    private ProductSkuServiceImpl productSkuService;

    @Test
    void doneSkuSynchronized() {
    }

    @Test
    public void pictureUpload(){
        String mainPicture = "1529238433241";
        String detailPicture = "detail-picture/hc7f6x07c5tbku65j.jpg";
        productSkuService.pictureUpload(Lists.newArrayList(mainPicture, detailPicture));
    }
}