package com.cosfo.manage.product.mapper;

import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import com.cosfo.manage.product.model.dto.ProductSkuQueryConditionDTO;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/4/10
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class ProductSkuMapperTest {
    @Resource
    private ProductSkuMapper productSkuMapper;

    @Test
    void listByCondition() {
        ProductSkuQueryConditionDTO productSkuQueryConditionDTO = new ProductSkuQueryConditionDTO();
        productSkuQueryConditionDTO.setSkuIds(Arrays.asList(16869L));
        List<ProductSkuDTO> productSkuDTOS = productSkuMapper.listByCondition(productSkuQueryConditionDTO);
        Assert.assertTrue(CollectionUtils.isEmpty(productSkuDTOS));
    }
}