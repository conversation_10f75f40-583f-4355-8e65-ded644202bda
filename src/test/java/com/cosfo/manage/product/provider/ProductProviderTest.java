package com.cosfo.manage.product.provider;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.client.product.ProductProvider;
import com.cosfo.manage.client.product.req.ProductQueryReq;
import com.cosfo.manage.client.product.req.SaasSkuMappingQueryReq;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * @author: monna.chen
 * @Date: 2023/5/24 18:50
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class ProductProviderTest {
    @Resource
    private ProductProvider productProvider;

    @Test
    void batchQueryBySummerfarmSkuIds() {
        ProductQueryReq queryReq = new ProductQueryReq();
        queryReq.setSummerfarmSkuIds(Arrays.asList(19240L,19242L));
        System.out.println(JSON.toJSONString(productProvider.batchQueryBySummerfarmSkuIds(queryReq)));;
    }

    @Test
    void batchQueryBySaasSkuIds() {
        SaasSkuMappingQueryReq queryReq = new SaasSkuMappingQueryReq();
        queryReq.setSkuIds(Arrays.asList(18740L,18738L));
        System.out.println(JSON.toJSONString(productProvider.batchQueryBySaasSkuIds(queryReq)));;
    }
}
