package com.cosfo.manage.wx;

import com.cosfo.manage.wechat.service.WeixinTpService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @description:
 * @author: George
 * @date: 2023-08-16
 **/
@SpringBootTest
@RunWith(SpringRunner.class)
public class WeixinTpServivceTest {

    @Resource
    private WeixinTpService weixinTpService;

    @Test
    public void testCallBack() {
        String appId = "wx0234b1d4eb212e12";
        Map<String, String> params = new HashMap<>();
        params.put("signature", "c9dd467e4c32cbe98440157081b4cb6767bc7e81");
        params.put("timestamp", "1692109874&nonce=1656968724");
        params.put("openId", "ow4yY5ZommK_14q1lrVR_hT2Mf70");
        params.put("encrypt_typ", "aes&msg_signature=1c76717c43d85f1d249c1751bd20100c608fb291");
        String body = "<xml>    <ToUserName><![CDATA[gh_1081a242e96b]]></ToUserName>    <Encrypt><![CDATA[t10kp0HLxPlqU0+iGmhNtZSK0d81SUxqGMXAQuv90K29hmls5lQTkzeEMZtvVs53gaGlRPo45RXuDqIVv94BcQXxMEURbGSSRaXGlSbl7pFV8fHbvD0vkohAhQcj8Hz9aYdONo+tylsKNMy1tKQNtI4MX2t2DgiL97C9uaqtarP4rD8wZhYb1gwuaCDf+cPq+KZmL1gd9pVnOxG8Mz32bt+e0NyYJTFFxOvSv1cQd7h2V3iKyiizF1lGxOqI69iKhW1kvF1Okerk0eFPKZdLH+DEaaiiS1tNRdpY+P7VEo7rFOXqek0h/P6Oo5s0bl6MUCno4gzzW/2btKsvtkIi+ZG3XVR7X+2fIr6tWmoldFa8xDEzThLGPZc05S/0APX3nKH1lA+Yy/tpErw8Lt5Zryp+A4pGUB1KphJw/6iSaQ6SvPjMXufKYJv+ITcv4kvR14Iv0cOez0yljXwt0ZN2ev4LwWwifoSX8ugXwVhU/iui1xUcqPyJuT4uUrr/bEHanNUWa96o4pBcMapAtL8hSsejFT1ixZibjah6TDuP4BnLXuVt5hXmBIboxdRYLULqh+oJIBSJpmYtomWXFSSH/aM2SwMrncsMLJPui7yyXRV1AEfY6QB45DWsFxrnXC3E7Y8XUVZ+ireFiKLnmi+qeknJZ2MOLXVKh4M+NpLdnhhVzgRSgtMY9sc3oYJT3P61PKm3/2SYSxPgzuPLDeU6Fi4HWSmMn7qRr1sAAe/AUW/0eW5Z4F2YGUKAEehzKuIJ7ob15uwnedkHjM4znGTdxUi0VYlkzyoWiYtB+Zjcg/6AZ81p33wL9hHLQLo0TbJ71XVj6/MQGK7f44LwNsM6a+OdfpJm/otgZk+me5uIMUHD2eBH0rjyCEM3vteCxSRxeu93O1Ir9aRsG2kJbgWu9BDW7LtARAJIdDIevjgXtq0=]]></Encrypt></xml>";
        weixinTpService.processMessage(appId, params, body);
    }
}
