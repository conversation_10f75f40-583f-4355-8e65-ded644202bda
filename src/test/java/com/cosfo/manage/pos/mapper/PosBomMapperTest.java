package com.cosfo.manage.pos.mapper;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.pos.model.po.PosBom;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class PosBomMapperTest {

    @Resource
    private PosBomMapper posBomMapper;
    @Test
    void batchInsert() {
        PosBom posBom = new PosBom();
        posBom.setChannelType(0);
        posBom.setTenantId(0L);
//        posBom.setOutStoreCode("123");
        posBom.setOutMenuSpecification("123");
        posBom.setOutMenuCode("123");
        posBom.setMerchantStoreId(0L);
        posBom.setOutMenuName("123");
        posBom.setPrice(new BigDecimal("1.1"));
        posBom.setAvailableDate(LocalDate.now());
        posBomMapper.batchInsert(Lists.newArrayList(posBom));

        System.out.println(posBom.getId());

    }

    @Test
    void listLatestBom() {
        List<PosBom> posBoms = posBomMapper.listLatestAvailableBom(2L);
        System.out.println(JSON.toJSONString(posBoms));
    }
}