package com.cosfo.manage.merchant.mapper;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.merchant.model.po.MerchantDeliveryFeeRule;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.mybatis.spring.boot.test.autoconfigure.MybatisTest;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@ActiveProfiles("test")
@MybatisTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class MerchantDeliveryFeeRuleMapperTest  {

    @Resource
    private MerchantDeliveryFeeRuleMapper merchantDeliveryFeeRuleMapper;

    @Test
    void insert() {
        MerchantDeliveryFeeRule merchantDeliveryFeeRule = new MerchantDeliveryFeeRule();
        merchantDeliveryFeeRule.setType(0);
        merchantDeliveryFeeRule.setRuleType(0);
        merchantDeliveryFeeRule.setPriceType(0);
        merchantDeliveryFeeRule.setTenantId(24461L);
        merchantDeliveryFeeRule.setDeliveryFee(BigDecimal.ZERO);
        merchantDeliveryFeeRule.setFreeDeliveryPrice(BigDecimal.ZERO);
        merchantDeliveryFeeRule.setRelateNumber(BigDecimal.ZERO);
        merchantDeliveryFeeRule.setDefaultType(0);
        merchantDeliveryFeeRule.setFreeDeliveryType(0);
        merchantDeliveryFeeRule.setFreeDeliveryQuantity(1);
        merchantDeliveryFeeRule.setCreateTime(LocalDateTime.now());
        merchantDeliveryFeeRule.setHitItemIds(Lists.newArrayList(1L));
        merchantDeliveryFeeRule.setHitAreas(Lists.newArrayList(Lists.newArrayList("测试1"), Lists.newArrayList("测试2")));

        merchantDeliveryFeeRuleMapper.insertSelective(merchantDeliveryFeeRule);

        MerchantDeliveryFeeRule merchantDeliveryFeeRule1 = merchantDeliveryFeeRuleMapper.selectByPrimaryKey(merchantDeliveryFeeRule.getId());
        System.out.println("------"+JSON.toJSONString(merchantDeliveryFeeRule1));
    }

    @Test
    void getRuleTest() {
        MerchantDeliveryFeeRule merchantDeliveryFeeRule = merchantDeliveryFeeRuleMapper.selectByPrimaryKey(417L);
        System.out.println(merchantDeliveryFeeRule);
    }


    @Test
    void updateWithItemAddTest() {
        Long id = 990L;
        List<MerchantDeliveryFeeRule> rules = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            MerchantDeliveryFeeRule rule = new MerchantDeliveryFeeRule();
            rule.setId(id+i);
            rule.setHitItemIds(Arrays.asList(1L,2L,3L));
            rules.add(rule);
        }

        int i = merchantDeliveryFeeRuleMapper.updateWithItemAdd(rules);
        System.out.println(i);
    }
}