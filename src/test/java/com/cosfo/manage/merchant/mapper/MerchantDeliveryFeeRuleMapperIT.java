package com.cosfo.manage.merchant.mapper;
import java.math.BigDecimal;
import com.google.common.collect.Lists;
import java.time.LocalDateTime;

import com.cosfo.manage.merchant.model.po.MerchantDeliveryFeeRule;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class MerchantDeliveryFeeRuleMapperIT {

    @Resource
    private MerchantDeliveryFeeRuleMapper merchantDeliveryFeeRuleMapper;

    @Test
    void listRuleByTenant() {
        MerchantDeliveryFeeRule merchantDeliveryFeeRule = merchantDeliveryFeeRuleMapper.selectByPrimaryKey(658L);
        System.out.println(merchantDeliveryFeeRule);
    }

    @Test
    void insertRuleTest() {
        MerchantDeliveryFeeRule merchantDeliveryFeeRule = new MerchantDeliveryFeeRule();
        merchantDeliveryFeeRule.setTenantId(0L);
        merchantDeliveryFeeRule.setType(0);
        merchantDeliveryFeeRule.setRuleType(0);
        merchantDeliveryFeeRule.setPriceType(0);
        merchantDeliveryFeeRule.setDeliveryFee(new BigDecimal("0"));
        merchantDeliveryFeeRule.setFreeDeliveryPrice(new BigDecimal("0"));
        merchantDeliveryFeeRule.setCreateTime(LocalDateTime.now());
        merchantDeliveryFeeRule.setUpdateTime(LocalDateTime.now());
        merchantDeliveryFeeRule.setRelateNumber(new BigDecimal("0"));
        merchantDeliveryFeeRule.setDefaultType(0);
        merchantDeliveryFeeRule.setFreeDeliveryQuantity(0);
        merchantDeliveryFeeRule.setFreeDeliveryType(0);
        merchantDeliveryFeeRule.setPriority(0);
        merchantDeliveryFeeRule.setHitItemIds(Lists.newArrayList(1L));
        merchantDeliveryFeeRule.setHitAreas(Lists.newArrayList(Lists.newArrayList("测试1"), Lists.newArrayList("测试2")));
        merchantDeliveryFeeRuleMapper.insertSelective(merchantDeliveryFeeRule);
    }

    @Test
    void delSpecialRuleTest() {
        merchantDeliveryFeeRuleMapper.deleteSpecialRule(1003L, 2, null);
    }
}