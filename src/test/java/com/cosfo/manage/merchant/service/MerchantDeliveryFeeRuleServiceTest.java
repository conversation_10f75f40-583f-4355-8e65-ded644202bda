package com.cosfo.manage.merchant.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class MerchantDeliveryFeeRuleServiceTest {

    @Resource
    private MerchantDeliveryFeeRuleService merchantDeliveryFeeRuleService;

    @Test
    void updateDeliveryFeeRuleWithItemDelete() {
        merchantDeliveryFeeRuleService.updateDeliveryFeeRuleWithItemDelete(24457L, 1633L);
    }
}