package com.cosfo.manage.merchant.service;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.config.MerchantAddressMappingNacosConfig;
import com.cosfo.manage.merchant.model.dto.MerchantStoreAddressDTO;
import com.cosfo.manage.merchant.model.dto.address.AddressNameMappingDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@SpringBootTest
@Slf4j
class MerchantStoreServiceTest {

    @Resource
    private MerchantStoreService merchantStoreService;

    @Resource
    private MerchantAddressMappingNacosConfig merchantAddressMappingNacosConfig;

    @Test
    public void getStoreIdAndAddress() {
        MerchantStoreAddressDTO result = merchantStoreService.getStoreIdAndAddress(2L, 4253L);
        log.info("result:>>>>>>" + JSON.toJSONString(result));
    }

    @Test
    public void querySaasMerchantAddressMapping() {
        AddressNameMappingDTO addressNameMappingDTO = merchantAddressMappingNacosConfig.querySaasMerchantAddressMapping();
        log.info("result:>>>>>>" + JSON.toJSONString(addressNameMappingDTO));
    }
}