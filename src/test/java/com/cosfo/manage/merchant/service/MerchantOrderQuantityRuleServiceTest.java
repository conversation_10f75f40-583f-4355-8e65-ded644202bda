package com.cosfo.manage.merchant.service;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.context.WarehouseTypeEnum;
import com.cosfo.manage.common.context.warehouse.WarehouseQueryEnum;
import com.cosfo.manage.merchant.model.dto.quantityrule.OrderQuantityRuleDTO;
import com.cosfo.manage.merchant.model.dto.quantityrule.OrderQuantityRuleDetailDTO;
import com.cosfo.manage.merchant.model.dto.quantityrule.OrderQuantityRuleManageDTO;
import com.cosfo.manage.merchant.model.vo.OrderQuantityRuleManageVO;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootTest
class MerchantOrderQuantityRuleServiceTest {

    @Resource
    private MerchantOrderQuantityRuleService merchantOrderQuantityRuleService;

    @Test
    void orderQuantityRuleTest() {
        OrderQuantityRuleManageVO manageVO = merchantOrderQuantityRuleService.queryOrderQuantityRule(24457L);
        System.out.println(JSON.toJSONString(manageVO));
    }

    @Test
    void queryEmptyTest() {
        OrderQuantityRuleManageVO manageVO = merchantOrderQuantityRuleService.queryOrderQuantityRule(1003L);
        System.out.println(manageVO);
    }


    @Test
    void modifyRuleTest() {
        OrderQuantityRuleManageDTO manageDTO = new OrderQuantityRuleManageDTO();
        OrderQuantityRuleDTO ruleDTO = generatorAllMallRule();
        manageDTO.setAllMallRule(ruleDTO);
        merchantOrderQuantityRuleService.modifyOrderQuantityRule(manageDTO, 1003L);
    }

    @Test
    void modifyWithOtherRule() {
        OrderQuantityRuleManageDTO manageDTO = new OrderQuantityRuleManageDTO();
        OrderQuantityRuleDTO ruleDTO = generatorAllMallRule();
        manageDTO.setAllMallRule(ruleDTO);
        manageDTO.setOtherRule(Lists.newArrayList(generatorOtherRule()));
        merchantOrderQuantityRuleService.modifyOrderQuantityRule(manageDTO, 1003L);
    }

    @Test
    void addNoWarehouseRuleTest() {
        OrderQuantityRuleManageDTO manageDTO = new OrderQuantityRuleManageDTO();
        OrderQuantityRuleDTO ruleDTO = generatorAllMallRule();
        manageDTO.setAllMallRule(ruleDTO);
        manageDTO.setOtherRule(Lists.newArrayList(generatorOtherRule()));
        merchantOrderQuantityRuleService.modifyOrderQuantityRule(manageDTO, 24457L);
    }

    @Test
    void listQuantityRuleTest() {
        OrderQuantityRuleManageVO orderQuantityRuleManageVO = merchantOrderQuantityRuleService.queryOrderQuantityRule(24457L);
        System.out.println(JSON.toJSONString(orderQuantityRuleManageVO));
    }

    private static OrderQuantityRuleDTO generatorAllMallRule() {
        OrderQuantityRuleDTO ruleDTO = new OrderQuantityRuleDTO();
        ruleDTO.setRuleTarget(0L);
        ruleDTO.setOperator("and");
        ruleDTO.setTenantId(24457L);
        ruleDTO.setWarehouseType(WarehouseTypeEnum.NO_WAREHOUSE.getCode());
        List<OrderQuantityRuleDetailDTO> ruleDetailList = new ArrayList<>();
        OrderQuantityRuleDetailDTO detailDTO = new OrderQuantityRuleDetailDTO();
        detailDTO.setRuleDetailType(1);
        detailDTO.setQuantity(1);
        ruleDetailList.add(detailDTO);
        ruleDTO.setRuleDetailList(ruleDetailList);
        return ruleDTO;
    }

    private static OrderQuantityRuleDTO generatorOtherRule() {
        OrderQuantityRuleDTO ruleDTO = new OrderQuantityRuleDTO();
        ruleDTO.setRuleTargetList(Lists.newArrayList(-1L, -2L));
        ruleDTO.setOperator("and");
        ruleDTO.setTenantId(24457L);
        ruleDTO.setWarehouseType(WarehouseTypeEnum.NO_WAREHOUSE.getCode());
        List<OrderQuantityRuleDetailDTO> ruleDetailList = new ArrayList<>();
        OrderQuantityRuleDetailDTO detailDTO = new OrderQuantityRuleDetailDTO();
        detailDTO.setRuleDetailType(1);
        detailDTO.setQuantity(1);
        ruleDetailList.add(detailDTO);
        ruleDTO.setRuleDetailList(ruleDetailList);
//        ruleDTO.setHitItemIds(Lists.newArrayList(1L));
        ruleDTO.setIncludeNewFlag(true);
        ruleDTO.setAllItemHit(true);
        return ruleDTO;
    }

    @Test
    void updateMerchantDeliveryRuleTestWithItemDel() {
        merchantOrderQuantityRuleService.updateRuleWithMarketItemUpdate(24457L, 1L, 1);
    }

    @Test
    void updateOtherRuleHitAllItemWithIncludeNewTest() {
        OrderQuantityRuleManageDTO manageDTO = new OrderQuantityRuleManageDTO();

        // 更新规则，所有商品包含后续新增
        OrderQuantityRuleDTO orderQuantityRuleDTO = generatorOtherRule();
        orderQuantityRuleDTO.setIncludeNewFlag(true);
        orderQuantityRuleDTO.setAllItemHit(true);

        OrderQuantityRuleDTO commonRule = generatorAllMallRule();
        commonRule.setId(2130L);

        manageDTO.setAllMallRule(commonRule);
        manageDTO.setOtherRule(Lists.newArrayList(orderQuantityRuleDTO));
        merchantOrderQuantityRuleService.modifyOrderQuantityRule(manageDTO, 24457L);
    }
}