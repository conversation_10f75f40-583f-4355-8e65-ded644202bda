package com.cosfo.manage.merchant.service;

import com.cosfo.manage.merchant.model.dto.MerchantStoreTradeSummaryInitDTO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * @description:
 * @author: George
 * @date: 2023-11-03
 **/
@SpringBootTest
public class MerchantStoreTradeSummaryTestService {

    @Resource
    private MerchantStoreTradeSummaryService merchantStoreTradeSummaryService;

    @Test
    public void testInitStoreDimensionTradeSummary() {
        MerchantStoreTradeSummaryInitDTO merchantStoreTradeSummaryInitDTO = new MerchantStoreTradeSummaryInitDTO();
        merchantStoreTradeSummaryInitDTO.setTenantId(24593L);
        LocalDateTime startTime = LocalDateTime.of(2023, 11, 03, 0, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(2023, 11, 04, 0, 0, 0);
        merchantStoreTradeSummaryInitDTO.setStartTime(startTime);
        merchantStoreTradeSummaryInitDTO.setEndTime(endTime);
        merchantStoreTradeSummaryService.initStoreDimensionTradeSummary(merchantStoreTradeSummaryInitDTO);
    }

    @Test
    public void test() {
        merchantStoreTradeSummaryService.auditStoreDimensionTradeSummary(24593L, "2023/11/03 00:00:00", "2023/11/04 00:00:00");
    }
}
