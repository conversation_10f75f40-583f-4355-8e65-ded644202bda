package com.cosfo.manage.merchant.repository;

import com.cosfo.manage.merchant.model.po.MerchantDeliveryStepFee;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class MerchantDeliveryStepFeeRepositoryTest {

    @Resource MerchantDeliveryStepFeeRepository merchantDeliveryStepFeeRepository;
    @Test
    public void batchInsert() {
        MerchantDeliveryStepFee stepFee = new MerchantDeliveryStepFee();
        stepFee.setRuleId(1L);
        stepFee.setDeliveryFee(BigDecimal.ONE);
        stepFee.setStepThreshold(BigDecimal.ONE);
        stepFee.setFeeRule(1);
        stepFee.setTenantId(2345L);
        merchantDeliveryStepFeeRepository.batchInsert(Lists.newArrayList(stepFee));
    }
}