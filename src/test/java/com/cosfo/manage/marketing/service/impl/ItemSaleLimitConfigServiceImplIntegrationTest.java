package com.cosfo.manage.marketing.service.impl;

import com.cosfo.manage.marketing.model.dto.ItemSaleLimitConfigDTO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class ItemSaleLimitConfigServiceImplIntegrationTest {

    @Resource
    private ItemSaleLimitConfigServiceImpl itemSaleLimitConfigServiceImplUnderTest;

    @Test
    void testQueryItemSaleLimitConfig() {
        // Setup
        final ItemSaleLimitConfigDTO expectedResult = ItemSaleLimitConfigDTO.builder().saleLimitQuantity(0).saleLimitRule(0).build();

        // Run the test
        final ItemSaleLimitConfigDTO result = itemSaleLimitConfigServiceImplUnderTest.queryItemSaleLimitConfig(0L, 0L);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    void testQueryItemSaleLimitConfigMap() {
        // Setup
        final Map<Long, ItemSaleLimitConfigDTO> expectedResult = new HashMap<>();

        // Run the test
        final Map<Long, ItemSaleLimitConfigDTO> result = itemSaleLimitConfigServiceImplUnderTest.queryItemSaleLimitConfigMap(
                0L, Arrays.asList(0L));

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    void testSaveItemSaleLimitConfig() {
        // Setup
        final ItemSaleLimitConfigDTO itemSaleLimitConfigDTO = ItemSaleLimitConfigDTO.builder().tenantId(123456L).saleLimitQuantity(11).saleLimitRule(1).marketItemId(10086L).build();

        // Run the test
        final boolean result = itemSaleLimitConfigServiceImplUnderTest.saveItemSaleLimitConfig(itemSaleLimitConfigDTO);

        // Verify the results
        assertTrue(result);
    }


    @Test
    void testQueryItemSaleLimitConfig1() {
        // Setup
        final ItemSaleLimitConfigDTO expectedResult = ItemSaleLimitConfigDTO.builder().saleLimitQuantity(11).saleLimitRule(1).build();

        // Run the test
        final ItemSaleLimitConfigDTO result = itemSaleLimitConfigServiceImplUnderTest.queryItemSaleLimitConfig(123456L, 10086L);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}