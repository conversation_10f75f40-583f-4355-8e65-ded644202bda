package com.cosfo.manage;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;

import java.util.stream.Stream;

@SpringBootTest(classes = TestApplication.class)
public class CosfoManageApplicationTests {

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    void contextLoads() {
        System.out.println(applicationContext.getBeanDefinitionCount());
        Stream.of(applicationContext.getBeanDefinitionNames()).forEach(e -> System.out.println(applicationContext.getBean(e).getClass()));
    }
}
