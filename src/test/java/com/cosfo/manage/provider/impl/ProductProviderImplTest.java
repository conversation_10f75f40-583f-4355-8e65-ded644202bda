package com.cosfo.manage.provider.impl;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.manage.TestApplication;
import com.cosfo.manage.client.page.PageResp;
import com.cosfo.manage.client.product.ProductProvider;
import com.cosfo.manage.client.product.req.ProductCategoryIdQueryReq;
import com.cosfo.manage.client.product.req.ProductQueryReq;
import com.cosfo.manage.client.product.req.SummerFarmSynchronizedSkuReq;
import com.cosfo.manage.client.product.resp.ProductInfoResp;
import com.cosfo.manage.client.product.resp.ProductSkuCodeResp;
import com.cosfo.manage.good.service.ProductService;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/1/30 13:53
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = TestApplication.class)
public class ProductProviderImplTest {

    @Resource
    private ProductProvider provider;

    @Autowired
    private ProductService productService;

    @Test
    public void test() {
        ProductQueryReq req = new ProductQueryReq();
        req.setSummerfarmSkuIds(Arrays.asList(12270L, 12269L));
        DubboResponse<ProductInfoResp> productInfoRespDubboResponse = provider.batchQueryBySummerfarmSkuIds(req);
    }

    @Test
    public void doneSkuSynchronizedTest(){
        String param = "{\"detailPicture\":\"\",\"firstCategoryId\":963,\"firstCategoryName\":\"后台消费水果是\",\"guaranteePeriod\":15,\"guaranteeUnit\":0,\"mainPicture\":\"\",\"secondCategoryId\":967,\"secondCategoryName\":\"巴拿马香蕉\",\"sku\":\"************\",\"skuId\":16665,\"skuIds\":[16665],\"specification\":\"10KG*10KG/精品/80～120\",\"specificationUnit\":\"件\",\"spuId\":16023,\"storageLocation\":0,\"subTitle\":\"\",\"thirdCategoryId\":968,\"thirdCategoryName\":\"巴拿马香蕉奥地利分蕉\",\"title\":\"zyt测试香蕉\",\"type\":0,\"volume\":\"0.30*0.20*0.30\",\"weightNum\":10.00}";
        SummerFarmSynchronizedSkuReq summerFarmSynchronizedSkuReq = JSONObject.parseObject(param, SummerFarmSynchronizedSkuReq.class);
        provider.doneSkuSynchronized(summerFarmSynchronizedSkuReq);
    }

    @Test
    public void testQuery() {
        ProductCategoryIdQueryReq req = new ProductCategoryIdQueryReq();
        req.setTenantId(2L);
        req.setCategoryId(22L);

        DubboResponse<PageResp<ProductSkuCodeResp>> resp = provider.queryProductBySaasCategoryId(req);
        System.err.println(resp.getData());
    }

    @Test
    public void testGenerate() {
        productService.generateImportProductTemplate();
    }
}
