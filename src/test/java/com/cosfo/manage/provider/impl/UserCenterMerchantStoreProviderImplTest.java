package com.cosfo.manage.provider.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.client.merchant.req.MerchantStoreQueryReq;
import com.cosfo.manage.client.merchant.resp.MerchantStoreAddressResp;
import com.cosfo.manage.client.merchant.resp.MerchantStoreResp;
import com.cosfo.manage.client.tenant.req.TenantQueryReq;
import com.cosfo.manage.client.tenant.resp.TenantInfoResp;
import com.cosfo.manage.client.tenant.resp.TenantResp;
import com.google.common.collect.Lists;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-08-03
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class UserCenterMerchantStoreProviderImplTest {


    @Resource
    private MerchantStoreProviderImpl merchantStoreProviderImpl;

//    @Resource
//    private MerchantStoreProviderOldImpl merchantStoreProviderOldImpl;

    @Test
    public void batchQueryStoreInfo() {
        List<Long> storeIds = Lists.newArrayList(1534L, 141957L, 4483L);
        MerchantStoreQueryReq merchantStoreQueryReq = new MerchantStoreQueryReq();
        merchantStoreQueryReq.setStoreIds(storeIds);
        DubboResponse<List<MerchantStoreResp>> newResponse = merchantStoreProviderImpl.batchQueryStoreInfo(merchantStoreQueryReq);
//        DubboResponse<List<MerchantStoreResp>> oldResponse = merchantStoreProviderOldImpl.batchQueryStoreInfo(merchantStoreQueryReq);
//
//        System.err.println(JSON.toJSONString(newResponse).equals(JSON.toJSONString(oldResponse)));
    }

    @Test
    public void batchQueryStoreAddress() {
        Long tenantId = 2L;
        DubboResponse<List<MerchantStoreAddressResp>> newResponse = merchantStoreProviderImpl.batchQueryStoreAddress(tenantId);
//        DubboResponse<List<MerchantStoreAddressResp>> oldResponse = merchantStoreProviderOldImpl.batchQueryStoreAddress(tenantId);
//
//        System.err.println(JSON.toJSONString(newResponse).equals(JSON.toJSONString(oldResponse)));
    }

    @Test
    public void batchQueryStoreAddressByIds() {
        Long tenantId = 2L;
        List<Long> storeIds = Lists.newArrayList(1534L, 141957L, 4483L);
        DubboResponse<List<MerchantStoreAddressResp>> newResponse = merchantStoreProviderImpl.batchQueryStoreAddressByIds(tenantId, storeIds);
//        DubboResponse<List<MerchantStoreAddressResp>> oldResponse = merchantStoreProviderOldImpl.batchQueryStoreAddressByIds(tenantId, storeIds);
//
//        System.err.println(JSON.toJSONString(newResponse).equals(JSON.toJSONString(oldResponse)));


        storeIds = Lists.newArrayList();
        newResponse = merchantStoreProviderImpl.batchQueryStoreAddressByIds(tenantId, storeIds);
//        oldResponse = merchantStoreProviderOldImpl.batchQueryStoreAddressByIds(tenantId, storeIds);
//
//        System.err.println(JSON.toJSONString(newResponse).equals(JSON.toJSONString(oldResponse)));
    }

    @Test
    public void queryStoreAddress() {
        Long tenantId = 2L;
        Long storeId = 1534L;
        DubboResponse<MerchantStoreAddressResp> newResponse = merchantStoreProviderImpl.queryStoreAddress(tenantId, storeId);
//        DubboResponse<MerchantStoreAddressResp> oldResponse = merchantStoreProviderOldImpl.queryStoreAddress(tenantId, storeId);
//
//        System.err.println(JSON.toJSONString(newResponse).equals(JSON.toJSONString(oldResponse)));
    }
}
