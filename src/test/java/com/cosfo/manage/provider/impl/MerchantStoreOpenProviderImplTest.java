package com.cosfo.manage.provider.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.client.merchant.openapi.MerchantStoreOpenProvider;
import com.cosfo.manage.client.merchant.req.MerchantStoreBatchCommandReq;
import com.cosfo.manage.client.merchant.req.MerchantStoreCloseReq;
import com.cosfo.manage.client.merchant.req.MerchantStoreOpenReq;
import com.cosfo.manage.client.merchant.resp.MerchantStoreOpenResp;
import com.cosfo.manage.common.config.OpenApiConfig;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.context.DefaultFlagEnum;
import com.cosfo.manage.common.context.MerchantAccountTypeEnum;
import com.cosfo.manage.common.context.StoreTypeEnum;
import com.cosfo.manage.common.task.NingjiStoreAutoAuditTask;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.account.IsvInfo;
import net.xianmu.common.account.IsvInfoHolder;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.robot.feishu.SignedFeishuBotUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-09-19
 * @Description:
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class MerchantStoreOpenProviderImplTest {

//    @DubboReference
    @Resource
    private MerchantStoreOpenProvider merchantStoreOpenProvider;

    @Resource
    private OpenApiConfig feishuBotConfig;

    @Resource
    private NingjiStoreAutoAuditTask ningjiStoreAutoAuditTask;

    @Test
    public void closeStore() {
        Long tenantId = 24579L;
        IsvInfo isvInfo = new IsvInfo();
        isvInfo.setAccountId(tenantId);
        IsvInfoHolder.setAccount(isvInfo);

        // 门店基础信息
        MerchantStoreCloseReq merchantStoreCloseReq = new MerchantStoreCloseReq();
        merchantStoreCloseReq.setStoreNo("0926");

        DubboResponse<Void> response = merchantStoreOpenProvider.closeStore(merchantStoreCloseReq);
    }


    @Test
    public void upsertStoreInfo() {
//        Long tenantId = 24579L;
        Long tenantId = 2L;
        IsvInfo isvInfo = new IsvInfo();
        isvInfo.setAccountId(tenantId);
        IsvInfoHolder.setAccount(isvInfo);

        // 门店基础信息
        MerchantStoreOpenReq merchantStoreOpenReq = new MerchantStoreOpenReq();
        merchantStoreOpenReq.setStoreName("********店铺");
        merchantStoreOpenReq.setStoreNo("********");
        merchantStoreOpenReq.setType(StoreTypeEnum.DIRECT.getCode());

        // 门店地址、门店联系人
        List<MerchantStoreOpenReq.ContactReq> contactList = Lists.newArrayList();
        MerchantStoreOpenReq.ContactReq contactReq = new MerchantStoreOpenReq.ContactReq();
        contactReq.setContactName("范松松");
        contactReq.setPhone("***********");
        contactReq.setDefaultFlag(DefaultFlagEnum.TURE.getFlag());

        MerchantStoreOpenReq.AddressReq addressInfo = new MerchantStoreOpenReq.AddressReq();
        addressInfo.setProvince("浙江省");
        addressInfo.setCity("杭州市");
        addressInfo.setArea("西湖区");
        addressInfo.setAddress("数娱大厦5楼");
//        MerchantStoreOpenReq.AddressReq addressInfo = new MerchantStoreOpenReq.AddressReq();
//        addressInfo.setProvince("云南省");
//        addressInfo.setCity("昆明市");
//        addressInfo.setArea("富民县");
//        addressInfo.setAddress("富民镇103号");
        contactReq.setAddressInfo(addressInfo);
        contactList.add(contactReq);
        merchantStoreOpenReq.setContactList(contactList);

        // 门店账号
        List<MerchantStoreOpenReq.AccountReq> accountList = Lists.newArrayList();
        MerchantStoreOpenReq.AccountReq accountReq = new MerchantStoreOpenReq.AccountReq();
        accountReq.setPhone("***********");
        accountReq.setAccountName("小店员");
        accountReq.setType(MerchantAccountTypeEnum.CLERK.getType());
        accountList.add(accountReq);

        merchantStoreOpenReq.setAccountList(accountList);

        String json = "{\"accountList\":[{\"accountName\":\"小药员\",\"phone\":\"***********\",\"type\":1}],\"contactList\":[{\"addressInfo\":{\"address\":\"hhhhhhaa\",\"area\":\"月湖区\",\"city\":\"鹰潭市\",\"province\":\"江西省\"},\"contactName\":\"药师\",\"defaultFlag\":0,\"phone\":\"***********\"}],\"storeName\":\"********门店下发\",\"storeNo\":\"********\",\"type\":0}";
        MerchantStoreOpenReq req2 = JSON.parseObject(json, MerchantStoreOpenReq.class);

        DubboResponse<MerchantStoreOpenResp> response = merchantStoreOpenProvider.upsertStoreInfo(req2);
    }

    @Test
    public void upsertStoreBatch() {
        String json = "{\"storeList\":[{\"storeNo\":\"AS0101\",\"contactList\":[{\"addressInfo\":{\"area\":\"\",\"address\":\"贵州省安顺市黄果树大街119号国际佳缘5栋商铺1楼20号（大润发先吃街内）（本来不该有）\",\"province\":\"贵州省\",\"city\":\"安顺市\"},\"defaultFlag\":1,\"phone\":\"\",\"contactName\":\"\"}],\"accountList\":[{\"accountName\":\"admin\",\"phone\":\"\",\"type\":1},{\"accountName\":\"贵州安顺西秀区国际佳缘店\",\"phone\":\"\",\"type\":1},{\"accountName\":\"TEST\",\"phone\":\"\",\"type\":1}],\"storeName\":\"AS0101门市\",\"type\":1},{\"storeNo\":\"BJ0101\",\"contactList\":[{\"addressInfo\":{\"area\":\"\",\"address\":\"贵州省毕节市七星关区学院路党校门口\",\"province\":\"贵州省\",\"city\":\"毕节市\"},\"defaultFlag\":1,\"phone\":\"\",\"contactName\":\"\"}],\"accountList\":[{\"accountName\":\"1143贵州省毕节市七星关区学院路店\",\"phone\":\"\",\"type\":1}],\"storeName\":\"BJ0101门市\",\"type\":1},{\"storeNo\":\"BJ0102\",\"contactList\":[{\"addressInfo\":{\"area\":\"\",\"address\":\"贵州省毕节市七星关区创美世纪售楼部门口\",\"province\":\"贵州省\",\"city\":\"毕节市\"},\"defaultFlag\":1,\"phone\":\"\",\"contactName\":\"\"}],\"accountList\":[{\"accountName\":\"1398贵州毕节七星关区创美世纪城店\",\"phone\":\"\",\"type\":1}],\"storeName\":\"BJ0102门市\",\"type\":1},{\"storeNo\":\"CAZ0201\",\"contactList\":[{\"addressInfo\":{\"area\":\"\",\"address\":\"江苏省常州市新北区锦绣路2号文化广场-1楼30号（本来不该有）\",\"province\":\"江苏省\",\"city\":\"常州市\"},\"defaultFlag\":1,\"phone\":\"\",\"contactName\":\"\"}],\"accountList\":[{\"accountName\":\"945江苏常州文化广场店\",\"phone\":\"\",\"type\":1}],\"storeName\":\"CAZ0201门市\",\"type\":1},{\"storeNo\":\"CAZ0401\",\"contactList\":[{\"addressInfo\":{\"area\":\"\",\"address\":\"江苏省常州市溧阳市溧城街道天目路41号\",\"province\":\"江苏省\",\"city\":\"常州市\"},\"defaultFlag\":1,\"phone\":\"\",\"contactName\":\"\"}],\"accountList\":[{\"accountName\":\"1429江苏常州溧阳市天目路店\",\"phone\":\"\",\"type\":1}],\"storeName\":\"CAZ0401门市\",\"type\":1},{\"storeNo\":\"CD0201\",\"contactList\":[{\"addressInfo\":{\"area\":\"\",\"address\":\"四川省雅安市雨城区人民路48号本来不该有鲜果咖啡\",\"province\":\"四川省\",\"city\":\"成都市\"},\"defaultFlag\":1,\"phone\":\"\",\"contactName\":\"\"}],\"accountList\":[{\"accountName\":\"999四川雅安市雨城区人民路店\",\"phone\":\"\",\"type\":1}],\"storeName\":\"CD0201门市\",\"type\":1},{\"storeNo\":\"CHD0101\",\"contactList\":[{\"addressInfo\":{\"area\":\"\",\"address\":\"湖南省常德市武陵区万达写字楼B座理想汽车旁\",\"province\":\"湖南省\",\"city\":\"常德市\"},\"defaultFlag\":1,\"phone\":\"\",\"contactName\":\"\"}],\"accountList\":[{\"accountName\":\"1087湖南常德万达\",\"phone\":\"\",\"type\":1}],\"storeName\":\"CHD0101门市\",\"type\":1},{\"storeNo\":\"CS0201\",\"contactList\":[{\"addressInfo\":{\"area\":\"\",\"address\":\"湖南省长沙市岳麓区观沙岭街道龙湖铂金岛T2-A1层103号\",\"province\":\"湖南省\",\"city\":\"长沙\"},\"defaultFlag\":1,\"phone\":\"\",\"contactName\":\"\"}],\"accountList\":[{\"accountName\":\"1189湖南长沙岳麓龙湖铂金岛店\",\"phone\":\"\",\"type\":1}],\"storeName\":\"CS0201门市\",\"type\":1},{\"storeNo\":\"CS0301\",\"contactList\":[{\"addressInfo\":{\"area\":\"\",\"address\":\"湖南省长沙市岳麓区阜埠河路169号 本来不该有咖啡\",\"province\":\"湖南省\",\"city\":\"长沙\"},\"defaultFlag\":1,\"phone\":\"\",\"contactName\":\"\"}],\"accountList\":[{\"accountName\":\"1274湖南长沙岳麓区阜埠河店\",\"phone\":\"\",\"type\":1}],\"storeName\":\"CS0301门市\",\"type\":1},{\"storeNo\":\"CUZ0101\",\"contactList\":[{\"addressInfo\":{\"area\":\"\",\"address\":\"安徽省滁州市琅琊区南谯北路求知书阁\",\"province\":\"安徽省\",\"city\":\"滁州市\"},\"defaultFlag\":1,\"phone\":\"\",\"contactName\":\"\"}],\"accountList\":[{\"accountName\":\"1423安徽省滁州市南谯北路店\",\"phone\":\"\",\"type\":1}],\"storeName\":\"CUZ0101门市\",\"type\":1},{\"storeNo\":\"CZ0101\",\"contactList\":[{\"addressInfo\":{\"area\":\"\",\"address\":\"广东省潮州市潮安区庵埠镇潮州潮安阳光水岸店彩文路与龙桥路交界东北侧13栋一号铺\",\"province\":\"广东省\",\"city\":\"潮州市\"},\"defaultFlag\":1,\"phone\":\"\",\"contactName\":\"\"}],\"accountList\":[{\"accountName\":\"本来不该有（潮州潮安店）\",\"phone\":\"\",\"type\":1}],\"storeName\":\"CZ0101门市\",\"type\":1},{\"storeNo\":\"CZ0201\",\"contactList\":[{\"addressInfo\":{\"area\":\"\",\"address\":\"湘桥区电信路中段北侧潮州市百熙城综合楼首层4号商铺\",\"province\":\"广东省\",\"city\":\"潮州市\"},\"defaultFlag\":1,\"phone\":\"\",\"contactName\":\"\"}],\"accountList\":[{\"accountName\":\"潮州市湘桥店\",\"phone\":\"\",\"type\":1}],\"storeName\":\"CZ0201门市\",\"type\":1},{\"storeNo\":\"CZ0301\",\"contactList\":[{\"addressInfo\":{\"area\":\"\",\"address\":\"广东省潮州市湘桥区凤城花园1号铺面（本来不该有）\",\"province\":\"广东省\",\"city\":\"潮州市\"},\"defaultFlag\":1,\"phone\":\"\",\"contactName\":\"\"}],\"accountList\":[{\"accountName\":\"潮州湘桥区绿茵店\",\"phone\":\"\",\"type\":1}],\"storeName\":\"CZ0301门市\",\"type\":1},{\"storeNo\":\"CZ0401\",\"contactList\":[{\"addressInfo\":{\"area\":\"\",\"address\":\"潮州市潮安区金石镇金石大道金湖花园7-8铺面（本来不该有）\",\"province\":\"广东省\",\"city\":\"潮州市\"},\"defaultFlag\":1,\"phone\":\"\",\"contactName\":\"\"}],\"accountList\":[{\"accountName\":\"984潮州金石店\",\"phone\":\"\",\"type\":1}],\"storeName\":\"CZ0401门市\",\"type\":1},{\"storeNo\":\"CZ0501\",\"contactList\":[{\"addressInfo\":{\"area\":\"\",\"address\":\"广东省潮州市饶平县海博现代城55号\",\"province\":\"广东省\",\"city\":\"潮州市\"},\"defaultFlag\":1,\"phone\":\"\",\"contactName\":\"\"}],\"accountList\":[{\"accountName\":\"1141广东潮州饶平店\",\"phone\":\"\",\"type\":1}],\"storeName\":\"CZ0501门市\",\"type\":1},{\"storeNo\":\"CZH0101\",\"contactList\":[{\"addressInfo\":{\"area\":\"\",\"address\":\"广西崇左扶绥县新宁镇空港大道，中都，新世界，本来不该有咖啡\",\"province\":\"广西省\",\"city\":\"崇左市\"},\"defaultFlag\":1,\"phone\":\"\",\"contactName\":\"\"}],\"accountList\":[{\"accountName\":\"1355广西崇左中都新世界广场店\",\"phone\":\"\",\"type\":1}],\"storeName\":\"CZH0101门市\",\"type\":1},{\"storeNo\":\"DG0101\",\"contactList\":[{\"addressInfo\":{\"area\":\"\",\"address\":\"东莞市寮步万润财富中心香市路80号16栋104\",\"province\":\"广东省\",\"city\":\"东莞市\"},\"defaultFlag\":1,\"phone\":\"\",\"contactName\":\"\"}],\"accountList\":[{\"accountName\":\"东莞寮步店\",\"phone\":\"\",\"type\":1}],\"storeName\":\"DG0101门市\",\"type\":1},{\"storeNo\":\"DG0201\",\"contactList\":[{\"addressInfo\":{\"area\":\"\",\"address\":\"广东省东莞市南城第一国际商业中心BX1-03313\",\"province\":\"广东省\",\"city\":\"东莞市\"},\"defaultFlag\":1,\"phone\":\"\",\"contactName\":\"\"}],\"accountList\":[{\"accountName\":\"东莞汇一城店\",\"phone\":\"\",\"type\":1}],\"storeName\":\"DG0201门市\",\"type\":1},{\"storeNo\":\"DG0501\",\"contactList\":[{\"addressInfo\":{\"area\":\"\",\"address\":\"广东省东莞市石龙镇汇星商业中心麦当劳隔壁（本来不该有）\",\"province\":\"广东省\",\"city\":\"东莞市\"},\"defaultFlag\":1,\"phone\":\"\",\"contactName\":\"\"}],\"accountList\":[{\"accountName\":\"东莞石龙汇星商业中心\",\"phone\":\"\",\"type\":1}],\"storeName\":\"DG0501门市\",\"type\":1}]}";

        MerchantStoreBatchCommandReq merchantStoreBatchCommandReq = JSON.parseObject(json, MerchantStoreBatchCommandReq.class);
        Long tenantId = 2L;
        IsvInfo isvInfo = new IsvInfo();
        isvInfo.setAccountId(tenantId);
        IsvInfoHolder.setAccount(isvInfo);
        merchantStoreOpenProvider.upsertStoreBatch(merchantStoreBatchCommandReq);
    }

    @Test
    public void sendTextMsgAndAtAll() {
        StringBuilder text = new StringBuilder();
        text.append("门店待审核提醒").append(Constants.LINE).append("租户ID：").append(2L).append(Constants.LINE).append("门店ID：").append(5L).append(Constants.LINE).append("门店编号：");
        text.append("haha123").append(Constants.LINE).append("门店名称：").append("0919测试店铺").append(Constants.LINE).append("门店处于待审核状态，请更新该门店与城配仓绑定关系");

        log.info("发送飞书群告警消息,text:{}", text.toString());
        SignedFeishuBotUtil.sendTextMsgAndAtAll(feishuBotConfig.getOpenApiWarnUrl(), text.toString(), feishuBotConfig.getOpenApiWarnUrlSign());
    }


    @Test
    public void autoAuditStore(){
        ningjiStoreAutoAuditTask.autoAuditStore(143182L);
    }

}
