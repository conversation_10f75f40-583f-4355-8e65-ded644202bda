package com.cosfo.manage.provider.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.client.tenant.TenantProvider;
import com.cosfo.manage.merchant.service.MerchantStoreService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest
public class TenantProviderImplTest {

    @Resource
    private MerchantStoreService storeService;
    @Resource
    private TenantProvider tenantProvider;

    @Resource
    private RedisTemplate redisTemplate;
    @Test
    public void listAddressTest() {
        System.out.println (storeService.listAddress (1003L));
    }

    @Test
    public void getTenantBaseInfo() {
        System.out.println(JSON.toJSONString(tenantProvider.getTenantBaseInfo(1003L)));
        System.out.println(JSON.toJSONString(tenantProvider.getTenantBaseInfo(2L)));
    }
}
