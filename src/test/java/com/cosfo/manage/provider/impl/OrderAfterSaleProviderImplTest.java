package com.cosfo.manage.provider.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.cosfo.manage.order.service.OrderAfterSaleService;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
public class OrderAfterSaleProviderImplTest {

    @Resource
    private OrderAfterSaleService orderAfterSaleService;


    @Test
    public void testQuery() {
        Long tenantId = 2L;
        LocalDateTime startTime = LocalDateTimeUtil.now().plusDays(-2).toLocalDate().atStartOfDay();
        LocalDateTime endTime = LocalDateTime.now().plusDays(-1).toLocalDate().atStartOfDay();
        List<OrderAfterSaleResp> list = orderAfterSaleService.queryBillOrderByStartTimeAndEndTime (tenantId, startTime, endTime);
        System.err.println(list);
    }

}
