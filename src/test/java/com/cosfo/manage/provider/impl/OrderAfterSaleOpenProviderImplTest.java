package com.cosfo.manage.provider.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.client.order.openapi.OrderAfterSaleOpenProvider;
import com.cosfo.manage.client.order.req.OrderAfterSaleBatchReq;
import com.cosfo.manage.client.order.req.OrderAfterSaleReq;
import com.cosfo.ordercenter.client.common.OrderAfterSaleServiceTypeEnum;
import com.cosfo.ordercenter.client.common.OrderAfterSaleTypeEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.account.IsvInfo;
import net.xianmu.common.account.IsvInfoHolder;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-09-20
 * @Description:
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class OrderAfterSaleOpenProviderImplTest {

    @Resource
    private OrderAfterSaleOpenProvider orderAfterSaleOpenProvider;

    @Test
    public void batchCreateAfterSale() {
        Long tenantId = 24589L;
        IsvInfo isvInfo = new IsvInfo();
        isvInfo.setAccountId(tenantId);
        IsvInfoHolder.setAccount(isvInfo);
//
//        OrderAfterSaleBatchReq orderAfterSaleBatchReq = new OrderAfterSaleBatchReq();
//        OrderAfterSaleReq orderAfterSaleReq = new OrderAfterSaleReq();
//        orderAfterSaleReq.setCustomerOrderId("NJ158426693");
//        orderAfterSaleReq.setCustomerOrderItemId("NJ1006");
//        orderAfterSaleReq.setCustomerAfterSaleOrderNo("NJAS10066");
//        orderAfterSaleReq.setAfterSaleType(OrderAfterSaleTypeEnum.DELIVERED.getType());
//        orderAfterSaleReq.setAmount(1);
//        orderAfterSaleReq.setServiceType(OrderAfterSaleServiceTypeEnum.REFUND_ENTER_BILL.getValue());
//        orderAfterSaleReq.setReason("不想要了");
//
//        OrderAfterSaleReq orderAfterSaleReq2 = new OrderAfterSaleReq();
//        orderAfterSaleReq2.setCustomerOrderId("NJ158426693");
//        orderAfterSaleReq2.setCustomerOrderItemId("NJ1007");
//        orderAfterSaleReq2.setCustomerAfterSaleOrderNo("NJAS10077");
//        orderAfterSaleReq2.setAfterSaleType(OrderAfterSaleTypeEnum.DELIVERED.getType());
//        orderAfterSaleReq2.setAmount(1);
//        orderAfterSaleReq2.setServiceType(OrderAfterSaleServiceTypeEnum.REFUND_ENTER_BILL.getValue());
//        orderAfterSaleReq2.setReason("不想要了");
//        orderAfterSaleBatchReq.setApplyAfterSaleList(Lists.newArrayList(orderAfterSaleReq, orderAfterSaleReq2));
//        String param = JSON.toJSONString(orderAfterSaleReq);
//        System.err.println(param);
        String json = "{\n" +
                "    \"applyAfterSaleList\": [\n" +
                "        {\n" +
                "            \"afterSaleType\": 0,\n" +
                "            \"amount\": 1,\n" +
                "            \"customerAfterSaleOrderNo\": \"NJAS1006666666\",\n" +
                "            \"customerOrderId\": \"nj20230928004\",\n" +
                "            \"customerOrderItemId\": \"NJ1001\",\n" +
                "            \"reason\": \"不想要了\",\n" +
                "            \"serviceType\": 2\n" +
                "        },\n" +
                "        {\n" +
                "            \"afterSaleType\": 0,\n" +
                "            \"amount\": 1,\n" +
                "            \"customerAfterSaleOrderNo\": \"NJAS1007777777\",\n" +
                "            \"customerOrderId\": \"nj20230928004\",\n" +
                "            \"customerOrderItemId\": \"NJ1002\",\n" +
                "            \"reason\": \"不想要了\",\n" +
                "            \"serviceType\": 2\n" +
                "        }\n" +
                "    ]\n" +
                "}";
        OrderAfterSaleBatchReq orderAfterSaleBatchReq = JSON.parseObject(json, OrderAfterSaleBatchReq.class);
        DubboResponse<Boolean> response = orderAfterSaleOpenProvider.batchCreateAfterSale(orderAfterSaleBatchReq);

    }
}
