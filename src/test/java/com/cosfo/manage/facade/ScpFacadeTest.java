package com.cosfo.manage.facade;

import com.google.common.collect.Table;
import net.xianmu.scp.client.req.replenishment.ReplenishmentStockConfigBaseQueryInput;
import net.xianmu.scp.client.resp.replenishment.ReplenishmentStockConfigDTO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest
class ScpFacadeTest {


    @Resource
    ScpFacade ScpFacade;


    @Test
    void queryReplenishmentStockConfig() {
        List<ReplenishmentStockConfigBaseQueryInput> replenishmentStockConfigBaseQueryInputs = new ArrayList<>();
        ReplenishmentStockConfigBaseQueryInput queryInput = new ReplenishmentStockConfigBaseQueryInput();
        queryInput.setSku("50132414307");
        queryInput.setWarehouseNo(1);
        replenishmentStockConfigBaseQueryInputs.add(queryInput);
        Table<String, Integer, ReplenishmentStockConfigDTO> table = ScpFacade.queryReplenishmentStockConfig(2L, replenishmentStockConfigBaseQueryInputs);
        System.err.println(table);
    }
}