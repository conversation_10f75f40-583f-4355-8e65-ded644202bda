package com.cosfo.manage.facade.usercenter;

import com.cosfo.manage.common.context.MerchantAccountTypeEnum;
import com.cosfo.manage.common.context.MerchantStoreAccountStatusEnum;
import com.google.common.collect.Lists;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountCommandReq;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.ArrayList;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class UserCenterMerchantStoreAccountFacadeTest {

    @Resource
    private UserCenterMerchantStoreAccountFacade userCenterMerchantStoreAccountFacade;

    @Test
    void createMerchantStoreAccount() {
        ArrayList<Integer> storeIds = Lists.newArrayList(4639, 4445, 4434, 4400, 4399, 4396, 4393, 4336, 4335, 4322, 4321, 4319, 4318, 4252, 4238, 4196, 4193, 4192, 4089, 4053, 3987, 3986, 3985, 3984, 3983, 3982, 3485, 3454, 3452, 3451, 3450, 3398, 3396, 3386, 3385, 3384, 3380, 3379, 3378, 3377, 3350, 3348, 3347, 3343, 3326, 3325, 3316, 3312, 3311);
        for (Integer storeId : storeIds) {
            MerchantStoreAccountCommandReq insertAccount = new MerchantStoreAccountCommandReq();
            insertAccount.setTenantId(1003L);
            insertAccount.setStoreId(Long.valueOf(storeId));
            insertAccount.setAccountName("测试123");
            insertAccount.setPhone("***********");
            insertAccount.setType(MerchantAccountTypeEnum.CLERK.getType());
            insertAccount.setRegisterTime(LocalDateTime.now());
            insertAccount.setAuditTime(LocalDateTime.now());
            insertAccount.setStatus((MerchantStoreAccountStatusEnum.AUDIT_SUCCESS.getStatus()));

            userCenterMerchantStoreAccountFacade.createMerchantStoreAccount(insertAccount);
        }


    }
}