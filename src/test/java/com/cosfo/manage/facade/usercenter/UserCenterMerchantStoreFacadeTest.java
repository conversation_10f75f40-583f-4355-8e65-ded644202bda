package com.cosfo.manage.facade.usercenter;

import com.cosfo.manage.common.context.BillSwitchEnum;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreCommandReq;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * @author: xiaowk
 * @time: 2025/1/17 下午3:58
 */
@SpringBootTest
public class UserCenterMerchantStoreFacadeTest {

    @Resource
    private UserCenterMerchantStoreFacade userCenterMerchantStoreFacade;


    @Test
    void updateMerchantStore() {
        MerchantStoreCommandReq req = new MerchantStoreCommandReq();
        req.setId(4260L);
        req.setBillSwitch(BillSwitchEnum.OPEN.getCode());
//        req.setBillSwitch(BillSwitchEnum.SHUTDOWN.getCode());
        Boolean flag = userCenterMerchantStoreFacade.updateMerchantStore(req);
    }
}
