package com.cosfo.manage.facade.ordercenter;

import com.cosfo.ordercenter.client.req.*;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleEnableResp;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleWithOrderResp;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class OrderAfterSaleQueryFacadeTest {

    @Resource
    private OrderAfterSaleQueryFacade orderAfterSaleQueryFacade;

    @Test
    void queryByOrderId() {
        List<OrderAfterSaleResp> orderAfterSaleResps = orderAfterSaleQueryFacade.queryByOrderId(93554L, 2L);
        System.out.println(orderAfterSaleResps);
    }

    @Test
    void queryEnableApply() {
        OrderAfterSaleEnableApplyReq req = new OrderAfterSaleEnableApplyReq();
        req.setOrderId(112494L);
        req.setTenantId(2L);
        Map<Long, OrderAfterSaleEnableResp> afterSaleEnableRespMap = orderAfterSaleQueryFacade.queryEnableApply(req);
        System.out.println(afterSaleEnableRespMap);
    }

    @Test
    void countOrderAfterSale() {
        OrderAfterSaleCountReq req = new OrderAfterSaleCountReq();
        req.setTenantId(2L);
        req.setOrderIds(Lists.newArrayList(93554L));
        req.setStoreId(4338L);
        Integer count = orderAfterSaleQueryFacade.countOrderAfterSale(req);
        System.out.println(count);
    }

    @Test
    void queryList() {
        OrderAfterSaleQueryReq req = new OrderAfterSaleQueryReq();
        req.setOrderIds(Lists.newArrayList(93554L));
        req.setTenantId(2L);
        List<OrderAfterSaleResp> orderAfterSaleResps = orderAfterSaleQueryFacade.queryList(req);
        System.out.println(orderAfterSaleResps);
    }

    @Test
    void queryPage() {
        OrderAfterSalePageQueryReq req = new OrderAfterSalePageQueryReq();
        req.setTenantId(2L);
        req.setPageNum(1);
        req.setPageSize(10);
        PageInfo<OrderAfterSaleWithOrderResp> orderAfterSaleResps = orderAfterSaleQueryFacade.queryPage(req);
        System.out.println(orderAfterSaleResps);
    }

    @Test
    void queryByNos() {
        List<OrderAfterSaleResp> orderAfterSaleResps = orderAfterSaleQueryFacade.queryByNos(Lists.newArrayList("AS1769976056249917447"));
        System.out.println(orderAfterSaleResps);
    }

    @Test
    void queryByIds() {
        List<OrderAfterSaleResp> orderAfterSaleResps = orderAfterSaleQueryFacade.queryByIds(Lists.newArrayList(20120L, 20114L));
        System.out.println(orderAfterSaleResps);
    }

    @Test
    void calculateRefundPrice() {
        OrderAfterSaleCalRefundPriceReq req = new OrderAfterSaleCalRefundPriceReq();
        req.setOrderItemId(289315L);
        req.setQuantity(10);
        BigDecimal bigDecimal = orderAfterSaleQueryFacade.calculateRefundPrice(req);
        System.out.println(bigDecimal);
    }

    @Test
    void getRecentlyUsedReturnAddressId() {
        Long recentlyUsedReturnAddressId = orderAfterSaleQueryFacade.getRecentlyUsedReturnAddressId(2L);
        System.out.println(recentlyUsedReturnAddressId);
    }

    @Test
    void queryOrderAfterSaleForBill() {
        QueryBillOrderAfterSaleReq req = new QueryBillOrderAfterSaleReq();
        req.setTenantId(2L);
        req.setStartTime(LocalDateTime.now().minusDays(2));
        req.setEndTime(LocalDateTime.now().minusDays(1));
        List<OrderAfterSaleResp> orderAfterSaleResps = orderAfterSaleQueryFacade.queryOrderAfterSaleForBill(req);
        System.out.println(orderAfterSaleResps);
    }
}