package com.cosfo.manage.facade.ordercenter;

import com.cosfo.ordercenter.client.req.OrderItemQueryReq;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemResp;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class OrderItemQueryFacadeTest {

    @Resource
    private OrderItemQueryFacade orderItemQueryFacade;

    @Test
    void queryById() {
        OrderItemResp orderItemResp = orderItemQueryFacade.queryById(289322L);
        System.out.println(orderItemResp);
    }

    @Test
    void queryByIds() {
        List<OrderItemResp> orderItemResps = orderItemQueryFacade.queryByIds(Lists.newArrayList(289322L, 289285L));
        System.out.println(orderItemResps);
    }

    @Test
    void queryDetailById() {
        OrderItemAndSnapshotResp orderItemResp = orderItemQueryFacade.queryDetailById(289322L);
        System.out.println(orderItemResp);
    }

    @Test
    void queryByOrderId() {
        List<OrderItemAndSnapshotResp> orderItemResps = orderItemQueryFacade.queryByOrderId(112413L);
        System.out.println(orderItemResps);
    }

    @Test
    void queryOrderItemList() {
        List<OrderItemResp> orderItemResps = orderItemQueryFacade.queryOrderItemList(112413L);
        System.out.println(orderItemResps);
    }

    @Test
    void queryOrderItemListByReq() {
        OrderItemQueryReq req = new OrderItemQueryReq();
        req.setTenantId(2L);
        req.setOrderIds(Lists.newArrayList(112413L));
        List<OrderItemAndSnapshotResp> orderItemAndSnapshotResps = orderItemQueryFacade.queryOrderItemList(req);
        System.out.println(orderItemAndSnapshotResps);
    }
}