package com.cosfo.manage.facade.ordercenter;

import com.cosfo.ordercenter.client.req.OrderItemSnapshotQueryReq;
import com.cosfo.ordercenter.client.resp.order.OrderItemSnapshotResp;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class OrderItemSnapshotQueryFacadeTest {

    @Resource
    private OrderItemSnapshotQueryFacade orderItemSnapshotQueryFacade;

    @Test
    void queryByOrderItemId() {
        OrderItemSnapshotResp orderItemSnapshotResp = orderItemSnapshotQueryFacade.queryByOrderItemId(289312L);
        System.out.println(orderItemSnapshotResp);
    }

    @Test
    void queryByOrderItemIds() {
        List<OrderItemSnapshotResp> orderItemSnapshotResps = orderItemSnapshotQueryFacade.queryByOrderItemIds(Lists.newArrayList(289312L, 289294L));
        System.out.println(orderItemSnapshotResps);
    }

    @Test
    void queryList() {
        OrderItemSnapshotQueryReq orderItemSnapshotQueryReq = new OrderItemSnapshotQueryReq();
        orderItemSnapshotQueryReq.setOrderId(112291L);
        orderItemSnapshotQueryReq.setTenantId(2L);
        List<OrderItemSnapshotResp> orderItemSnapshotResps = orderItemSnapshotQueryFacade.queryList(orderItemSnapshotQueryReq);
        System.out.println(orderItemSnapshotResps);
    }
}