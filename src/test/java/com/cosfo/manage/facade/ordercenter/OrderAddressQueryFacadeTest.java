package com.cosfo.manage.facade.ordercenter;

import com.cosfo.ordercenter.client.resp.order.OrderAddressResp;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class OrderAddressQueryFacadeTest {

    @Resource
    private OrderAddressQueryFacade orderAddressQueryFacade;

    @Test
    void queryByOrderId() {
        OrderAddressResp orderAddressResp = orderAddressQueryFacade.queryByOrderId(2L, 112494L);
        System.out.println(orderAddressResp);
    }

    @Test
    void queryByOrderIds() {
        List<OrderAddressResp> orderAddressResps = orderAddressQueryFacade.queryByOrderIds(2L, Lists.newArrayList(112494L, 112502L));
        System.out.println(orderAddressResps);
    }
}