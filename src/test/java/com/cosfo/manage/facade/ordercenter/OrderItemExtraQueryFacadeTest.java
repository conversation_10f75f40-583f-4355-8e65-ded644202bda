package com.cosfo.manage.facade.ordercenter;

import com.cosfo.ordercenter.client.req.OrderItemExtraQueryReq;
import com.cosfo.ordercenter.client.resp.order.OrderItemExtraResp;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootTest
class OrderItemExtraQueryFacadeTest {

    @Resource
    private OrderItemExtraQueryFacade orderItemExtraQueryFacade;

    @Test
    void queryOrderItemExtraListN() {
        OrderItemExtraQueryReq req = new OrderItemExtraQueryReq();
        req.setTenantId(2L);
        req.setOrderId(110842L);
        req.setCustomerOrderItemIdList(Lists.newArrayList("**********"));
        List<OrderItemExtraResp> orderItemExtraResps = orderItemExtraQueryFacade.queryOrderItemExtraList(req);
        System.out.println(orderItemExtraResps);
    }
}