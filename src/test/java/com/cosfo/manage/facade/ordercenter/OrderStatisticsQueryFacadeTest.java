package com.cosfo.manage.facade.ordercenter;

import com.cosfo.ordercenter.client.common.OrderStatusEnum;
import com.cosfo.ordercenter.client.common.WarehouseTypeEnum;
import com.cosfo.ordercenter.client.req.OrderDetailReq;
import com.cosfo.ordercenter.client.req.OrderSkuSaleReq;
import com.cosfo.ordercenter.client.req.OrderSummaryReq;
import com.cosfo.ordercenter.client.req.SupplierOrderTotalReq;
import com.cosfo.ordercenter.client.resp.SupplierOrderTotalResp;
import com.cosfo.ordercenter.client.resp.order.OrderDetailResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderSkuQuantityResp;
import com.cosfo.ordercenter.client.resp.order.OrderSummaryResp;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.text.DateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class OrderStatisticsQueryFacadeTest {

    @Resource
    private OrderStatisticsQueryFacade orderStatisticsQueryFacade;

    @Test
    void queryOrderSummary() {
        OrderSummaryReq orderSummaryReq = new OrderSummaryReq();
        orderSummaryReq.setTenantId(24592L);
        orderSummaryReq.setStoreIds(Lists.newArrayList(162302L));
        orderSummaryReq.setStartTime(LocalDateTime.parse("2024-03-15 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        orderSummaryReq.setEndTime(LocalDateTime.parse("2024-03-16 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        OrderSummaryResp orderSummaryResp = orderStatisticsQueryFacade.queryOrderSummary(orderSummaryReq);
        System.out.println(orderSummaryResp);
    }

    @Test
    void getWaitDeliveryQuantity() {
        Integer waitDeliveryQuantity = orderStatisticsQueryFacade.getWaitDeliveryQuantity(24592L);
        System.out.println(waitDeliveryQuantity);
    }

    @Test
    void querySkuSaleQuantity() {
        OrderSkuSaleReq orderSummaryReq = new OrderSkuSaleReq();
        orderSummaryReq.setTenantId(2L);
        orderSummaryReq.setSkuIds(Lists.newArrayList(31969L));
        orderSummaryReq.setStartTime(LocalDateTime.parse("2024-03-15 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        orderSummaryReq.setEndTime(LocalDateTime.parse("2024-03-16 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        List<OrderSkuQuantityResp> orderSkuQuantityResps = orderStatisticsQueryFacade.querySkuSaleQuantity(orderSummaryReq);
        System.out.println(orderSkuQuantityResps);
    }

    @Test
    void querySkuSaleWithStoreNoQuantity() {
        OrderSkuSaleReq orderSummaryReq = new OrderSkuSaleReq();
        orderSummaryReq.setTenantId(2L);
        orderSummaryReq.setSkuIds(Lists.newArrayList(31969L));
        orderSummaryReq.setStartTime(LocalDateTime.parse("2024-03-15 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        orderSummaryReq.setEndTime(LocalDateTime.parse("2024-03-16 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        List<OrderSkuQuantityResp> orderSkuQuantityResps = orderStatisticsQueryFacade.querySkuSaleWithStoreNoQuantity(orderSummaryReq);
        System.out.println(orderSkuQuantityResps);
    }

    @Test
    void querySkuSaleWithCityQuantity() {
        OrderSkuSaleReq orderSummaryReq = new OrderSkuSaleReq();
        orderSummaryReq.setTenantId(2L);
        orderSummaryReq.setSkuIds(Lists.newArrayList(31969L));
        orderSummaryReq.setStartTime(LocalDateTime.parse("2024-03-15 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        orderSummaryReq.setEndTime(LocalDateTime.parse("2024-03-16 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        List<OrderSkuQuantityResp> orderSkuQuantityResps = orderStatisticsQueryFacade.querySkuSaleWithCityQuantity(orderSummaryReq);
        System.out.println(orderSkuQuantityResps);
    }

    @Test
    void queryOrderDetail() {
        OrderDetailReq orderDetailReq = new OrderDetailReq();
        orderDetailReq.setOrderIds(Lists.newArrayList(112417L));
        orderDetailReq.setTenantId(2L);
        List<OrderDetailResp> orderDetailResps = orderStatisticsQueryFacade.queryOrderDetail(orderDetailReq);
        System.out.println(orderDetailResps);
    }

    @Test
    void querySkuSaleQuantityTest() {
        OrderItemSaleResp orderItemSaleResp = orderStatisticsQueryFacade.querySkuSaleQuantity(Lists.newArrayList(112417L), 2L);
        System.out.println(orderItemSaleResp);
    }

    @Test
    void querySupplierOrderSummary() {
        SupplierOrderTotalReq req = new SupplierOrderTotalReq();
        req.setTenantId(2L);
        req.setSupplierIds(Lists.newArrayList(3695L));
        req.setWarehouseType(WarehouseTypeEnum.PROPRIETARY.getCode());
        req.setStatusList(Lists.newArrayList(OrderStatusEnum.WAIT_DELIVERY.getCode(), OrderStatusEnum.WAITING_DELIVERY.getCode(), OrderStatusEnum.SEGMENT_WAITING_DELIVERY.getCode()));
        List<SupplierOrderTotalResp> supplierOrderTotalResps = orderStatisticsQueryFacade.querySupplierOrderSummary(req);
        System.out.println(supplierOrderTotalResps);
    }
}