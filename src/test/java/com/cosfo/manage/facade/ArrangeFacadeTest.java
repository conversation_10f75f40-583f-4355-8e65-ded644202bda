package com.cosfo.manage.facade;

import com.google.common.collect.Lists;
import net.summerfarm.pms.client.req.ReplenishmentSkuQueryReq;
import net.summerfarm.pms.client.resp.ReplenishmentResultResp;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class ArrangeFacadeTest {

    @Resource
    private ArrangeFacade arrangeFacade;
    @Test
    void queryPurchase() {
        ReplenishmentSkuQueryReq replenishmentSkuQueryReq = new ReplenishmentSkuQueryReq();
        replenishmentSkuQueryReq.setUniqueId("1");
        replenishmentSkuQueryReq.setSkuCode("979245627804");
        replenishmentSkuQueryReq.setWarehouseNoList(Lists.newArrayList(2));


        List<ReplenishmentResultResp> replenishmentResultResps = arrangeFacade.queryPurchase(Lists.newArrayList(replenishmentSkuQueryReq));
        System.out.println(replenishmentResultResps);
    }
}