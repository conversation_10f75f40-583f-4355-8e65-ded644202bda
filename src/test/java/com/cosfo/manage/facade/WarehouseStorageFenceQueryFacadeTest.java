package com.cosfo.manage.facade;

import com.google.common.collect.Lists;
import net.summerfarm.wnc.client.provider.warehouse.WarehouseStorageFenceQueryProvider;
import net.summerfarm.wnc.client.req.WarehouseSkuFenceReq;
import net.summerfarm.wnc.client.resp.WarehouseSkuFenceResp;
import net.xianmu.common.result.DubboResponse;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class WarehouseStorageFenceQueryFacadeTest {

    @Resource
    private WarehouseStorageFenceQueryProvider warehouseStorageFenceQueryProvider;

    @Test
    void queryWarehouseSkuFence() {

        WarehouseSkuFenceReq warehouseSkuFenceReq = new WarehouseSkuFenceReq();
        warehouseSkuFenceReq.setSku("50501611780");
        warehouseSkuFenceReq.setWarehouseNos(Lists.newArrayList(328));
        DubboResponse<List<WarehouseSkuFenceResp>> listDubboResponse = warehouseStorageFenceQueryProvider.queryWarehouseSkuFence(Lists.newArrayList(warehouseSkuFenceReq));
        System.out.println(listDubboResponse);
    }
}