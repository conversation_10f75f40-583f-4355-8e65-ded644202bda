package com.cosfo.manage.facade;

import com.alibaba.fastjson.JSON;
import net.summerfarm.wnc.client.resp.WarehouseStorageResp;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

@RunWith(SpringRunner.class)
@SpringBootTest
class WarehouseStorageQueryFacadeTest {

    @Resource
    private WarehouseStorageQueryFacade warehouseStorageQueryFacade;

    @Test
    void queryWarehouseStorageList() {

        List<WarehouseStorageResp> list = warehouseStorageQueryFacade.queryWarehouseStorageList(24589L,null, null);
        System.err.println(JSON.toJSONString(list));

    }

    @Test
    void queryXmWarehouseNosBySkuCity(){
        Map<String, Set<Integer>> stringSetMap = warehouseStorageQueryFacade.queryXmWarehouseNosBySkuCity(Lists.newArrayList("979245627022"), Lists.newArrayList("曲靖市", "昆明市", "中山市", "杭州市"));
        System.err.println(stringSetMap);

    }
}