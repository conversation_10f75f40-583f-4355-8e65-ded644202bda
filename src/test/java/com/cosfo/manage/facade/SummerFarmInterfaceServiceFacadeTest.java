package com.cosfo.manage.facade;

import com.cosfo.manage.report.model.dto.ProductAgentShelfLifeQueryDTO;
import com.google.common.collect.Lists;
import net.summerfarm.wms.saleinventory.dto.req.BatchQueryMinShelfLifePurchaseBatchReq;
import net.xianmu.common.input.PageSortInput;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class SummerFarmInterfaceServiceFacadeTest {

    @Resource
    private SummerFarmInterfaceServiceFacade summerFarmInterfaceServiceFacade;

    @Resource
    private SaasInventoryFacade saasInventoryFacade;

    @Test
    void pageQueryAgentSkuWarehouseData() {
    }

    @Test
    void pageQueryBatchInventory() {
        ProductAgentShelfLifeQueryDTO queryDTO = new ProductAgentShelfLifeQueryDTO();
        queryDTO.setSkuId(15572L);
        queryDTO.setWarehouseNo(1);
        queryDTO.setPageSize(10);
        queryDTO.setPageIndex(1);
        PageSortInput pageSortInput = new PageSortInput();
        pageSortInput.setSortBy("quality_date");
        pageSortInput.setOrderBy("desc");
        queryDTO.setSortList(Lists.newArrayList(pageSortInput));
        saasInventoryFacade.pageQueryPurchaseBatchInventory(queryDTO);
    }

    @Test
    void batchQueryLatestShelfLife() {
        BatchQueryMinShelfLifePurchaseBatchReq batchReq = new BatchQueryMinShelfLifePurchaseBatchReq();
        BatchQueryMinShelfLifePurchaseBatchReq.PurchaseBatchQueryDTO dto= BatchQueryMinShelfLifePurchaseBatchReq.PurchaseBatchQueryDTO.builder().skuId(12103L).warehouseNo(1).build();
        batchReq.setQueryDTOList(Lists.newArrayList(dto));
        saasInventoryFacade.batchQueryLatestShelfLife(batchReq);
    }


}