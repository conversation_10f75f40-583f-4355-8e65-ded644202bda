package com.cosfo.manage.facade;

import com.cosfo.manage.tenant.model.dto.MenuPurviewDTO;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.AuthMenuPurview;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;


@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class AuthUserFacadeTest {

    @Resource
    private AuthUserFacade authUserFacade;

    @Test
    void listUserMenuPurviewById() {
        List<AuthMenuPurview> authMenuPurviews = authUserFacade.listUserMenuPurviewById(2L, 1292L);
        System.out.println(authMenuPurviews);
    }


    @Test
    void listUserMenuPurview() {
        List<MenuPurviewDTO> menuPurviewDTOS = authUserFacade.listUserMenuPurview();
        System.out.println(menuPurviewDTOS);
    }

    @Test
    void listUserSecondaryMenuPurviewById() {
        List<AuthMenuPurview> authMenuPurviews = authUserFacade.listUserSecondaryMenuPurviewById(2L, 1292L);
        System.out.println(authMenuPurviews);
    }
}