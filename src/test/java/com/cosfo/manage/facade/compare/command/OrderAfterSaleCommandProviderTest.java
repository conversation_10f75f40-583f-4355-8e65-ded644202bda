package com.cosfo.manage.facade.compare.command;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.common.SystemSourceEnum;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleCommandProvider;
import com.cosfo.ordercenter.client.req.OrderAfterSaleAuditReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleModifyQuantityReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleUpdateReq;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @author: xiaowk
 * @time: 2024/5/9 下午3:40
 */
@SpringBootTest
public class OrderAfterSaleCommandProviderTest {


    @DubboReference
    private OrderAfterSaleCommandProvider orderAfterSaleCommandProvider;


    @Test
    public void modifyQuantityTest(){
        OrderAfterSaleModifyQuantityReq req = new OrderAfterSaleModifyQuantityReq();
        req.setAfterSaleId(21349L);
        req.setOperatorName("xxx");
        req.setAmount(1);
        req.setApplyPrice(new BigDecimal("2.34"));
        req.setTotalPrice(new BigDecimal("2.34"));
        DubboResponse<Boolean> response = orderAfterSaleCommandProvider.modifyQuantity(req);
        System.err.println(JSON.toJSONString(response));
        Assertions.assertTrue(response.getData());
    }

    @Test
    public void updateByIdTest(){
        // orderAfterSaleMutateService.updateById
        OrderAfterSaleUpdateReq updateReq = new OrderAfterSaleUpdateReq();
        updateReq.setId(21349L);
        updateReq.setAdminRemark("xixixi_AdminRemark");
        updateReq.setAdminRemarkTime(LocalDateTime.now());
        DubboResponse<Boolean> response = orderAfterSaleCommandProvider.updateById(updateReq);
        System.err.println(JSON.toJSONString(response));
        Assertions.assertTrue(response.getData());
    }

    @Test
    public void reviewSubmissionsTest(){
//        Boolean flag = RpcResultUtil.handle(orderAfterSaleMutateService.reviewSubmissions(req));
        String str = "{\"afterSaleOrderNo\":\"AS1788486129696145408\",\"auditStatus\":1,\"totalPrice\":2.34,\"supplierTotalRefundPrice\":2.34,\"handleRemark\":\"退运费，dddddsd\",\"amount\":1,\"recycleTime\":null,\"returnWarehouseNo\":\"379\",\"responsibilityType\":0}";
        OrderAfterSaleAuditReq req = JSON.parseObject(str, OrderAfterSaleAuditReq.class);
        req.setSystemSource(SystemSourceEnum.MANAGE.getCode());

        DubboResponse<Boolean> response = orderAfterSaleCommandProvider.reviewSubmissions(req);
        System.err.println(JSON.toJSONString(response));
        Assertions.assertTrue(response.getData());
    }

}
