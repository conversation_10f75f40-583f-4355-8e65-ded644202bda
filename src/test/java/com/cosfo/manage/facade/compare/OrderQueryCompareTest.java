package com.cosfo.manage.facade.compare;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.req.OrderNeedDeliveryReq;
import com.cosfo.ordercenter.client.req.OrderQueryReq;
import com.cosfo.ordercenter.client.resp.OrderDTO;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.cosfo.ordercenter.client.service.OrderQueryService;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootTest
public class OrderQueryCompareTest {

    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @DubboReference
    private OrderQueryService orderQueryService;

    @Test
    void queryByIdCompareTest() {

        // 普通订单
//        Long orderId = 114174L;

        // 计划单订单
//        Long orderId = 114042L;

        // 组合订单
//        Long orderId = 114223L;

        List<Long> orderIds = Arrays.asList(114174L, 114042L, 114223L);

        for(Long orderId : orderIds) {
            DubboResponse<OrderResp> orderRespDubboResponse = orderQueryProvider.queryById(orderId);
            DubboResponse<OrderDTO> orderDTODubboResponse = orderQueryService.queryById(orderId);
            Assertions.assertEquals(JSON.toJSONString(orderRespDubboResponse.getData()), JSON.toJSONString(orderDTODubboResponse.getData()));
        }

    }

    @Test
    void queryByNoCompareTest() {

        // 普通订单
//        String orderNo = "OR171505258038143";

        // 计划单订单
//        String orderNo = "OR171447048133795";

        // 组合订单
//        String orderNo = "OR171506884151396";

        List<String> orderNos = Arrays.asList("OR171505258038143", "OR171447048133795", "OR171506884151396");

        for(String orderNo : orderNos) {
            DubboResponse<OrderResp> orderRespDubboResponse = orderQueryProvider.queryByNo(orderNo);
            DubboResponse<OrderDTO> orderDTODubboResponse = orderQueryService.queryByNo(orderNo);
            Assertions.assertEquals(JSON.toJSONString(orderRespDubboResponse.getData()), JSON.toJSONString(orderDTODubboResponse.getData()));
        }
    }

    @Test
    void queryByIdsCompareTest() {
        List<Long> orderIds = Arrays.asList(114174L, 114042L, 114223L);

        DubboResponse<List<OrderDTO>> listDubboResponse = orderQueryService.queryByIds(orderIds);
        DubboResponse<List<OrderResp>> response = orderQueryProvider.queryByIds(orderIds);
        Assertions.assertEquals(JSON.toJSONString(response.getData()), JSON.toJSONString(listDubboResponse.getData()));
    }

    @Test
    void queryByNosCompareTest() {
        List<String> orderNos = Arrays.asList("OR171505258038143", "OR171447048133795", "OR171506884151396");

        DubboResponse<List<OrderDTO>> listDubboResponse = orderQueryService.queryByNos(orderNos);
        DubboResponse<List<OrderResp>> response = orderQueryProvider.queryByNos(orderNos);
        Assertions.assertEquals(JSON.toJSONString(response.getData()), JSON.toJSONString(listDubboResponse.getData()));
    }

    @Test
    void queryOrderListCompareTest() {
        OrderQueryReq req = new OrderQueryReq();
        req.setTenantId(2L);
        req.setBatchSize(23);
        DubboResponse<List<OrderDTO>> listDubboResponse = orderQueryService.queryOrderList(req);
        DubboResponse<List<OrderResp>> response = orderQueryProvider.queryOrderList(req);
        Assertions.assertEquals(JSON.toJSONString(response.getData()), JSON.toJSONString(listDubboResponse.getData()));
    }

    @Test
    void queryOrderPageCompareTest() {
        OrderQueryReq queryReq = new OrderQueryReq();
        queryReq.setPageNum(1);
        queryReq.setPageSize(13);
        queryReq.setTenantId(2L);
        DubboResponse<PageInfo<OrderDTO>> listDubboResponse = orderQueryService.queryOrderPage(queryReq);
        DubboResponse<PageInfo<OrderResp>> response = orderQueryProvider.queryOrderPage(queryReq);
        Assertions.assertEquals(JSON.toJSONString(response.getData()), JSON.toJSONString(listDubboResponse.getData()));
    }

    @Test
    void queryNeedDeliveryOrderCompareTest() {
        OrderNeedDeliveryReq queryReq = new OrderNeedDeliveryReq();
        queryReq.setTenantId(2L);
        queryReq.setCreateTime(LocalDateTime.now().minusDays(3));
        DubboResponse<List<String>> listDubboResponse = orderQueryService.queryNeedDeliveryOrder(queryReq);
        DubboResponse<List<String>> response = orderQueryProvider.queryNeedDeliveryOrder(queryReq);
        Assertions.assertEquals(JSON.toJSONString(response.getData()), JSON.toJSONString(listDubboResponse.getData()));
    }
}
