package com.cosfo.manage.facade.compare;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.provider.OrderItemSnapshotQueryProvider;
import com.cosfo.ordercenter.client.req.OrderItemSnapshotQueryReq;
import com.cosfo.ordercenter.client.resp.OrderItemSnapshotDTO;
import com.cosfo.ordercenter.client.resp.order.OrderItemSnapshotResp;
import com.cosfo.ordercenter.client.service.OrderItemSnapshotQueryService;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootTest
public class OrderItemSnapshotQueryCompareTest {
    
    @DubboReference
    private OrderItemSnapshotQueryProvider orderItemSnapshotQueryProvider;
    @DubboReference
    private OrderItemSnapshotQueryService orderItemSnapshotQueryService;

    @Test
    void queryByOrderItemIdCompareTest() {
        List<Long> ids = Arrays.asList(291282L, 291281L, 291280L, 291279L, 291278L, 291277L, 291276L, 291275L, 291274L, 291273L);
        for (Long id : ids) {
            DubboResponse<OrderItemSnapshotResp> orderItemSnapshotRespDubboResponse = orderItemSnapshotQueryProvider.queryByOrderItemId(id);
            DubboResponse<OrderItemSnapshotDTO> orderItemSnapshotDTODubboResponse = orderItemSnapshotQueryService.queryByOrderItemId(id);
            Assertions.assertEquals(JSON.toJSONString(orderItemSnapshotRespDubboResponse.getData()), JSON.toJSONString(orderItemSnapshotDTODubboResponse.getData()));
        }
    }

    @Test
    void queryByOrderItemIdsCompareTest() {
//        List<Long> ids = Lists.newArrayList(289312L, 289294L);
        List<Long> ids = Arrays.asList(291282L, 291281L, 291280L, 291279L, 291278L, 291277L, 291276L, 291275L, 291274L, 291273L);

        DubboResponse<List<OrderItemSnapshotDTO>> listDubboResponse = orderItemSnapshotQueryService.queryByOrderItemIds(ids);
        DubboResponse<List<OrderItemSnapshotResp>> listDubboResponse1 = orderItemSnapshotQueryProvider.queryByOrderItemIds(ids);
        Assertions.assertEquals(JSON.toJSONString(listDubboResponse.getData()), JSON.toJSONString(listDubboResponse1.getData()));
    }

    @Test
    void queryListCompareTest() {
        OrderItemSnapshotQueryReq orderItemSnapshotQueryReq = new OrderItemSnapshotQueryReq();
        orderItemSnapshotQueryReq.setOrderId(114002L);
        orderItemSnapshotQueryReq.setTenantId(2L);
        DubboResponse<List<OrderItemSnapshotDTO>> listDubboResponse = orderItemSnapshotQueryService.queryList(orderItemSnapshotQueryReq);
        DubboResponse<List<OrderItemSnapshotResp>> listDubboResponse1 = orderItemSnapshotQueryProvider.queryList(orderItemSnapshotQueryReq);
        Assertions.assertEquals(JSON.toJSONString(listDubboResponse.getData()), JSON.toJSONString(listDubboResponse1.getData()));
    }
}
