package com.cosfo.manage.facade.compare;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleRuleQueryProvider;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleRuleDTO;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleRuleResp;
import com.cosfo.ordercenter.client.service.OrderAfterSaleRuleService;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootTest
public class OrderAfterSaleRuleQueryCompareTest {
    
    @DubboReference
    private OrderAfterSaleRuleQueryProvider orderAfterSaleRuleQueryProvider;
    @DubboReference
    private OrderAfterSaleRuleService orderAfterSaleRuleService;

    @Test
    void queryByTenantIdCompareTest() {
        DubboResponse<List<OrderAfterSaleRuleResp>> listDubboResponse = orderAfterSaleRuleQueryProvider.queryByTenantId(2L);
        DubboResponse<List<OrderAfterSaleRuleDTO>> listDubboResponse1 = orderAfterSaleRuleService.queryByTenantId(2L);
        Assertions.assertEquals(JSON.toJSONString(listDubboResponse.getData()), JSON.toJSONString(listDubboResponse1.getData()));
    }
}
