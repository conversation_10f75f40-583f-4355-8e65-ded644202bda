package com.cosfo.manage.facade.compare.command;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.common.OrderStatusEnum;
import com.cosfo.ordercenter.client.provider.OrderCommandProvider;
import com.cosfo.ordercenter.client.req.OrderSelfLiftReq;
import com.cosfo.ordercenter.client.req.OrderStatusUpdateReq;
import com.cosfo.ordercenter.client.req.ProfitSharingFinishTimeReq;
import com.cosfo.ordercenter.client.req.event.OrderAuditReq;
import com.cosfo.ordercenter.client.req.event.OrderCloseReq;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: xiaowk
 * @time: 2024/5/8 下午5:23
 */
@SpringBootTest
public class OrderCommandProviderTest {

    @DubboReference
    private OrderCommandProvider orderCommandProvider;


    @Test
    public void batchUpdateProfitSharingFinishTimeTest(){
        ProfitSharingFinishTimeReq req = new ProfitSharingFinishTimeReq();
        List<ProfitSharingFinishTimeReq.ProfitSharingFinishTimeOrder> orderList = new ArrayList<>();
        ProfitSharingFinishTimeReq.ProfitSharingFinishTimeOrder order = new ProfitSharingFinishTimeReq.ProfitSharingFinishTimeOrder();
        order.setOrderId(114223L);
        order.setProfitSharingFinishTime(LocalDateTime.now());
        orderList.add(order);

        ProfitSharingFinishTimeReq.ProfitSharingFinishTimeOrder order1 = new ProfitSharingFinishTimeReq.ProfitSharingFinishTimeOrder();
        order1.setOrderId(114042L);
        order1.setProfitSharingFinishTime(LocalDateTime.now().plusDays(1));
        orderList.add(order1);

        req.setProfitSharingFinishTimeOrderList(orderList);
        DubboResponse<Boolean> response = orderCommandProvider.batchUpdateProfitSharingFinishTime(req);

        System.err.println(JSON.toJSONString(response));

    }

    @Test
    public void updateStatusTest(){
        // orderMutateService.updateStatus
        OrderStatusUpdateReq req = new OrderStatusUpdateReq();
        req.setStatus(OrderStatusEnum.DELIVERING.getCode());
        req.setOriginStatus(11);
        req.setTenantId(2L);
        req.setOrderId(114277L);
        DubboResponse<Boolean> response = orderCommandProvider.updateStatus(req);
        System.err.println(JSON.toJSONString(response));

    }


    @Test
    public void selfLiftingTest(){
//        DubboResponse<Boolean> response = orderMutateService.selfLifting(req);
        OrderSelfLiftReq req = new OrderSelfLiftReq();
        req.setOrderId(114278L);
        DubboResponse<Boolean> response = orderCommandProvider.selfLifting(req);
        System.err.println(JSON.toJSONString(response));

    }

    @Test
    public void closeTest(){
//        DubboResponse<Boolean> response = orderMutateService.close(req);
        OrderCloseReq req = new OrderCloseReq();
        req.setOrderId(114279L);
        req.setTenantId(2L);
        DubboResponse<Boolean> response = orderCommandProvider.close(req);
        System.err.println(JSON.toJSONString(response));

    }

    @Test
    public void auditSuccessTest(){
//        Boolean flag = RpcResultUtil.handle(orderMutateService.auditSuccess(req));

        OrderAuditReq req = new OrderAuditReq();
        req.setOrderId(114280L);
        req.setTenantId(2L);
        DubboResponse<Boolean> response = orderCommandProvider.auditSuccess(req);
        System.err.println(JSON.toJSONString(response));

    }

}
