package com.cosfo.manage.facade.compare.command;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.provider.OrderItemCommandProvider;
import com.cosfo.ordercenter.client.req.OrderItemUpdateDeliveryQuantityReq;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: xiaowk
 * @time: 2024/5/9 上午11:08
 */
@SpringBootTest
public class OrderItemCommandProviderTest {

    @DubboReference
    private OrderItemCommandProvider orderItemCommandProvider;


    @Test
    public void updateDeliveryQuantityTest(){

        OrderItemUpdateDeliveryQuantityReq req = new OrderItemUpdateDeliveryQuantityReq();
        req.setOrderId(114277L);
        List<OrderItemUpdateDeliveryQuantityReq.OrderItemQuantity> orderItemQuantities = new ArrayList<>();
        OrderItemUpdateDeliveryQuantityReq.OrderItemQuantity orderItemQuantity = new OrderItemUpdateDeliveryQuantityReq.OrderItemQuantity();
        orderItemQuantity.setOrderItemId(291286L);
        orderItemQuantity.setQuantity(1);
        orderItemQuantities.add(orderItemQuantity);
        req.setOrderItemQuantities(orderItemQuantities);

        DubboResponse<Boolean> response = orderItemCommandProvider.updateDeliveryQuantity(req);
        System.err.println(JSON.toJSONString(response));

    }
}
