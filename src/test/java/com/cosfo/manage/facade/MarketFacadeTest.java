package com.cosfo.manage.facade;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.cofso.item.client.resp.MarketItemInfoResp;
import com.cofso.item.client.resp.MarketItemPriceStrategyUpdateResultResp;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.facade.convert.Convert;
import com.cosfo.manage.facade.dto.SummerfarmProductInfoDTO;
import com.cosfo.manage.market.model.dto.MarketItemPriceStrategyInput;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.client.saas.SaasInterfaceServiceProvider;
import net.summerfarm.manage.client.saas.req.SummerFarmSkuReq;
import net.summerfarm.manage.client.saas.resp.SummerfarmProductInfoResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class MarketFacadeTest {
    @Resource
    private MarketFacade marketFacade;
    @Resource
    private SummerFarmInterfaceServiceFacade summerFarmInterfaceServiceFacade;
    @DubboReference
    private SaasInterfaceServiceProvider saasInterfaceServiceProvider;

    @Test
    void batchUpdateTenantTargetTypePriceStrategy() {
        List<MarketItemPriceStrategyInput> inputs = new ArrayList<>();
        MarketItemPriceStrategyInput input = new MarketItemPriceStrategyInput();
        input.setItemId(9999999L);
        inputs.add(input);
        List<MarketItemPriceStrategyUpdateResultResp> respList = marketFacade.batchUpdateTenantTargetTypePriceStrategy(inputs);
        log.info("respList:" + JSON.toJSONString(respList));

    }

    @Test
    public void queryMarketItemList(){
        Long tenantId = 1L;
        Map<Long, Long> skuId2AgentSkuIDMap = new HashMap<>();
        skuId2AgentSkuIDMap.put(41L, 41L);
        skuId2AgentSkuIDMap.put(26774L, 22484L);
        skuId2AgentSkuIDMap.put(26824L, 22536L);

        Map<Long, MarketItemInfoResp> marketItemInfoRespMap = marketFacade.queryQuotationMarketItemMap(Lists.newArrayList(skuId2AgentSkuIDMap.keySet()));
        Map<Long, SummerfarmProductInfoDTO> summerfarmProductInfoDTOMap = callSummerfarmQuerySkuInfo(Lists.newArrayList(skuId2AgentSkuIDMap.values()));

        for (Map.Entry<Long, MarketItemInfoResp> entry : marketItemInfoRespMap.entrySet()) {
            Long skuId = entry.getKey();
            MarketItemInfoResp marketItemInfoResp = entry.getValue();
            SummerfarmProductInfoDTO summerfarmProductInfoDTO = summerfarmProductInfoDTOMap.get(skuId2AgentSkuIDMap.get(skuId));

            System.err.println("new ：skuId=" + skuId + " " + marketItemInfoResp.getMaxAfterSaleAmount() + " " + marketItemInfoResp.getAfterSaleUnit());
            System.err.println("old ：skuId=" + skuId + " " + summerfarmProductInfoDTO.getMaxAfterSaleAmount() + " " + summerfarmProductInfoDTO.getAfterSaleUnit());
            boolean flag = marketItemInfoResp.getMaxAfterSaleAmount().equals(summerfarmProductInfoDTO.getMaxAfterSaleAmount()) && marketItemInfoResp.getAfterSaleUnit().equals(summerfarmProductInfoDTO.getAfterSaleUnit());
            System.err.println("new == old:" + flag);
        }
    }



    public Map<Long, SummerfarmProductInfoDTO> callSummerfarmQuerySkuInfo(List<Long> supplyIds) {
        Map<Long, SummerfarmProductInfoDTO> summerfarmProductInfoDTOMap = new HashMap<>(NumberConstants.TEN);
        try {
            List<SummerfarmProductInfoDTO> summerfarmProductInfoDTOList = batchQuerySkuInfo(supplyIds);
            if (CollectionUtils.isEmpty(summerfarmProductInfoDTOList)) {
                return Collections.emptyMap();
            }
            return summerfarmProductInfoDTOList.stream().collect(Collectors.toMap(SummerfarmProductInfoDTO::getSkuId, item -> item));
        } catch (Exception e) {
            log.error("调用鲜沐rpc查询商品接口失败", e);
        }
        return summerfarmProductInfoDTOMap;
    }


    public List<SummerfarmProductInfoDTO> batchQuerySkuInfo(List<Long> agentSkuIds) {
        if(CollectionUtil.isEmpty (agentSkuIds)){
            return Collections.emptyList ();
        }
        SummerFarmSkuReq req = new SummerFarmSkuReq();
        req.setSkuIds(agentSkuIds);
        log.info ("调用鲜沐rpc查询商品接口,req={}", JSON.toJSONString (req));
        DubboResponse<List<SummerfarmProductInfoResp>> resp = saasInterfaceServiceProvider.batchQuerySkuInfo(req);
        log.info ("调用鲜沐rpc查询商品接口,resp={}", JSON.toJSONString (resp));
        if (!resp.isSuccess()) {
            throw new ProviderException(resp.getMsg());
        }
        List<SummerfarmProductInfoResp> data = resp.getData();
        if (CollectionUtils.isEmpty(data)) {
            return Lists.newArrayList();
        }
        return data.stream().map(Convert::summerfarmProductInfoResp2Dto).collect(Collectors.toList());
    }
}