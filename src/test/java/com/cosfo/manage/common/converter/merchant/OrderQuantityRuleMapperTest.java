package com.cosfo.manage.common.converter.merchant;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.merchant.model.dto.quantityrule.OrderQuantityRuleDTO;
import com.cosfo.manage.merchant.model.po.MerchantOrderQuantityRule;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
class OrderQuantityRuleMapperTest {


    @Test
    public void ruleToDtoTest() {
        MerchantOrderQuantityRule rule = JSON.parseObject("{\"rule_type\":1,\"rule\":\"{\\\"op\\\":\\\"and\\\", \\\"rule\\\":[{\\\"type\\\":1,\\\"amount\\\":1}]}\"}", MerchantOrderQuantityRule.class);
        OrderQuantityRuleDTO orderQuantityRuleDTO = OrderQuantityRuleMapper.INSTANCE.ruleToDTO(rule);
        System.out.println(orderQuantityRuleDTO);
    }

}