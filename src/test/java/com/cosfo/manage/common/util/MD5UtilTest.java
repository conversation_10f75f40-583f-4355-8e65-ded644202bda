package com.cosfo.manage.common.util;

import cn.hutool.core.util.NumberUtil;
import com.cosfo.manage.common.constant.NumberConstant;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/7/4
 */
class MD5UtilTest {

    @Test
    void string2MD5() {
        System.out.println(StringUtils.checkPassword("hello123456"));
        String s = MD5Util.string2MD5("2023@2679");
        System.out.println(s);
        System.out.println(convertMD5(convertMD5(s)));
        List<String> list =new ArrayList<>();
        list.add("西湖区");
        list.add("余杭区");
        list.add("滨江区");
        System.out.println(list.toString());
        String s1 = "0_222斤*2个";
        if(s1.contains("_")){
            String substring = s1.substring(NumberConstant.TWO);
            System.out.println(substring);
        }else {
            System.out.println(s1);
        }
    }

    public static String convertMD5(String inStr){

        char[] a = inStr.toCharArray();
        for (int i = 0; i < a.length; i++){
            a[i] = (char) (a[i] ^ 't');
        }
        String s = new String(a);
        return s;

    }

}