package com.cosfo.manage.common.util;

import com.cosfo.common.util.TimeUtils;
import com.cosfo.manage.common.util.qiNiu.QiNiuUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;


@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class TimeUtilsTest {

    @Test
    void changeString2Date() throws IOException{
       log.info(TimeUtils.changeString2Date("2023-09-04").toString());
    }

    @Test
    void compareTime() throws IOException{
        log.info("执行结果：" + TimeUtils.compareTime("2023-09-04","2023-09-03"));
    }

    @Test
    void getDatesBetweenTwoDate() throws IOException{
        log.info("执行结果：" + TimeUtils.getDatesBetweenTwoDate("2023-09-04","2023-09-03","yyyy-MM-dd"));
    }
}

