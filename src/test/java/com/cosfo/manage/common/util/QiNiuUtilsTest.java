package com.cosfo.manage.common.util;

import com.cosfo.manage.common.util.qiNiu.QiNiuUtils;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/6/27
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class QiNiuUtilsTest {

    @Test
    void uploadFile() {

    }

    @Test
    void downloadFile() throws IOException{
       //  QiNiuUtils.uploadPhoto("https://cdn.summerfarm.net/", "test/1ll4m7gwq87uymbrf.jpg");
        File file = new File("D:\\xianmu\\cosfo-manage\\1660209298593.xlsx");
        MultipartFile multipartFile = FileUtil.fileToMultipartFile(file);
        QiNiuUtils.uploadFile(multipartFile,"bill/1660209298593.xlsx");
    }

    @Test
    void copyFileBatch() {
        QiNiuUtils.copyFileBatch(new String[]{"111"});
    }
}