package com.cosfo.manage.common.task;

import net.xianmu.task.vo.input.XmJobInput;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@SpringBootTest
class SkuAgentHistoryTaskTest {

    @Resource
    private SkuAgentHistoryLogTask skuAgentHistoryLogTask;

    @Test
    void processResult() throws Exception {
        skuAgentHistoryLogTask.processResult(new XmJobInput());
    }

    @Test
    void processResultDate() throws Exception {
        XmJobInput xmJobInput = new XmJobInput();
        xmJobInput.setInstanceParameters("{\"tenantId\":2,\"skuIds\":[1,2,3]}");
        skuAgentHistoryLogTask.processResult(xmJobInput);
    }
}