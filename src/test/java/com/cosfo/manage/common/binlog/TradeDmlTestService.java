package com.cosfo.manage.common.binlog;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.model.bo.DtsModelBO;
import com.cosfo.manage.common.util.binlog.impl.PaymentDmlServiceImpl;
import com.cosfo.manage.common.util.binlog.impl.RefundDmlServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * @description:
 * @author: George
 * @date: 2023-11-01
 **/
@SpringBootTest
public class TradeDmlTestService {

    @Resource
    private PaymentDmlServiceImpl paymentDmlService;
    @Resource
    private RefundDmlServiceImpl refundDmlService;
    @Test
    public void testGenerateTradePaymentSummary() {
        String jsonData = "{\"data\":[{\"tenant_id\":\"2\",\"update_time\":\"2023-11-01 14:50:29\",\"id\":\"71156\",\"store_id\":\"4064\",\"total_price\":\"1.02\",\"create_time\":\"2023-11-01 14:50:29\",\"sp_openid\":\"o3geR6grjvmN33eTPFgs9Bhu9MCI\",\"account_id\":\"3324\",\"payment_no\":\"P1719607828032196608\",\"trade_type\":\"BILL\",\"status\":\"1\"}],\"database\":\"cosfodb\",\"msgKey\":\"********\",\"old\":[{\"status\":\"0\"}],\"table\":\"payment\",\"type\":\"UPDATE\"}";
        DtsModelBO dtsModelBO = JSON.parseObject(jsonData, DtsModelBO.class);
        paymentDmlService.tableDml(dtsModelBO);
    }

    @Test
    public void testGenerateTradeRefundSummary() {
        String jsonData = "{\"data\":[{\"tenant_id\":\"2\",\"payment_price\":\"1.02\",\"refund_status\":\"2\",\"create_time\":\"2023-11-01 15:53:06\",\"refund_no\":\"R1719623585352847360\",\"refund_price\":\"1.02\",\"retry_num\":\"0\",\"update_time\":\"2023-11-01 15:54:00\",\"after_sale_id\":\"12816\",\"payment_id\":\"71166\",\"id\":\"166376\"}],\"database\":\"cosfodb\",\"msgKey\":\"********\",\"old\":[{\"refund_status\":\"1\"}],\"table\":\"refund\",\"type\":\"UPDATE\"}";
        DtsModelBO dtsModelBO = JSON.parseObject(jsonData, DtsModelBO.class);
        refundDmlService.tableDml(dtsModelBO);
    }
}
