package com.cosfo.manage.agentorder.service;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;


/**
 * @author: monna.chen
 * @Date: 2024/3/1 11:15
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class PlanOrderServiceTest {

    @Resource
    private PlanOrderService planOrderService;

    @Test
    public void testExportStatement() {
        String agentOrderNo = "AO17092841833838545";
        planOrderService.autoCancelPanOrder(agentOrderNo);
    }

}
