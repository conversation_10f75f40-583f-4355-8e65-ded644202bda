package com.cosfo.manage.downloadcenter.agentorderouter;

import net.xianmu.authentication.client.dto.ShiroUser;
import net.xianmu.download.support.dto.DownloadCenterDataMsg;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest
class AgentOrderImportOuterHandlerTest {


    @Resource
    private AgentOrderImportOuterHandler agentOrderImportOuterHandler;

    @Test
    void dealExcelData() {

        List<AgentOrderOuterExcelDTO> list = new ArrayList<>();

        AgentOrderOuterExcelDTO dto1 = new AgentOrderOuterExcelDTO();
        dto1.setStoreNo("xwk123");
        dto1.setItemCode("ITEM11");
        dto1.setItemAmount(1);

        AgentOrderOuterExcelDTO dto2 = new AgentOrderOuterExcelDTO();
        dto2.setStoreNo("xwk123");
//        dto2.setItemCode(48242L);
        dto2.setItemCode("ITEM11344333");
        dto2.setItemAmount(1);

        AgentOrderOuterExcelDTO dto3 = new AgentOrderOuterExcelDTO();
        dto3.setStoreNo("xwk123");
        dto3.setItemCode("11");
        dto3.setItemAmount(1);

        AgentOrderOuterExcelDTO dto4 = new AgentOrderOuterExcelDTO();
        dto4.setStoreNo("xwk123");
        dto4.setItemCode("test123"); // 倍数订货3
        dto4.setItemAmount(3);

        AgentOrderOuterExcelDTO dto5 = new AgentOrderOuterExcelDTO();
        dto5.setStoreNo("12334");
        dto5.setItemCode("test123"); // 倍数订货3
        dto5.setItemAmount(1);


        list.add(dto1);
        list.add(dto2);
        list.add(dto3);
        list.add(dto4);
        list.add(dto5);

        DownloadCenterDataMsg downloadCenterDataMsg = new DownloadCenterDataMsg();
        ShiroUser shiroUser = new ShiroUser();
        shiroUser.setTenantId(2L);
        shiroUser.setId(10170L);
        downloadCenterDataMsg.setAuthUser(shiroUser);

        agentOrderImportOuterHandler.dealExcelData(list, downloadCenterDataMsg);
    }
}