package com.cosfo.manage.downloadcenter.agentorder;

import net.xianmu.authentication.client.dto.ShiroUser;
import net.xianmu.download.support.dto.DownloadCenterDataMsg;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest
class AgentOrderImportHandlerTest {

    @Resource
    private AgentOrderImportHandler agentOrderImportHandler;

    @Test
    void dealExcelData() {

        List<AgentOrderExcelDTO> list = new ArrayList<>();

        AgentOrderExcelDTO dto1 = new AgentOrderExcelDTO();
        dto1.setStoreId(4260L);
        dto1.setItemId(49937L);
        dto1.setItemAmount(1);

        AgentOrderExcelDTO dto2 = new AgentOrderExcelDTO();
        dto2.setStoreId(4260L);
//        dto2.setItemId(48242L);
        dto2.setItemId(49571111L);
        dto2.setItemAmount(1);

        AgentOrderExcelDTO dto3 = new AgentOrderExcelDTO();
        dto3.setStoreId(162463L);
        dto3.setItemId(49937L);
        dto3.setItemAmount(1);

        AgentOrderExcelDTO dto4 = new AgentOrderExcelDTO();
        dto4.setStoreId(4260L);
        dto4.setItemId(49847L); // 倍数订货3
        dto4.setItemAmount(3);

        AgentOrderExcelDTO dto5 = new AgentOrderExcelDTO();
        dto5.setStoreId(111L);
        dto5.setItemId(49847L); // 倍数订货3
        dto5.setItemAmount(1);


        list.add(dto1);
        list.add(dto2);
        list.add(dto3);
        list.add(dto4);
        list.add(dto5);

        DownloadCenterDataMsg downloadCenterDataMsg = new DownloadCenterDataMsg();
        ShiroUser shiroUser = new ShiroUser();
        shiroUser.setTenantId(2L);
        shiroUser.setId(10170L);
        downloadCenterDataMsg.setAuthUser(shiroUser);

        agentOrderImportHandler.dealExcelData(list, downloadCenterDataMsg);
    }
}