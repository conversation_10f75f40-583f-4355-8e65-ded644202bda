package com.cosfo.manage.task;

import com.cosfo.manage.common.task.ScheduledTasks;
import lombok.SneakyThrows;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * @author: monna.chen
 * @Date: 2023/8/31 14:39
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class ScheduledTasksTest {

    @Resource
    private ScheduledTasks scheduledTasks;


    @SneakyThrows
    @Test
    public void processResult() {
        XmJobInput jobContext = new XmJobInput();
//        XmJobInput jobContext = JobContext.newBuilder()
//            .setJobParameters("")
//           /* .setJobParameters("{\n" +
//                "    \"jobName\":\"token_refresh1111\"\n" +
//                "}")*/
//            .build();
        scheduledTasks.processResult(jobContext);
    }
}
