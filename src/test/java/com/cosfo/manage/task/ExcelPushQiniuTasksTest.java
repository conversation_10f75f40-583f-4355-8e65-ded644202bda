package com.cosfo.manage.task;

import com.cosfo.manage.common.task.ExcelPushQiniuTasks;
import com.cosfo.manage.common.task.InitMarketItemTask;
import com.cosfo.manage.common.util.qiNiu.QiNiuUtils;
import lombok.SneakyThrows;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * @author: monna.chen
 * @Date: 2023/8/31 14:39
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class ExcelPushQiniuTasksTest {

    @Resource
    private ExcelPushQiniuTasks excelPushQiniuTasks;

    @Resource
    private InitMarketItemTask InitMarketItemTask;
    @SneakyThrows
    @Test
    public void uploadFile() {
        String filePath = "/Users/<USER>/Documents/workspace-xm/cosfo-manage/src/main/resources/excel/门店批量导入模板.xlsx";
        String url = "file/门店批量导入模板.xlsx";
        String filename ="门店批量导入模板.xlsx";

//        String url = "file/dev/报价单导入模板.xlsx";
//        String filename ="dev/报价单导入模板.xlsx";

        QiNiuUtils.deleteFile(new String[]{url});
        QiNiuUtils.uploadFile(filePath, filename);
    }
    @SneakyThrows
    @Test
    public void processResult() {
        XmJobInput jobContext = new XmJobInput();
        jobContext.setInstanceParameters("6");
        excelPushQiniuTasks.processResult(jobContext);
    }
    @SneakyThrows
    @Test
    public void initMarketItemTask() {
        XmJobInput jobContext = new XmJobInput();
        jobContext.setInstanceParameters("{\n" +
                "    \"tenant\": \"1024\",\n" +
                "    \"goodsType\": \"1\"\n" +
                "}");
        InitMarketItemTask.processResult(jobContext);
    }
}
