package com.cosfo.manage.task;


import com.cosfo.manage.common.task.PullPosOrderFLTask;
import com.cosfo.manage.common.task.PullPosOrderTask;
import com.cosfo.manage.common.task.PullPosOrderZXTask;
import lombok.SneakyThrows;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
@RunWith(SpringRunner.class)
@SpringBootTest
public class PullPosOrderTaskTest {
    @Autowired
    private PullPosOrderTask task;
    @Autowired
    private PullPosOrderZXTask zxTask;
    @Autowired
    private PullPosOrderFLTask flTask;

    @SneakyThrows
    @Test
    public void processResultZX() {
        XmJobInput jobContext = new XmJobInput();
        flTask.processResult(jobContext);
    }
}
