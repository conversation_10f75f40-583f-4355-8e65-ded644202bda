package com.cosfo.manage.task;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.manage.common.constant.XianmuSupplyTenant;
import com.cosfo.manage.common.listener.AddProductPricingSupplyListener;
import com.cosfo.manage.common.task.PreDeliveryOrderAutoCloseTask;
import com.cosfo.manage.common.task.SupplierOrderTotalNotifyTask;
import com.cosfo.manage.common.task.XianmuProductCityStockTask;
import com.cosfo.manage.product.mapper.ProductAgentSkuMappingMapper;
import com.cosfo.manage.product.model.po.ProductAgentSkuMapping;
import com.cosfo.manage.report.service.ProductAgentStockReportService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author: fansongsong
 * @Date: 2023-05-19
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TaskTest {

    @Resource
    private XianmuProductCityStockTask xianmuProductCityStockTask;
    @Resource
    private AddProductPricingSupplyListener addProductPricingSupplyListener;
    @Resource
    private ProductAgentStockReportService productAgentStockReportService;
    @Resource
    private ProductAgentSkuMappingMapper productAgentSkuMappingMapper;
    @Resource
    private SupplierOrderTotalNotifyTask supplierOrderTotalNotifyTask;
    @Resource
    private PreDeliveryOrderAutoCloseTask autoCreatePreDeliveryAfterSaleTask;

    @Test
    public void autoCreatePreDeliveryAfterSaleTask() {
        ProcessResult processResult = autoCreatePreDeliveryAfterSaleTask.processResult(new XmJobInput());
        Assert.assertTrue(null != processResult);
        log.info("DONE:{}", processResult);
    }

    @Test
    public void testSupplierOrderTotalNotifyTask() throws InterruptedException {
        ProcessResult processResult = supplierOrderTotalNotifyTask.processResult(new XmJobInput());
        Assert.assertTrue(null != processResult);
        log.info("DONE:{}", processResult);
    }

    @Test
    public void xianmuProductCityStockTasktest() {
        xianmuProductCityStockTask.processResult(null);
    }

    @Test
    public void testXianmuProductCityStockTask() {
        ProcessResult processResult = xianmuProductCityStockTask.processResult(new XmJobInput());
        Assert.assertTrue(null != processResult);
        log.info("DONE:{}", processResult);
    }

    @Test
    public void onMessage() {
//        SupplyMessageDTO supplyMessageDTO = new SupplyMessageDTO();
//        supplyMessageDTO.setSupplyIdList(Lists.newArrayList(1090L));
//        addProductPricingSupplyListener.process(supplyMessageDTO);

        productAgentStockReportService.correction(255L);
    }

    @Test
    public void stockChangeSync() {
        String sku = "1052535102021";
        int areaNo = 1;
        // 不存在相应鲜沐报价单，return
        ProductAgentSkuMapping productAgentSkuMapping = productAgentSkuMappingMapper.selectByAgentTenantInfo(XianmuSupplyTenant.TENANT_ID, sku);
        if (Objects.isNull(productAgentSkuMapping)) {
            return;
        }

        // 问题1:wnc 返回的仓库编号查询到福州市，并匹配到城市报价单,但是根据sku+福州市查询，无法查到城市下的仓库列表
        // 问题2:pms 列表接口，获取补货时间RPC接口异常
        // 问题3:wnc 根据城市+sku获取仓库列表好像会抛出异常 ea1af401e816846510800023477d0001
        // queryWarehouseStorageFence查询自营仓围栏配送顺序请求报文{"city":"宁波市","skuList":["6153867337"],"tenantId":1}
        productAgentStockReportService.stockChangeSync(productAgentSkuMapping, areaNo);
    }
}
