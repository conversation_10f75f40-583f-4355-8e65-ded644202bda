package com.cosfo.manage.task;

import com.cosfo.manage.TestApplication;
import com.cosfo.manage.facade.SummerFarmInterfaceServiceFacade;
import com.cosfo.manage.good.service.SyncProductService;
import com.cosfo.manage.product.service.ProductSkuService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 *
 * @author: xiaowk
 * @time: 2023/6/27 上午10:57
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {TestApplication.class})
public class ListenerTest {

    @Resource
    private SummerFarmInterfaceServiceFacade summerFarmInterfaceServiceFacade;
    @Autowired
    private ProductSkuService productSkuService;
    @Resource
    private SyncProductService syncProductService;


    @Test
    public void testAddTenantInfoListener() {
        Long adminId = 4610L;
        List<Long> skuIdList = summerFarmInterfaceServiceFacade.querySkuIdsByAdminId(adminId);

        Long tenantId = 24529L;
        syncProductService.syncAgentProductFromXianmu(tenantId, skuIdList);
    }


}
