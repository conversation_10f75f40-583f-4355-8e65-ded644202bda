package com.cosfo.manage.test;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.common.util.TimeUtils;
import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.ordercenter.client.resp.OrderItemSnapshotDTO;
import com.google.common.collect.Lists;
import net.xianmu.common.result.CommonResult;
import org.apache.commons.lang3.RandomStringUtils;

import java.math.BigDecimal;
import java.text.Collator;
import java.text.DecimalFormat;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Test {

    private static final String PASSWORD_REGEX = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[-!@#$%^&*()_+{}|\\\\:;\"'<>,.?/~`+=])[\\w!@#$%^&*()_+{}|\\\\:;\"'<>,.?/~`+=]{8,20}$";

    @org.junit.Test
    public void test(){
        String password = "Qq123456";
        boolean success = StringUtils.checkAccountPassword(password);
        System.err.println(success);

//        int successNum = 0;
//        for (int i = 0; i < 10000; i++) {
//            String randomNumber = RandomStringUtils.randomAlphabetic(4);
//            randomNumber = "2024@8646" + randomNumber;
//            boolean matcher = Pattern.compile(PASSWORD_REGEX).matcher(randomNumber).matches();
//            if (matcher) {
//                successNum++;
//            }
//            System.err.println(randomNumber + "是否符合密码规则:" + matcher);
//        }
//        System.err.println("successNum=" + successNum);

        Map<Long, OrderItemSnapshotDTO> orderItemSnapshotDTOMap = null;
        long orderItemId = 1;
        orderItemSnapshotDTOMap = Collections.emptyMap();
        Long skuId = Optional.ofNullable(orderItemSnapshotDTOMap).map(map -> map.get(orderItemId)).map(OrderItemSnapshotDTO::getSkuId).orElse(null);
        System.err.println(skuId);
        String keyWord ="(tenantId:%s, phone:%s, authUserId:%s)";
        String format = String.format(keyWord, "12aa3", "3aa21", "12aa3");
        System.err.println(format);
        keyWord = "{\"amount13\":\"%s\",\"time14\":\"%s\",\"const16\":\"%s\"}";
        format = String.format(keyWord, "123", "321", "123");
        System.err.println(format);

        Date currentDate = new Date();
        String endTime = TimeUtils.changeDate2String(currentDate, TimeUtils.FORMAT);
        String startTime = TimeUtils.getBeforeTimeString(currentDate, 5);
        startTime = startTime + StringConstants.CUT_OFF_TIME;
        System.err.println(endTime);
        System.err.println(startTime);
        System.err.println(DecimalFormat.getNumberInstance().format(0));
        BigDecimal value = new BigDecimal(12243).setScale(2,BigDecimal.ROUND_HALF_UP);


        DecimalFormat df1 = new DecimalFormat("##,##0.00");

        String strVal = df1.format(value);
        System.err.println(strVal +"-"+DecimalFormat.getNumberInstance().format(BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP)));
        System.err.println(new DecimalFormat("##,##0.00").format(BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP)));
    }

    @org.junit.Test
    public void sort(){

        AtomicReference<Integer> refund = null;

        System.err.println(refund.get());

        List<Integer> a = Lists.newArrayList(1, 2, 1339);
        List<Integer> b = Lists.newArrayList(2, 1339);
        System.err.println(a.containsAll(b));
        System.err.println(String.format(Constants.DELIVERY_QUERY_URL, "顺丰快", "1111111111115"));

        String json = "{\"data\":[\" Buffalo\",\"\\\\\\\"兴鹏物流\",\"apgecommerce\",\"beiou express\",\"CBL Logistica\",\"CK物流\",\"DFGL\",\"DF物流\",\"dhluk\",\"Dotzot\",\"DYB\",\"E2G速递\",\"Ecom Express\",\"EFSPOST\",\"EMS物流\",\"ETEEN专线\",\"E速达\",\"GTT EXPRESS快递\",\"LBC Express\",\"Lite Express\",\"LUCFLOW EXPRESS\",\"mailamericas\",\"MCIH国际快递\",\"MoreLink\",\"Pandu Logistics\",\"PP速运\",\"WATT\",\"YDH\",\"一智通\",\"一正达速运\",\"一起送\",\"一辉物流\",\"一运全成物流\",\"一速递\",\"一邦速递\",\"万家康物流\",\"万家物流\",\"万理诺物流\",\"万达美\",\"万邑通\",\"三志物流\",\"三态速递\",\"三盛快递\",\"三真驿道\",\"三象速递\",\"上大物流\",\"上海生生物流\",\"上海缤纷物流\",\"上海航瑞货运\",\"世华通物流\",\"世航通运\",\"世运快递\",\"东方汇\",\"东瀚物流\",\"东红物流\",\"东风全球速递\",\"两点之间\",\"中俄速通（淼信）\",\"中健云康\",\"中天万运\",\"中安物流\",\"中宏物流\",\"中强物流\",\"中技物流\",\"中时顺物流\",\"中欧物流\",\"中汲物流\",\"中环快递\",\"中电华远物流\",\"中融泰隆\",\"中迅三方\",\"中运全速\",\"中远快运\",\"中途国际速递\",\"中途速递\",\"中通冷链\",\"中通快运\",\"中通快递\",\"中速快递\",\"中邮物流\",\"中邮电商\",\"中邮速递\",\"中铁快运\",\"中铁物流供应链\",\"中铁飞豹\",\"中集冷云\",\"丰程物流\",\"丰网速运\",\"丰羿\",\"丰通快运\",\"丹递56\",\"丹鸟\",\"丽狮物流\",\"久久物流\",\"久易快递\",\"乐天速递\",\"乐达全球速递\",\"乐递供应链\",\"九曳供应链\",\"云南滇驿物流\",\"云南诚中物流\",\"云达通\",\"五六快运\",\"亚历克斯供应链\",\"亚风速递\",\"京东快运\",\"京东物流\",\"京广速递\",\"人人转运\",\"亿德隆物流\",\"亿隆速运\",\"亿领速运\",\"伍圆速递\",\"众派速递\",\"众辉达物流\",\"众邮快递\",\"优优速递\",\"优联吉运\",\"优莎速运\",\"优速\",\"优速通达\",\"优邦速运\",\"传喜物流\",\"佩奇集运\",\"佰乐捷通\",\"佰纳博通\",\"佰麒快递\",\"佳吉快运\",\"佳吉快递\",\"佳家通货运\",\"佳怡物流\",\"佳成快递 \",\"佳捷翔物流\",\"俄顺达\",\"信丰物流\",\"元智捷诚\",\"光线速递\",\"全一快递\",\"全之鑫物流\",\"全信通快递\",\"全峰快递\",\"全川摩运\",\"全日通\",\"全时速运\",\"全程快递\",\"全网物流\",\"全联速运\",\"全速物流\",\"全速通\",\"全际通\",\"八达通\",\"兰州伙伴物流\",\"共联配\",\"共速达\",\"冠捷物流 \",\"冠达丰物流\",\"准实快运\",\"凡仕特物流\",\"凡宇快递\",\"凤凰快递\",\"凯信达\",\"创一快递\",\"创运物流\",\"力展物流\",\"加佳物流\",\"加州猫速递\",\"加拿大龙行速运\",\"加运美\",\"北极星快运\",\"十方通物流\",\"千里速递\",\"千顺快递\",\"华中快递\",\"华企快运\",\"华光国际快运\",\"华夏货运\",\"华欣物流\",\"华瀚快递\",\"华美快递\",\"华航快递\",\"华赫物流\",\"华达快运\",\"华通快运\",\"卓实快运\",\"卓志速运\",\"卓烨快递\",\"南天物流\",\"南方传媒物流\",\"卡邦配送\",\"原飞航\",\"叁虎物流\",\"友家速递\",\"双鹤物流\",\"叮咚快递\",\"可可树美中速运\",\"合心速递\",\"吉运家物流\",\"同城快寄\",\"同舟行物流\",\"名航速运\",\"品信快递\",\"品速心达快递\",\"品骏快递\",\"哥士传奇速递\",\"哪吒速运\",\"商桥物流\",\"商海德物流\",\"嗖一下同城快递\",\"嘉荣物流\",\"嘉诚速达\",\"嘉贤物流\",\"嘉里大荣物流\",\"嘉里大通\",\"四季安物流\",\"四川星程快递\",\"四海捷运\",\"国迅物流\",\"国送快运\",\"国顺达物流\",\"圆通速递\",\"圣安物流\",\"城宁供应链\",\"城市映急\",\"城通物流\",\"城铁速递\",\"城际快递\",\"堡昕德速递\",\"壹米滴答\",\"大洋物流\",\"大田物流\",\"大达物流\",\"大道物流\",\"大韩通运\",\"天地华宇\",\"天天快物流\",\"天天快递\",\"天纵物流\",\"天翔快递\",\"天翼快递\",\"天联快运\",\"天辰物流\",\"天马迅达\",\"天鸽快运\",\"奔腾物流\",\"好来运\",\"威时沛运货运\",\"威盛快递\",\"威速递\",\"宁夏万家通\",\"宅急送\",\"宇佳物流\",\"宇航通物流\",\"宇达物流\",\"宇鑫物流\",\"安世通快递\",\"安信达\",\"安家同城快运\",\"安得物流\",\"安捷物流\",\"安敏物流\",\"安时递\",\"安能快运\",\"安能快递\",\"安达速递\",\"安迅物流\",\"安鲜达\",\"宏品物流\",\"宏桥国际物流\",\"宏运发物流\",\"宏递快运\",\"宜送\",\"宜送物流\",\"实利配送\",\"家家通快递\",\"容智快运\",\"富吉速运\",\"小飞侠速递\",\"尚橙物流\",\"尚途国际货运\",\"山西建华\",\"平安达腾飞\",\"广东诚通物流\",\"广东通路\",\"广博物流\",\"广州信邦\",\"广州安能聚创物流\",\"彩丰物流\",\"彪记快递\",\"御风速运\",\"微特派\",\"德中快递\",\"德坤物流\",\"德方物流\",\"德淘邦\",\"德速电商物流\",\"德邦\",\"德邦快递\",\"心怡物流\",\"志腾物流\",\"忠信达\",\"快卡\",\"快弟来了\",\"快捷快物流\",\"快捷物流\",\"快捷速递\",\"快服务\",\"快达物流\",\"快速递\",\"急先达\",\"急顺通\",\"恒宇运通\",\"恒瑞物流\",\"恒路物流\",\"想乐送\",\"成都东骏物流\",\"成都立即送\",\"户通物流\",\"承诺达\",\"报通快递\",\"拉火速运\",\"招金精炼\",\"捎客物流\",\"捷仕\",\"捷安达\",\"捷祥物流\",\"捷运达快递\",\"探路速运\",\"敏華物流\",\"斑马物流\",\"新亚物流\",\"新元快递\",\"新宁物流\",\"新宏达物流\",\"新干线快递\",\"新时速物流\",\"新易泰\",\"新杰物流\",\"新速航\",\"新颜物流\",\"新骐点物流\",\"新鹏快递\",\"无忧物流\",\"无限速递\",\"无限配\",\"日日顺快线\",\"日日顺智慧物联\",\"日日顺物流\",\"日昱物流\",\"早道佳\",\"时安达速递\",\"时达通\",\"明亮物流\",\"明大快递\",\"明辉物流\",\"易优包裹\",\"易普递\",\"易航物流\",\"易达快运\",\"易达通\",\"易通达\",\"星云速递\",\"星运快递\",\"星速递\",\"春风物流\",\"晋越快递\",\"普畅物流\",\"景光物流\",\"景顺物流\",\"智谷特货\",\"智通物流\",\"杰响物流\",\"极兔速递\",\"极地快递\",\"极速达物流\",\"林安物流\",\"柬埔寨中通\",\"株式会社T.M.G\",\"楽道物流\",\"次晨达物流\",\"欧利\",\"正途供应链\",\"武汉优进汇\",\"民航快递\",\"水趣到家\",\"永世通物流\",\"永邦快递\",\"汇峰物流\",\"汇强快递\",\"汇捷物流\",\"汇森速运\",\"汇达物流\",\"汇通天下物流\",\"汇霖大货网\",\"汉信快递\",\"河南全速通\",\"法翔速运\",\"泛太优达\",\"泛球物流\",\"波兰小包(Poczta Polska)\",\"波音速递\",\"泰嘉物流\",\"泰实货运\",\"泰进物流\",\"派尔快递\",\"浩博物流\",\"浩运物流\",\"海中转运\",\"海信物流\",\"海星桥快递\",\"海淘物流\",\"海盟速递\",\"海硕高铁速递\",\"海红网送\",\"海联快递\",\"润百特货\",\"润禾物流\",\"淘特物流快递\",\"深圳德创物流\",\"渡石医药\",\"渭鹏速递\",\"湘粤华通\",\"源安达\",\"澳达国际物流\",\"牛仔速运\",\"特急便物流\",\"特急送\",\"猴急送\",\"王牌快递\",\"玥玛速运\",\"环东物流\",\"环创物流\",\"环国运物流\",\"珠峰速运\",\"申必达\",\"申通快递\",\"畅邮速递\",\"疯狂快递\",\"百世云配\",\"百世快运\",\"百世快递\",\"百事亨通\",\"百千诚物流\",\"百米快运\",\"百腾物流\",\"百通物流\",\"皇家云仓\",\"皇家国际速运\",\"皇家物流\",\"皮牙子快递\",\"盈通物流\",\"益企发物流\",\"益加盛快递\",\"盛丰物流\",\"盛辉物流\",\"盛通快递\",\"睿和泰速运\",\"神马快递\",\"神骏物流\",\"祥龙运通物流\",\"禄昌物流\",\"科捷物流\",\"秦岭智能速运\",\"秦远物流\",\"秦邦快运\",\"穗佳物流\",\"穗航物流\",\"立白宝凯物流\",\"签收快递\",\"粤九通物流\",\"红背心\",\"红远物流\",\"红马甲物流\",\"维普恩物流\",\"美七国际快递\",\"美乐维冷链物流\",\"美泰物流\",\"美联快递\",\"美达快递\",\"美通\",\"翔宇物流\",\"翔腾物流\",\"翰丰快递\",\"耀奇物流\",\"耀飞同城快递\",\"老扬州物流\",\"考拉速递\",\"联合速运\",\"联昊通\",\"联运快递\",\"联运通物流\",\"联邦快递\",\"联邦快递-英文\",\"聚中大\",\"聚物物流\",\"聚盟共建\",\"聚盟物流\",\"聚鼎物流\",\"能装能送\",\"能达速递\",\"臣邦同城\",\"航宇快递\",\"艾瑞斯远\",\"芒果速递\",\"芝麻开门\",\"苏宁物流\",\"苏豪快递\",\"英脉物流\",\"荣庆物流\",\"荣通国际\",\"蓝天物流\",\"蓝弧快递\",\"蓝镖快递\",\"蕴国物流\",\"蜜蜂速递\",\"行必达\",\"衫达快运\",\"袋鼠速递\",\"西安喜来快递\",\"西安运逸快递\",\"西游寄速递\",\"诚和通\",\"豌豆物流\",\"豪翔物流\",\"贝业物流\",\"货六六\",\"货拉拉物流\",\"贰仟家物流\",\"贵州星程快递\",\"越丰物流\",\"跨越速运\",\"路尊物流\",\"路港易通\",\"路遥物流\",\"路邦物流\",\"车联天下\",\"转瞬达集运\",\"达发物流\",\"达方物流\",\"达速物流\",\"迅达速递\",\"迅选物流\",\"迅速快递\",\"迈隆递运\",\"运通快运\",\"近端\",\"远成快运\",\"远成物流\",\"远盾物流\",\"远辉物流\",\"远通盛源\",\"逐电快运\",\"递五方云仓\",\"递四方\",\"递达速运\",\"通和天下\",\"通胜物流\",\"通达兴物流\",\"通达物流\",\"速佳达快运\",\"速呈\",\"速呈宅配\",\"速品快递\",\"速尔快递\",\"速必达\",\"速派快递\",\"速翼快递\",\"速腾快递\",\"速舟物流\",\"速豹\",\"速达通\",\"速远同城快递\",\"速通物流\",\"速通鸿达\",\"速速达\",\"速邮达\",\"速风快递\",\"邦泰快运\",\"邦送物流\",\"邮政标准快递\",\"邮来速递\",\"邮驿帮高铁速运\",\"邮鸽速运\",\"郑州速捷\",\"配思货运\",\"重庆星程快递\",\"金大物流\",\"金岸物流\",\"鑫世锐达\",\"鑫圣邦物流\",\"鑫宏福物流\",\"鑫宸物流\",\"鑫梦成\",\"鑫正一快递\",\"鑫锐达\",\"钏博物流\",\"铁中快运\",\"银捷速递\",\"银河物流\",\"银雁专送\",\"锋鸟物流\",\"锐界同城速递\",\"锦程快递\",\"锦程物流\",\"长吉物流\",\"长顺海运\",\"长风物流\",\"闪货极速达\",\"阳光快递\",\"阿联酋(Emirates Post)\",\"陪行物流\",\"隆浪快递\",\"集先锋快递\",\"雪域快递\",\"雪域易购\",\"青云物流\",\"青岛安捷快递\",\"韵达快运\",\"韵达快递\",\"顺丰-繁体\",\"顺丰冷链\",\"顺丰快运\",\"顺心捷达\",\"顺捷丰达\",\"顺捷美中速递\",\"顺捷达\",\"顺时达物流\",\"顺昌国际\",\"顺达快递\",\"顺通快递\",\"领送送\",\"飞力士物流\",\"飞康达\",\"飞快达\",\"飞狐快递\",\"飞豹快递\",\"飞豹速递\",\"飞邦快递\",\"飞鹰物流\",\"首达速运\",\"首通快运\",\"驿将快运\",\"驿递汇速递\",\"骏达快递\",\"高捷快运\",\"高田物流\",\"高考通知书\",\"高铁快运\",\"高铁速递\",\"魔速达\",\"鲁通快运\",\"鸢速货运\",\"鸿泰物流\",\"鸿远物流\",\"黄马甲\",\"黑猫同城送\",\"黑猫速运\",\"黑豹物流\",\"鼎润物流\",\"鼹鼠快送\",\"龙兴物流\",\"龙行天下\"],\"errCode\":\"\",\"msg\":\"请求成功\",\"status\":200}";
        CommonResult commonResult = JSON.parseObject(json, CommonResult.class);
        List<String> list = (List<String>) commonResult.getData();
        Comparator<Object> com= Collator.getInstance(java.util.Locale.CHINA);
        Collections.sort(list, com);
        for(String i:list){
            System.out.print(i+"  ");
        }
    }
}
